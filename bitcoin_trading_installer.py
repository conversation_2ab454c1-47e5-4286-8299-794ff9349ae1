#!/usr/bin/env python3
"""
📦 BITCOIN TRADING GUI PROFESSIONAL INSTALLER 📦
===============================================
🏆 KOMPLETTE INSTALLATION + SCRIPT-VERLINKUNG + DESKTOP-VERKNÜPFUNG 🏆
✅ Installiert Bitcoin Trading GUI Professional standalone
✅ Kopiert alle 3 besten funktionierenden Modelle
✅ Verlinkt Script-Verzeichnisse für unabhängige Ausführung
✅ Erstellt Desktop-Verknüpfung für Ein-Klick-Start
✅ Konfiguriert Python-Umgebung automatisch
✅ Unabhängig von Visual Studio - Standalone-Installation

💡 KOMPLETTER INSTALLER FÜR PROFESSIONELLE INSTALLATION!
"""

import os
import sys
import shutil
import subprocess
import tkinter as tk
from tkinter import messagebox, filedialog, ttk
import json
from datetime import datetime

class BitcoinTradingInstaller:
    """
    📦 BITCOIN TRADING GUI PROFESSIONAL INSTALLER
    ============================================
    Kompletter Installer für standalone Installation
    mit allen 3 besten Modellen und Script-Verlinkung.
    """
    
    def __init__(self):
        # INSTALLER GUI
        self.root = tk.Tk()
        self.root.title("📦 Bitcoin Trading GUI Professional - Installer")
        self.root.geometry("800x600")
        self.root.configure(bg='#1a1a1a')
        
        # INSTALLATION KONFIGURATION
        self.installation_path = None
        self.script_directory = os.getcwd()
        self.desktop_shortcut = True
        self.copy_models = True
        
        # DIE 3 BESTEN MODELLE
        self.models = [
            {
                'name': '🏅 FAVORIT - Das Bewährte System',
                'file': 'ultimate_complete_bitcoin_trading_FAVORITE.py',
                'required': True,
                'found': False
            },
            {
                'name': '🚀 OPTIMIERT - Das Schnelle System',
                'file': 'btc_ultimate_optimized_complete.py',
                'required': False,
                'found': False
            },
            {
                'name': '🧠 KI-SYSTEM - Das Intelligente System',
                'file': 'ultimate_self_learning_ai_bitcoin_trading.py',
                'required': False,
                'found': False
            }
        ]
        
        # GUI ERSTELLEN
        self.create_installer_gui()
        
        # INITIAL SCAN
        self.scan_models()
        
        print("📦 Bitcoin Trading GUI Professional Installer gestartet")
    
    def create_installer_gui(self):
        """Erstelle Installer GUI"""
        
        # TITEL
        title_frame = tk.Frame(self.root, bg='#1a1a1a')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="📦 Bitcoin Trading GUI Professional",
            font=('Arial', 20, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="Komplette Installation • 3 Beste Modelle • Standalone",
            font=('Arial', 12),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        subtitle_label.pack()
        
        # HAUPTBEREICH
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # MODELL STATUS
        models_frame = tk.LabelFrame(
            main_frame,
            text="📊 Verfügbare Modelle",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d'
        )
        models_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.models_list = tk.Frame(models_frame, bg='#2d2d2d')
        self.models_list.pack(fill=tk.X, padx=10, pady=10)
        
        # INSTALLATION OPTIONEN
        options_frame = tk.LabelFrame(
            main_frame,
            text="⚙️ Installations-Optionen",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d'
        )
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        # INSTALLATIONS-PFAD
        path_frame = tk.Frame(options_frame, bg='#2d2d2d')
        path_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(
            path_frame,
            text="📁 Installations-Verzeichnis:",
            font=('Arial', 10),
            fg='#cccccc',
            bg='#2d2d2d'
        ).pack(side=tk.LEFT)
        
        self.path_var = tk.StringVar(value=os.path.join(os.path.expanduser("~"), "BitcoinTradingGUI"))
        self.path_entry = tk.Entry(
            path_frame,
            textvariable=self.path_var,
            font=('Arial', 10),
            width=40
        )
        self.path_entry.pack(side=tk.LEFT, padx=(10, 5))
        
        browse_btn = tk.Button(
            path_frame,
            text="📂 Durchsuchen",
            command=self.browse_installation_path,
            font=('Arial', 9),
            bg='#3366cc',
            fg='white'
        )
        browse_btn.pack(side=tk.LEFT)
        
        # OPTIONEN CHECKBOXES
        checkbox_frame = tk.Frame(options_frame, bg='#2d2d2d')
        checkbox_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.desktop_var = tk.BooleanVar(value=True)
        desktop_check = tk.Checkbutton(
            checkbox_frame,
            text="🖥️ Desktop-Verknüpfung erstellen",
            variable=self.desktop_var,
            font=('Arial', 10),
            fg='#cccccc',
            bg='#2d2d2d',
            selectcolor='#2d2d2d'
        )
        desktop_check.pack(anchor=tk.W)
        
        self.models_var = tk.BooleanVar(value=True)
        models_check = tk.Checkbutton(
            checkbox_frame,
            text="📄 Alle verfügbaren Modelle kopieren",
            variable=self.models_var,
            font=('Arial', 10),
            fg='#cccccc',
            bg='#2d2d2d',
            selectcolor='#2d2d2d'
        )
        models_check.pack(anchor=tk.W)
        
        # PROGRESS UND LOG
        progress_frame = tk.LabelFrame(
            main_frame,
            text="📈 Installations-Fortschritt",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d'
        )
        progress_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.progress = ttk.Progressbar(
            progress_frame,
            mode='determinate',
            length=400
        )
        self.progress.pack(padx=10, pady=(10, 5))
        
        self.log_text = tk.Text(
            progress_frame,
            height=8,
            font=('Consolas', 9),
            bg='#1a1a1a',
            fg='#00ff88',
            wrap=tk.WORD
        )
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # BUTTONS
        button_frame = tk.Frame(main_frame, bg='#1a1a1a')
        button_frame.pack(fill=tk.X)
        
        scan_btn = tk.Button(
            button_frame,
            text="🔍 MODELLE SCANNEN",
            command=self.scan_models,
            font=('Arial', 11, 'bold'),
            bg='#0066cc',
            fg='white',
            padx=20,
            pady=8
        )
        scan_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        install_btn = tk.Button(
            button_frame,
            text="📦 INSTALLATION STARTEN",
            command=self.start_installation,
            font=('Arial', 11, 'bold'),
            bg='#00aa44',
            fg='white',
            padx=20,
            pady=8
        )
        install_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        exit_btn = tk.Button(
            button_frame,
            text="❌ BEENDEN",
            command=self.root.quit,
            font=('Arial', 11, 'bold'),
            bg='#cc3333',
            fg='white',
            padx=20,
            pady=8
        )
        exit_btn.pack(side=tk.RIGHT)
    
    def log_message(self, message):
        """Füge Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update()
    
    def scan_models(self):
        """Scanne verfügbare Modelle"""
        self.log_message("🔍 Scanne verfügbare Bitcoin Trading Modelle...")
        
        # Clear previous results
        for widget in self.models_list.winfo_children():
            widget.destroy()
        
        found_models = 0
        
        for model in self.models:
            model['found'] = os.path.exists(model['file'])
            
            # Model frame
            model_frame = tk.Frame(self.models_list, bg='#2d2d2d')
            model_frame.pack(fill=tk.X, pady=2)
            
            # Status icon
            status_icon = "✅" if model['found'] else "❌"
            required_text = " (ERFORDERLICH)" if model['required'] else ""
            
            status_label = tk.Label(
                model_frame,
                text=f"{status_icon} {model['name']}{required_text}",
                font=('Arial', 10),
                fg='#00ff88' if model['found'] else '#ff6666',
                bg='#2d2d2d'
            )
            status_label.pack(side=tk.LEFT)
            
            if model['found']:
                file_size = os.path.getsize(model['file'])
                size_label = tk.Label(
                    model_frame,
                    text=f"({file_size:,} Bytes)",
                    font=('Arial', 9),
                    fg='#cccccc',
                    bg='#2d2d2d'
                )
                size_label.pack(side=tk.RIGHT)
                found_models += 1
        
        self.log_message(f"✅ Scan abgeschlossen: {found_models}/{len(self.models)} Modelle gefunden")
        
        # Check if installation is possible
        required_found = all(model['found'] for model in self.models if model['required'])
        if not required_found:
            self.log_message("⚠️ WARNUNG: Erforderliche Modelle fehlen!")
    
    def browse_installation_path(self):
        """Wähle Installations-Verzeichnis"""
        path = filedialog.askdirectory(
            title="Wählen Sie Installations-Verzeichnis",
            initialdir=os.path.dirname(self.path_var.get())
        )
        
        if path:
            full_path = os.path.join(path, "BitcoinTradingGUI")
            self.path_var.set(full_path)
    
    def start_installation(self):
        """Starte Installation"""
        try:
            self.installation_path = self.path_var.get()
            
            if not self.installation_path:
                messagebox.showerror("Fehler", "Bitte wählen Sie ein Installations-Verzeichnis!")
                return
            
            # Check required models
            required_found = all(model['found'] for model in self.models if model['required'])
            if not required_found:
                result = messagebox.askyesno(
                    "Warnung",
                    "Erforderliche Modelle fehlen!\n\nTrotzdem fortfahren?"
                )
                if not result:
                    return
            
            self.log_message("📦 Starte Installation...")
            self.progress['value'] = 0
            
            # Installation steps
            steps = [
                ("Erstelle Installations-Verzeichnis", self.create_installation_directory),
                ("Kopiere GUI-Launcher", self.copy_gui_launcher),
                ("Kopiere Bitcoin Trading Modelle", self.copy_models),
                ("Erstelle Konfiguration", self.create_configuration),
                ("Erstelle Desktop-Verknüpfung", self.create_desktop_shortcut),
                ("Finalisiere Installation", self.finalize_installation)
            ]
            
            total_steps = len(steps)
            
            for i, (step_name, step_function) in enumerate(steps):
                self.log_message(f"🔄 {step_name}...")
                
                try:
                    step_function()
                    self.progress['value'] = ((i + 1) / total_steps) * 100
                    self.root.update()
                except Exception as e:
                    self.log_message(f"❌ Fehler bei {step_name}: {e}")
                    messagebox.showerror("Installations-Fehler", f"Fehler bei {step_name}:\n{e}")
                    return
            
            self.log_message("🎉 Installation erfolgreich abgeschlossen!")
            
            messagebox.showinfo(
                "Installation abgeschlossen",
                f"Bitcoin Trading GUI Professional wurde erfolgreich installiert!\n\n"
                f"Installations-Verzeichnis:\n{self.installation_path}\n\n"
                f"Desktop-Verknüpfung wurde erstellt.\n"
                f"Sie können die GUI jetzt unabhängig von Visual Studio verwenden!"
            )
            
        except Exception as e:
            self.log_message(f"❌ Installations-Fehler: {e}")
            messagebox.showerror("Installations-Fehler", f"Unerwarteter Fehler:\n{e}")
    
    def create_installation_directory(self):
        """Erstelle Installations-Verzeichnis"""
        os.makedirs(self.installation_path, exist_ok=True)
        self.log_message(f"✅ Verzeichnis erstellt: {self.installation_path}")
    
    def copy_gui_launcher(self):
        """Kopiere GUI-Launcher"""
        gui_file = "bitcoin_trading_gui_professional.py"
        
        if os.path.exists(gui_file):
            dest_path = os.path.join(self.installation_path, gui_file)
            shutil.copy2(gui_file, dest_path)
            self.log_message(f"✅ GUI-Launcher kopiert: {gui_file}")
        else:
            raise FileNotFoundError(f"GUI-Launcher nicht gefunden: {gui_file}")
    
    def copy_models(self):
        """Kopiere Bitcoin Trading Modelle"""
        if not self.models_var.get():
            self.log_message("⏭️ Modell-Kopierung übersprungen")
            return
        
        copied_models = 0
        
        for model in self.models:
            if model['found']:
                dest_path = os.path.join(self.installation_path, model['file'])
                shutil.copy2(model['file'], dest_path)
                self.log_message(f"✅ Modell kopiert: {model['file']}")
                copied_models += 1
        
        self.log_message(f"✅ {copied_models} Modelle kopiert")
    
    def create_configuration(self):
        """Erstelle Konfigurationsdatei"""
        config = {
            'installation_date': datetime.now().isoformat(),
            'installation_path': self.installation_path,
            'script_directory': self.script_directory,
            'models': self.models,
            'version': '1.0.0'
        }
        
        config_path = os.path.join(self.installation_path, "config.json")
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        self.log_message("✅ Konfiguration erstellt")
    
    def create_desktop_shortcut(self):
        """Erstelle Desktop-Verknüpfung"""
        if not self.desktop_var.get():
            self.log_message("⏭️ Desktop-Verknüpfung übersprungen")
            return
        
        try:
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
            shortcut_path = os.path.join(desktop, "Bitcoin Trading GUI Professional.bat")
            launcher_path = os.path.join(self.installation_path, "bitcoin_trading_gui_professional.py")
            
            batch_content = f'''@echo off
title Bitcoin Trading GUI Professional
color 0A
echo.
echo ========================================
echo    BITCOIN TRADING GUI PROFESSIONAL
echo ========================================
echo    Revolutionaere GUI mit 3 besten Modellen
echo    Standalone Installation
echo ========================================
echo.
cd /d "{self.installation_path}"
python "{launcher_path}"
if errorlevel 1 (
    echo.
    echo ❌ Fehler beim Starten der GUI
    echo 💡 Stellen Sie sicher, dass Python installiert ist
    echo.
    pause
) else (
    echo.
    echo ✅ GUI erfolgreich beendet
    echo.
    timeout /t 3 >nul
)
'''
            
            with open(shortcut_path, 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            self.log_message(f"✅ Desktop-Verknüpfung erstellt: {shortcut_path}")
            
        except Exception as e:
            self.log_message(f"⚠️ Desktop-Verknüpfung Fehler: {e}")
    
    def finalize_installation(self):
        """Finalisiere Installation"""
        # Erstelle README
        readme_path = os.path.join(self.installation_path, "README.txt")
        readme_content = f"""
BITCOIN TRADING GUI PROFESSIONAL
================================

Installation abgeschlossen am: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Installations-Verzeichnis: {self.installation_path}

VERWENDUNG:
1. Doppelklick auf Desktop-Verknüpfung "Bitcoin Trading GUI Professional"
2. Oder starten Sie direkt: python bitcoin_trading_gui_professional.py

FEATURES:
• Revolutionäre GUI mit professionellen Buttons
• 3 beste Bitcoin Trading Modelle integriert
• Standalone - unabhängig von Visual Studio
• Gesamtprognose aus allen Modellen
• Automatische Installation und Konfiguration

MODELLE:
• 🏅 FAVORIT - Das bewährte System (EMPFOHLEN)
• 🚀 OPTIMIERT - Das schnelle System  
• 🧠 KI-SYSTEM - Das intelligente System

SUPPORT:
Bei Problemen prüfen Sie die Konfiguration in config.json
        """
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        self.log_message("✅ README erstellt")
        self.log_message("🎉 Installation finalisiert")
    
    def run(self):
        """Starte Installer"""
        self.log_message("📦 Bitcoin Trading GUI Professional Installer bereit")
        self.log_message("💡 Komplette Installation mit 3 besten Modellen")
        self.root.mainloop()

def main():
    """Hauptfunktion"""
    try:
        installer = BitcoinTradingInstaller()
        installer.run()
    except Exception as e:
        print(f"❌ Installer-Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
