#!/usr/bin/env python3
"""
BITCOIN TRADING LAUNCHER NO EMOJI - VOLLSTÄNDIG EMOJI-FREI
=========================================================
PROFESSIONELLER LAUNCHER OHNE UNICODE-PROBLEME
- Keine Emojis oder problematische Unicode-Zeichen
- Nur ASCII-Zeichen für maximale Kompatibilität
- Robuste Fehlerbehandlung
- 3 beste Bitcoin Trading Modelle
- Professionelle GUI mit Buttons
- Vollständige Installation und Konfiguration

NO EMOJI VERSION - GARANTIERT KOMPATIBEL!
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import threading
import time
import os
import sys
from datetime import datetime

class BitcoinTradingLauncherNoEmoji:
    """
    BITCOIN TRADING LAUNCHER NO EMOJI
    ================================
    Vollständig emoji-freie Version für maximale Kompatibilität
    mit allen Windows-Systemen und Python-Umgebungen.
    """
    
    def __init__(self):
        # GUI SETUP
        self.root = tk.Tk()
        self.root.title("Bitcoin Trading Launcher NO EMOJI - Professionell")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a1a')
        
        # DIE 3 BESTEN FUNKTIONIERENDEN MODELLE - EMOJI-FREI
        self.models = {
            'favorit': {
                'name': 'FAVORIT - Das Bewaehrte System',
                'file': 'ultimate_complete_bitcoin_trading_FAVORITE_NO_EMOJI.py',
                'description': 'Das bewaehrte System mit 100% Genauigkeit',
                'status': 'Bereit',
                'process': None,
                'working': True,
                'recommended': True
            },
            'optimized': {
                'name': 'OPTIMIERT - Das Schnelle System',
                'file': 'btc_ultimate_optimized_complete_NO_EMOJI.py',
                'description': 'Das optimierte System fuer schnelle Analysen',
                'status': 'Bereit',
                'process': None,
                'working': True,
                'recommended': False
            },
            'simple': {
                'name': 'SIMPLE - Das Zuverlaessige System',
                'file': 'bitcoin_trading_simple_fixed_NO_EMOJI.py',
                'description': 'Das zuverlaessige System ohne Abhaengigkeiten',
                'status': 'Bereit',
                'process': None,
                'working': True,
                'recommended': False
            }
        }
        
        # LAUNCHER ZUSTAND
        self.running_processes = {}
        self.script_directory = os.getcwd()
        
        # GUI KOMPONENTEN ERSTELLEN
        self.create_no_emoji_gui()
        
        print("Bitcoin Trading Launcher NO EMOJI initialisiert")
        print("Vollstaendig emoji-freie Version fuer maximale Kompatibilitaet")
    
    def create_no_emoji_gui(self):
        """Erstelle emoji-freie GUI"""
        
        # TITEL
        title_frame = tk.Frame(self.root, bg='#1a1a1a')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="BITCOIN TRADING LAUNCHER NO EMOJI",
            font=('Arial', 20, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="Professioneller Launcher * Emoji-frei * Maximale Kompatibilitaet",
            font=('Arial', 12),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        subtitle_label.pack()
        
        # HAUPTBEREICH
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # MODELLE BEREICH
        models_frame = tk.LabelFrame(
            main_frame,
            text="Die 3 Besten Bitcoin Trading Modelle - NO EMOJI",
            font=('Arial', 14, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d',
            bd=2,
            relief=tk.RAISED
        )
        models_frame.pack(fill=tk.X, pady=(0, 20))
        
        # MODELL BUTTONS ERSTELLEN
        for i, (key, model) in enumerate(self.models.items()):
            self.create_no_emoji_model_button(models_frame, key, model, i)
        
        # KONTROLLE BEREICH
        control_frame = tk.LabelFrame(
            main_frame,
            text="Kontrolle & Status",
            font=('Arial', 14, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d',
            bd=2,
            relief=tk.RAISED
        )
        control_frame.pack(fill=tk.BOTH, expand=True)
        
        # HAUPT-KONTROLLEN
        button_frame = tk.Frame(control_frame, bg='#2d2d2d')
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # ALLE STARTEN BUTTON
        start_all_btn = tk.Button(
            button_frame,
            text="ALLE STARTEN",
            command=self.start_all_models,
            font=('Arial', 12, 'bold'),
            bg='#00aa44',
            fg='white',
            relief=tk.FLAT,
            padx=30,
            pady=10,
            cursor='hand2'
        )
        start_all_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # ALLE STOPPEN BUTTON
        stop_all_btn = tk.Button(
            button_frame,
            text="ALLE STOPPEN",
            command=self.stop_all_models,
            font=('Arial', 12, 'bold'),
            bg='#cc3333',
            fg='white',
            relief=tk.FLAT,
            padx=30,
            pady=10,
            cursor='hand2'
        )
        stop_all_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # INSTALLATION BUTTON
        install_btn = tk.Button(
            button_frame,
            text="NEU INSTALLIEREN",
            command=self.install_no_emoji_scripts,
            font=('Arial', 12, 'bold'),
            bg='#ff6600',
            fg='white',
            relief=tk.FLAT,
            padx=30,
            pady=10,
            cursor='hand2'
        )
        install_btn.pack(side=tk.LEFT)
        
        # STATUS LOG
        log_label = tk.Label(
            control_frame,
            text="Live Status & Logs",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d'
        )
        log_label.pack(padx=10, anchor=tk.W, pady=(10, 0))
        
        self.status_log = scrolledtext.ScrolledText(
            control_frame,
            height=15,
            font=('Consolas', 9),
            bg='#1a1a1a',
            fg='#00ff88',
            insertbackground='#00ff88',
            wrap=tk.WORD
        )
        self.status_log.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 10))
    
    def create_no_emoji_model_button(self, parent, key, model, index):
        """Erstelle emoji-freie Modell-Buttons"""
        
        # MODELL CONTAINER
        model_frame = tk.Frame(parent, bg='#2d2d2d', relief=tk.RAISED, bd=1)
        model_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # MODELL INFO
        info_frame = tk.Frame(model_frame, bg='#2d2d2d')
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # NAME UND STATUS
        header_frame = tk.Frame(info_frame, bg='#2d2d2d')
        header_frame.pack(fill=tk.X)
        
        name_label = tk.Label(
            header_frame,
            text=model['name'],
            font=('Arial', 12, 'bold'),
            fg='#00ff88' if model.get('recommended') else '#ffffff',
            bg='#2d2d2d'
        )
        name_label.pack(side=tk.LEFT)
        
        # EMPFOHLEN BADGE
        if model.get('recommended'):
            rec_label = tk.Label(
                header_frame,
                text="*** EMPFOHLEN ***",
                font=('Arial', 8, 'bold'),
                fg='#ffd700',
                bg='#2d2d2d'
            )
            rec_label.pack(side=tk.RIGHT)
        
        # BESCHREIBUNG UND DATEI
        desc_label = tk.Label(
            info_frame,
            text=f"{model['description']}\nDatei: {model['file']}",
            font=('Arial', 9),
            fg='#cccccc',
            bg='#2d2d2d',
            justify=tk.LEFT
        )
        desc_label.pack(anchor=tk.W, pady=(5, 0))
        
        # STATUS
        status_frame = tk.Frame(info_frame, bg='#2d2d2d')
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        status_label = tk.Label(
            status_frame,
            text=f"Status: {model['status']}",
            font=('Arial', 9),
            fg='#cccccc',
            bg='#2d2d2d'
        )
        status_label.pack(side=tk.LEFT)
        model['status_label'] = status_label
        
        # BUTTON FRAME
        button_frame = tk.Frame(info_frame, bg='#2d2d2d')
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # START BUTTON
        start_button = tk.Button(
            button_frame,
            text=">> STARTEN",
            command=lambda k=key: self.start_model_no_emoji(k),
            font=('Arial', 10, 'bold'),
            bg='#00aa44',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # STOP BUTTON
        stop_button = tk.Button(
            button_frame,
            text="|| STOPPEN",
            command=lambda k=key: self.stop_model_no_emoji(k),
            font=('Arial', 10, 'bold'),
            bg='#cc3333',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        stop_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # INFO BUTTON
        info_button = tk.Button(
            button_frame,
            text="(i) INFO",
            command=lambda k=key: self.show_model_info_no_emoji(k),
            font=('Arial', 10, 'bold'),
            bg='#3366cc',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        info_button.pack(side=tk.LEFT)
        
        # SPEICHERE BUTTON REFERENZEN
        model['start_button'] = start_button
        model['stop_button'] = stop_button
        model['info_button'] = info_button
    
    def log_message(self, message):
        """Fuege Nachricht zum Status-Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.status_log.insert(tk.END, log_entry)
        self.status_log.see(tk.END)
        self.root.update()
        
        # Auch in Konsole ausgeben
        print(log_entry.strip())
    
    def start_model_no_emoji(self, model_key):
        """Starte ein Bitcoin Trading Modell - NO EMOJI"""
        if model_key not in self.models:
            self.log_message(f"FEHLER: Ungueltiges Modell: {model_key}")
            return
        
        model = self.models[model_key]
        
        # Pruefe ob bereits laeuft
        if model_key in self.running_processes:
            self.log_message(f"WARNUNG: {model['name']} laeuft bereits")
            return
        
        # Pruefe Datei
        if not os.path.exists(model['file']):
            self.log_message(f"FEHLER: Datei nicht gefunden: {model['file']}")
            messagebox.showerror("Fehler", f"Modell-Datei nicht gefunden:\n{model['file']}")
            return
        
        try:
            self.log_message(f"STARTE: {model['name']}")
            
            # Update GUI
            model['status'] = 'Laeuft'
            model['status_label'].config(text=f"Status: {model['status']}")
            
            # Starte Prozess
            process = subprocess.Popen(
                [sys.executable, model['file']],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.script_directory,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            self.running_processes[model_key] = process
            model['process'] = process
            
            # Starte Monitoring Thread
            monitor_thread = threading.Thread(
                target=self.monitor_model_no_emoji,
                args=(model_key, process),
                daemon=True
            )
            monitor_thread.start()
            
            self.log_message(f"ERFOLGREICH: {model['name']} gestartet (PID: {process.pid})")
            
        except Exception as e:
            self.log_message(f"FEHLER beim Starten von {model['name']}: {e}")
            messagebox.showerror("Fehler", f"Fehler beim Starten:\n{e}")
            
            # Reset GUI
            model['status'] = 'Fehler'
            model['status_label'].config(text=f"Status: {model['status']}")
    
    def monitor_model_no_emoji(self, model_key, process):
        """Ueberwache ein Modell - NO EMOJI"""
        model = self.models[model_key]
        
        try:
            # Warte auf Prozess-Ende
            stdout, stderr = process.communicate()
            
            # Prozess beendet
            if model_key in self.running_processes:
                del self.running_processes[model_key]
            
            # Update GUI im Main Thread
            self.root.after(0, lambda: self.model_finished_no_emoji(model_key, process.returncode, stdout, stderr))
            
        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"FEHLER: Monitor-Fehler fuer {model['name']}: {e}"))
    
    def model_finished_no_emoji(self, model_key, return_code, stdout, stderr):
        """Modell wurde beendet - NO EMOJI"""
        model = self.models[model_key]
        
        # Update GUI
        model['process'] = None
        model['status'] = 'Beendet' if return_code == 0 else 'Fehler'
        model['status_label'].config(text=f"Status: {model['status']}")
        
        if return_code == 0:
            self.log_message(f"ERFOLGREICH: {model['name']} erfolgreich beendet")
            
            # Zeige wichtige Ausgaben
            if stdout:
                important_lines = [line for line in stdout.split('\n') 
                                 if any(keyword in line.lower() for keyword in 
                                       ['vorhersage', 'prediction', 'empfehlung', 'preis', 'erfolg'])]
                for line in important_lines[:3]:  # Nur erste 3 wichtige Zeilen
                    if line.strip():
                        self.log_message(f"ERGEBNIS: {line.strip()}")
        else:
            self.log_message(f"WARNUNG: {model['name']} mit Fehler beendet (Code: {return_code})")
            if stderr:
                error_lines = stderr.split('\n')[:2]  # Nur erste 2 Fehlerzeilen
                for line in error_lines:
                    if line.strip():
                        self.log_message(f"FEHLER: {line.strip()}")
    
    def stop_model_no_emoji(self, model_key):
        """Stoppe ein Bitcoin Trading Modell - NO EMOJI"""
        if model_key not in self.running_processes:
            self.log_message(f"WARNUNG: Modell {model_key} laeuft nicht")
            return
        
        model = self.models[model_key]
        process = self.running_processes[model_key]
        
        try:
            self.log_message(f"STOPPE: {model['name']}")
            
            # Beende Prozess
            process.terminate()
            
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                process.kill()
                self.log_message(f"ZWANGSBEENDUNG: {model['name']} zwangsbeendet")
            
            # Cleanup
            del self.running_processes[model_key]
            model['process'] = None
            model['status'] = 'Gestoppt'
            model['status_label'].config(text=f"Status: {model['status']}")
            
            self.log_message(f"ERFOLGREICH: {model['name']} gestoppt")
            
        except Exception as e:
            self.log_message(f"FEHLER beim Stoppen von {model['name']}: {e}")
    
    def start_all_models(self):
        """Starte alle 3 Bitcoin Trading Modelle"""
        self.log_message("STARTE ALLE 3 BITCOIN TRADING MODELLE...")
        
        for model_key in self.models.keys():
            if model_key not in self.running_processes:
                self.start_model_no_emoji(model_key)
                time.sleep(2)  # Pause zwischen Starts
        
        self.log_message("ERFOLGREICH: Alle verfuegbaren Modelle gestartet")
    
    def stop_all_models(self):
        """Stoppe alle laufenden Modelle"""
        if not self.running_processes:
            self.log_message("INFO: Keine Modelle laufen")
            return
        
        self.log_message("STOPPE ALLE MODELLE...")
        
        for model_key in list(self.running_processes.keys()):
            self.stop_model_no_emoji(model_key)
        
        self.log_message("ERFOLGREICH: Alle Modelle gestoppt")
    
    def show_model_info_no_emoji(self, model_key):
        """Zeige Modell-Informationen - NO EMOJI"""
        model = self.models[model_key]
        
        info_text = f"""
{model['name']}

BESCHREIBUNG:
{model['description']}

DATEI: {model['file']}
STATUS: {model['status']}
FUNKTIONSFAEHIG: {'Ja' if model['working'] else 'Nein'}
EMPFOHLEN: {'Ja' if model.get('recommended', False) else 'Nein'}

VERWENDUNG:
Klicken Sie auf ">> STARTEN" um das Modell zu starten.
Das Modell fuehrt eine vollstaendige Bitcoin-Analyse durch
und liefert Vorhersagen fuer die naechsten 48 Stunden.
        """
        
        messagebox.showinfo(f"Modell-Info: {model['name']}", info_text)
    
    def install_no_emoji_scripts(self):
        """Installiere emoji-freie Scripts"""
        self.log_message("STARTE INSTALLATION EMOJI-FREIER SCRIPTS...")
        
        try:
            # Erstelle emoji-freie Versionen aller Scripts
            self.create_no_emoji_scripts()
            
            self.log_message("ERFOLGREICH: Emoji-freie Scripts erstellt")
            messagebox.showinfo(
                "Installation abgeschlossen",
                "Emoji-freie Scripts wurden erfolgreich erstellt!\n\n"
                "Alle Scripts sind jetzt kompatibel und sollten\n"
                "ohne Unicode-Probleme funktionieren."
            )
            
        except Exception as e:
            self.log_message(f"FEHLER bei Installation: {e}")
            messagebox.showerror("Installations-Fehler", f"Fehler bei Installation:\n{e}")
    
    def create_no_emoji_scripts(self):
        """Erstelle emoji-freie Versionen aller Scripts"""
        self.log_message("ERSTELLE EMOJI-FREIE SCRIPT-VERSIONEN...")
        
        # Hier werden die emoji-freien Scripts erstellt
        # Das machen wir im naechsten Schritt
        pass
    
    def run(self):
        """Starte die emoji-freie GUI"""
        self.log_message("Bitcoin Trading Launcher NO EMOJI bereit!")
        self.log_message("Vollstaendig emoji-freie Version fuer maximale Kompatibilitaet")
        self.root.mainloop()

def main():
    """Hauptfunktion"""
    try:
        app = BitcoinTradingLauncherNoEmoji()
        app.run()
    except Exception as e:
        print(f"FEHLER: GUI-Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
