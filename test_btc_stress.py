#!/usr/bin/env python3
"""
STRESS-TEST - Bitcoin Prediction Model
Testet Grenzen und Stabilität unter extremen Bedingungen
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, <PERSON>se, Dropout
from tensorflow.keras.optimizers.legacy import Adam
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, r2_score
import time
import psutil
import gc
import warnings
warnings.filterwarnings('ignore')

print("💥 STRESS-TEST - Bitcoin Prediction Model")
print("=" * 60)

class StressTester:
    def __init__(self):
        self.results = []
        self.start_memory = psutil.virtual_memory().used / 1024 / 1024  # MB
    
    def monitor_resources(self):
        """Überwache Systemressourcen"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        memory_used = memory.used / 1024 / 1024  # MB
        memory_percent = memory.percent
        
        return {
            'cpu_percent': cpu_percent,
            'memory_used_mb': memory_used,
            'memory_percent': memory_percent,
            'memory_increase_mb': memory_used - self.start_memory
        }
    
    def stress_test_data_size(self):
        """Teste verschiedene Datengrößen"""
        print("\n🔥 STRESS-TEST: Datengrößen")
        print("-" * 40)
        
        df = pd.read_csv('crypto_data.csv')
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        # Verschiedene Datengrößen testen
        data_sizes = [100, 500, 1000, len(df)]
        
        for size in data_sizes:
            print(f"\n   📊 Teste mit {size} Datenpunkten...")
            
            try:
                start_time = time.time()
                start_resources = self.monitor_resources()
                
                # Daten begrenzen
                df_test = df.tail(size).copy()
                
                # Einfache Features
                features = self.create_simple_features(df_test)
                
                # Modell trainieren
                success, metrics = self.train_simple_model(features)
                
                end_time = time.time()
                end_resources = self.monitor_resources()
                
                if success:
                    result = {
                        'test_type': 'data_size',
                        'parameter': size,
                        'success': True,
                        'r2': metrics['r2'],
                        'time': end_time - start_time,
                        'cpu_usage': end_resources['cpu_percent'],
                        'memory_increase': end_resources['memory_increase_mb']
                    }
                    
                    print(f"      ✅ Erfolg: R² = {metrics['r2']:.4f}, Zeit = {result['time']:.1f}s")
                    print(f"         CPU: {result['cpu_usage']:.1f}%, RAM: +{result['memory_increase']:.1f}MB")
                else:
                    result = {
                        'test_type': 'data_size',
                        'parameter': size,
                        'success': False,
                        'time': end_time - start_time
                    }
                    print(f"      ❌ Fehlgeschlagen nach {result['time']:.1f}s")
                
                self.results.append(result)
                
                # Memory cleanup
                gc.collect()
                
            except Exception as e:
                print(f"      💥 Fehler: {e}")
                self.results.append({
                    'test_type': 'data_size',
                    'parameter': size,
                    'success': False,
                    'error': str(e)
                })
    
    def stress_test_model_complexity(self):
        """Teste verschiedene Modellkomplexitäten"""
        print("\n🔥 STRESS-TEST: Modellkomplexität")
        print("-" * 40)
        
        df = pd.read_csv('crypto_data.csv')
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        # Daten vorbereiten (mittlere Größe)
        features = self.create_simple_features(df.tail(1000))
        
        # Verschiedene LSTM-Größen testen
        lstm_sizes = [16, 32, 64, 128, 256, 512]
        
        for lstm_size in lstm_sizes:
            print(f"\n   🤖 Teste LSTM mit {lstm_size} Units...")
            
            try:
                start_time = time.time()
                start_resources = self.monitor_resources()
                
                success, metrics = self.train_complex_model(features, lstm_size)
                
                end_time = time.time()
                end_resources = self.monitor_resources()
                
                if success:
                    result = {
                        'test_type': 'model_complexity',
                        'parameter': lstm_size,
                        'success': True,
                        'r2': metrics['r2'],
                        'parameters': metrics['parameters'],
                        'time': end_time - start_time,
                        'cpu_usage': end_resources['cpu_percent'],
                        'memory_increase': end_resources['memory_increase_mb']
                    }
                    
                    print(f"      ✅ Erfolg: R² = {metrics['r2']:.4f}, Parameter = {metrics['parameters']:,}")
                    print(f"         Zeit = {result['time']:.1f}s, CPU: {result['cpu_usage']:.1f}%")
                    print(f"         RAM: +{result['memory_increase']:.1f}MB")
                else:
                    result = {
                        'test_type': 'model_complexity',
                        'parameter': lstm_size,
                        'success': False,
                        'time': end_time - start_time
                    }
                    print(f"      ❌ Fehlgeschlagen nach {result['time']:.1f}s")
                
                self.results.append(result)
                
                # Memory cleanup
                tf.keras.backend.clear_session()
                gc.collect()
                
            except Exception as e:
                print(f"      💥 Fehler: {e}")
                self.results.append({
                    'test_type': 'model_complexity',
                    'parameter': lstm_size,
                    'success': False,
                    'error': str(e)
                })
    
    def stress_test_sequence_length(self):
        """Teste verschiedene Sequenzlängen"""
        print("\n🔥 STRESS-TEST: Sequenzlängen")
        print("-" * 40)
        
        df = pd.read_csv('crypto_data.csv')
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        features = self.create_simple_features(df.tail(1000))
        
        # Verschiedene Look-Back Perioden
        look_backs = [6, 12, 24, 48, 96, 168]  # 6h bis 1 Woche
        
        for look_back in look_backs:
            print(f"\n   📦 Teste Sequenzlänge {look_back}...")
            
            try:
                start_time = time.time()
                start_resources = self.monitor_resources()
                
                success, metrics = self.train_sequence_model(features, look_back)
                
                end_time = time.time()
                end_resources = self.monitor_resources()
                
                if success:
                    result = {
                        'test_type': 'sequence_length',
                        'parameter': look_back,
                        'success': True,
                        'r2': metrics['r2'],
                        'sequences': metrics['sequences'],
                        'time': end_time - start_time,
                        'cpu_usage': end_resources['cpu_percent'],
                        'memory_increase': end_resources['memory_increase_mb']
                    }
                    
                    print(f"      ✅ Erfolg: R² = {metrics['r2']:.4f}, Sequenzen = {metrics['sequences']}")
                    print(f"         Zeit = {result['time']:.1f}s, RAM: +{result['memory_increase']:.1f}MB")
                else:
                    result = {
                        'test_type': 'sequence_length',
                        'parameter': look_back,
                        'success': False,
                        'time': end_time - start_time
                    }
                    print(f"      ❌ Fehlgeschlagen nach {result['time']:.1f}s")
                
                self.results.append(result)
                
                # Memory cleanup
                gc.collect()
                
            except Exception as e:
                print(f"      💥 Fehler: {e}")
                self.results.append({
                    'test_type': 'sequence_length',
                    'parameter': look_back,
                    'success': False,
                    'error': str(e)
                })
    
    def create_simple_features(self, df):
        """Erstelle einfache Features"""
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # Basis technische Indikatoren
        features['sma_10'] = df['close'].rolling(10).mean()
        features['sma_20'] = df['close'].rolling(20).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # Volatilität
        features['volatility'] = df['close'].pct_change().rolling(10).std()
        
        return features.dropna()
    
    def train_simple_model(self, features):
        """Trainiere einfaches Modell"""
        try:
            X = features.drop('close', axis=1)
            y = features['close'].values
            
            scaler_X = MinMaxScaler()
            scaler_y = MinMaxScaler()
            
            X_scaled = scaler_X.fit_transform(X)
            y_scaled = scaler_y.fit_transform(y.reshape(-1, 1)).flatten()
            
            # Einfache Sequenzen
            X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, 12)
            
            if len(X_seq) < 20:
                return False, None
            
            # Train-Test Split
            train_size = int(len(X_seq) * 0.8)
            X_train, X_test = X_seq[:train_size], X_seq[train_size:]
            y_train, y_test = y_seq[:train_size], y_seq[train_size:]
            
            # Einfaches Modell
            model = Sequential([
                LSTM(32, input_shape=(X_train.shape[1], X_train.shape[2])),
                Dense(1)
            ])
            
            model.compile(optimizer=Adam(0.01), loss='mse')
            
            # Schnelles Training
            model.fit(X_train, y_train, epochs=10, batch_size=32, verbose=0)
            
            # Evaluation
            y_pred = model.predict(X_test, verbose=0)
            y_test_orig = scaler_y.inverse_transform(y_test.reshape(-1, 1)).flatten()
            y_pred_orig = scaler_y.inverse_transform(y_pred).flatten()
            
            r2 = r2_score(y_test_orig, y_pred_orig)
            
            return True, {'r2': r2}
            
        except Exception as e:
            return False, None
    
    def train_complex_model(self, features, lstm_size):
        """Trainiere komplexes Modell"""
        try:
            X = features.drop('close', axis=1)
            y = features['close'].values
            
            scaler_X = MinMaxScaler()
            scaler_y = MinMaxScaler()
            
            X_scaled = scaler_X.fit_transform(X)
            y_scaled = scaler_y.fit_transform(y.reshape(-1, 1)).flatten()
            
            X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, 24)
            
            if len(X_seq) < 20:
                return False, None
            
            train_size = int(len(X_seq) * 0.8)
            X_train, X_test = X_seq[:train_size], X_seq[train_size:]
            y_train, y_test = y_seq[:train_size], y_seq[train_size:]
            
            # Komplexes Modell
            model = Sequential([
                LSTM(lstm_size, return_sequences=True, dropout=0.2, input_shape=(X_train.shape[1], X_train.shape[2])),
                LSTM(lstm_size//2, return_sequences=False, dropout=0.2),
                Dense(lstm_size//4, activation='relu'),
                Dropout(0.3),
                Dense(1)
            ])
            
            model.compile(optimizer=Adam(0.001), loss='mse')
            
            # Training
            model.fit(X_train, y_train, epochs=20, batch_size=16, verbose=0)
            
            # Evaluation
            y_pred = model.predict(X_test, verbose=0)
            y_test_orig = scaler_y.inverse_transform(y_test.reshape(-1, 1)).flatten()
            y_pred_orig = scaler_y.inverse_transform(y_pred).flatten()
            
            r2 = r2_score(y_test_orig, y_pred_orig)
            
            return True, {'r2': r2, 'parameters': model.count_params()}
            
        except Exception as e:
            return False, None
    
    def train_sequence_model(self, features, look_back):
        """Trainiere Modell mit verschiedenen Sequenzlängen"""
        try:
            X = features.drop('close', axis=1)
            y = features['close'].values
            
            scaler_X = MinMaxScaler()
            scaler_y = MinMaxScaler()
            
            X_scaled = scaler_X.fit_transform(X)
            y_scaled = scaler_y.fit_transform(y.reshape(-1, 1)).flatten()
            
            X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, look_back)
            
            if len(X_seq) < 20:
                return False, None
            
            train_size = int(len(X_seq) * 0.8)
            X_train, X_test = X_seq[:train_size], X_seq[train_size:]
            y_train, y_test = y_seq[:train_size], y_seq[train_size:]
            
            # Modell
            model = Sequential([
                LSTM(64, input_shape=(X_train.shape[1], X_train.shape[2])),
                Dense(32, activation='relu'),
                Dense(1)
            ])
            
            model.compile(optimizer=Adam(0.001), loss='mse')
            
            # Training
            model.fit(X_train, y_train, epochs=15, batch_size=32, verbose=0)
            
            # Evaluation
            y_pred = model.predict(X_test, verbose=0)
            y_test_orig = scaler_y.inverse_transform(y_test.reshape(-1, 1)).flatten()
            y_pred_orig = scaler_y.inverse_transform(y_pred).flatten()
            
            r2 = r2_score(y_test_orig, y_pred_orig)
            
            return True, {'r2': r2, 'sequences': len(X_seq)}
            
        except Exception as e:
            return False, None
    
    def create_sequences(self, data, target, look_back):
        """Erstelle Sequenzen"""
        X, y = [], []
        for i in range(look_back, len(data)):
            X.append(data[i-look_back:i])
            y.append(target[i])
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)
    
    def analyze_results(self):
        """Analysiere Stress-Test Ergebnisse"""
        print("\n" + "=" * 60)
        print("📊 STRESS-TEST ANALYSE")
        print("=" * 60)
        
        # Erfolgsrate pro Test-Typ
        test_types = ['data_size', 'model_complexity', 'sequence_length']
        
        for test_type in test_types:
            type_results = [r for r in self.results if r['test_type'] == test_type]
            if type_results:
                success_count = sum(1 for r in type_results if r['success'])
                total_count = len(type_results)
                success_rate = success_count / total_count * 100
                
                print(f"\n🔥 {test_type.upper()}:")
                print(f"   Erfolgsrate: {success_count}/{total_count} ({success_rate:.1f}%)")
                
                # Beste Performance
                successful = [r for r in type_results if r['success']]
                if successful:
                    if 'r2' in successful[0]:
                        best = max(successful, key=lambda x: x['r2'])
                        print(f"   Beste R²: {best['r2']:.4f} (Parameter: {best['parameter']})")
                    
                    fastest = min(successful, key=lambda x: x['time'])
                    print(f"   Schnellste: {fastest['time']:.1f}s (Parameter: {fastest['parameter']})")
        
        # Visualisierung
        self.plot_stress_results()
    
    def plot_stress_results(self):
        """Visualisiere Stress-Test Ergebnisse"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        test_types = ['data_size', 'model_complexity', 'sequence_length']
        
        for i, test_type in enumerate(test_types):
            type_results = [r for r in self.results if r['test_type'] == test_type and r['success']]
            
            if type_results:
                params = [r['parameter'] for r in type_results]
                r2_scores = [r['r2'] for r in type_results]
                times = [r['time'] for r in type_results]
                
                # R² vs Parameter
                axes[0, i].plot(params, r2_scores, 'o-', linewidth=2, markersize=8)
                axes[0, i].set_title(f'{test_type.replace("_", " ").title()} - R² Score')
                axes[0, i].set_xlabel('Parameter')
                axes[0, i].set_ylabel('R² Score')
                axes[0, i].grid(True, alpha=0.3)
                
                # Zeit vs Parameter
                axes[1, i].plot(params, times, 's-', color='red', linewidth=2, markersize=8)
                axes[1, i].set_title(f'{test_type.replace("_", " ").title()} - Ausführungszeit')
                axes[1, i].set_xlabel('Parameter')
                axes[1, i].set_ylabel('Zeit (Sekunden)')
                axes[1, i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

def main():
    """Hauptfunktion für Stress-Test"""
    print(f"💻 System Info:")
    print(f"   CPU-Kerne: {psutil.cpu_count()}")
    print(f"   RAM: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f} GB")
    print(f"   GPU: {'Verfügbar' if len(tf.config.experimental.list_physical_devices('GPU')) > 0 else 'Nicht verfügbar'}")
    
    tester = StressTester()
    
    # Alle Stress-Tests durchführen
    tester.stress_test_data_size()
    tester.stress_test_model_complexity()
    tester.stress_test_sequence_length()
    
    # Ergebnisse analysieren
    tester.analyze_results()
    
    print(f"\n✅ STRESS-TEST ABGESCHLOSSEN!")
    print(f"   {len(tester.results)} Tests durchgeführt")

if __name__ == "__main__":
    main()
