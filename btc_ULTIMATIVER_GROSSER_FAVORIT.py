#!/usr/bin/env python3
"""
🏆 ULTIMATIVER GROSSER FAVORIT - BITCOIN TRADING TOOL 🏆
========================================================
KOMPLETT NEU AUFGEBAUT - ALLE VERBESSERUNGEN IMPLEMENTIERT
Professionell, Effizient, Genau, Schnell
"""

import warnings
import time
import json
import hashlib
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
# Plotly optional - Fallback auf Matplotlib
try:
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    print("⚠️ Plotly nicht verfügbar - verwende Matplotlib")
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.model_selection import cross_val_score
import yfinance as yf
import multiprocessing
import os
import gc
from typing import Dict, List, Tuple, Optional, Any

# Erweiterte Imports für bessere Modelle
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ XGBoost nicht verfügbar - wird übersprungen")

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

# ULTIMATIVE KONFIGURATION
class UltimativeConfig:
    """Zentrale Konfiguration - externalisiert und anpassbar"""
    
    def __init__(self):
        self.PREDICTION_HORIZONS = [1, 6, 24]
        self.MAX_CORES = multiprocessing.cpu_count()
        self.LOOKBACK_DAYS = 7
        self.UPDATE_INTERVAL = 300  # 5 Minuten für Live-Modus
        
        # Adaptive Schwellenwerte (Basis)
        self.BASE_THRESHOLDS = {1: 0.003, 6: 0.008, 24: 0.015}
        
        # Modell-Parameter
        self.MODEL_PARAMS = {
            'RandomForest': {
                'n_estimators': 150,
                'max_depth': 18,
                'min_samples_split': 3,
                'max_features': 'sqrt',
                'random_state': 42
            },
            'ExtraTrees': {
                'n_estimators': 120,
                'max_depth': 15,
                'min_samples_split': 2,
                'max_features': 'sqrt',
                'random_state': 42
            },
            'XGBoost': {
                'n_estimators': 100,
                'max_depth': 8,
                'learning_rate': 0.1,
                'random_state': 42
            },
            'LogisticRegression': {
                'max_iter': 1000,
                'C': 1.0,
                'random_state': 42
            }
        }
        
        # Horizont-Gewichtung
        self.HORIZON_WEIGHTS = {1: 0.5, 6: 0.3, 24: 0.2}
        
        # Signal-Schwellenwerte
        self.SIGNAL_THRESHOLDS = {
            'STARKER_KAUF': 0.7,
            'KAUF': 0.6,
            'VERKAUF': 0.4,
            'STARKER_VERKAUF': 0.3
        }
    
    def save_config(self, filepath: str = 'ultimative_config.json'):
        """Speichert Konfiguration in JSON-Datei"""
        config_dict = {
            'prediction_horizons': self.PREDICTION_HORIZONS,
            'max_cores': self.MAX_CORES,
            'base_thresholds': self.BASE_THRESHOLDS,
            'model_params': self.MODEL_PARAMS,
            'horizon_weights': self.HORIZON_WEIGHTS,
            'signal_thresholds': self.SIGNAL_THRESHOLDS
        }
        
        with open(filepath, 'w') as f:
            json.dump(config_dict, f, indent=2)
    
    def load_config(self, filepath: str = 'ultimative_config.json'):
        """Lädt Konfiguration aus JSON-Datei"""
        try:
            with open(filepath, 'r') as f:
                config_dict = json.load(f)
            
            self.PREDICTION_HORIZONS = config_dict.get('prediction_horizons', self.PREDICTION_HORIZONS)
            self.BASE_THRESHOLDS = config_dict.get('base_thresholds', self.BASE_THRESHOLDS)
            self.MODEL_PARAMS = config_dict.get('model_params', self.MODEL_PARAMS)
            self.HORIZON_WEIGHTS = config_dict.get('horizon_weights', self.HORIZON_WEIGHTS)
            self.SIGNAL_THRESHOLDS = config_dict.get('signal_thresholds', self.SIGNAL_THRESHOLDS)
            
            print(f"✅ Konfiguration geladen aus: {filepath}")
        except FileNotFoundError:
            print(f"⚠️ Konfigurationsdatei nicht gefunden, verwende Standardwerte")
            self.save_config(filepath)

print("🏆 ULTIMATIVER GROSSER FAVORIT - BITCOIN TRADING TOOL")
print("=" * 55)

# Konfiguration initialisieren
config = UltimativeConfig()
config.load_config()

print(f"💻 CPU: {config.MAX_CORES} Kerne")
print(f"🎯 FOKUS: Ultimative KAUF/VERKAUF Signale")
print(f"📊 Horizonte: {config.PREDICTION_HORIZONS}h")
print(f"🕐 Zeit: {datetime.now().strftime('%H:%M:%S')}")

# Verzeichnisse erstellen
os.makedirs('./ultimativer_favorit', exist_ok=True)
os.makedirs('./ultimativer_favorit/backtest', exist_ok=True)
os.makedirs('./ultimativer_favorit/models', exist_ok=True)

class UltimativerGrosserFavorit:
    """Ultimative Bitcoin Trading Tool Klasse"""
    
    def __init__(self, config: UltimativeConfig):
        self.config = config
        self.models: Dict[str, Dict] = {}
        self.scalers: Dict[str, RobustScaler] = {}
        self.cached_features: Optional[pd.DataFrame] = None
        self.last_data_hash: Optional[str] = None
        self.feature_importance: Dict[str, Dict] = {}
        self.backtest_results: Optional[pd.DataFrame] = None
        
    def _calculate_data_hash(self, df: pd.DataFrame) -> str:
        """Berechnet Hash der Daten für Caching"""
        return hashlib.md5(pd.util.hash_pandas_object(df).values).hexdigest()
    
    def get_bitcoin_data_robust(self) -> Tuple[pd.DataFrame, bool]:
        """Robuste Bitcoin-Datensammlung mit verbesserter Fehlerbehandlung"""
        try:
            print("📊 Sammle echte Bitcoin-Daten...")
            btc = yf.Ticker("BTC-USD")
            df = btc.history(period=f"{self.config.LOOKBACK_DAYS}d", interval="1h")
            
            if len(df) > 50:
                df.columns = [col.lower() for col in df.columns]
                df = df.dropna()
                
                # Memory-Optimierung
                df = self._optimize_memory(df)
                
                print(f"✅ Echte Bitcoin-Daten: {len(df)} Stunden")
                return df, True
            else:
                raise ValueError("Zu wenig echte Daten verfügbar")
                
        except (ConnectionError, TimeoutError) as e:
            print(f"❌ Netzwerk-Fehler: {e}")
            return self._generate_fallback_data(), False
        except (ValueError, KeyError) as e:
            print(f"❌ Daten-Fehler: {e}")
            return self._generate_fallback_data(), False
        except Exception as e:
            print(f"❌ Unbekannter Fehler: {e}")
            return self._generate_fallback_data(), False
    
    def _optimize_memory(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimiert Speicherverbrauch"""
        # Float64 → Float32 (50% weniger Speicher)
        float_cols = df.select_dtypes(include=['float64']).columns
        df[float_cols] = df[float_cols].astype('float32')
        
        # Int64 → Int32
        int_cols = df.select_dtypes(include=['int64']).columns
        df[int_cols] = df[int_cols].astype('int32')
        
        return df
    
    def _generate_fallback_data(self) -> pd.DataFrame:
        """Generiert realistische Fallback-Daten"""
        print("🔄 Generiere realistische Fallback-Daten...")
        
        end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        start_time = end_time - timedelta(days=self.config.LOOKBACK_DAYS)
        dates = pd.date_range(start=start_time, end=end_time, freq='H')
        
        n_points = len(dates)
        np.random.seed(int(time.time()) % 1000)
        
        # Erweiterte realistische Preismodellierung
        base_price = 67000
        
        # Multi-Trend Komponenten
        long_trend = np.cumsum(np.random.normal(0, 150, n_points))
        medium_trend = np.cumsum(np.random.normal(0, 300, n_points))
        short_trend = np.cumsum(np.random.normal(0, 500, n_points))
        
        # Volatilitäts-Regime mit Autokorrelation
        vol_regime = np.random.choice([0.5, 1.0, 2.0, 4.0], n_points//24, p=[0.4, 0.35, 0.2, 0.05])
        vol_regime = np.repeat(vol_regime, 24)[:n_points]
        volatility = np.random.normal(0, 800, n_points) * vol_regime
        
        # Autokorrelation für Volatilität
        for i in range(1, n_points):
            volatility[i] += 0.3 * volatility[i-1]
        
        # Zyklen
        daily_cycle = 400 * np.sin(2 * np.pi * np.arange(n_points) / 24)
        weekly_cycle = 600 * np.sin(2 * np.pi * np.arange(n_points) / (24 * 7))
        
        # News Events mit verschiedenen Stärken
        news_events = np.random.choice([0, 1], n_points, p=[0.995, 0.005])
        news_magnitude = np.random.choice([1000, 2500, 5000], n_points)
        news_direction = np.random.choice([-1, 1], n_points)
        news_impact = news_events * news_magnitude * news_direction
        
        prices = (base_price + long_trend + medium_trend + short_trend + 
                 volatility + daily_cycle + weekly_cycle + news_impact)
        prices = np.maximum(prices, 30000)
        
        # Realistische OHLCV mit Korrelationen
        high_mult = np.random.uniform(1.001, 1.03, n_points)
        low_mult = np.random.uniform(0.97, 0.999, n_points)
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * high_mult,
            'low': prices * low_mult,
            'open': prices * np.random.uniform(0.998, 1.002, n_points),
            'volume': np.random.lognormal(15, 0.4, n_points)
        }, index=dates)
        
        # Memory-Optimierung
        df = self._optimize_memory(df)
        
        print(f"✅ Realistische Fallback-Daten: {len(df)} Stunden")
        return df

    def create_ultimative_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Ultimative Feature-Engineering mit allen bewährten Indikatoren"""

        # Caching prüfen
        data_hash = self._calculate_data_hash(df)
        if self.cached_features is not None and self.last_data_hash == data_hash:
            print("✅ Features aus Cache geladen")
            return self.cached_features

        print("🔧 Erstelle ultimative Features...")
        df = df.copy()

        # === PREIS-BASIERTE FEATURES ===

        # Returns und Momentum (erweitert)
        for period in [1, 2, 3, 4, 6, 8, 12, 18, 24, 36, 48]:
            df[f'returns_{period}h'] = df['close'].pct_change(periods=period)
            df[f'log_returns_{period}h'] = np.log(df['close'] / df['close'].shift(period))
            df[f'momentum_{period}h'] = df['close'] / df['close'].shift(period) - 1

            # Momentum-Beschleunigung (vereinfacht)
            if period == 6:  # Nur für 6h vs 3h
                df[f'momentum_accel_{period}h'] = df[f'momentum_{period}h'] - df[f'momentum_3h']
            elif period == 12:  # Nur für 12h vs 6h
                df[f'momentum_accel_{period}h'] = df[f'momentum_{period}h'] - df[f'momentum_6h']

        # Moving Averages (umfassend)
        for window in [3, 6, 9, 12, 18, 24, 36, 48, 72, 96]:
            df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
            df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
            df[f'wma_{window}'] = df['close'].rolling(window=window).apply(
                lambda x: np.average(x, weights=np.arange(1, len(x)+1)), raw=True)

            # MA Signale und Slopes
            df[f'price_above_sma_{window}'] = (df['close'] > df[f'sma_{window}']).astype(float)
            df[f'price_above_ema_{window}'] = (df['close'] > df[f'ema_{window}']).astype(float)
            df[f'sma_slope_{window}'] = df[f'sma_{window}'].diff()
            df[f'ema_slope_{window}'] = df[f'ema_{window}'].diff()

            # MA Distanz
            df[f'price_distance_sma_{window}'] = (df['close'] - df[f'sma_{window}']) / df[f'sma_{window}']
            df[f'price_distance_ema_{window}'] = (df['close'] - df[f'ema_{window}']) / df[f'ema_{window}']

        # Golden/Death Cross Signale (alle wichtigen Kombinationen)
        ma_pairs = [(6, 24), (12, 48), (24, 72), (48, 96)]
        for short, long in ma_pairs:
            df[f'golden_cross_{short}_{long}'] = (df[f'sma_{short}'] > df[f'sma_{long}']).astype(float)
            df[f'ema_cross_{short}_{long}'] = (df[f'ema_{short}'] > df[f'ema_{long}']).astype(float)
            df[f'ma_spread_{short}_{long}'] = (df[f'sma_{short}'] - df[f'sma_{long}']) / df[f'sma_{long}']

        # === VOLATILITÄTS-FEATURES ===

        # Volatilität (alle Zeiträume)
        for window in [6, 12, 18, 24, 36, 48, 72, 96]:
            df[f'volatility_{window}'] = df['close'].rolling(window=window).std()
            df[f'vol_ratio_{window}'] = df[f'volatility_{window}'] / df['close']
            df[f'vol_percentile_{window}'] = df[f'volatility_{window}'].rolling(window=168).rank(pct=True)

            # GARCH-ähnliche Features
            returns = df['close'].pct_change()
            df[f'ewm_vol_{window}'] = returns.ewm(span=window).std()
            df[f'vol_of_vol_{window}'] = df[f'volatility_{window}'].rolling(window=24).std()

            # Volatilitäts-Breakouts
            df[f'vol_breakout_{window}'] = (df[f'volatility_{window}'] >
                                          df[f'volatility_{window}'].rolling(window=48).quantile(0.8)).astype(float)

        # Volatilitäts-Regime
        df['vol_regime_low'] = (df['volatility_24'] < df['volatility_24'].rolling(window=168).quantile(0.33)).astype(float)
        df['vol_regime_high'] = (df['volatility_24'] > df['volatility_24'].rolling(window=168).quantile(0.67)).astype(float)

        # === TECHNISCHE INDIKATOREN ===

        # RSI (multiple Perioden)
        for period in [9, 14, 21, 28]:
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / (loss + 1e-10)
            df[f'rsi_{period}'] = 100 - (100 / (1 + rs))

            # RSI Signale
            df[f'rsi_{period}_oversold'] = (df[f'rsi_{period}'] < 30).astype(float)
            df[f'rsi_{period}_overbought'] = (df[f'rsi_{period}'] > 70).astype(float)
            df[f'rsi_{period}_divergence'] = (df[f'rsi_{period}'].diff() * df['returns_1h'] < 0).astype(float)
            df[f'rsi_{period}_momentum'] = df[f'rsi_{period}'].diff()

        # MACD (multiple Kombinationen)
        macd_params = [(12, 26, 9), (8, 21, 5), (19, 39, 9)]
        for fast, slow, signal in macd_params:
            ema_fast = df['close'].ewm(span=fast).mean()
            ema_slow = df['close'].ewm(span=slow).mean()
            df[f'macd_{fast}_{slow}'] = ema_fast - ema_slow
            df[f'macd_signal_{fast}_{slow}'] = df[f'macd_{fast}_{slow}'].ewm(span=signal).mean()
            df[f'macd_histogram_{fast}_{slow}'] = df[f'macd_{fast}_{slow}'] - df[f'macd_signal_{fast}_{slow}']
            df[f'macd_bullish_{fast}_{slow}'] = (df[f'macd_{fast}_{slow}'] > df[f'macd_signal_{fast}_{slow}']).astype(float)
            df[f'macd_momentum_{fast}_{slow}'] = df[f'macd_histogram_{fast}_{slow}'].diff()

        # Stochastic Oscillator
        for k_period in [14, 21]:
            for d_period in [3, 5]:
                low_min = df['low'].rolling(window=k_period).min()
                high_max = df['high'].rolling(window=k_period).max()
                df[f'stoch_k_{k_period}'] = 100 * (df['close'] - low_min) / (high_max - low_min + 1e-10)
                df[f'stoch_d_{k_period}_{d_period}'] = df[f'stoch_k_{k_period}'].rolling(window=d_period).mean()
                df[f'stoch_oversold_{k_period}'] = (df[f'stoch_k_{k_period}'] < 20).astype(float)
                df[f'stoch_overbought_{k_period}'] = (df[f'stoch_k_{k_period}'] > 80).astype(float)

        # Williams %R
        for period in [14, 21]:
            high_max = df['high'].rolling(window=period).max()
            low_min = df['low'].rolling(window=period).min()
            df[f'williams_r_{period}'] = -100 * (high_max - df['close']) / (high_max - low_min + 1e-10)
            df[f'williams_r_{period}_oversold'] = (df[f'williams_r_{period}'] < -80).astype(float)
            df[f'williams_r_{period}_overbought'] = (df[f'williams_r_{period}'] > -20).astype(float)

        # Commodity Channel Index (CCI)
        for period in [14, 20]:
            tp = (df['high'] + df['low'] + df['close']) / 3
            sma_tp = tp.rolling(window=period).mean()
            mad = tp.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
            df[f'cci_{period}'] = (tp - sma_tp) / (0.015 * mad)
            df[f'cci_{period}_oversold'] = (df[f'cci_{period}'] < -100).astype(float)
            df[f'cci_{period}_overbought'] = (df[f'cci_{period}'] > 100).astype(float)

        # Bollinger Bands (multiple Perioden und Standardabweichungen)
        for window in [20, 50]:
            for std_mult in [1.5, 2.0, 2.5]:
                bb_middle = df['close'].rolling(window=window).mean()
                bb_std = df['close'].rolling(window=window).std()
                df[f'bb_upper_{window}_{std_mult}'] = bb_middle + std_mult * bb_std
                df[f'bb_lower_{window}_{std_mult}'] = bb_middle - std_mult * bb_std
                df[f'bb_position_{window}_{std_mult}'] = ((df['close'] - df[f'bb_lower_{window}_{std_mult}']) /
                                                        (df[f'bb_upper_{window}_{std_mult}'] - df[f'bb_lower_{window}_{std_mult}'] + 1e-10))
                df[f'bb_width_{window}_{std_mult}'] = ((df[f'bb_upper_{window}_{std_mult}'] - df[f'bb_lower_{window}_{std_mult}']) /
                                                     (bb_middle + 1e-10))
                df[f'bb_squeeze_{window}_{std_mult}'] = (df[f'bb_width_{window}_{std_mult}'] <
                                                       df[f'bb_width_{window}_{std_mult}'].rolling(window=50).quantile(0.2)).astype(float)

        print(f"✅ Ultimative Features erstellt: {df.shape[1]} Spalten")

        # Caching
        self.cached_features = df
        self.last_data_hash = data_hash

        return df

    def add_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Erweiterte Features - High-Low, Volume, Zeit, Markt-Mikrostruktur"""

        # === HIGH-LOW-FEATURES ===

        # High-Low Ratios und Positionen
        df['hl_ratio'] = df['high'] / (df['low'] + 1e-10)
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'] + 1e-10)
        df['high_low_spread'] = (df['high'] - df['low']) / (df['close'] + 1e-10)

        # ATR (Average True Range) - erweitert
        df['tr'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                np.abs(df['high'] - df['close'].shift()),
                np.abs(df['low'] - df['close'].shift())
            )
        )
        for window in [7, 14, 21, 28]:
            df[f'atr_{window}'] = df['tr'].rolling(window=window).mean()
            df[f'atr_ratio_{window}'] = df[f'atr_{window}'] / (df['close'] + 1e-10)
            df[f'atr_percentile_{window}'] = df[f'atr_{window}'].rolling(window=168).rank(pct=True)
            df[f'atr_expansion_{window}'] = (df[f'atr_{window}'] >
                                           df[f'atr_{window}'].rolling(window=48).quantile(0.8)).astype(float)

        # Support/Resistance Levels (erweitert)
        for window in [12, 24, 48, 72]:
            df[f'local_high_{window}'] = (df['close'].rolling(window=window, center=True).max() == df['close']).astype(float)
            df[f'local_low_{window}'] = (df['close'].rolling(window=window, center=True).min() == df['close']).astype(float)
            df[f'resistance_strength_{window}'] = df[f'local_high_{window}'].rolling(window=168).sum()
            df[f'support_strength_{window}'] = df[f'local_low_{window}'].rolling(window=168).sum()

            # Breakout-Signale
            df[f'resistance_break_{window}'] = ((df['close'] > df['close'].rolling(window=window).max().shift()) &
                                               (df[f'resistance_strength_{window}'] > 2)).astype(float)
            df[f'support_break_{window}'] = ((df['close'] < df['close'].rolling(window=window).min().shift()) &
                                            (df[f'support_strength_{window}'] > 2)).astype(float)

        # === VOLUME-FEATURES ===

        # Volume Indikatoren (erweitert)
        for window in [6, 12, 24, 48, 72]:
            df[f'volume_sma_{window}'] = df['volume'].rolling(window=window).mean()
            df[f'volume_ratio_{window}'] = df['volume'] / (df[f'volume_sma_{window}'] + 1e-10)
            df[f'volume_spike_{window}'] = (df['volume'] > df[f'volume_sma_{window}'] * 2).astype(float)
            df[f'volume_dry_{window}'] = (df['volume'] < df[f'volume_sma_{window}'] * 0.5).astype(float)

        # OBV (On-Balance Volume) - erweitert
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).cumsum()
        for window in [12, 24, 48]:
            df[f'obv_sma_{window}'] = df['obv'].rolling(window=window).mean()
            df[f'obv_ratio_{window}'] = df['obv'] / (df[f'obv_sma_{window}'] + 1e-10)
            df[f'obv_divergence_{window}'] = (np.sign(df['obv'].diff()) != np.sign(df['close'].diff())).astype(float)

        # VWAP (Volume Weighted Average Price) - erweitert
        df['vwap'] = (df['close'] * df['volume']).cumsum() / (df['volume'].cumsum() + 1e-10)
        df['price_vs_vwap'] = (df['close'] - df['vwap']) / (df['vwap'] + 1e-10)

        # Intraday VWAP
        df['hour'] = df.index.hour
        df['vwap_intraday'] = df.groupby(df.index.date).apply(
            lambda x: (x['close'] * x['volume']).cumsum() / (x['volume'].cumsum() + 1e-10)
        ).values
        df['price_vs_vwap_intraday'] = (df['close'] - df['vwap_intraday']) / (df['vwap_intraday'] + 1e-10)

        # Volume-Price Trend (VPT)
        df['vpt'] = ((df['close'].diff() / (df['close'].shift() + 1e-10)) * df['volume']).cumsum()
        df['vpt_sma'] = df['vpt'].rolling(window=24).mean()
        df['vpt_signal'] = (df['vpt'] > df['vpt_sma']).astype(float)

        # === ZEIT-FEATURES ===

        # Zeit-basierte Features (erweitert)
        df['hour'] = df.index.hour
        df['day_of_week'] = df.index.dayofweek
        df['day_of_month'] = df.index.day
        df['month'] = df.index.month
        df['quarter'] = df.index.quarter
        df['week_of_year'] = df.index.isocalendar().week

        # Cyclical Encoding (erweitert)
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        df['week_sin'] = np.sin(2 * np.pi * df['week_of_year'] / 52)
        df['week_cos'] = np.cos(2 * np.pi * df['week_of_year'] / 52)

        # Trading Sessions (erweitert)
        df['is_us_trading'] = ((df['hour'] >= 9) & (df['hour'] <= 16)).astype(float)
        df['is_asian_session'] = ((df['hour'] >= 0) & (df['hour'] <= 8)).astype(float)
        df['is_european_session'] = ((df['hour'] >= 8) & (df['hour'] <= 16)).astype(float)
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(float)
        df['is_month_end'] = (df['day_of_month'] >= 28).astype(float)
        df['is_quarter_end'] = ((df['month'] % 3 == 0) & (df['day_of_month'] >= 28)).astype(float)

        # Session-basierte Features
        df['session_open'] = df.groupby([df.index.date,
                                        ((df['hour'] >= 9) & (df['hour'] <= 16)).astype(int)])['close'].transform('first')
        df['session_return'] = (df['close'] - df['session_open']) / (df['session_open'] + 1e-10)

        # === MARKT-MIKROSTRUKTUR ===

        # Bid-Ask Spread Proxy (erweitert)
        df['spread_proxy'] = (df['high'] - df['low']) / (df['close'] + 1e-10)
        df['spread_percentile'] = df['spread_proxy'].rolling(window=168).rank(pct=True)

        # Price Impact
        df['price_impact'] = np.abs(df['close'].diff()) / (df['volume'] + 1e-10)
        df['price_impact_sma'] = df['price_impact'].rolling(window=24).mean()

        # Trend Strength (erweitert)
        for window in [12, 24, 48, 72]:
            df[f'trend_strength_{window}'] = np.abs(df[f'returns_{window}h'])
            df[f'strong_trend_{window}'] = (df[f'trend_strength_{window}'] >
                                          df[f'trend_strength_{window}'].rolling(window=168).quantile(0.8)).astype(float)

            # Trend Konsistenz
            df[f'trend_consistency_{window}'] = (df[f'returns_{window//4}h'].rolling(window=4).apply(
                lambda x: (np.sign(x) == np.sign(x).mode()[0]).sum() / len(x)
            ))

        # === LAG FEATURES ===

        # Lag Features (erweitert)
        for lag in [1, 2, 3, 6, 12, 24]:
            df[f'close_lag_{lag}'] = df['close'].shift(lag)
            df[f'returns_lag_{lag}'] = df['returns_1h'].shift(lag)
            df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
            df[f'volatility_lag_{lag}'] = df['volatility_24'].shift(lag)
            df[f'rsi_lag_{lag}'] = df['rsi_14'].shift(lag)

        # Rolling Features
        for window in [6, 12, 24]:
            df[f'returns_rolling_mean_{window}'] = df['returns_1h'].rolling(window=window).mean()
            df[f'returns_rolling_std_{window}'] = df['returns_1h'].rolling(window=window).std()
            df[f'returns_rolling_skew_{window}'] = df['returns_1h'].rolling(window=window).skew()
            df[f'returns_rolling_kurt_{window}'] = df['returns_1h'].rolling(window=window).kurt()

        return df

    def get_adaptive_threshold(self, df: pd.DataFrame, horizon_hours: int) -> float:
        """Adaptive Schwellenwerte basierend auf aktueller Marktlage"""

        # Basis-Schwellenwert
        base_threshold = self.config.BASE_THRESHOLDS[horizon_hours]

        # Aktuelle Volatilität
        recent_volatility = df['close'].pct_change().rolling(24).std().iloc[-1]
        normal_volatility = 0.02  # 2% als Normal
        volatility_multiplier = max(0.5, min(2.0, recent_volatility / normal_volatility))

        # Trend-Stärke
        trend_strength = abs(df['returns_24h'].iloc[-1])
        trend_multiplier = max(0.8, min(1.5, 1 + trend_strength))

        # Volume-Anomalie
        volume_ratio = df['volume_ratio_24'].iloc[-1] if 'volume_ratio_24' in df.columns else 1.0
        volume_multiplier = max(0.9, min(1.3, volume_ratio))

        # Kombinierter adaptiver Schwellenwert
        adaptive_threshold = base_threshold * volatility_multiplier * trend_multiplier * volume_multiplier

        return adaptive_threshold

    def create_trading_labels(self, df: pd.DataFrame, horizon_hours: int) -> pd.Series:
        """Optimierte Trading-Labels mit adaptiven Schwellenwerten"""

        future_price = df['close'].shift(-horizon_hours)
        current_price = df['close']
        future_return = (future_price / current_price - 1).fillna(0)

        # Adaptive Schwellenwerte verwenden
        threshold = self.get_adaptive_threshold(df, horizon_hours)

        # Labels: 1 = KAUF (Gewinn erwartet), 0 = VERKAUF
        labels = (future_return > threshold).astype(int)

        return labels

    def train_ultimative_models(self, df: pd.DataFrame) -> bool:
        """Ultimatives Modell-Training mit allen verfügbaren Modellen"""

        print("🤖 Trainiere ultimative Modelle...")

        # Ultimative Features erstellen
        df_features = self.create_ultimative_features(df)
        df_features = self.add_advanced_features(df_features)

        # Robuste Bereinigung
        df_features = df_features.fillna(method='ffill').fillna(method='bfill').fillna(0)
        df_features = df_features.replace([np.inf, -np.inf], 0)

        # Memory-Optimierung
        df_features = self._optimize_memory(df_features)

        # Feature-Auswahl (nur numerische, keine OHLCV)
        exclude_cols = ['close', 'high', 'low', 'open', 'volume']
        feature_cols = [col for col in df_features.columns if col not in exclude_cols]

        print(f"📊 Verwende {len(feature_cols)} Features für Training")

        results = {}

        for horizon in self.config.PREDICTION_HORIZONS:
            print(f"  📈 Training für {horizon}h Horizont...")

            # Adaptive Labels für diesen Horizont
            labels = self.create_trading_labels(df_features, horizon)

            X = df_features[feature_cols].values
            y = labels.values

            # Robuste Bereinigung
            valid_mask = ~(np.isnan(X).any(axis=1) | np.isnan(y) | np.isinf(X).any(axis=1))
            X = X[valid_mask]
            y = y[valid_mask]

            if len(X) < 100:
                print(f"    ❌ Zu wenig Daten für {horizon}h ({len(X)} Samples)")
                continue

            # Skalierung
            scaler = RobustScaler()
            X_scaled = scaler.fit_transform(X)
            self.scalers[f'{horizon}h'] = scaler

            # Train/Test Split
            split_idx = int(len(X_scaled) * 0.8)
            X_train, X_test = X_scaled[:split_idx], X_scaled[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]

            # Ultimative Modell-Suite
            models = {
                f'RandomForest_{horizon}h': RandomForestClassifier(
                    **self.config.MODEL_PARAMS['RandomForest'],
                    n_jobs=self.config.MAX_CORES
                ),
                f'ExtraTrees_{horizon}h': ExtraTreesClassifier(
                    **self.config.MODEL_PARAMS['ExtraTrees'],
                    n_jobs=self.config.MAX_CORES
                ),
                f'LogisticRegression_{horizon}h': LogisticRegression(
                    **self.config.MODEL_PARAMS['LogisticRegression']
                )
            }

            # XGBoost hinzufügen falls verfügbar
            if XGBOOST_AVAILABLE:
                models[f'XGBoost_{horizon}h'] = xgb.XGBClassifier(
                    **self.config.MODEL_PARAMS['XGBoost'],
                    n_jobs=self.config.MAX_CORES,
                    eval_metric='logloss'
                )

            horizon_results = {}

            for name, model in models.items():
                try:
                    # Training mit Timing
                    start_time = time.time()
                    model.fit(X_train, y_train)
                    training_time = time.time() - start_time

                    # Evaluierung
                    y_pred = model.predict(X_test)
                    y_pred_proba = model.predict_proba(X_test)

                    accuracy = accuracy_score(y_test, y_pred)

                    # Cross-Validation für robuste Bewertung
                    cv_scores = cross_val_score(model, X_train, y_train, cv=3, scoring='accuracy')

                    # Feature Importance (falls verfügbar)
                    if hasattr(model, 'feature_importances_'):
                        feature_importance = dict(zip(feature_cols, model.feature_importances_))
                        top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:20]
                        self.feature_importance[name] = top_features

                    # Confusion Matrix
                    cm = confusion_matrix(y_test, y_pred)

                    horizon_results[name] = {
                        'model': model,
                        'accuracy': accuracy,
                        'cv_mean': cv_scores.mean(),
                        'cv_std': cv_scores.std(),
                        'training_time': training_time,
                        'feature_cols': feature_cols,
                        'confusion_matrix': cm,
                        'y_pred_proba': y_pred_proba
                    }

                    print(f"    ✅ {name}: {accuracy:.3f} (CV: {cv_scores.mean():.3f}±{cv_scores.std():.3f}) - {training_time:.1f}s")

                except Exception as e:
                    print(f"    ❌ {name}: Fehler - {e}")
                    continue

            if horizon_results:
                results[f'{horizon}h'] = horizon_results

            # Memory cleanup
            gc.collect()

        self.models = results

        # Modelle speichern
        self._save_models()

        return len(results) > 0

    def _save_models(self):
        """Speichert trainierte Modelle"""
        try:
            import joblib
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            for horizon_key, horizon_models in self.models.items():
                for model_name, model_data in horizon_models.items():
                    filename = f'ultimativer_favorit/models/{model_name}_{timestamp}.joblib'
                    joblib.dump({
                        'model': model_data['model'],
                        'scaler': self.scalers.get(horizon_key),
                        'feature_cols': model_data['feature_cols'],
                        'accuracy': model_data['accuracy']
                    }, filename)

            print(f"💾 Modelle gespeichert mit Zeitstempel: {timestamp}")

        except Exception as e:
            print(f"⚠️ Modell-Speicherung fehlgeschlagen: {e}")

    def predict_ultimative_signals(self, df: pd.DataFrame) -> Optional[Dict]:
        """Ultimative Trading-Signal Vorhersage mit allen Verbesserungen"""

        if not self.models:
            print("❌ Keine trainierten Modelle verfügbar")
            return None

        print("🔮 Erstelle ultimative Trading-Signale...")

        # Ultimative Features erstellen (mit Caching)
        df_features = self.create_ultimative_features(df)
        df_features = self.add_advanced_features(df_features)

        # Robuste Bereinigung
        df_features = df_features.fillna(method='ffill').fillna(method='bfill').fillna(0)
        df_features = df_features.replace([np.inf, -np.inf], 0)

        predictions = {}

        for horizon_key, horizon_models in self.models.items():
            horizon = int(horizon_key.replace('h', ''))

            # Features für diesen Horizont
            feature_cols = list(horizon_models.values())[0]['feature_cols']
            X_latest = df_features[feature_cols].iloc[-1:].values

            # Robuste Bereinigung
            if np.isnan(X_latest).any() or np.isinf(X_latest).any():
                X_latest = np.nan_to_num(X_latest, 0)

            # Skalierung
            scaler = self.scalers.get(horizon_key)
            if scaler is None:
                print(f"    ❌ Kein Scaler für {horizon_key}")
                continue

            try:
                X_scaled = scaler.transform(X_latest)
            except Exception as e:
                print(f"    ❌ Skalierung Fehler für {horizon_key}: {e}")
                continue

            # Ensemble-Vorhersage mit verbesserter Gewichtung
            ensemble_predictions = []
            ensemble_weights = []
            model_details = []

            for model_name, model_data in horizon_models.items():
                try:
                    pred_proba = model_data['model'].predict_proba(X_scaled)[0]
                    buy_probability = pred_proba[1]

                    ensemble_predictions.append(buy_probability)

                    # Gewichtung basierend auf CV-Score und Konfidenz
                    cv_weight = model_data.get('cv_mean', model_data['accuracy'])
                    confidence_weight = max(buy_probability, 1-buy_probability)
                    combined_weight = cv_weight * (1 + confidence_weight)

                    ensemble_weights.append(combined_weight)

                    model_details.append({
                        'name': model_name,
                        'probability': buy_probability,
                        'accuracy': model_data['accuracy'],
                        'weight': combined_weight
                    })

                except Exception as e:
                    print(f"    ❌ Vorhersage Fehler für {model_name}: {e}")
                    continue

            if not ensemble_predictions:
                print(f"    ❌ Keine Vorhersagen für {horizon_key}")
                continue

            # Gewichtetes Ensemble
            weights = np.array(ensemble_weights)
            weights = weights / weights.sum()
            ensemble_prob = np.average(ensemble_predictions, weights=weights)

            # Ultimative Trading-Signale mit konfigurierbaren Schwellenwerten
            thresholds = self.config.SIGNAL_THRESHOLDS

            if ensemble_prob > thresholds['STARKER_KAUF']:
                signal = "STARKER KAUF 🔥🔥🔥"
                action = "SOFORT KAUFEN!"
                confidence = ensemble_prob
            elif ensemble_prob > thresholds['KAUF']:
                signal = "KAUF 🔥🔥"
                action = "KAUFEN"
                confidence = ensemble_prob
            elif ensemble_prob < thresholds['STARKER_VERKAUF']:
                signal = "STARKER VERKAUF 🔻🔻🔻"
                action = "SOFORT VERKAUFEN!"
                confidence = 1 - ensemble_prob
            elif ensemble_prob < thresholds['VERKAUF']:
                signal = "VERKAUF 🔻🔻"
                action = "VERKAUFEN"
                confidence = 1 - ensemble_prob
            else:
                signal = "HALTEN ⚖️"
                action = "POSITION HALTEN"
                confidence = 0.5

            predictions[f'{horizon}h'] = {
                'signal': signal,
                'action': action,
                'probability': ensemble_prob,
                'confidence': confidence,
                'models_used': len(ensemble_predictions),
                'model_details': model_details,
                'adaptive_threshold': self.get_adaptive_threshold(df_features, horizon)
            }

        # Ultimatives Gesamtsignal mit verbesserter Gewichtung
        if predictions:
            weighted_prob = 0
            total_weight = 0

            for horizon_key, pred in predictions.items():
                horizon = int(horizon_key.replace('h', ''))
                base_weight = self.config.HORIZON_WEIGHTS.get(horizon, 0.1)

                # Gewichtung basierend auf Konfidenz
                confidence_multiplier = 1 + (pred['confidence'] - 0.5)
                final_weight = base_weight * confidence_multiplier

                weighted_prob += pred['probability'] * final_weight
                total_weight += final_weight

            if total_weight > 0:
                overall_prob = weighted_prob / total_weight

                thresholds = self.config.SIGNAL_THRESHOLDS

                if overall_prob > thresholds['STARKER_KAUF']:
                    overall_signal = "STARKER KAUF 🔥🔥🔥"
                    overall_action = "🚀 SOFORT KAUFEN!"
                elif overall_prob > thresholds['KAUF']:
                    overall_signal = "KAUF 🔥🔥"
                    overall_action = "🔥 KAUFEN"
                elif overall_prob < thresholds['STARKER_VERKAUF']:
                    overall_signal = "STARKER VERKAUF 🔻🔻🔻"
                    overall_action = "💥 SOFORT VERKAUFEN!"
                elif overall_prob < thresholds['VERKAUF']:
                    overall_signal = "VERKAUF 🔻🔻"
                    overall_action = "🔻 VERKAUFEN"
                else:
                    overall_signal = "HALTEN ⚖️"
                    overall_action = "⚖️ POSITION HALTEN"

                predictions['GESAMT'] = {
                    'signal': overall_signal,
                    'action': overall_action,
                    'probability': overall_prob,
                    'confidence': max(overall_prob, 1-overall_prob),
                    'total_models': sum(pred['models_used'] for pred in predictions.values() if 'models_used' in pred)
                }

        current_price = df['close'].iloc[-1]
        current_time = df.index[-1]

        return {
            'time': current_time,
            'price': current_price,
            'predictions': predictions,
            'feature_count': len(df_features.columns),
            'data_quality': self._assess_data_quality(df_features)
        }

    def _assess_data_quality(self, df: pd.DataFrame) -> Dict[str, float]:
        """Bewertet die Qualität der Eingangsdaten"""

        # Vollständigkeit
        completeness = 1 - (df.isnull().sum().sum() / (df.shape[0] * df.shape[1]))

        # Volatilität (normale Marktbedingungen)
        recent_volatility = df['close'].pct_change().rolling(24).std().iloc[-1]
        volatility_score = max(0, min(1, 1 - abs(recent_volatility - 0.02) / 0.05))

        # Datenaktualität
        last_timestamp = df.index[-1]
        time_diff = (datetime.now() - last_timestamp.to_pydatetime()).total_seconds() / 3600
        freshness_score = max(0, min(1, 1 - time_diff / 24))  # Penalisiert Daten älter als 24h

        return {
            'completeness': completeness,
            'volatility_score': volatility_score,
            'freshness_score': freshness_score,
            'overall_score': (completeness + volatility_score + freshness_score) / 3
        }

    def display_ultimatives_dashboard(self, prediction_result: Dict):
        """Ultimatives Trading-Dashboard - professionell und detailliert"""

        print("\n" + "="*90)
        print("🏆 ULTIMATIVER GROSSER FAVORIT - LIVE DASHBOARD 🏆")
        print("="*90)

        if prediction_result and prediction_result['predictions']:
            predictions = prediction_result['predictions']

            print(f"\n📊 LIVE TRADING STATUS:")
            print(f"🕐 Zeit: {prediction_result['time'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"💰 Bitcoin Preis: ${prediction_result['price']:,.2f}")
            print(f"🎯 Features: {prediction_result['feature_count']}")
            print(f"📈 Datenqualität: {prediction_result['data_quality']['overall_score']:.1%}")

            # Gesamtsignal (wichtigste Info)
            if 'GESAMT' in predictions:
                gesamt = predictions['GESAMT']
                print(f"\n🎯 HAUPTSIGNAL: {gesamt['signal']}")
                print(f"💡 EMPFEHLUNG: {gesamt['action']}")
                print(f"📈 Wahrscheinlichkeit: {gesamt['probability']:.1%}")
                print(f"🎪 Konfidenz: {gesamt['confidence']:.1%}")
                print(f"🤖 Modelle gesamt: {gesamt['total_models']}")

            # Detaillierte Horizonte
            print(f"\n🔮 MULTI-HORIZONT SIGNALE:")
            print(f"{'Horizont':<8} | {'Signal':<22} | {'Aktion':<18} | {'Wahrsch.':<10} | {'Konfidenz':<10} | {'Modelle':<8}")
            print("-" * 95)

            for horizon_key, pred in predictions.items():
                if horizon_key != 'GESAMT':
                    print(f"{horizon_key:<8} | {pred['signal']:<22} | {pred['action']:<18} | "
                          f"{pred['probability']:.1%}{'':>3} | {pred['confidence']:.1%}{'':>3} | {pred['models_used']:<8}")

            # Modell-Details
            print(f"\n🤖 MODELL-DETAILS:")
            for horizon_key, pred in predictions.items():
                if horizon_key != 'GESAMT' and 'model_details' in pred:
                    print(f"\n  {horizon_key} Modelle:")
                    for model_detail in pred['model_details']:
                        print(f"    {model_detail['name']}: {model_detail['probability']:.1%} "
                              f"(Acc: {model_detail['accuracy']:.1%}, Weight: {model_detail['weight']:.3f})")

            # Adaptive Schwellenwerte
            print(f"\n⚙️ ADAPTIVE SCHWELLENWERTE:")
            for horizon_key, pred in predictions.items():
                if horizon_key != 'GESAMT' and 'adaptive_threshold' in pred:
                    base_threshold = self.config.BASE_THRESHOLDS.get(int(horizon_key.replace('h', '')), 0.01)
                    adaptive_threshold = pred['adaptive_threshold']
                    adjustment = (adaptive_threshold / base_threshold - 1) * 100
                    print(f"  {horizon_key}: {adaptive_threshold:.1%} (Basis: {base_threshold:.1%}, "
                          f"Anpassung: {adjustment:+.0f}%)")

            # Ultimative Trading-Empfehlung
            if 'GESAMT' in predictions:
                gesamt = predictions['GESAMT']

                print(f"\n💼 ULTIMATIVE TRADING-EMPFEHLUNG:")
                if "STARKER KAUF" in gesamt['signal']:
                    print(f"   🚀 AKTION: SOFORT KAUFEN!")
                    print(f"   📝 Grund: {gesamt['probability']:.1%} Chance auf Gewinn")
                    print(f"   ⚡ Dringlichkeit: SEHR HOCH")
                    print(f"   💰 Erwartung: Starker Preisanstieg erwartet")
                    print(f"   🎯 Empfohlene Position: 80-100% des verfügbaren Kapitals")
                elif "KAUF" in gesamt['signal']:
                    print(f"   🔥 AKTION: KAUFEN")
                    print(f"   📝 Grund: {gesamt['probability']:.1%} Chance auf Gewinn")
                    print(f"   ⚡ Dringlichkeit: HOCH")
                    print(f"   💰 Erwartung: Preisanstieg wahrscheinlich")
                    print(f"   🎯 Empfohlene Position: 50-70% des verfügbaren Kapitals")
                elif "STARKER VERKAUF" in gesamt['signal']:
                    print(f"   💥 AKTION: SOFORT VERKAUFEN!")
                    print(f"   📝 Grund: {1-gesamt['probability']:.1%} Chance auf Verlust")
                    print(f"   ⚡ Dringlichkeit: SEHR HOCH")
                    print(f"   📉 Erwartung: Starker Preisrückgang erwartet")
                    print(f"   🎯 Empfohlene Position: 0% (Alles verkaufen)")
                elif "VERKAUF" in gesamt['signal']:
                    print(f"   🔻 AKTION: VERKAUFEN")
                    print(f"   📝 Grund: {1-gesamt['probability']:.1%} Chance auf Verlust")
                    print(f"   ⚡ Dringlichkeit: HOCH")
                    print(f"   📉 Erwartung: Preisrückgang wahrscheinlich")
                    print(f"   🎯 Empfohlene Position: 0-30% des verfügbaren Kapitals")
                else:
                    print(f"   ⚖️ AKTION: POSITION HALTEN")
                    print(f"   📝 Grund: Unklare Marktrichtung")
                    print(f"   ⚡ Dringlichkeit: NIEDRIG")
                    print(f"   📊 Erwartung: Abwarten und beobachten")
                    print(f"   🎯 Empfohlene Position: Aktuelle Position beibehalten")
        else:
            print("\n❌ Keine Signale verfügbar")

        # System-Status
        print(f"\n⚡ ULTIMATIVES SYSTEM:")
        print(f"   🔄 Modelle: {len(self.models)} Horizonte trainiert")
        print(f"   📊 Features: {prediction_result.get('feature_count', 'N/A')}")
        print(f"   💻 CPU: {self.config.MAX_CORES} Kerne")
        print(f"   🎯 Konfiguration: Extern gespeichert")
        print(f"   📈 Datenqualität: {prediction_result['data_quality']['overall_score']:.1%}")
        print("="*90)

    def backtest_strategy(self, df: pd.DataFrame, days_back: int = 30) -> Optional[pd.DataFrame]:
        """Backtesting der Trading-Strategie"""

        print(f"\n📊 Starte Backtesting ({days_back} Tage)...")

        if len(df) < days_back * 24 + 48:  # Nicht genug Daten
            print("❌ Zu wenig Daten für Backtesting")
            return None

        results = []

        # Backtesting-Schleife
        for i in range(days_back * 24, len(df) - 48, 6):  # Alle 6 Stunden
            try:
                # Historische Daten bis zu diesem Punkt
                historical_data = df.iloc[:i]

                # Modelle auf historischen Daten trainieren
                temp_tool = UltimativerGrosserFavorit(self.config)
                if not temp_tool.train_ultimative_models(historical_data):
                    continue

                # Vorhersage machen
                prediction = temp_tool.predict_ultimative_signals(historical_data)
                if not prediction or 'GESAMT' not in prediction['predictions']:
                    continue

                gesamt_signal = prediction['predictions']['GESAMT']
                predicted_direction = 1 if gesamt_signal['probability'] > 0.5 else 0
                confidence = gesamt_signal['confidence']

                # Tatsächliches Ergebnis nach 24 Stunden
                if i + 24 < len(df):
                    actual_return = (df['close'].iloc[i + 24] / df['close'].iloc[i] - 1)
                    actual_direction = 1 if actual_return > 0.01 else 0  # 1% Schwellenwert

                    correct = (predicted_direction == actual_direction)

                    results.append({
                        'timestamp': df.index[i],
                        'price': df['close'].iloc[i],
                        'predicted_direction': predicted_direction,
                        'actual_direction': actual_direction,
                        'predicted_probability': gesamt_signal['probability'],
                        'confidence': confidence,
                        'actual_return': actual_return,
                        'correct': correct,
                        'signal': gesamt_signal['signal']
                    })

            except Exception as e:
                print(f"    ❌ Backtesting Fehler bei Index {i}: {e}")
                continue

        if not results:
            print("❌ Keine Backtesting-Ergebnisse")
            return None

        backtest_df = pd.DataFrame(results)
        self.backtest_results = backtest_df

        print(f"✅ Backtesting abgeschlossen: {len(backtest_df)} Vorhersagen")
        return backtest_df

    def display_backtest_results(self, backtest_df: pd.DataFrame):
        """Zeigt Backtesting-Ergebnisse an"""

        print(f"\n📊 BACKTESTING ERGEBNISSE:")
        print("-" * 50)

        # Gesamtstatistiken
        total_predictions = len(backtest_df)
        correct_predictions = backtest_df['correct'].sum()
        accuracy = correct_predictions / total_predictions

        print(f"📈 Gesamtgenauigkeit: {accuracy:.1%} ({correct_predictions}/{total_predictions})")

        # Genauigkeit nach Konfidenz
        high_confidence = backtest_df[backtest_df['confidence'] > 0.7]
        if len(high_confidence) > 0:
            high_conf_accuracy = high_confidence['correct'].mean()
            print(f"🎯 Hohe Konfidenz (>70%): {high_conf_accuracy:.1%} ({len(high_confidence)} Vorhersagen)")

        # Genauigkeit nach Signal-Typ
        for signal_type in backtest_df['signal'].unique():
            signal_data = backtest_df[backtest_df['signal'] == signal_type]
            signal_accuracy = signal_data['correct'].mean()
            print(f"📊 {signal_type}: {signal_accuracy:.1%} ({len(signal_data)} Vorhersagen)")

        # Durchschnittliche Returns
        avg_return = backtest_df['actual_return'].mean()
        print(f"💰 Durchschnittlicher Return: {avg_return:.2%}")

        # Speichern
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'ultimativer_favorit/backtest/backtest_results_{timestamp}.csv'
        backtest_df.to_csv(filename, index=False)
        print(f"💾 Backtesting-Ergebnisse gespeichert: {filename}")

    def create_ultimative_visualisierung(self, prediction_result: Dict, df: pd.DataFrame):
        """Ultimative interaktive Visualisierung mit Plotly"""

        if not prediction_result or not prediction_result['predictions']:
            return

        print("\n📊 Erstelle ultimative Visualisierung...")

        predictions = prediction_result['predictions']
        current_price = prediction_result['price']
        current_time = prediction_result['time']

        # Direkt Matplotlib verwenden (einfacher und zuverlässiger)
        self._create_matplotlib_fallback(prediction_result, df)

    def _create_matplotlib_fallback(self, prediction_result: Dict, df: pd.DataFrame):
        """Matplotlib-Fallback Visualisierung"""

        print("📊 Erstelle Matplotlib-Fallback Visualisierung...")

        predictions = prediction_result['predictions']
        current_price = prediction_result['price']
        current_time = prediction_result['time']

        # Große, professionelle Visualisierung
        fig, axes = plt.subplots(2, 3, figsize=(24, 16))
        fig.suptitle('🏆 ULTIMATIVER GROSSER FAVORIT - DASHBOARD 🏆',
                     fontsize=28, color='white', weight='bold', y=0.98)

        # 1. Hauptchart: Preis + Signal
        ax1 = axes[0, 0]
        recent_df = df.tail(72)
        times = recent_df.index
        prices = recent_df['close']

        ax1.plot(times, prices, color='white', linewidth=4, label='Bitcoin Preis', alpha=0.9)

        # Moving Averages
        if len(recent_df) > 24:
            sma_6 = recent_df['close'].rolling(window=6).mean()
            sma_24 = recent_df['close'].rolling(window=24).mean()
            ax1.plot(times, sma_6, color='#00ff88', linewidth=3, alpha=0.8, label='SMA 6h')
            ax1.plot(times, sma_24, color='#ff6b35', linewidth=3, alpha=0.8, label='SMA 24h')

        # Signal-Punkt
        if 'GESAMT' in predictions:
            gesamt = predictions['GESAMT']
            if "STARKER KAUF" in gesamt['signal']:
                color, marker, size = '#00ff00', '^', 600
            elif "KAUF" in gesamt['signal']:
                color, marker, size = '#00ff88', '^', 500
            elif "STARKER VERKAUF" in gesamt['signal']:
                color, marker, size = '#ff0000', 'v', 600
            elif "VERKAUF" in gesamt['signal']:
                color, marker, size = '#ff4757', 'v', 500
            else:
                color, marker, size = '#ffa502', 'o', 400

            ax1.scatter([current_time], [current_price], color=color, s=size, marker=marker,
                       zorder=10, edgecolors='white', linewidth=4)

        ax1.set_title('📈 BITCOIN PREIS + ULTIMATIVES SIGNAL', fontsize=20, color='white', weight='bold', pad=20)
        ax1.set_xlabel('Zeit', color='white', fontsize=16)
        ax1.set_ylabel('Preis (USD)', color='white', fontsize=16)
        ax1.legend(fontsize=14, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 2. Multi-Horizont Signale
        ax2 = axes[0, 1]
        horizonte = []
        wahrscheinlichkeiten = []
        colors = []

        for horizon_key, pred in predictions.items():
            if horizon_key != 'GESAMT':
                horizonte.append(horizon_key)
                wahrscheinlichkeiten.append(pred['probability'])

                if "STARKER KAUF" in pred['signal']:
                    colors.append('#00ff00')
                elif "KAUF" in pred['signal']:
                    colors.append('#00ff88')
                elif "STARKER VERKAUF" in pred['signal']:
                    colors.append('#ff0000')
                elif "VERKAUF" in pred['signal']:
                    colors.append('#ff4757')
                else:
                    colors.append('#ffa502')

        bars = ax2.bar(horizonte, wahrscheinlichkeiten, color=colors, alpha=0.8,
                      edgecolor='white', linewidth=3)

        # Werte auf Balken
        for bar, prob in zip(bars, wahrscheinlichkeiten):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                    f'{prob:.0%}', ha='center', va='bottom',
                    color='white', fontweight='bold', fontsize=18)

        ax2.axhline(y=0.5, color='white', linestyle='--', alpha=0.7, linewidth=3)
        ax2.set_title('📊 MULTI-HORIZONT KAUF-WAHRSCHEINLICHKEIT', fontsize=20, color='white', weight='bold', pad=20)
        ax2.set_xlabel('Horizont', color='white', fontsize=16)
        ax2.set_ylabel('Kauf-Wahrscheinlichkeit', color='white', fontsize=16)
        ax2.set_ylim(0, 1)
        ax2.grid(True, alpha=0.3)

        # 3. Konfidenz-Analyse
        ax3 = axes[0, 2]
        if 'GESAMT' in predictions:
            gesamt = predictions['GESAMT']
            confidence = gesamt['confidence']

            # Konfidenz-Meter als Donut-Chart
            sizes = [confidence, 1-confidence]
            colors_pie = ['#00ff88' if confidence > 0.7 else '#ffa502' if confidence > 0.5 else '#ff4757', '#333333']
            labels = [f'Konfidenz\n{confidence:.0%}', f'Unsicherheit\n{1-confidence:.0%}']

            wedges, texts, autotexts = ax3.pie(sizes, labels=labels, colors=colors_pie, autopct='',
                                              startangle=90, textprops={'color': 'white', 'fontsize': 16, 'weight': 'bold'},
                                              wedgeprops=dict(width=0.5, edgecolor='white', linewidth=3))

            ax3.set_title('🎯 ULTIMATIVE KONFIDENZ', fontsize=20, color='white', weight='bold', pad=20)

        # 4. Modell-Performance
        ax4 = axes[1, 0]
        if self.models:
            model_names = []
            accuracies = []

            for horizon_key, horizon_models in self.models.items():
                for model_name, model_data in horizon_models.items():
                    short_name = f"{model_name.split('_')[0]}\n{horizon_key}"
                    model_names.append(short_name)
                    accuracies.append(model_data['accuracy'])

            bars = ax4.bar(model_names, accuracies, color=['#00ff88', '#ff6b35', '#3742fa', '#ffa502'] * 4,
                          alpha=0.8, edgecolor='white', linewidth=2)

            # Werte auf Balken
            for bar, acc in zip(bars, accuracies):
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{acc:.0%}', ha='center', va='bottom',
                        color='white', fontweight='bold', fontsize=14)

            ax4.set_title('🤖 ULTIMATIVE MODELL-PERFORMANCE', fontsize=20, color='white', weight='bold', pad=20)
            ax4.set_xlabel('Modell', color='white', fontsize=16)
            ax4.set_ylabel('Genauigkeit', color='white', fontsize=16)
            ax4.set_ylim(0, 1.1)
            ax4.grid(True, alpha=0.3)

        # 5. Volatilität & Trends
        ax5 = axes[1, 1]
        if len(recent_df) > 24:
            volatility = recent_df['close'].pct_change().rolling(24).std()
            ax5.plot(times, volatility, color='#e74c3c', linewidth=3, alpha=0.9)
            ax5.fill_between(times, volatility, alpha=0.3, color='#e74c3c')

            ax5.set_title('📉 24H VOLATILITÄT', fontsize=20, color='white', weight='bold', pad=20)
            ax5.set_xlabel('Zeit', color='white', fontsize=16)
            ax5.set_ylabel('Volatilität', color='white', fontsize=16)
            ax5.grid(True, alpha=0.3)

        # 6. Ultimative Empfehlung
        ax6 = axes[1, 2]
        ax6.axis('off')

        if 'GESAMT' in predictions:
            gesamt = predictions['GESAMT']

            empfehlung_text = f"""ULTIMATIVE EMPFEHLUNG:

{gesamt['action']}

📈 Wahrscheinlichkeit: {gesamt['probability']:.0%}
🎪 Konfidenz: {gesamt['confidence']:.0%}
🤖 Modelle: {gesamt['total_models']}

💰 Preis: ${current_price:,.0f}
🕐 Zeit: {current_time.strftime('%H:%M:%S')}

🎯 Features: {prediction_result['feature_count']}
📊 Qualität: {prediction_result['data_quality']['overall_score']:.0%}"""

            # Hintergrundfarbe je nach Signal
            if "STARKER KAUF" in gesamt['signal']:
                bg_color = '#004d00'
                text_color = '#00ff00'
            elif "KAUF" in gesamt['signal']:
                bg_color = '#003300'
                text_color = '#00ff88'
            elif "STARKER VERKAUF" in gesamt['signal']:
                bg_color = '#4d0000'
                text_color = '#ff4444'
            elif "VERKAUF" in gesamt['signal']:
                bg_color = '#330000'
                text_color = '#ff6666'
            else:
                bg_color = '#333300'
                text_color = '#ffaa00'

            ax6.text(0.5, 0.5, empfehlung_text, transform=ax6.transAxes,
                    fontsize=18, color=text_color, ha='center', va='center', fontweight='bold',
                    bbox=dict(boxstyle='round,pad=1', facecolor=bg_color, alpha=0.9, edgecolor='white', linewidth=3))

        plt.tight_layout()

        # Speichern
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'ultimativer_favorit/ultimative_matplotlib_dashboard_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='black', edgecolor='white')
        plt.show()

        print(f"✅ Matplotlib-Visualisierung gespeichert: {filename}")

def run_ultimativer_grosser_favorit(live_mode: bool = False) -> Optional[Dict]:
    """Hauptfunktion - Ultimativer Grosser Favorit"""

    tool = UltimativerGrosserFavorit(config)

    if live_mode:
        print(f"\n🏆 STARTE ULTIMATIVER GROSSER FAVORIT - LIVE MODUS...")
        print(f"🔄 Kontinuierliche Analyse alle {config.UPDATE_INTERVAL}s")

        iteration = 0
        while True:
            try:
                iteration += 1
                start_time = time.time()

                print(f"\n{'='*70}")
                print(f"🔄 LIVE ITERATION {iteration} - {datetime.now().strftime('%H:%M:%S')}")
                print(f"{'='*70}")

                # Analyse durchführen
                result = _run_single_analysis(tool)

                if result:
                    # Visualisierung nur alle 3 Iterationen
                    if iteration % 3 == 0:
                        tool.create_ultimative_visualisierung(result['prediction_result'], result['df'])

                # Timing
                elapsed_time = time.time() - start_time
                print(f"\n⚡ Live Iteration {iteration} in {elapsed_time:.1f}s")

                # Warten
                sleep_time = max(0, config.UPDATE_INTERVAL - elapsed_time)
                if sleep_time > 0:
                    print(f"💤 Warte {sleep_time:.1f}s bis nächste Iteration...")
                    time.sleep(sleep_time)

            except KeyboardInterrupt:
                print(f"\n🛑 ULTIMATIVER GROSSER FAVORIT Live-Modus gestoppt")
                break
            except Exception as e:
                print(f"❌ Live-Modus Fehler: {e}")
                import traceback
                traceback.print_exc()
                time.sleep(config.UPDATE_INTERVAL)

    else:
        print(f"\n🏆 STARTE ULTIMATIVER GROSSER FAVORIT - EINMALIGE ANALYSE...")
        return _run_single_analysis(tool)

def _run_single_analysis(tool: UltimativerGrosserFavorit) -> Optional[Dict]:
    """Führt eine einzelne Analyse durch"""

    try:
        start_time = time.time()

        print(f"\n{'='*70}")
        print(f"🔄 ULTIMATIVE ANALYSE - {datetime.now().strftime('%H:%M:%S')}")
        print(f"{'='*70}")

        # 1. Bitcoin-Daten sammeln
        df, is_real = tool.get_bitcoin_data_robust()

        # 2. Ultimative Modelle trainieren
        training_success = tool.train_ultimative_models(df)

        if not training_success:
            print("❌ Training fehlgeschlagen")
            return None

        # 3. Ultimative Signale vorhersagen
        prediction_result = tool.predict_ultimative_signals(df)

        if not prediction_result:
            print("❌ Vorhersage fehlgeschlagen")
            return None

        # 4. Ultimatives Dashboard anzeigen
        tool.display_ultimatives_dashboard(prediction_result)

        # 5. Ultimative Visualisierung
        tool.create_ultimative_visualisierung(prediction_result, df)

        # 6. Backtesting (optional)
        if len(df) > 100:
            backtest_results = tool.backtest_strategy(df)
            if backtest_results is not None:
                tool.display_backtest_results(backtest_results)

        # 7. Timing
        elapsed_time = time.time() - start_time
        print(f"\n⚡ ULTIMATIVE Analyse abgeschlossen in {elapsed_time:.1f}s")

        return {
            'prediction_result': prediction_result,
            'df': df,
            'is_real_data': is_real,
            'elapsed_time': elapsed_time,
            'backtest_results': getattr(tool, 'backtest_results', None)
        }

    except Exception as e:
        print(f"❌ ULTIMATIVE Analyse Fehler: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    import sys

    # Kommandozeilen-Argumente
    live_mode = '--live' in sys.argv

    if live_mode:
        print("🔴 LIVE-MODUS aktiviert - Drücken Sie Ctrl+C zum Stoppen")
        result = run_ultimativer_grosser_favorit(live_mode=True)
    else:
        print("🟢 EINMALIGE ANALYSE")
        result = run_ultimativer_grosser_favorit(live_mode=False)

        if result:
            print(f"\n🎉 ULTIMATIVER GROSSER FAVORIT erfolgreich abgeschlossen!")
            print(f"📊 Datenquelle: {'Echte Bitcoin-Daten' if result['is_real_data'] else 'Simulationsdaten'}")
            print(f"⚡ Laufzeit: {result['elapsed_time']:.1f}s")
            print(f"🎯 Features: {result['prediction_result']['feature_count']}")
            print(f"📈 Datenqualität: {result['prediction_result']['data_quality']['overall_score']:.1%}")
            print(f"💾 Visualisierung gespeichert in: ./ultimativer_favorit/")

            # Konfiguration speichern
            config.save_config()
            print(f"⚙️ Konfiguration gespeichert: ultimative_config.json")
        else:
            print(f"\n❌ ULTIMATIVER GROSSER FAVORIT fehlgeschlagen")
