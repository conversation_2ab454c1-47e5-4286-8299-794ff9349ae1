#!/usr/bin/env python3
"""
🖥️ GUI-LAUNCHER DESKTOP-VERKNÜPFUNG ERSTELLER 🖥️
===============================================
🏆 AUTOMATISCHE DESKTOP-VERKNÜPFUNG FÜR GUI-LAUNCHER 🏆
✅ Erstellt Desktop-Verknüpfung für freundlichen GUI-Launcher
✅ Windows-kompatibel mit schönem Icon
✅ Ein-Klick-Start für kontinuierliche Bitcoin Trading
✅ Professionelle Verknüpfung mit Beschreibung

💡 EINFACH AUSFÜHREN UND GUI-LAUNCHER IST VOM DESKTOP STARTBAR!
"""

import os
import sys

def create_gui_desktop_shortcut():
    """Erstelle Desktop-Verknüpfung für GUI-Launcher"""
    
    print("🖥️ Erstelle Desktop-Verknüpfung für GUI-Launcher...")
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        # Pfade ermitteln
        current_dir = os.getcwd()
        launcher_path = os.path.join(current_dir, "bitcoin_gui_launcher.py")
        python_path = sys.executable
        
        # Desktop-Pfad ermitteln
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "Bitcoin Trading GUI-Launcher.lnk")
        
        # Prüfe ob GUI-Launcher existiert
        if not os.path.exists(launcher_path):
            print(f"❌ FEHLER: bitcoin_gui_launcher.py nicht gefunden!")
            print(f"💡 Stellen Sie sicher, dass der GUI-Launcher im aktuellen Ordner ist.")
            return False
        
        # Erstelle Verknüpfung
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        
        # Verknüpfungs-Eigenschaften
        shortcut.Targetpath = python_path
        shortcut.Arguments = f'"{launcher_path}"'
        shortcut.WorkingDirectory = current_dir
        shortcut.Description = "Bitcoin Trading GUI-Launcher - Freundliche Oberfläche mit kontinuierlicher Berechnung"
        shortcut.IconLocation = python_path + ",0"  # Python-Icon verwenden
        
        # Speichere Verknüpfung
        shortcut.save()
        
        print(f"✅ GUI-Desktop-Verknüpfung erfolgreich erstellt!")
        print(f"📍 Speicherort: {shortcut_path}")
        print(f"🎯 Ziel: {launcher_path}")
        print(f"💡 Sie können jetzt den GUI-Launcher vom Desktop starten!")
        
        return True
        
    except ImportError as e:
        print(f"❌ FEHLER: Benötigte Module nicht gefunden!")
        print(f"💡 Module sind bereits installiert - verwende Batch-Alternative")
        return create_gui_batch_launcher()
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Verknüpfung: {e}")
        return create_gui_batch_launcher()

def create_gui_batch_launcher():
    """Erstelle Batch-Datei für GUI-Launcher"""
    
    print("🔧 Erstelle Batch-Launcher für GUI...")
    
    try:
        current_dir = os.getcwd()
        launcher_path = os.path.join(current_dir, "bitcoin_gui_launcher.py")
        batch_path = os.path.join(current_dir, "Bitcoin Trading GUI-Launcher.bat")
        
        # Batch-Inhalt
        batch_content = f'''@echo off
title Bitcoin Trading GUI-Launcher - Kontinuierliche Berechnung
cd /d "{current_dir}"
python "{launcher_path}"
if errorlevel 1 (
    echo.
    echo ❌ Fehler beim Starten des GUI-Launchers
    echo 💡 Stellen Sie sicher, dass Python installiert ist
    echo.
    pause
)
'''
        
        # Schreibe Batch-Datei
        with open(batch_path, 'w') as f:
            f.write(batch_content)
        
        print(f"✅ GUI-Batch-Launcher erstellt: {batch_path}")
        print(f"💡 Sie können diese .bat-Datei direkt ausführen!")
        
        return True
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Batch-Datei: {e}")
        return False

def main():
    """Hauptfunktion"""
    
    print("🖥️ GUI-LAUNCHER DESKTOP-VERKNÜPFUNG ERSTELLER")
    print("=" * 60)
    print("🚀 Erstelle Desktop-Verknüpfung für freundlichen GUI-Launcher...")
    print("")
    
    # Versuche Desktop-Verknüpfung zu erstellen
    shortcut_success = create_gui_desktop_shortcut()
    
    print("\n" + "=" * 60)
    
    if shortcut_success:
        print("🎉 ERFOLGREICH!")
        print("✅ Desktop-Verknüpfung wurde erstellt")
        print("🖱️ Doppelklicken Sie auf 'Bitcoin Trading GUI-Launcher' auf Ihrem Desktop")
    else:
        print("❌ FEHLER!")
        print("💡 Starten Sie den GUI-Launcher manuell mit: python bitcoin_gui_launcher.py")
    
    print("\n💡 GUI-LAUNCHER FEATURES:")
    print("🎯 Freundliche Benutzeroberfläche mit Buttons")
    print("🔄 Kontinuierliche Berechnung für bessere Ergebnisse")
    print("🛑 Automatischer Script-Stop beim Schließen")
    print("📊 Live-Status-Anzeige aller laufenden Modelle")
    print("⚡ Ein-Klick-Start für alle Bitcoin Trading Systeme")
    
    print("\n🚀 VERWENDUNG:")
    print("1. Doppelklick auf Desktop-Verknüpfung")
    print("2. Klicken Sie auf 'Starten' für gewünschte Systeme")
    print("3. Aktivieren Sie 'Kontinuierliche Berechnung' für beste Ergebnisse")
    print("4. Überwachen Sie den Live-Status")
    print("5. Schließen Sie den Launcher - alle Prozesse werden automatisch gestoppt")
    
    print("\n👋 GUI-Setup abgeschlossen!")

if __name__ == "__main__":
    main()
