#!/usr/bin/env python3
"""
SUPER SCHNELLES Bitcoin Prediction Model
Optimiert für maximale Geschwindigkeit
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import <PERSON>
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# SUPER SCHNELLE Konfiguration
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.85,
    'look_back': 12,        # Sehr kurze Sequenzen
    'future_steps': 12,     # 12h Prognose
    'batch_size': 256,      # Große Batches
    'epochs': 15,           # Wenige Epochen
    'patience': 5,
    'monte_carlo_sims': 20  # Wenige Simulationen
}

def load_and_prepare_data():
    """Schnelle Datenvorbereitung"""
    print("📊 Lade Daten...")
    df = pd.read_csv(CONFIG['data_file'])
    df['time'] = pd.to_datetime(df['time'])
    df.set_index('time', inplace=True)
    
    print(f"✅ {len(df)} Datenpunkte geladen")
    print(f"   Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
    
    # Nur die wichtigsten Features
    features = ['close', 'volume', 'high', 'low']
    
    # Einfache technische Indikatoren
    df['sma_5'] = df['close'].rolling(5).mean()
    df['sma_10'] = df['close'].rolling(10).mean()
    df['rsi'] = calculate_rsi(df['close'], 14)
    df['volatility'] = df['close'].pct_change().rolling(10).std()
    
    features.extend(['sma_5', 'sma_10', 'rsi', 'volatility'])
    
    # NaN entfernen
    df = df[features].dropna()
    
    return df

def calculate_rsi(prices, window=14):
    """Schnelle RSI Berechnung"""
    delta = prices.diff()
    gain = delta.where(delta > 0, 0).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def create_sequences(data, look_back):
    """Sequenzen erstellen"""
    X, y = [], []
    for i in range(len(data) - look_back):
        X.append(data[i:i + look_back])
        y.append(data[i + look_back, 0])  # Nur close price
    return np.array(X), np.array(y)

def build_fast_model(input_shape):
    """Super schnelles Modell"""
    model = Sequential([
        LSTM(32, return_sequences=True, input_shape=input_shape),
        Dropout(0.2),
        LSTM(16, return_sequences=False),
        Dense(8, activation='relu'),
        Dense(1)
    ])
    
    model.compile(optimizer=Adam(learning_rate=0.002), loss='mse', metrics=['mae'])
    return model

def predict_future(model, scaler, last_sequence, steps):
    """Schnelle Zukunftsprognose"""
    predictions = []
    current_seq = last_sequence.copy()
    
    for _ in range(steps):
        pred = model.predict(current_seq.reshape(1, *current_seq.shape), verbose=0)[0, 0]
        predictions.append(pred)
        
        # Neue Sequenz mit einfacher Approximation
        new_row = current_seq[-1].copy()
        new_row[0] = pred  # close price
        current_seq = np.vstack([current_seq[1:], new_row.reshape(1, -1)])
    
    # Skalierung rückgängig machen
    pred_array = np.array(predictions).reshape(-1, 1)
    pred_rescaled = scaler.inverse_transform(
        np.hstack([pred_array, np.zeros((len(pred_array), scaler.n_features_in_ - 1))])
    )[:, 0]
    
    return pred_rescaled

def plot_results(df, train_size, y_test, y_pred, future_pred):
    """Schnelle Visualisierung"""
    plt.figure(figsize=(15, 10))
    
    # Subplot 1: Hauptchart
    plt.subplot(2, 2, 1)
    dates = df.index
    plt.plot(dates[:train_size], df['close'].iloc[:train_size], 'b-', label='Training', alpha=0.7)
    plt.plot(dates[train_size:train_size+len(y_test)], y_test, 'g-', label='Test Actual', linewidth=2)
    plt.plot(dates[train_size:train_size+len(y_pred)], y_pred, 'r--', label='Test Predicted', linewidth=2)
    
    # Zukunftsprognose
    future_dates = pd.date_range(start=dates[-1], periods=len(future_pred)+1, freq='1H')[1:]
    plt.plot(future_dates, future_pred, 'purple', linewidth=2, label='Future Prediction')
    
    plt.title('Bitcoin Price Prediction - Fast Model')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Subplot 2: Metriken
    plt.subplot(2, 2, 2)
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    metrics_text = f"""
    PERFORMANCE METRICS
    
    RMSE: ${rmse:.2f}
    MAE: ${mae:.2f}
    R²: {r2:.4f}
    
    Data Points: {len(df)}
    Training Time: <2 min
    """
    
    plt.text(0.1, 0.5, metrics_text, fontsize=12, verticalalignment='center',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    plt.axis('off')
    
    # Subplot 3: Zukunftsprognose Detail
    plt.subplot(2, 2, 3)
    hours = range(1, len(future_pred) + 1)
    plt.plot(hours, future_pred, 'purple', linewidth=2, marker='o')
    plt.title('12-Hour Future Prediction')
    plt.xlabel('Hours Ahead')
    plt.ylabel('Price (USD)')
    plt.grid(True, alpha=0.3)
    
    # Subplot 4: Residuals
    plt.subplot(2, 2, 4)
    residuals = y_test - y_pred
    plt.scatter(y_pred, residuals, alpha=0.6)
    plt.axhline(y=0, color='red', linestyle='--')
    plt.title('Residuals')
    plt.xlabel('Predicted')
    plt.ylabel('Residuals')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def main():
    """Hauptfunktion - Super schnell!"""
    print("🚀 SUPER FAST BITCOIN PREDICTION MODEL")
    print("=" * 50)
    
    import time
    start_time = time.time()
    
    try:
        # 1. Daten laden
        df = load_and_prepare_data()
        
        # 2. Daten skalieren
        print("🔄 Skaliere Daten...")
        scaler = MinMaxScaler()
        scaled_data = scaler.fit_transform(df.values)
        
        # 3. Sequenzen erstellen
        print("📦 Erstelle Sequenzen...")
        X, y = create_sequences(scaled_data, CONFIG['look_back'])
        
        # Train-Test Split
        train_size = int(len(X) * CONFIG['train_split'])
        X_train, X_test = X[:train_size], X[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        print(f"✅ Training: {X_train.shape}, Test: {X_test.shape}")
        
        # 4. Modell erstellen und trainieren
        print("🏗️  Erstelle schnelles Modell...")
        model = build_fast_model((X_train.shape[1], X_train.shape[2]))
        
        print(f"📋 Parameter: {model.count_params():,}")
        
        print("🎯 Starte schnelles Training...")
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            verbose=1
        )
        
        # 5. Evaluation
        print("📊 Evaluiere Modell...")
        y_pred = model.predict(X_test, verbose=0)
        
        # Skalierung rückgängig machen
        y_test_orig = scaler.inverse_transform(
            np.hstack([y_test.reshape(-1, 1), np.zeros((len(y_test), scaler.n_features_in_ - 1))])
        )[:, 0]
        
        y_pred_orig = scaler.inverse_transform(
            np.hstack([y_pred, np.zeros((len(y_pred), scaler.n_features_in_ - 1))])
        )[:, 0]
        
        # Metriken
        rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
        mae = mean_absolute_error(y_test_orig, y_pred_orig)
        r2 = r2_score(y_test_orig, y_pred_orig)
        
        print(f"\n📊 PERFORMANCE:")
        print(f"RMSE: ${rmse:.2f}")
        print(f"MAE: ${mae:.2f}")
        print(f"R²: {r2:.4f}")
        
        # 6. Zukunftsprognose
        print("🔮 Erstelle 12h Prognose...")
        last_sequence = X_test[-1]
        future_pred = predict_future(model, scaler, last_sequence, CONFIG['future_steps'])
        
        # 7. Visualisierung
        print("📈 Erstelle Visualisierung...")
        plot_results(df, train_size, y_test_orig, y_pred_orig, future_pred)
        
        # 8. Prognose-Zusammenfassung
        current_price = df['close'].iloc[-1]
        print(f"\n🎯 12-STUNDEN PROGNOSE:")
        for hours in [3, 6, 12]:
            if hours <= len(future_pred):
                pred_price = future_pred[hours-1]
                change_pct = ((pred_price / current_price) - 1) * 100
                print(f"In {hours}h: ${pred_price:.2f} ({change_pct:+.2f}%)")
        
        total_time = time.time() - start_time
        print(f"\n✅ Fertig! Gesamtzeit: {total_time:.1f} Sekunden")
        
    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
