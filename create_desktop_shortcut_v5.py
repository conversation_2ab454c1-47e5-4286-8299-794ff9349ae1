#!/usr/bin/env python3
"""
DESKTOP-VERKNÜPFUNG ERSTELLER V5.1
==================================
Erstellt eine Desktop-Verknüpfung für das Ultimate Bitcoin Trading System V5.1
Enhanced Edition mit Kreuz-Cursor und erweiterten Features
"""

import os
import sys

def create_desktop_shortcut_v5():
    """Erstelle Desktop-Verknüpfung für Ultimate Bitcoin Trading System V5.1"""
    try:
        print("=" * 70)
        print("DESKTOP-VERKNÜPFUNG ERSTELLER V5.1")
        print("Ultimate Bitcoin Trading System V5.1 - Enhanced Edition")
        print("=" * 70)
        
        # Aktueller Pfad
        current_dir = os.path.dirname(os.path.abspath(__file__))
        gui_script = os.path.join(current_dir, "ultimate_bitcoin_gui_v5.py")
        
        # Prüfe ob GUI-Script existiert
        if not os.path.exists(gui_script):
            print(f"❌ FEHLER: GUI-Script nicht gefunden: {gui_script}")
            return False
        
        # Python-Executable
        python_exe = sys.executable
        
        # Desktop-Pfad (Fallback-sicher)
        try:
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
            if not os.path.exists(desktop):
                desktop = os.path.join(os.path.expanduser("~"), "OneDrive", "Desktop")
            if not os.path.exists(desktop):
                desktop = current_dir
        except:
            desktop = current_dir
        
        print(f"Desktop-Pfad: {desktop}")
        
        # Erstelle Batch-Datei für Windows
        batch_path = os.path.join(desktop, "🚀 Ultimate Bitcoin Trading V5.1.bat")
        
        batch_content = f'''@echo off
title Ultimate Bitcoin Trading System V5.1 - Enhanced Edition
color 0A
echo ================================================================
echo 🚀 ULTIMATE BITCOIN TRADING SYSTEM V5.1 - ENHANCED EDITION 🚀
echo ================================================================
echo ✅ Kreuz-Cursor in allen Charts
echo ✅ Erweiterte Gitterlinien für präzise Beschriftung
echo ✅ Live-Preisanzeige im Header
echo ✅ Funktionierendes Trading-Signal
echo ✅ Verbessertes Shutdown-Management
echo ✅ Alle Threads werden beim Schließen gestoppt
echo ================================================================
echo.
echo 🔄 Starte Enhanced Trading System...
echo.
cd /d "{current_dir}"
"{python_exe}" "{gui_script}"
echo.
echo ✅ System beendet.
echo.
pause
'''
        
        with open(batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"✅ Desktop-Verknüpfung erstellt: {batch_path}")
        
        # Erstelle auch Start-Script im aktuellen Verzeichnis
        start_script_path = os.path.join(current_dir, "🚀 START_TRADING_SYSTEM_V5.1.bat")
        
        start_content = f'''@echo off
title Ultimate Bitcoin Trading System V5.1 - Enhanced Edition
color 0A
cls
echo ================================================================
echo 🚀 ULTIMATE BITCOIN TRADING SYSTEM V5.1 - ENHANCED EDITION 🚀
echo ================================================================
echo.
echo 🎯 NEUE FEATURES V5.1:
echo    ✅ Kreuz-Cursor in allen Charts für präzise Navigation
echo    ✅ Erweiterte Gitterlinien für genauere Beschriftung
echo    ✅ Live-Preisanzeige mit 24h-Änderung im Header
echo    ✅ Funktionierendes Trading-Signal mit Konfidenz
echo    ✅ Verbessertes Shutdown-Management
echo    ✅ Alle Threads werden beim Schließen automatisch gestoppt
echo    ✅ Enhanced Edition mit allen Verbesserungen
echo.
echo 🔧 TECHNISCHE VERBESSERUNGEN:
echo    ✅ Thread-Management für sauberes Beenden
echo    ✅ Erweiterte Chart-Funktionalität
echo    ✅ Verbesserte Benutzeroberfläche
echo    ✅ Optimierte Performance
echo.
echo ================================================================
echo.
echo 🔄 Starte Ultimate Bitcoin Trading System V5.1...
echo.
cd /d "{current_dir}"
"{python_exe}" "{gui_script}"
echo.
echo ✅ Ultimate Bitcoin Trading System V5.1 beendet.
echo.
pause
'''
        
        with open(start_script_path, 'w', encoding='utf-8') as f:
            f.write(start_content)
        
        print(f"✅ Start-Script erstellt: {start_script_path}")
        
        # Erstelle auch PowerShell-Script
        ps_script_path = os.path.join(current_dir, "Start-TradingSystem-V5.1.ps1")
        
        ps_content = f'''# Ultimate Bitcoin Trading System V5.1 - PowerShell Launcher
Write-Host "================================================================" -ForegroundColor Green
Write-Host "🚀 ULTIMATE BITCOIN TRADING SYSTEM V5.1 - ENHANCED EDITION 🚀" -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Green
Write-Host ""
Write-Host "🎯 Enhanced Features:" -ForegroundColor Cyan
Write-Host "   ✅ Kreuz-Cursor in allen Charts" -ForegroundColor Green
Write-Host "   ✅ Erweiterte Gitterlinien" -ForegroundColor Green
Write-Host "   ✅ Live-Preisanzeige" -ForegroundColor Green
Write-Host "   ✅ Funktionierendes Trading-Signal" -ForegroundColor Green
Write-Host "   ✅ Verbessertes Shutdown-Management" -ForegroundColor Green
Write-Host ""
Write-Host "🔄 Starte System..." -ForegroundColor Yellow

Set-Location "{current_dir}"
& "{python_exe}" "{gui_script}"

Write-Host ""
Write-Host "✅ System beendet." -ForegroundColor Green
Read-Host "Drücken Sie Enter zum Beenden"
'''
        
        with open(ps_script_path, 'w', encoding='utf-8') as f:
            f.write(ps_content)
        
        print(f"✅ PowerShell-Script erstellt: {ps_script_path}")
        
        print("\n" + "=" * 70)
        print("🏆 VERKNÜPFUNGEN ERFOLGREICH ERSTELLT!")
        print("=" * 70)
        print("Sie können das Ultimate Bitcoin Trading System V5.1 starten über:")
        print(f"1. 🖥️  Desktop: 🚀 Ultimate Bitcoin Trading V5.1.bat")
        print(f"2. 📁 Lokal: 🚀 START_TRADING_SYSTEM_V5.1.bat")
        print(f"3. 💻 PowerShell: Start-TradingSystem-V5.1.ps1")
        print("=" * 70)
        print("🎯 Enhanced Edition Features:")
        print("   ✅ Kreuz-Cursor in allen Charts")
        print("   ✅ Erweiterte Gitterlinien für präzise Beschriftung")
        print("   ✅ Live-Preisanzeige mit 24h-Änderung")
        print("   ✅ Funktionierendes Trading-Signal")
        print("   ✅ Verbessertes Shutdown-Management")
        print("   ✅ Alle Threads werden beim Schließen gestoppt")
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Desktop-Verknüpfung: {e}")
        return False

if __name__ == "__main__":
    print("Erstelle Desktop-Verknüpfung für Ultimate Bitcoin Trading System V5.1...")
    
    success = create_desktop_shortcut_v5()
    
    if success:
        print("\n🎉 Verknüpfung-Erstellung erfolgreich abgeschlossen!")
    else:
        print("\n❌ Verknüpfung-Erstellung fehlgeschlagen!")
    
    input("\nDrücken Sie Enter zum Beenden...")
