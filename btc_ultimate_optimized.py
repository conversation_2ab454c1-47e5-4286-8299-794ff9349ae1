#!/usr/bin/env python3
"""
ULTIMATE OPTIMIZED BITCOIN PREDICTION MODEL
Kombiniert die besten Elemente aus allen Scripts für maximale Performance
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import (LSTM, GRU, Dense, Dropout, BatchNormalization, 
                                   Bidirectional, Input, Concatenate, Add, MultiHeadAttention, LayerNormalization)
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.optimizers.legacy import Adam, RMSprop
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import TimeSeriesSplit
import warnings
import time
import os
warnings.filterwarnings('ignore')

print("🚀 ULTIMATE OPTIMIZED BITCOIN PREDICTION MODEL")
print("=" * 70)
print("🎯 Kombiniert die BESTEN Elemente aus allen Scripts")
print("💻 Ziel: >95% Genauigkeit mit optimaler Performance")
print("=" * 70)

# ULTIMATE OPTIMIERTE KONFIGURATION (Best of All Scripts)
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.85,           # Aus btc_ultimate_80percent_v2.py
    'validation_split': 0.1,       # Separate Validation
    'look_back': 48,               # Optimale Sequenzlänge
    'future_steps': 12,            # Kürzere Prognose für höhere Genauigkeit
    'batch_size': 64,              # Aus btc_ultimate_prediction_model.py
    'epochs': 100,                 # Ausreichend für Konvergenz
    'patience': 20,                # Früh stoppen
    'n_features_select': 50,       # Feature Selection
    'ensemble_models': 5,          # Anzahl Ensemble-Modelle
    'monte_carlo_sims': 1000,      # Für Unsicherheitsschätzung
    'target_accuracy': 0.95        # Ziel-Genauigkeit
}

# MAXIMALE CPU/GPU OPTIMIERUNG
print("⚙️  Optimiere Hardware...")
tf.config.threading.set_intra_op_parallelism_threads(0)  # Alle CPU-Kerne
tf.config.threading.set_inter_op_parallelism_threads(0)
os.environ['OMP_NUM_THREADS'] = str(os.cpu_count())

# GPU-Optimierung
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"🎮 GPU aktiviert: {len(gpus)} GPU(s)")
    except RuntimeError as e:
        print(f"GPU-Konfiguration: {e}")

print(f"💻 CPU-Kerne: {os.cpu_count()}")

class UltimateOptimizedPredictor:
    """Ultimate Optimized Bitcoin Predictor - Best of All Scripts"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_selector = None
        self.selected_features = None
        self.performance_history = []
    
    def load_and_create_ultimate_features(self):
        """Ultimate Feature Engineering - Kombiniert alle besten Features"""
        print("\n📊 ULTIMATE FEATURE ENGINEERING")
        print("-" * 50)
        
        df = pd.read_csv(CONFIG['data_file'])
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        print(f"📈 Daten: {len(df)} Punkte von {df.index[0]} bis {df.index[-1]}")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
        
        # Basis OHLCV
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # === 1. PREIS-BASIERTE FEATURES (Aus allen Scripts) ===
        print("   🔧 Erstelle Preis-Features...")
        
        # Returns und Ratios
        features['returns'] = df['close'].pct_change()
        features['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        features['hl_ratio'] = df['high'] / df['low']
        features['co_ratio'] = df['close'] / df['open']
        features['hc_ratio'] = df['high'] / df['close']
        features['lc_ratio'] = df['low'] / df['close']
        
        # Candlestick Features
        features['body'] = abs(df['close'] - df['open'])
        features['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
        features['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
        features['body_ratio'] = features['body'] / (df['high'] - df['low'])
        features['shadow_ratio'] = (features['upper_shadow'] + features['lower_shadow']) / features['body']
        
        # === 2. MOVING AVERAGES SYSTEM (Optimiert) ===
        print("   📈 Erstelle Moving Average System...")
        
        # Fibonacci-basierte Perioden für optimale Performance
        ma_periods = [5, 8, 13, 21, 34, 55, 89, 144]
        for period in ma_periods:
            if period <= len(df):
                # Simple und Exponential MA
                features[f'sma_{period}'] = df['close'].rolling(period).mean()
                features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
                
                # MA Ratios (wichtig für Trend-Erkennung)
                features[f'price_sma_{period}_ratio'] = df['close'] / features[f'sma_{period}']
                features[f'price_ema_{period}_ratio'] = df['close'] / features[f'ema_{period}']
                
                # MA Crossovers (starke Signale)
                if period > 5:
                    features[f'sma_cross_{period}'] = (features['sma_5'] > features[f'sma_{period}']).astype(int)
                    features[f'ema_cross_{period}'] = (features['ema_5'] > features[f'ema_{period}']).astype(int)
                
                # MA Slopes (Trend-Stärke)
                features[f'sma_{period}_slope'] = features[f'sma_{period}'].diff()
                features[f'ema_{period}_slope'] = features[f'ema_{period}'].diff()
        
        # === 3. MOMENTUM INDIKATOREN (Erweitert) ===
        print("   ⚡ Erstelle Momentum-Indikatoren...")
        
        # RSI Familie (verschiedene Perioden)
        for period in [9, 14, 21, 25]:
            delta = df['close'].diff()
            gain = delta.where(delta > 0, 0).rolling(period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            features[f'rsi_{period}'] = rsi
            features[f'rsi_{period}_overbought'] = (rsi > 70).astype(int)
            features[f'rsi_{period}_oversold'] = (rsi < 30).astype(int)
            features[f'rsi_{period}_slope'] = rsi.diff()
            features[f'rsi_{period}_momentum'] = rsi.diff().diff()
        
        # MACD Familie (mehrere Kombinationen)
        macd_configs = [(12, 26, 9), (5, 35, 5), (19, 39, 9)]
        for fast, slow, signal in macd_configs:
            ema_fast = df['close'].ewm(span=fast).mean()
            ema_slow = df['close'].ewm(span=slow).mean()
            macd = ema_fast - ema_slow
            macd_signal = macd.ewm(span=signal).mean()
            macd_histogram = macd - macd_signal
            
            features[f'macd_{fast}_{slow}'] = macd
            features[f'macd_signal_{fast}_{slow}'] = macd_signal
            features[f'macd_histogram_{fast}_{slow}'] = macd_histogram
            features[f'macd_cross_{fast}_{slow}'] = (macd > macd_signal).astype(int)
            features[f'macd_slope_{fast}_{slow}'] = macd.diff()
        
        # Stochastic Oscillator
        for period in [14, 21]:
            low_min = df['low'].rolling(period).min()
            high_max = df['high'].rolling(period).max()
            stoch_k = 100 * ((df['close'] - low_min) / (high_max - low_min))
            stoch_d = stoch_k.rolling(3).mean()
            
            features[f'stoch_k_{period}'] = stoch_k
            features[f'stoch_d_{period}'] = stoch_d
            features[f'stoch_cross_{period}'] = (stoch_k > stoch_d).astype(int)
            features[f'stoch_overbought_{period}'] = (stoch_k > 80).astype(int)
            features[f'stoch_oversold_{period}'] = (stoch_k < 20).astype(int)
        
        # Williams %R
        for period in [14, 21]:
            high_max = df['high'].rolling(period).max()
            low_min = df['low'].rolling(period).min()
            features[f'williams_r_{period}'] = -100 * ((high_max - df['close']) / (high_max - low_min))
        
        # === 4. VOLATILITÄT INDIKATOREN (Erweitert) ===
        print("   📊 Erstelle Volatilitäts-Indikatoren...")
        
        # Bollinger Bands (mehrere Perioden)
        for period in [20, 50]:
            sma = df['close'].rolling(period).mean()
            std = df['close'].rolling(period).std()
            
            features[f'bb_upper_{period}'] = sma + (std * 2)
            features[f'bb_lower_{period}'] = sma - (std * 2)
            features[f'bb_middle_{period}'] = sma
            features[f'bb_width_{period}'] = (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}']) / sma
            features[f'bb_position_{period}'] = (df['close'] - features[f'bb_lower_{period}']) / (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}'])
            features[f'bb_squeeze_{period}'] = (features[f'bb_width_{period}'] < features[f'bb_width_{period}'].rolling(20).mean()).astype(int)
            
            # BB Breakouts
            features[f'bb_breakout_upper_{period}'] = (df['close'] > features[f'bb_upper_{period}']).astype(int)
            features[f'bb_breakout_lower_{period}'] = (df['close'] < features[f'bb_lower_{period}']).astype(int)
        
        # ATR (Average True Range)
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        
        for period in [7, 14, 21]:
            atr = true_range.rolling(period).mean()
            features[f'atr_{period}'] = atr
            features[f'atr_percent_{period}'] = atr / df['close'] * 100
            features[f'atr_ratio_{period}'] = atr / atr.rolling(50).mean()
        
        # Realized Volatility
        for period in [5, 10, 20]:
            features[f'realized_vol_{period}'] = features['log_returns'].rolling(period).std() * np.sqrt(24)
            features[f'vol_rank_{period}'] = features[f'realized_vol_{period}'].rolling(100).rank(pct=True)
        
        # === 5. VOLUMEN INDIKATOREN (Erweitert) ===
        print("   📦 Erstelle Volumen-Indikatoren...")
        
        # OBV (On-Balance Volume)
        obv = [0]
        for i in range(1, len(df)):
            if df['close'].iloc[i] > df['close'].iloc[i-1]:
                obv.append(obv[-1] + df['volume'].iloc[i])
            elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                obv.append(obv[-1] - df['volume'].iloc[i])
            else:
                obv.append(obv[-1])
        
        features['obv'] = obv
        features['obv_ema'] = features['obv'].ewm(span=20).mean()
        features['obv_slope'] = features['obv'].diff()
        features['obv_trend'] = (features['obv'] > features['obv_ema']).astype(int)
        
        # Volume Profile
        for period in [10, 20, 50]:
            vol_sma = df['volume'].rolling(period).mean()
            features[f'volume_sma_{period}'] = vol_sma
            features[f'volume_ratio_{period}'] = df['volume'] / vol_sma
            features[f'volume_spike_{period}'] = (features[f'volume_ratio_{period}'] > 2).astype(int)
            features[f'volume_trend_{period}'] = (df['volume'] > vol_sma).astype(int)
        
        # VWAP (Volume Weighted Average Price)
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        features['vwap'] = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        features['vwap_ratio'] = df['close'] / features['vwap']
        features['vwap_distance'] = (df['close'] - features['vwap']) / features['vwap'] * 100
        
        # === 6. ZEIT-BASIERTE FEATURES (Optimiert) ===
        print("   🕐 Erstelle Zeit-Features...")
        
        # Basis Zeit-Features
        features['hour'] = df.index.hour
        features['day_of_week'] = df.index.dayofweek
        features['day_of_month'] = df.index.day
        features['month'] = df.index.month
        features['quarter'] = df.index.quarter
        
        # Trigonometrische Transformationen (wichtig für Zyklizität)
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
        features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
        features['month_sin'] = np.sin(2 * np.pi * features['month'] / 12)
        features['month_cos'] = np.cos(2 * np.pi * features['month'] / 12)
        
        # Markt-Sessions (wichtig für Bitcoin)
        features['asian_session'] = ((features['hour'] >= 0) & (features['hour'] < 8)).astype(int)
        features['european_session'] = ((features['hour'] >= 8) & (features['hour'] < 16)).astype(int)
        features['american_session'] = ((features['hour'] >= 16) & (features['hour'] < 24)).astype(int)
        features['weekend'] = (features['day_of_week'] >= 5).astype(int)
        
        # === 7. STATISTISCHE FEATURES (Erweitert) ===
        print("   📊 Erstelle statistische Features...")
        
        # Rolling Statistics
        for period in [5, 10, 20, 50]:
            # Skewness und Kurtosis
            features[f'skew_{period}'] = features['returns'].rolling(period).skew()
            features[f'kurt_{period}'] = features['returns'].rolling(period).kurt()
            
            # Quantile
            features[f'q25_{period}'] = df['close'].rolling(period).quantile(0.25)
            features[f'q75_{period}'] = df['close'].rolling(period).quantile(0.75)
            features[f'iqr_{period}'] = features[f'q75_{period}'] - features[f'q25_{period}']
            
            # Z-Score
            features[f'zscore_{period}'] = (df['close'] - df['close'].rolling(period).mean()) / df['close'].rolling(period).std()
            
            # Percentile Rank
            features[f'percentile_rank_{period}'] = df['close'].rolling(period).rank(pct=True)
        
        # === 8. TREND INDIKATOREN ===
        print("   📈 Erstelle Trend-Indikatoren...")
        
        # Trend Strength
        for period in [20, 50, 100]:
            if f'sma_{period}' in features.columns:
                features[f'trend_strength_{period}'] = (df['close'] - features[f'sma_{period}']) / features[f'sma_{period}'] * 100
                features[f'uptrend_{period}'] = (df['close'] > features[f'sma_{period}']).astype(int)
                features[f'trend_acceleration_{period}'] = features[f'trend_strength_{period}'].diff()
        
        # === 9. CLEANUP UND FEATURE SELECTION ===
        print("   🧹 Bereinige und selektiere Features...")
        
        # Entferne NaN-Werte
        features = features.dropna()
        
        print(f"   ✅ {len(features.columns)} Ultimate Features erstellt")
        print(f"   📊 {len(features)} saubere Datenpunkte")
        
        return features

    def intelligent_feature_selection(self, features):
        """Intelligente Feature-Selektion für optimale Performance"""
        print("\n🧠 INTELLIGENTE FEATURE-SELEKTION")
        print("-" * 40)

        X = features.drop('close', axis=1)
        y = features['close']

        print(f"📊 Vor Selektion: {X.shape[1]} Features")

        # SelectKBest mit f_regression
        self.feature_selector = SelectKBest(score_func=f_regression, k=CONFIG['n_features_select'])
        X_selected = self.feature_selector.fit_transform(X, y)

        # Hole die Namen der selektierten Features
        selected_mask = self.feature_selector.get_support()
        self.selected_features = X.columns[selected_mask].tolist()

        print(f"✅ Nach Selektion: {len(self.selected_features)} Features")
        print(f"📈 Feature-Reduktion: {(1 - len(self.selected_features)/X.shape[1])*100:.1f}%")

        # Zeige Top 10 Features
        feature_scores = self.feature_selector.scores_[selected_mask]
        top_features = sorted(zip(self.selected_features, feature_scores), key=lambda x: x[1], reverse=True)

        print(f"\n🏆 TOP 10 FEATURES:")
        for i, (feature, score) in enumerate(top_features[:10]):
            print(f"   {i+1}. {feature}: {score:.2f}")

        # Erstelle DataFrame mit selektierten Features
        selected_features_df = pd.DataFrame(X_selected, columns=self.selected_features, index=features.index)
        selected_features_df['close'] = features['close']

        return selected_features_df

    def create_sequences(self, data, target, look_back):
        """Optimierte Sequenz-Erstellung"""
        X, y = [], []
        for i in range(look_back, len(data)):
            X.append(data[i-look_back:i])
            y.append(target[i])
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)

    def prepare_data(self, features):
        """Optimierte Datenaufbereitung"""
        print("\n🔄 OPTIMIERTE DATENAUFBEREITUNG")
        print("-" * 40)

        # Feature Selection
        features_selected = self.intelligent_feature_selection(features)

        # Features und Target trennen
        X = features_selected.drop('close', axis=1)
        y = features_selected['close'].values

        print(f"📊 Finale Features: {X.shape[1]}")
        print(f"📊 Samples: {len(y)}")

        # Optimierte Skalierung (RobustScaler für Outlier-Resistenz)
        print("   🔧 Skaliere mit RobustScaler...")
        feature_scaler = RobustScaler()
        target_scaler = StandardScaler()

        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()

        # Sequenzen erstellen
        print(f"   📦 Erstelle Sequenzen (Länge: {CONFIG['look_back']})...")
        X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, CONFIG['look_back'])

        print(f"   ✅ {len(X_seq)} Sequenzen erstellt")
        print(f"   📐 Sequenz-Shape: {X_seq.shape}")

        # Speichere Scaler
        self.scalers['feature'] = feature_scaler
        self.scalers['target'] = target_scaler

        return X_seq, y_seq, self.selected_features

    def split_data(self, X, y):
        """Optimierte Zeitreihen-Aufteilung"""
        print("\n✂️  OPTIMIERTE DATENAUFTEILUNG")
        print("-" * 35)

        total_size = len(X)
        train_size = int(total_size * CONFIG['train_split'])
        val_size = int(total_size * CONFIG['validation_split'])
        test_size = total_size - train_size - val_size

        X_train = X[:train_size]
        y_train = y[:train_size]
        X_val = X[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        X_test = X[train_size+val_size:]
        y_test = y[train_size+val_size:]

        print(f"📊 Training: {len(X_train)} ({len(X_train)/total_size*100:.1f}%)")
        print(f"📊 Validation: {len(X_val)} ({len(X_val)/total_size*100:.1f}%)")
        print(f"📊 Test: {len(X_test)} ({len(X_test)/total_size*100:.1f}%)")

        return (X_train, y_train), (X_val, y_val), (X_test, y_test)

    def build_transformer_attention_model(self, input_shape):
        """Transformer-basiertes Modell mit Multi-Head Attention"""
        print("🤖 Baue Transformer-Attention-Modell...")

        inputs = Input(shape=input_shape, name='sequence_input')

        # Multi-Head Attention Layers
        attention_1 = MultiHeadAttention(
            num_heads=8,
            key_dim=64,
            dropout=0.1,
            name='multi_head_attention_1'
        )(inputs, inputs)
        attention_1 = LayerNormalization()(attention_1)
        attention_1 = Add()([inputs, attention_1])  # Residual connection

        attention_2 = MultiHeadAttention(
            num_heads=4,
            key_dim=32,
            dropout=0.1,
            name='multi_head_attention_2'
        )(attention_1, attention_1)
        attention_2 = LayerNormalization()(attention_2)
        attention_2 = Add()([attention_1, attention_2])  # Residual connection

        # Global Average Pooling
        pooled = tf.keras.layers.GlobalAveragePooling1D()(attention_2)

        # Dense Layers mit Regularization
        dense_1 = Dense(256, activation='relu', name='dense_1')(pooled)
        dense_1 = BatchNormalization()(dense_1)
        dense_1 = Dropout(0.3)(dense_1)

        dense_2 = Dense(128, activation='relu', name='dense_2')(dense_1)
        dense_2 = BatchNormalization()(dense_2)
        dense_2 = Dropout(0.2)(dense_2)

        dense_3 = Dense(64, activation='relu', name='dense_3')(dense_2)
        dense_3 = Dropout(0.1)(dense_3)

        outputs = Dense(1, name='price_output')(dense_3)

        model = Model(inputs=inputs, outputs=outputs, name='TransformerAttentionPredictor')

        # Optimierter Optimizer
        optimizer = Adam(learning_rate=0.001, beta_1=0.9, beta_2=0.999, epsilon=1e-7)
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae', 'mse'])

        print(f"   ✅ Transformer: {model.count_params():,} Parameter")
        return model

    def build_bidirectional_lstm_model(self, input_shape):
        """Optimiertes Bidirectional LSTM Modell"""
        print("🧠 Baue Bidirectional LSTM Modell...")

        model = Sequential(name='BidirectionalLSTMPredictor')

        # Bidirectional LSTM Layers
        model.add(Bidirectional(
            LSTM(128, return_sequences=True, dropout=0.2, recurrent_dropout=0.2),
            input_shape=input_shape,
            name='bidirectional_lstm_1'
        ))
        model.add(BatchNormalization())

        model.add(Bidirectional(
            LSTM(64, return_sequences=True, dropout=0.2, recurrent_dropout=0.2),
            name='bidirectional_lstm_2'
        ))
        model.add(BatchNormalization())

        model.add(Bidirectional(
            LSTM(32, return_sequences=False, dropout=0.2, recurrent_dropout=0.2),
            name='bidirectional_lstm_3'
        ))
        model.add(BatchNormalization())

        # Dense Layers
        model.add(Dense(128, activation='relu', name='dense_1'))
        model.add(BatchNormalization())
        model.add(Dropout(0.3))

        model.add(Dense(64, activation='relu', name='dense_2'))
        model.add(Dropout(0.2))

        model.add(Dense(1, name='price_output'))

        optimizer = Adam(learning_rate=0.0005)
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae', 'mse'])

        print(f"   ✅ Bidirectional LSTM: {model.count_params():,} Parameter")
        return model

    def build_gru_attention_model(self, input_shape):
        """GRU mit Attention Mechanism"""
        print("⚡ Baue GRU-Attention Modell...")

        inputs = Input(shape=input_shape, name='sequence_input')

        # GRU Layers
        gru_1 = Bidirectional(GRU(64, return_sequences=True, dropout=0.2), name='bidirectional_gru_1')(inputs)
        gru_1 = BatchNormalization()(gru_1)

        gru_2 = Bidirectional(GRU(32, return_sequences=True, dropout=0.2), name='bidirectional_gru_2')(gru_1)
        gru_2 = BatchNormalization()(gru_2)

        # Attention Layer
        attention = MultiHeadAttention(num_heads=4, key_dim=32, dropout=0.1, name='gru_attention')(gru_2, gru_2)
        attention = LayerNormalization()(attention)

        # Combine GRU and Attention
        combined = Add()([gru_2, attention])
        pooled = tf.keras.layers.GlobalAveragePooling1D()(combined)

        # Dense layers
        dense_1 = Dense(128, activation='relu', name='dense_1')(pooled)
        dense_1 = BatchNormalization()(dense_1)
        dense_1 = Dropout(0.3)(dense_1)

        dense_2 = Dense(64, activation='relu', name='dense_2')(dense_1)
        dense_2 = Dropout(0.2)(dense_2)

        outputs = Dense(1, name='price_output')(dense_2)

        model = Model(inputs=inputs, outputs=outputs, name='GRUAttentionPredictor')

        optimizer = RMSprop(learning_rate=0.001)
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae', 'mse'])

        print(f"   ✅ GRU-Attention: {model.count_params():,} Parameter")
        return model

    def build_hybrid_ensemble_model(self, input_shape):
        """Hybrid-Modell mit mehreren Architekturen"""
        print("🔥 Baue Hybrid-Ensemble Modell...")

        inputs = Input(shape=input_shape, name='sequence_input')

        # LSTM Branch
        lstm_branch = LSTM(64, return_sequences=True, dropout=0.2, name='lstm_branch')(inputs)
        lstm_branch = BatchNormalization()(lstm_branch)
        lstm_branch = LSTM(32, return_sequences=False, dropout=0.2)(lstm_branch)
        lstm_branch = Dense(32, activation='relu')(lstm_branch)

        # GRU Branch
        gru_branch = GRU(64, return_sequences=True, dropout=0.2, name='gru_branch')(inputs)
        gru_branch = BatchNormalization()(gru_branch)
        gru_branch = GRU(32, return_sequences=False, dropout=0.2)(gru_branch)
        gru_branch = Dense(32, activation='relu')(gru_branch)

        # Attention Branch
        attention_branch = MultiHeadAttention(
            num_heads=4, key_dim=32, dropout=0.1, name='attention_branch'
        )(inputs, inputs)
        attention_branch = LayerNormalization()(attention_branch)
        attention_branch = tf.keras.layers.GlobalAveragePooling1D()(attention_branch)
        attention_branch = Dense(32, activation='relu')(attention_branch)

        # Combine all branches
        combined = Concatenate(name='combine_branches')([lstm_branch, gru_branch, attention_branch])

        # Final dense layers
        dense_1 = Dense(128, activation='relu', name='final_dense_1')(combined)
        dense_1 = BatchNormalization()(dense_1)
        dense_1 = Dropout(0.3)(dense_1)

        dense_2 = Dense(64, activation='relu', name='final_dense_2')(dense_1)
        dense_2 = Dropout(0.2)(dense_2)

        outputs = Dense(1, name='price_output')(dense_2)

        model = Model(inputs=inputs, outputs=outputs, name='HybridEnsemblePredictor')

        optimizer = Adam(learning_rate=0.0008)
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae', 'mse'])

        print(f"   ✅ Hybrid-Ensemble: {model.count_params():,} Parameter")
        return model

    def build_deep_residual_model(self, input_shape):
        """Deep Residual Network für Zeitreihen"""
        print("🏗️  Baue Deep Residual Modell...")

        inputs = Input(shape=input_shape, name='sequence_input')

        # Erste LSTM Schicht
        x = LSTM(128, return_sequences=True, dropout=0.2, name='lstm_1')(inputs)
        x = BatchNormalization()(x)

        # Residual Blocks
        for i in range(3):
            # LSTM Block
            lstm_out = LSTM(64, return_sequences=True, dropout=0.2, name=f'residual_lstm_{i}')(x)
            lstm_out = BatchNormalization()(lstm_out)

            # Residual Connection (wenn Dimensionen passen)
            if x.shape[-1] == lstm_out.shape[-1]:
                x = Add(name=f'residual_add_{i}')([x, lstm_out])
            else:
                x = lstm_out

            x = tf.keras.layers.Activation('relu')(x)

        # Final LSTM
        x = LSTM(32, return_sequences=False, dropout=0.2, name='final_lstm')(x)
        x = BatchNormalization()(x)

        # Dense Layers
        x = Dense(128, activation='relu', name='dense_1')(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)

        x = Dense(64, activation='relu', name='dense_2')(x)
        x = Dropout(0.2)(x)

        outputs = Dense(1, name='price_output')(x)

        model = Model(inputs=inputs, outputs=outputs, name='DeepResidualPredictor')

        optimizer = Adam(learning_rate=0.0005)
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae', 'mse'])

        print(f"   ✅ Deep Residual: {model.count_params():,} Parameter")
        return model

    def train_model_optimized(self, model, X_train, y_train, X_val, y_val, model_name):
        """Optimiertes Modell-Training mit besten Callbacks"""
        print(f"\n🎯 Trainiere {model_name}...")

        # Erstelle models Verzeichnis
        os.makedirs('models', exist_ok=True)

        # Optimierte Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=CONFIG['patience'],
                restore_best_weights=True,
                verbose=1,
                mode='min'
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=CONFIG['patience']//2,
                min_lr=1e-7,
                verbose=1,
                mode='min'
            ),
            ModelCheckpoint(
                filepath=f'models/best_{model_name.lower()}.h5',
                monitor='val_loss',
                save_best_only=True,
                verbose=1,
                mode='min'
            )
        ]

        start_time = time.time()

        # Training mit optimierten Parametern
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            callbacks=callbacks,
            verbose=1
        )

        training_time = time.time() - start_time

        print(f"   ✅ {model_name} Training: {training_time:.1f}s")

        return model, history, training_time

    def evaluate_model_comprehensive(self, model, X_test, y_test, model_name):
        """Umfassende Modell-Evaluation"""
        print(f"\n📊 Evaluiere {model_name}...")

        # Vorhersagen
        y_pred = model.predict(X_test, verbose=0)

        # Skalierung rückgängig machen
        y_test_orig = self.scalers['target'].inverse_transform(y_test.reshape(-1, 1)).flatten()
        y_pred_orig = self.scalers['target'].inverse_transform(y_pred).flatten()

        # Umfassende Metriken
        r2 = r2_score(y_test_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
        mae = mean_absolute_error(y_test_orig, y_pred_orig)
        mape = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig)) * 100

        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(y_pred_orig) > 0
            direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            direction_acc = 0

        # Zusätzliche Metriken
        max_error = np.max(np.abs(y_test_orig - y_pred_orig))
        median_error = np.median(np.abs(y_test_orig - y_pred_orig))

        # Accuracy Bands
        accuracy_1pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.01) * 100
        accuracy_5pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.05) * 100
        accuracy_10pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.10) * 100

        # Sharpe-ähnliche Metrik
        returns_pred = np.diff(y_pred_orig) / y_pred_orig[:-1]
        returns_actual = np.diff(y_test_orig) / y_test_orig[:-1]
        prediction_sharpe = np.mean(returns_pred) / np.std(returns_pred) if np.std(returns_pred) > 0 else 0

        results = {
            'model_name': model_name,
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'mape': mape,
            'direction_accuracy': direction_acc,
            'max_error': max_error,
            'median_error': median_error,
            'accuracy_1pct': accuracy_1pct,
            'accuracy_5pct': accuracy_5pct,
            'accuracy_10pct': accuracy_10pct,
            'prediction_sharpe': prediction_sharpe,
            'y_test_orig': y_test_orig,
            'y_pred_orig': y_pred_orig
        }

        print(f"   📈 R²: {r2:.4f} ({r2*100:.1f}%)")
        print(f"   💰 RMSE: ${rmse:.2f}")
        print(f"   📊 MAPE: {mape:.2f}%")
        print(f"   🎯 Direction Acc: {direction_acc:.1f}%")
        print(f"   ✅ 5% Accuracy: {accuracy_5pct:.1f}%")
        print(f"   📈 Pred. Sharpe: {prediction_sharpe:.3f}")

        return results

    def create_ultimate_ensemble(self, models_results):
        """Ultimate Ensemble mit intelligenter Gewichtung"""
        print(f"\n🏆 ULTIMATE ENSEMBLE aus {len(models_results)} Modellen")
        print("-" * 50)

        # Sammle Vorhersagen und berechne intelligente Gewichte
        predictions = []
        weights = []
        model_names = []

        for result in models_results:
            predictions.append(result['y_pred_orig'])

            # Intelligente Gewichtung: R² (50%) + Direction Acc (30%) + Low MAPE (20%)
            r2_weight = result['r2']
            direction_weight = result['direction_accuracy'] / 100
            mape_weight = max(0, 1 - result['mape'] / 100)  # Invertiert: niedrigere MAPE = höheres Gewicht

            combined_weight = (r2_weight * 0.5 + direction_weight * 0.3 + mape_weight * 0.2)
            weight = max(0.05, combined_weight)  # Mindestgewicht 5%

            weights.append(weight)
            model_names.append(result['model_name'])

            print(f"   {result['model_name']}: R²={result['r2']:.4f}, Dir={result['direction_accuracy']:.1f}%, Gewicht={weight:.3f}")

        # Normalisiere Gewichte
        weights = np.array(weights)
        weights = weights / np.sum(weights)

        # Gewichtete Ensemble-Vorhersage
        ensemble_pred = np.average(predictions, axis=0, weights=weights)

        # Ensemble-Evaluation
        y_test_orig = models_results[0]['y_test_orig']  # Alle haben die gleichen Test-Daten

        ensemble_r2 = r2_score(y_test_orig, ensemble_pred)
        ensemble_rmse = np.sqrt(mean_squared_error(y_test_orig, ensemble_pred))
        ensemble_mae = mean_absolute_error(y_test_orig, ensemble_pred)
        ensemble_mape = np.mean(np.abs((y_test_orig - ensemble_pred) / y_test_orig)) * 100

        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(ensemble_pred) > 0
            ensemble_direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            ensemble_direction_acc = 0

        # Accuracy Bands
        ensemble_accuracy_5pct = np.mean(np.abs((y_test_orig - ensemble_pred) / y_test_orig) < 0.05) * 100

        ensemble_result = {
            'model_name': 'Ultimate_Ensemble',
            'r2': ensemble_r2,
            'rmse': ensemble_rmse,
            'mae': ensemble_mae,
            'mape': ensemble_mape,
            'direction_accuracy': ensemble_direction_acc,
            'accuracy_5pct': ensemble_accuracy_5pct,
            'weights': weights,
            'model_names': model_names,
            'y_test_orig': y_test_orig,
            'y_pred_orig': ensemble_pred
        }

        print(f"\n🎯 ENSEMBLE PERFORMANCE:")
        print(f"   📈 R²: {ensemble_r2:.4f} ({ensemble_r2*100:.1f}%)")
        print(f"   💰 RMSE: ${ensemble_rmse:.2f}")
        print(f"   📊 MAPE: {ensemble_mape:.2f}%")
        print(f"   🎯 Direction Acc: {ensemble_direction_acc:.1f}%")
        print(f"   ✅ 5% Accuracy: {ensemble_accuracy_5pct:.1f}%")

        return ensemble_result

    def plot_ultimate_results(self, models_results, ensemble_result):
        """Ultimate Visualisierung aller Ergebnisse"""
        print(f"\n📈 ULTIMATE VISUALISIERUNG")
        print("-" * 30)

        # Erstelle große Figure
        fig = plt.figure(figsize=(24, 18))
        fig.suptitle('🚀 ULTIMATE OPTIMIZED BITCOIN PREDICTION - RESULTS', fontsize=20, fontweight='bold')

        # 1. Performance Vergleich
        ax1 = plt.subplot(3, 4, 1)
        all_results = models_results + [ensemble_result]
        names = [r['model_name'] for r in all_results]
        r2_scores = [r['r2'] for r in all_results]

        colors = plt.cm.viridis(np.linspace(0, 1, len(names)))
        colors[-1] = 'gold'  # Ensemble in Gold

        bars = ax1.bar(names, r2_scores, color=colors)
        ax1.set_title('R² Score Vergleich', fontsize=14, fontweight='bold')
        ax1.set_ylabel('R² Score')
        ax1.set_ylim(0, 1)
        plt.xticks(rotation=45)
        ax1.grid(True, alpha=0.3)

        # Ziel-Linie
        ax1.axhline(y=CONFIG['target_accuracy'], color='red', linestyle='--', linewidth=2, label=f'Ziel: {CONFIG["target_accuracy"]*100:.0f}%')
        ax1.legend()

        # Beste markieren
        best_idx = np.argmax(r2_scores)
        bars[best_idx].set_edgecolor('red')
        bars[best_idx].set_linewidth(3)

        # 2. RMSE Vergleich
        ax2 = plt.subplot(3, 4, 2)
        rmse_scores = [r['rmse'] for r in all_results]
        ax2.bar(names, rmse_scores, color='lightcoral')
        ax2.set_title('RMSE Vergleich', fontsize=14, fontweight='bold')
        ax2.set_ylabel('RMSE ($)')
        plt.xticks(rotation=45)
        ax2.grid(True, alpha=0.3)

        # 3. Direction Accuracy
        ax3 = plt.subplot(3, 4, 3)
        direction_accs = [r['direction_accuracy'] for r in all_results]
        ax3.bar(names, direction_accs, color='lightgreen')
        ax3.set_title('Richtungsgenauigkeit', fontsize=14, fontweight='bold')
        ax3.set_ylabel('Genauigkeit (%)')
        ax3.set_ylim(0, 100)
        plt.xticks(rotation=45)
        ax3.grid(True, alpha=0.3)

        # 4. MAPE Vergleich
        ax4 = plt.subplot(3, 4, 4)
        mape_scores = [r['mape'] for r in all_results]
        ax4.bar(names, mape_scores, color='orange')
        ax4.set_title('MAPE Vergleich', fontsize=14, fontweight='bold')
        ax4.set_ylabel('MAPE (%)')
        plt.xticks(rotation=45)
        ax4.grid(True, alpha=0.3)

        # 5. Beste Vorhersage (Ensemble)
        ax5 = plt.subplot(3, 4, (5, 6))
        y_test = ensemble_result['y_test_orig'][:100]  # Erste 100 Punkte
        y_pred = ensemble_result['y_pred_orig'][:100]

        ax5.plot(y_test, 'g-', label='Actual', linewidth=2, alpha=0.8)
        ax5.plot(y_pred, 'r--', label='Ultimate Ensemble', linewidth=2)
        ax5.set_title('Ultimate Ensemble Vorhersage', fontsize=14, fontweight='bold')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

        # 6. Scatter Plot
        ax6 = plt.subplot(3, 4, 7)
        ax6.scatter(ensemble_result['y_test_orig'], ensemble_result['y_pred_orig'],
                   alpha=0.6, c='blue', s=20)
        min_val = min(ensemble_result['y_test_orig'].min(), ensemble_result['y_pred_orig'].min())
        max_val = max(ensemble_result['y_test_orig'].max(), ensemble_result['y_pred_orig'].max())
        ax6.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
        ax6.set_title('Scatter Plot - Ensemble', fontsize=14, fontweight='bold')
        ax6.set_xlabel('Actual')
        ax6.set_ylabel('Predicted')
        ax6.grid(True, alpha=0.3)

        # 7. Error Distribution
        ax7 = plt.subplot(3, 4, 8)
        residuals = ensemble_result['y_test_orig'] - ensemble_result['y_pred_orig']
        ax7.hist(residuals, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax7.set_title('Error Distribution', fontsize=14, fontweight='bold')
        ax7.set_xlabel('Prediction Error')
        ax7.set_ylabel('Frequency')
        ax7.grid(True, alpha=0.3)

        # 8. Ensemble Weights
        ax8 = plt.subplot(3, 4, 9)
        model_names = ensemble_result['model_names']
        weights = ensemble_result['weights']
        ax8.pie(weights, labels=model_names, autopct='%1.1f%%', startangle=90)
        ax8.set_title('Ensemble Gewichte', fontsize=14, fontweight='bold')

        # 9. Accuracy Comparison
        ax9 = plt.subplot(3, 4, 10)
        accuracy_5pct = [r['accuracy_5pct'] for r in all_results]
        ax9.bar(names, accuracy_5pct, color='purple')
        ax9.set_title('5% Accuracy Band', fontsize=14, fontweight='bold')
        ax9.set_ylabel('Accuracy (%)')
        ax9.set_ylim(0, 100)
        plt.xticks(rotation=45)
        ax9.grid(True, alpha=0.3)

        # 10. Performance Radar Chart
        ax10 = plt.subplot(3, 4, 11, projection='polar')

        # Normalisiere Metriken für Radar Chart
        metrics = ['R²', 'Direction Acc', '5% Accuracy', 'Low MAPE', 'Low RMSE']
        ensemble_values = [
            ensemble_result['r2'],
            ensemble_result['direction_accuracy'] / 100,
            ensemble_result['accuracy_5pct'] / 100,
            max(0, 1 - ensemble_result['mape'] / 100),  # Invertiert
            max(0, 1 - min(ensemble_result['rmse'] / 10000, 1))  # Normalisiert und invertiert
        ]

        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        ensemble_values += ensemble_values[:1]  # Schließe den Kreis
        angles += angles[:1]

        ax10.plot(angles, ensemble_values, 'o-', linewidth=2, label='Ultimate Ensemble')
        ax10.fill(angles, ensemble_values, alpha=0.25)
        ax10.set_xticks(angles[:-1])
        ax10.set_xticklabels(metrics)
        ax10.set_ylim(0, 1)
        ax10.set_title('Performance Radar', fontsize=14, fontweight='bold')
        ax10.legend()

        # 11. Summary Statistics
        ax11 = plt.subplot(3, 4, 12)
        ax11.axis('off')

        best_model = max(all_results, key=lambda x: x['r2'])
        target_reached = "✅ JA" if ensemble_result['r2'] >= CONFIG['target_accuracy'] else "❌ NEIN"

        summary_text = f"""
🏆 ULTIMATE OPTIMIZED RESULTS

🎯 ZIEL-ERREICHUNG:
   Target: {CONFIG['target_accuracy']*100:.0f}% R²
   Erreicht: {target_reached}
   Ensemble: {ensemble_result['r2']*100:.1f}%

🚀 BESTE PERFORMANCE:
   Model: {best_model['model_name']}
   R²: {best_model['r2']:.4f} ({best_model['r2']*100:.1f}%)
   Direction: {best_model['direction_accuracy']:.1f}%

📊 SYSTEM STATS:
   Features: {len(self.selected_features)}
   Modelle: {len(models_results)}
   Ensemble Gewichte: Intelligent
   Hardware: {os.cpu_count()} CPU + {'GPU' if gpus else 'CPU'}
        """

        ax11.text(0.1, 0.5, summary_text, fontsize=12, verticalalignment='center',
                 bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()
        plt.show()

        print("   ✅ Visualisierung abgeschlossen")

def main():
    """ULTIMATE OPTIMIZED HAUPTFUNKTION"""
    print("🚀 STARTE ULTIMATE OPTIMIZED BITCOIN PREDICTION SYSTEM")
    print("=" * 70)

    start_time = time.time()

    # System initialisieren
    predictor = UltimateOptimizedPredictor()

    # === DATENAUFBEREITUNG ===
    print(f"\n📊 DATENAUFBEREITUNG")
    print("=" * 30)

    # Features erstellen
    features = predictor.load_and_create_ultimate_features()

    # Daten vorbereiten (mit intelligenter Feature-Selektion)
    X, y, selected_features = predictor.prepare_data(features)

    # Daten aufteilen
    (X_train, y_train), (X_val, y_val), (X_test, y_test) = predictor.split_data(X, y)

    print(f"\n✅ DATENAUFBEREITUNG ABGESCHLOSSEN!")
    print(f"   📊 {len(selected_features)} selektierte Features")
    print(f"   📦 {len(X)} Sequenzen")

    # === MODELL-TRAINING ===
    print(f"\n🤖 ULTIMATE MODELL-TRAINING")
    print("=" * 40)

    input_shape = (X_train.shape[1], X_train.shape[2])
    models_results = []

    # 1. Transformer-Attention Model
    try:
        print(f"\n🔥 Modell 1/5: Transformer-Attention")
        transformer_model = predictor.build_transformer_attention_model(input_shape)
        transformer_model, _, _ = predictor.train_model_optimized(
            transformer_model, X_train, y_train, X_val, y_val, 'Transformer_Attention'
        )
        transformer_results = predictor.evaluate_model_comprehensive(
            transformer_model, X_test, y_test, 'Transformer_Attention'
        )
        models_results.append(transformer_results)
        predictor.models['transformer_attention'] = transformer_model
    except Exception as e:
        print(f"❌ Transformer-Attention Fehler: {e}")

    # 2. Bidirectional LSTM Model
    try:
        print(f"\n🔥 Modell 2/5: Bidirectional LSTM")
        lstm_model = predictor.build_bidirectional_lstm_model(input_shape)
        lstm_model, _, _ = predictor.train_model_optimized(
            lstm_model, X_train, y_train, X_val, y_val, 'Bidirectional_LSTM'
        )
        lstm_results = predictor.evaluate_model_comprehensive(
            lstm_model, X_test, y_test, 'Bidirectional_LSTM'
        )
        models_results.append(lstm_results)
        predictor.models['bidirectional_lstm'] = lstm_model
    except Exception as e:
        print(f"❌ Bidirectional LSTM Fehler: {e}")

    # 3. GRU-Attention Model
    try:
        print(f"\n🔥 Modell 3/5: GRU-Attention")
        gru_model = predictor.build_gru_attention_model(input_shape)
        gru_model, _, _ = predictor.train_model_optimized(
            gru_model, X_train, y_train, X_val, y_val, 'GRU_Attention'
        )
        gru_results = predictor.evaluate_model_comprehensive(
            gru_model, X_test, y_test, 'GRU_Attention'
        )
        models_results.append(gru_results)
        predictor.models['gru_attention'] = gru_model
    except Exception as e:
        print(f"❌ GRU-Attention Fehler: {e}")

    # 4. Hybrid-Ensemble Model
    try:
        print(f"\n🔥 Modell 4/5: Hybrid-Ensemble")
        hybrid_model = predictor.build_hybrid_ensemble_model(input_shape)
        hybrid_model, _, _ = predictor.train_model_optimized(
            hybrid_model, X_train, y_train, X_val, y_val, 'Hybrid_Ensemble'
        )
        hybrid_results = predictor.evaluate_model_comprehensive(
            hybrid_model, X_test, y_test, 'Hybrid_Ensemble'
        )
        models_results.append(hybrid_results)
        predictor.models['hybrid_ensemble'] = hybrid_model
    except Exception as e:
        print(f"❌ Hybrid-Ensemble Fehler: {e}")

    # 5. Deep Residual Model
    try:
        print(f"\n🔥 Modell 5/5: Deep Residual")
        residual_model = predictor.build_deep_residual_model(input_shape)
        residual_model, _, _ = predictor.train_model_optimized(
            residual_model, X_train, y_train, X_val, y_val, 'Deep_Residual'
        )
        residual_results = predictor.evaluate_model_comprehensive(
            residual_model, X_test, y_test, 'Deep_Residual'
        )
        models_results.append(residual_results)
        predictor.models['deep_residual'] = residual_model
    except Exception as e:
        print(f"❌ Deep Residual Fehler: {e}")

    # === ULTIMATE ENSEMBLE ===
    if models_results:
        ensemble_result = predictor.create_ultimate_ensemble(models_results)

        # === FINALE ANALYSE ===
        print(f"\n🏆 FINALE ULTIMATE ANALYSE")
        print("=" * 50)

        total_time = time.time() - start_time

        # Sortiere Modelle nach Performance
        sorted_results = sorted(models_results, key=lambda x: x['r2'], reverse=True)

        print(f"\n📊 MODELL-RANKING:")
        for i, result in enumerate(sorted_results):
            print(f"   {i+1}. {result['model_name']}: {result['r2']*100:.1f}% R²")

        print(f"\n🎯 ULTIMATE ENSEMBLE: {ensemble_result['r2']*100:.1f}% R²")

        # Ziel-Check
        if ensemble_result['r2'] >= CONFIG['target_accuracy']:
            print(f"\n🎉🎉🎉 ZIEL ERREICHT! 🎉🎉🎉")
            print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
            improvement = (ensemble_result['r2'] - CONFIG['target_accuracy']) * 100
            print(f"🚀 Übererfüllung: +{improvement:.1f} Prozentpunkte!")
        else:
            print(f"\n💪 STARKE PERFORMANCE!")
            print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
            gap = (CONFIG['target_accuracy'] - ensemble_result['r2']) * 100
            print(f"Noch {gap:.1f} Prozentpunkte bis zum Ziel")

        # Performance-Details
        print(f"\n📈 DETAILLIERTE PERFORMANCE:")
        print(f"   R²: {ensemble_result['r2']:.4f} ({ensemble_result['r2']*100:.1f}%)")
        print(f"   RMSE: ${ensemble_result['rmse']:.2f}")
        print(f"   MAPE: {ensemble_result['mape']:.2f}%")
        print(f"   Direction Accuracy: {ensemble_result['direction_accuracy']:.1f}%")
        print(f"   5% Accuracy Band: {ensemble_result['accuracy_5pct']:.1f}%")

        # System-Stats
        print(f"\n⚡ SYSTEM-STATISTIKEN:")
        print(f"   Gesamtzeit: {total_time:.1f} Sekunden")
        print(f"   Modelle trainiert: {len(models_results)}")
        print(f"   Features selektiert: {len(selected_features)}")
        print(f"   CPU-Kerne genutzt: {os.cpu_count()}")
        print(f"   GPU verfügbar: {'Ja' if gpus else 'Nein'}")

        # Beste Features anzeigen
        print(f"\n🏆 TOP 5 FEATURES:")
        for i, feature in enumerate(selected_features[:5]):
            print(f"   {i+1}. {feature}")

        # Visualisierung
        predictor.plot_ultimate_results(models_results, ensemble_result)

        # Finale Bewertung
        if ensemble_result['r2'] >= 0.95:
            print(f"\n🎉🎉🎉 EXZELLENT! ZIEL ERREICHT! 🎉🎉🎉")
            print(f"Das Ultimate Optimized System erreicht {ensemble_result['r2']*100:.1f}% Genauigkeit!")
        elif ensemble_result['r2'] >= 0.90:
            print(f"\n🔥🔥 HERVORRAGEND! 🔥🔥")
            print(f"Sehr starke Performance mit {ensemble_result['r2']*100:.1f}% Genauigkeit!")
        elif ensemble_result['r2'] >= 0.80:
            print(f"\n💪💪 SEHR GUT! 💪💪")
            print(f"Solide Performance mit {ensemble_result['r2']*100:.1f}% Genauigkeit!")
        else:
            print(f"\n✅ GUTE BASIS!")
            print(f"Gute Grundlage mit {ensemble_result['r2']*100:.1f}% - Optimierung möglich!")

        print(f"\n✅ ULTIMATE OPTIMIZED BITCOIN PREDICTION SYSTEM ABGESCHLOSSEN!")

        return ensemble_result

    else:
        print(f"\n❌ Keine Modelle erfolgreich trainiert!")
        return None

if __name__ == "__main__":
    result = main()

    if result and result['r2'] >= CONFIG['target_accuracy']:
        print(f"\n🎯 MISSION ACCOMPLISHED! 🎯")
        print(f"Ultimate Optimized System erreicht das Ziel von {CONFIG['target_accuracy']*100:.0f}%!")
    else:
        print(f"\n🔧 WEITERE OPTIMIERUNG EMPFOHLEN")
        print(f"Für noch bessere Ergebnisse können weitere Optimierungen vorgenommen werden.")
