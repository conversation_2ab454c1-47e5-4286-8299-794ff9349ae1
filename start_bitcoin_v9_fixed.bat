@echo off
title Bitcoin Trading System V9.0 - FEHLERFREIE EDITION
color 0E

echo ================================================================================
echo BITCOIN TRADING SYSTEM V9.0 - FEHLERFREIE EDITION STARTER
echo ================================================================================
echo.
echo Starte Bitcoin Trading System V9.0 FIXED...
echo.

cd /d "e:\Dev"

echo Pruefe Python Installation...
python --version
if errorlevel 1 (
    echo FEHLER: Python ist nicht installiert oder nicht im PATH!
    echo Bitte installieren Sie Python 3.8+ von https://python.org
    pause
    exit /b 1
)

echo.
echo Pruefe erweiterte Pakete...
python -c "import xgboost, scipy" 2>nul
if errorlevel 1 (
    echo Installiere erweiterte Pakete...
    pip install xgboost scipy
)

echo.
echo ================================================================================
echo STARTE BITCOIN TRADING SYSTEM V9.0 - FEHLERFREIE EDITION
echo ================================================================================
echo.

python ultimate_bitcoin_trading_system_v9_fixed.py

echo.
echo ================================================================================
echo Bitcoin Trading System V9.0 FIXED beendet.
echo ================================================================================
pause
