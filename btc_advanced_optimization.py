#!/usr/bin/env python3
"""
ADVANCED BITCOIN PREDICTION OPTIMIZATION
Systematische Optimierung mit verfügbaren Tools
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, BatchNormalization, Bidirectional
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers.legacy import Adam, RMSprop
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.model_selection import TimeSeriesSplit
import time
import warnings
warnings.filterwarnings('ignore')

print("🔥 ADVANCED BITCOIN PREDICTION OPTIMIZATION")
print("=" * 60)

# OPTIMIERTE PARAMETER-KOMBINATIONEN
OPTIMIZATION_CONFIGS = [
    {
        'name': 'Ultra_Performance',
        'look_back': 48,
        'lstm_units': 128,
        'lstm_layers': 3,
        'dense_units': 64,
        'dropout_rate': 0.2,
        'learning_rate': 0.001,
        'batch_size': 32,
        'optimizer': 'adam',
        'scaler': 'robust',
        'architecture': 'bidirectional',
        'epochs': 100
    },
    {
        'name': 'Speed_Optimized',
        'look_back': 24,
        'lstm_units': 64,
        'lstm_layers': 2,
        'dense_units': 32,
        'dropout_rate': 0.3,
        'learning_rate': 0.005,
        'batch_size': 64,
        'optimizer': 'rmsprop',
        'scaler': 'minmax',
        'architecture': 'lstm',
        'epochs': 50
    },
    {
        'name': 'Balanced_Hybrid',
        'look_back': 36,
        'lstm_units': 96,
        'lstm_layers': 2,
        'dense_units': 48,
        'dropout_rate': 0.25,
        'learning_rate': 0.002,
        'batch_size': 48,
        'optimizer': 'adam',
        'scaler': 'standard',
        'architecture': 'hybrid',
        'epochs': 75
    },
    {
        'name': 'Deep_Complex',
        'look_back': 72,
        'lstm_units': 256,
        'lstm_layers': 4,
        'dense_units': 128,
        'dropout_rate': 0.15,
        'learning_rate': 0.0005,
        'batch_size': 16,
        'optimizer': 'adam',
        'scaler': 'robust',
        'architecture': 'bidirectional',
        'epochs': 150
    },
    {
        'name': 'Lightweight_Fast',
        'look_back': 12,
        'lstm_units': 32,
        'lstm_layers': 1,
        'dense_units': 16,
        'dropout_rate': 0.4,
        'learning_rate': 0.01,
        'batch_size': 128,
        'optimizer': 'rmsprop',
        'scaler': 'minmax',
        'architecture': 'gru',
        'epochs': 30
    }
]

class AdvancedOptimizer:
    def __init__(self):
        self.results = []
        self.best_models = {}
    
    def load_and_engineer_features(self):
        """Erweiterte Feature-Engineering"""
        print("📊 Lade Daten und erstelle erweiterte Features...")
        
        df = pd.read_csv('crypto_data.csv')
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        # Basis Features
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # === TECHNISCHE INDIKATOREN ===
        
        # Moving Averages
        ma_periods = [5, 10, 14, 20, 21, 26, 50, 100]
        for period in ma_periods:
            if period <= len(df):
                features[f'sma_{period}'] = df['close'].rolling(period).mean()
                features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
                # MA Crossovers
                if period > 5:
                    features[f'sma_cross_{period}'] = (features['sma_5'] > features[f'sma_{period}']).astype(int)
        
        # MACD System
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        features['macd'] = ema_12 - ema_26
        features['macd_signal'] = features['macd'].ewm(span=9).mean()
        features['macd_histogram'] = features['macd'] - features['macd_signal']
        features['macd_cross'] = (features['macd'] > features['macd_signal']).astype(int)
        
        # RSI System
        for period in [9, 14, 21]:
            delta = df['close'].diff()
            gain = delta.where(delta > 0, 0).rolling(period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            features[f'rsi_{period}'] = rsi
            features[f'rsi_{period}_overbought'] = (rsi > 70).astype(int)
            features[f'rsi_{period}_oversold'] = (rsi < 30).astype(int)
        
        # Bollinger Bands
        for period in [20, 50]:
            sma = df['close'].rolling(period).mean()
            std = df['close'].rolling(period).std()
            features[f'bb_upper_{period}'] = sma + (std * 2)
            features[f'bb_lower_{period}'] = sma - (std * 2)
            features[f'bb_width_{period}'] = (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}']) / sma
            features[f'bb_position_{period}'] = (df['close'] - features[f'bb_lower_{period}']) / (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}'])
            features[f'bb_squeeze_{period}'] = (features[f'bb_width_{period}'] < features[f'bb_width_{period}'].rolling(20).mean()).astype(int)
        
        # Stochastic Oscillator
        for period in [14, 21]:
            low_min = df['low'].rolling(period).min()
            high_max = df['high'].rolling(period).max()
            stoch_k = 100 * ((df['close'] - low_min) / (high_max - low_min))
            features[f'stoch_k_{period}'] = stoch_k
            features[f'stoch_d_{period}'] = stoch_k.rolling(3).mean()
            features[f'stoch_overbought_{period}'] = (stoch_k > 80).astype(int)
            features[f'stoch_oversold_{period}'] = (stoch_k < 20).astype(int)
        
        # ATR (Volatilität)
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        features['atr'] = true_range.rolling(14).mean()
        features['atr_percent'] = features['atr'] / df['close'] * 100
        
        # === VOLUMEN INDIKATOREN ===
        
        # OBV (On-Balance Volume)
        obv = [0]
        for i in range(1, len(df)):
            if df['close'].iloc[i] > df['close'].iloc[i-1]:
                obv.append(obv[-1] + df['volume'].iloc[i])
            elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                obv.append(obv[-1] - df['volume'].iloc[i])
            else:
                obv.append(obv[-1])
        features['obv'] = obv
        features['obv_sma'] = features['obv'].rolling(20).mean()
        features['obv_trend'] = (features['obv'] > features['obv_sma']).astype(int)
        
        # Volume Profile
        for period in [10, 20]:
            features[f'volume_sma_{period}'] = df['volume'].rolling(period).mean()
            features[f'volume_ratio_{period}'] = df['volume'] / features[f'volume_sma_{period}']
            features[f'volume_spike_{period}'] = (features[f'volume_ratio_{period}'] > 2).astype(int)
        
        # === MOMENTUM INDIKATOREN ===
        
        # Rate of Change
        for period in [5, 10, 20]:
            features[f'roc_{period}'] = df['close'].pct_change(periods=period) * 100
            features[f'momentum_{period}'] = df['close'] - df['close'].shift(period)
        
        # === VOLATILITÄT ===
        
        for period in [5, 10, 20]:
            features[f'volatility_{period}'] = df['close'].pct_change().rolling(period).std() * 100
            features[f'volatility_rank_{period}'] = features[f'volatility_{period}'].rolling(50).rank(pct=True)
        
        # === PREIS PATTERN ===
        
        # Candlestick Patterns
        features['doji'] = (abs(df['close'] - df['open']) / (df['high'] - df['low']) < 0.1).astype(int)
        features['hammer'] = ((df['low'] < df['open']) & (df['low'] < df['close']) & 
                             ((df['open'] - df['low']) > 2 * abs(df['close'] - df['open']))).astype(int)
        features['shooting_star'] = ((df['high'] > df['open']) & (df['high'] > df['close']) & 
                                    ((df['high'] - df['open']) > 2 * abs(df['close'] - df['open']))).astype(int)
        
        # Price Ratios
        features['high_low_ratio'] = df['high'] / df['low']
        features['close_open_ratio'] = df['close'] / df['open']
        features['body_size'] = abs(df['close'] - df['open']) / df['open'] * 100
        
        # === TREND INDIKATOREN ===
        
        # Trend Strength
        for period in [20, 50]:
            if f'sma_{period}' in features.columns:
                features[f'trend_strength_{period}'] = (df['close'] - features[f'sma_{period}']) / features[f'sma_{period}'] * 100
                features[f'uptrend_{period}'] = (df['close'] > features[f'sma_{period}']).astype(int)
        
        # === ZYKLISCHE FEATURES ===
        
        # Zeit-basierte Features
        features['hour'] = df.index.hour
        features['day_of_week'] = df.index.dayofweek
        features['day_of_month'] = df.index.day
        features['month'] = df.index.month
        
        # Trigonometrische Transformationen
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
        features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
        features['month_sin'] = np.sin(2 * np.pi * features['month'] / 12)
        features['month_cos'] = np.cos(2 * np.pi * features['month'] / 12)
        
        # === MARKT MIKROSTRUKTUR ===
        
        # Spread Approximation
        features['spread_approx'] = (df['high'] - df['low']) / df['close'] * 100
        
        # Price Impact
        features['price_impact'] = abs(df['close'].pct_change()) / (df['volume'] / df['volume'].rolling(20).mean())
        
        print(f"   ✅ {len(features.columns)} erweiterte Features erstellt")
        return features.dropna()
    
    def create_sequences(self, data, target, look_back):
        """Erstelle Sequenzen für LSTM"""
        X, y = [], []
        for i in range(look_back, len(data)):
            X.append(data[i-look_back:i])
            y.append(target[i])
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)
    
    def get_scaler(self, scaler_type):
        """Hole Scaler"""
        scalers = {
            'minmax': MinMaxScaler(),
            'standard': StandardScaler(),
            'robust': RobustScaler()
        }
        return scalers.get(scaler_type, MinMaxScaler())
    
    def get_optimizer(self, optimizer_type, learning_rate):
        """Hole Optimizer"""
        if optimizer_type == 'adam':
            return Adam(learning_rate=learning_rate)
        elif optimizer_type == 'rmsprop':
            return RMSprop(learning_rate=learning_rate)
        else:
            return Adam(learning_rate=learning_rate)
    
    def build_advanced_model(self, config, input_shape):
        """Baue erweiterte Modell-Architektur"""
        model = Sequential()
        
        # Erste Schicht basierend auf Architektur
        if config['architecture'] == 'lstm':
            if config['lstm_layers'] == 1:
                model.add(LSTM(config['lstm_units'], return_sequences=False, 
                              dropout=config['dropout_rate'], input_shape=input_shape))
            else:
                model.add(LSTM(config['lstm_units'], return_sequences=True, 
                              dropout=config['dropout_rate'], input_shape=input_shape))
        
        elif config['architecture'] == 'gru':
            if config['lstm_layers'] == 1:
                model.add(GRU(config['lstm_units'], return_sequences=False, 
                             dropout=config['dropout_rate'], input_shape=input_shape))
            else:
                model.add(GRU(config['lstm_units'], return_sequences=True, 
                             dropout=config['dropout_rate'], input_shape=input_shape))
        
        elif config['architecture'] == 'bidirectional':
            if config['lstm_layers'] == 1:
                model.add(Bidirectional(LSTM(config['lstm_units'], return_sequences=False, 
                                           dropout=config['dropout_rate']), input_shape=input_shape))
            else:
                model.add(Bidirectional(LSTM(config['lstm_units'], return_sequences=True, 
                                           dropout=config['dropout_rate']), input_shape=input_shape))
        
        elif config['architecture'] == 'hybrid':
            model.add(LSTM(config['lstm_units'], return_sequences=True, 
                          dropout=config['dropout_rate'], input_shape=input_shape))
            model.add(BatchNormalization())
            model.add(GRU(config['lstm_units']//2, return_sequences=False, 
                         dropout=config['dropout_rate']))
        
        # Zusätzliche LSTM/GRU Schichten
        for i in range(1, config['lstm_layers']):
            units = max(config['lstm_units'] // (2 ** i), 16)
            return_sequences = i < config['lstm_layers'] - 1
            
            if config['architecture'] == 'lstm':
                model.add(LSTM(units, return_sequences=return_sequences, 
                              dropout=config['dropout_rate']))
            elif config['architecture'] == 'gru':
                model.add(GRU(units, return_sequences=return_sequences, 
                             dropout=config['dropout_rate']))
            elif config['architecture'] == 'bidirectional':
                model.add(Bidirectional(LSTM(units, return_sequences=return_sequences, 
                                           dropout=config['dropout_rate'])))
            
            if return_sequences:
                model.add(BatchNormalization())
        
        # Dense Schichten mit Batch Normalization
        model.add(BatchNormalization())
        model.add(Dense(config['dense_units'], activation='relu'))
        model.add(Dropout(config['dropout_rate']))
        
        # Zusätzliche Dense Schicht für komplexere Modelle
        if config['dense_units'] > 32:
            model.add(Dense(config['dense_units']//2, activation='relu'))
            model.add(Dropout(config['dropout_rate']/2))
        
        # Output
        model.add(Dense(1))
        
        # Kompiliere
        optimizer = self.get_optimizer(config['optimizer'], config['learning_rate'])
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae'])
        
        return model
    
    def cross_validate_model(self, config, features):
        """Cross-Validation für Zeitreihen"""
        print(f"\n🔄 Cross-Validation für {config['name']}...")
        
        X = features.drop('close', axis=1)
        y = features['close'].values
        
        # Skalierung
        feature_scaler = self.get_scaler(config['scaler'])
        target_scaler = self.get_scaler(config['scaler'])
        
        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        
        # Sequenzen
        X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, config['look_back'])
        
        # Time Series Split
        tscv = TimeSeriesSplit(n_splits=3)
        cv_scores = []
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X_seq)):
            print(f"   Fold {fold+1}/3...")
            
            X_train, X_val = X_seq[train_idx], X_seq[val_idx]
            y_train, y_val = y_seq[train_idx], y_seq[val_idx]
            
            # Modell erstellen
            model = self.build_advanced_model(config, (X_train.shape[1], X_train.shape[2]))
            
            # Callbacks
            callbacks = [
                EarlyStopping(patience=20, restore_best_weights=True, verbose=0),
                ReduceLROnPlateau(factor=0.5, patience=10, min_lr=1e-7, verbose=0)
            ]
            
            # Training
            model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=config['epochs'],
                batch_size=config['batch_size'],
                callbacks=callbacks,
                verbose=0
            )
            
            # Evaluation
            y_pred = model.predict(X_val, verbose=0)
            
            # Skalierung rückgängig
            y_val_orig = target_scaler.inverse_transform(y_val.reshape(-1, 1)).flatten()
            y_pred_orig = target_scaler.inverse_transform(y_pred).flatten()
            
            # Score
            r2 = r2_score(y_val_orig, y_pred_orig)
            cv_scores.append(r2)
            
            print(f"      R²: {r2:.4f}")
            
            # Memory cleanup
            tf.keras.backend.clear_session()
        
        avg_score = np.mean(cv_scores)
        std_score = np.std(cv_scores)
        
        print(f"   ✅ CV Score: {avg_score:.4f} ± {std_score:.4f}")
        
        return avg_score, std_score, cv_scores

    def train_final_model(self, config, features):
        """Trainiere finales Modell mit allen Daten"""
        print(f"\n🎯 Trainiere finales Modell: {config['name']}...")

        start_time = time.time()

        X = features.drop('close', axis=1)
        y = features['close'].values

        # Skalierung
        feature_scaler = self.get_scaler(config['scaler'])
        target_scaler = self.get_scaler(config['scaler'])

        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()

        # Sequenzen
        X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, config['look_back'])

        # Train-Test Split (80-20)
        train_size = int(len(X_seq) * 0.8)
        X_train, X_test = X_seq[:train_size], X_seq[train_size:]
        y_train, y_test = y_seq[:train_size], y_seq[train_size:]

        # Modell erstellen
        model = self.build_advanced_model(config, (X_train.shape[1], X_train.shape[2]))

        # Callbacks
        callbacks = [
            EarlyStopping(patience=25, restore_best_weights=True, verbose=0),
            ReduceLROnPlateau(factor=0.5, patience=12, min_lr=1e-7, verbose=0)
        ]

        # Training
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=config['epochs'],
            batch_size=config['batch_size'],
            callbacks=callbacks,
            verbose=1
        )

        training_time = time.time() - start_time

        # Finale Evaluation
        y_pred = model.predict(X_test, verbose=0)

        # Skalierung rückgängig
        y_test_orig = target_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        y_pred_orig = target_scaler.inverse_transform(y_pred).flatten()

        # Umfassende Metriken
        r2 = r2_score(y_test_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
        mae = mean_absolute_error(y_test_orig, y_pred_orig)
        mape = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig)) * 100

        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(y_pred_orig) > 0
            direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            direction_acc = 0

        # Zusätzliche Metriken
        max_error = np.max(np.abs(y_test_orig - y_pred_orig))
        median_error = np.median(np.abs(y_test_orig - y_pred_orig))

        # Accuracy Bands
        accuracy_5pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.05) * 100
        accuracy_10pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.10) * 100

        result = {
            'config_name': config['name'],
            'config': config,
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'mape': mape,
            'direction_accuracy': direction_acc,
            'max_error': max_error,
            'median_error': median_error,
            'accuracy_5pct': accuracy_5pct,
            'accuracy_10pct': accuracy_10pct,
            'training_time': training_time,
            'model_params': model.count_params(),
            'epochs_trained': len(history.history['loss']),
            'final_val_loss': min(history.history['val_loss']),
            'model': model,
            'feature_scaler': feature_scaler,
            'target_scaler': target_scaler,
            'y_test_orig': y_test_orig,
            'y_pred_orig': y_pred_orig
        }

        print(f"   ✅ Training abgeschlossen:")
        print(f"      R²: {r2:.4f} ({r2*100:.1f}%)")
        print(f"      RMSE: ${rmse:.2f}")
        print(f"      MAPE: {mape:.2f}%")
        print(f"      Direction Acc: {direction_acc:.1f}%")
        print(f"      Training Zeit: {training_time:.1f}s")
        print(f"      Parameter: {model.count_params():,}")

        return result

    def create_ensemble_prediction(self, features):
        """Erstelle Ensemble-Vorhersage aus allen Modellen"""
        print(f"\n🏆 Erstelle Ensemble aus {len(self.best_models)} Modellen...")

        if not self.best_models:
            print("❌ Keine Modelle für Ensemble verfügbar!")
            return None

        # Bereite Daten vor (verwende letztes Modell als Referenz)
        reference_result = list(self.best_models.values())[0]
        config = reference_result['config']

        X = features.drop('close', axis=1)
        y = features['close'].values

        feature_scaler = reference_result['feature_scaler']
        target_scaler = reference_result['target_scaler']

        X_scaled = feature_scaler.transform(X)
        y_scaled = target_scaler.transform(y.reshape(-1, 1)).flatten()

        X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, config['look_back'])

        # Test-Daten
        train_size = int(len(X_seq) * 0.8)
        X_test = X_seq[train_size:]
        y_test = y_seq[train_size:]

        # Sammle Vorhersagen von allen Modellen
        all_predictions = []
        model_weights = []

        for name, result in self.best_models.items():
            model = result['model']
            model_scaler = result['target_scaler']

            # Vorhersage
            y_pred = model.predict(X_test, verbose=0)
            y_pred_orig = model_scaler.inverse_transform(y_pred).flatten()

            all_predictions.append(y_pred_orig)

            # Gewicht basierend auf R²
            weight = max(0.1, result['r2'])
            model_weights.append(weight)

            print(f"   {name}: R² = {result['r2']:.4f}, Gewicht = {weight:.3f}")

        # Normalisiere Gewichte
        model_weights = np.array(model_weights)
        model_weights = model_weights / np.sum(model_weights)

        # Gewichtete Ensemble-Vorhersage
        ensemble_pred = np.average(all_predictions, axis=0, weights=model_weights)

        # Evaluation
        y_test_orig = target_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()

        ensemble_r2 = r2_score(y_test_orig, ensemble_pred)
        ensemble_rmse = np.sqrt(mean_squared_error(y_test_orig, ensemble_pred))
        ensemble_mae = mean_absolute_error(y_test_orig, ensemble_pred)
        ensemble_mape = np.mean(np.abs((y_test_orig - ensemble_pred) / y_test_orig)) * 100

        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(ensemble_pred) > 0
            ensemble_direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            ensemble_direction_acc = 0

        ensemble_result = {
            'r2': ensemble_r2,
            'rmse': ensemble_rmse,
            'mae': ensemble_mae,
            'mape': ensemble_mape,
            'direction_accuracy': ensemble_direction_acc,
            'model_weights': model_weights,
            'predictions': ensemble_pred,
            'actual': y_test_orig
        }

        print(f"\n🎯 Ensemble Performance:")
        print(f"   R²: {ensemble_r2:.4f} ({ensemble_r2*100:.1f}%)")
        print(f"   RMSE: ${ensemble_rmse:.2f}")
        print(f"   MAPE: {ensemble_mape:.2f}%")
        print(f"   Direction Acc: {ensemble_direction_acc:.1f}%")

        return ensemble_result

    def plot_optimization_results(self):
        """Visualisiere Optimierungsergebnisse"""
        if not self.results:
            return

        plt.figure(figsize=(20, 15))

        # 1. Performance Vergleich
        plt.subplot(3, 4, 1)
        names = [r['config_name'] for r in self.results]
        r2_scores = [r['r2'] for r in self.results]
        colors = plt.cm.viridis(np.linspace(0, 1, len(names)))

        bars = plt.bar(names, r2_scores, color=colors)
        plt.title('R² Score Vergleich')
        plt.ylabel('R² Score')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)

        # Beste markieren
        best_idx = np.argmax(r2_scores)
        bars[best_idx].set_color('gold')

        # 2. RMSE Vergleich
        plt.subplot(3, 4, 2)
        rmse_scores = [r['rmse'] for r in self.results]
        plt.bar(names, rmse_scores, color='lightcoral')
        plt.title('RMSE Vergleich')
        plt.ylabel('RMSE ($)')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)

        # 3. Training Zeit
        plt.subplot(3, 4, 3)
        times = [r['training_time'] for r in self.results]
        plt.bar(names, times, color='lightblue')
        plt.title('Training Zeit')
        plt.ylabel('Zeit (Sekunden)')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)

        # 4. Richtungsgenauigkeit
        plt.subplot(3, 4, 4)
        direction_accs = [r['direction_accuracy'] for r in self.results]
        plt.bar(names, direction_accs, color='lightgreen')
        plt.title('Richtungsgenauigkeit')
        plt.ylabel('Genauigkeit (%)')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)

        # 5. MAPE Vergleich
        plt.subplot(3, 4, 5)
        mape_scores = [r['mape'] for r in self.results]
        plt.bar(names, mape_scores, color='orange')
        plt.title('MAPE Vergleich')
        plt.ylabel('MAPE (%)')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)

        # 6. Model Parameter
        plt.subplot(3, 4, 6)
        params = [r['model_params'] for r in self.results]
        plt.bar(names, params, color='purple')
        plt.title('Modell Parameter')
        plt.ylabel('Anzahl Parameter')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)

        # 7. Accuracy Bands
        plt.subplot(3, 4, 7)
        acc_5pct = [r['accuracy_5pct'] for r in self.results]
        acc_10pct = [r['accuracy_10pct'] for r in self.results]

        x = np.arange(len(names))
        width = 0.35

        plt.bar(x - width/2, acc_5pct, width, label='5% Accuracy', color='darkgreen')
        plt.bar(x + width/2, acc_10pct, width, label='10% Accuracy', color='lightgreen')

        plt.title('Accuracy Bands')
        plt.ylabel('Accuracy (%)')
        plt.xticks(x, names, rotation=45)
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 8. Scatter: R² vs Training Zeit
        plt.subplot(3, 4, 8)
        plt.scatter(times, r2_scores, c=colors, s=100, alpha=0.7)
        for i, name in enumerate(names):
            plt.annotate(name, (times[i], r2_scores[i]), xytext=(5, 5),
                        textcoords='offset points', fontsize=8)
        plt.title('R² vs Training Zeit')
        plt.xlabel('Training Zeit (s)')
        plt.ylabel('R² Score')
        plt.grid(True, alpha=0.3)

        # 9-12. Beste Modell Vorhersagen
        best_result = max(self.results, key=lambda x: x['r2'])

        plt.subplot(3, 4, 9)
        y_test = best_result['y_test_orig'][:50]  # Erste 50 Punkte
        y_pred = best_result['y_pred_orig'][:50]

        plt.plot(y_test, 'g-', label='Actual', linewidth=2)
        plt.plot(y_pred, 'r--', label='Predicted', linewidth=2)
        plt.title(f'Beste Vorhersage: {best_result["config_name"]}')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.subplot(3, 4, 10)
        plt.scatter(best_result['y_test_orig'], best_result['y_pred_orig'], alpha=0.6)
        min_val = min(best_result['y_test_orig'].min(), best_result['y_pred_orig'].min())
        max_val = max(best_result['y_test_orig'].max(), best_result['y_pred_orig'].max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
        plt.title('Scatter Plot - Beste Modell')
        plt.xlabel('Actual')
        plt.ylabel('Predicted')
        plt.grid(True, alpha=0.3)

        plt.subplot(3, 4, 11)
        residuals = best_result['y_test_orig'] - best_result['y_pred_orig']
        plt.hist(residuals, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.title('Error Distribution - Beste Modell')
        plt.xlabel('Prediction Error')
        plt.ylabel('Frequency')
        plt.grid(True, alpha=0.3)

        plt.subplot(3, 4, 12)
        # Zusammenfassung
        summary_text = f"""
        OPTIMIZATION SUMMARY

        Beste Konfiguration: {best_result['config_name']}
        R²: {best_result['r2']:.4f} ({best_result['r2']*100:.1f}%)
        RMSE: ${best_result['rmse']:.2f}
        MAPE: {best_result['mape']:.2f}%
        Direction Acc: {best_result['direction_accuracy']:.1f}%

        Training Zeit: {best_result['training_time']:.1f}s
        Parameter: {best_result['model_params']:,}

        Getestete Konfigurationen: {len(self.results)}
        """

        plt.text(0.1, 0.5, summary_text, fontsize=10, verticalalignment='center',
                 bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        plt.axis('off')
        plt.title('Optimization Summary')

        plt.tight_layout()
        plt.show()

    def run_optimization(self):
        """Führe komplette Optimierung durch"""
        print("🚀 Starte Advanced Bitcoin Prediction Optimization...")

        # Daten laden
        features = self.load_and_engineer_features()

        # Alle Konfigurationen testen
        for config in OPTIMIZATION_CONFIGS:
            print(f"\n{'='*60}")
            print(f"🔥 Teste Konfiguration: {config['name']}")
            print(f"{'='*60}")

            # Cross-Validation
            cv_score, cv_std, cv_scores = self.cross_validate_model(config, features)

            # Finales Training
            result = self.train_final_model(config, features)
            result['cv_score'] = cv_score
            result['cv_std'] = cv_std
            result['cv_scores'] = cv_scores

            self.results.append(result)
            self.best_models[config['name']] = result

            # Memory cleanup
            tf.keras.backend.clear_session()

        # Ensemble erstellen
        ensemble_result = self.create_ensemble_prediction(features)

        # Ergebnisse analysieren
        self.analyze_final_results(ensemble_result)

        # Visualisierung
        self.plot_optimization_results()

    def analyze_final_results(self, ensemble_result):
        """Finale Ergebnisanalyse"""
        print("\n" + "="*70)
        print("📊 FINALE OPTIMIZATION ANALYSE")
        print("="*70)

        # Sortiere nach R²
        sorted_results = sorted(self.results, key=lambda x: x['r2'], reverse=True)

        print(f"\n🏆 RANKING DER KONFIGURATIONEN:")
        for i, result in enumerate(sorted_results):
            print(f"\n   {i+1}. {result['config_name']}:")
            print(f"      R²: {result['r2']:.4f} ({result['r2']*100:.1f}%)")
            print(f"      CV Score: {result['cv_score']:.4f} ± {result['cv_std']:.4f}")
            print(f"      RMSE: ${result['rmse']:.2f}")
            print(f"      MAPE: {result['mape']:.2f}%")
            print(f"      Direction Acc: {result['direction_accuracy']:.1f}%")
            print(f"      Training Zeit: {result['training_time']:.1f}s")

        # Beste Konfiguration
        best = sorted_results[0]
        print(f"\n🎯 BESTE KONFIGURATION: {best['config_name']}")
        print(f"   Finale Performance: {best['r2']*100:.1f}% R²")

        # Ensemble Performance
        if ensemble_result:
            print(f"\n🏆 ENSEMBLE PERFORMANCE:")
            print(f"   R²: {ensemble_result['r2']:.4f} ({ensemble_result['r2']*100:.1f}%)")
            print(f"   RMSE: ${ensemble_result['rmse']:.2f}")
            print(f"   MAPE: {ensemble_result['mape']:.2f}%")
            print(f"   Direction Acc: {ensemble_result['direction_accuracy']:.1f}%")

            if ensemble_result['r2'] > best['r2']:
                print(f"   🎉 Ensemble übertrifft beste Einzelkonfiguration!")
                improvement = (ensemble_result['r2'] - best['r2']) * 100
                print(f"   📈 Verbesserung: +{improvement:.2f} Prozentpunkte")

        # Empfehlungen
        print(f"\n💡 EMPFEHLUNGEN:")

        # Beste für verschiedene Anwendungsfälle
        fastest = min(self.results, key=lambda x: x['training_time'])
        most_accurate = max(self.results, key=lambda x: x['r2'])
        best_direction = max(self.results, key=lambda x: x['direction_accuracy'])

        print(f"   🎯 Höchste Genauigkeit: {most_accurate['config_name']} ({most_accurate['r2']*100:.1f}% R²)")
        print(f"   ⚡ Schnellste: {fastest['config_name']} ({fastest['training_time']:.1f}s)")
        print(f"   📈 Beste Richtung: {best_direction['config_name']} ({best_direction['direction_accuracy']:.1f}%)")

        if ensemble_result and ensemble_result['r2'] >= 0.8:
            print(f"\n🎉🎉🎉 ZIEL ERREICHT! 🎉🎉🎉")
            print(f"Ensemble erreicht {ensemble_result['r2']*100:.1f}% Genauigkeit!")
        elif best['r2'] >= 0.8:
            print(f"\n🎉🎉 ZIEL ERREICHT! 🎉🎉")
            print(f"Beste Konfiguration erreicht {best['r2']*100:.1f}% Genauigkeit!")
        else:
            print(f"\n💪 STARKE PERFORMANCE!")
            print(f"Beste Genauigkeit: {best['r2']*100:.1f}%")

def main():
    """Hauptfunktion"""
    optimizer = AdvancedOptimizer()
    optimizer.run_optimization()

    print(f"\n✅ ADVANCED OPTIMIZATION ABGESCHLOSSEN!")

if __name__ == "__main__":
    main()
