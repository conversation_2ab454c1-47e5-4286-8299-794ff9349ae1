#!/usr/bin/env python3
"""
ULTIMATE BITCOIN PREDICTION SYSTEM - FINALE VERSION
Das ultimative System mit modernster KI-Architektur für >95% Genauigkeit
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import (LSTM, GRU, Dense, Dropout, BatchNormalization, 
                                   Bidirectional, Input, Concatenate, Add)
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers.legacy import Adam, RMSprop
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import warnings
import time
import os
warnings.filterwarnings('ignore')

# Setze Stil
plt.style.use('default')
sns.set_palette("husl")

print("🚀 ULTIMATE BITCOIN PREDICTION SYSTEM - FINALE VERSION")
print("=" * 70)
print("🎯 Ziel: >95% Genauigkeit mit modernster KI-Architektur")
print("💻 Features: Multi-Model Ensemble, Advanced Features, Optimized Training")
print("=" * 70)

# ULTIMATE KONFIGURATION
CONFIG = {
    'data_file': 'crypto_data.csv',
    'target_accuracy': 0.95,
    'sequence_length': 48,
    'prediction_horizons': [1, 6, 12, 24, 48],
    'epochs': 100,
    'batch_size': 32,
    'patience': 20
}

class UltimateBitcoinPredictor:
    """Ultimate Bitcoin Prediction System"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.setup_environment()
    
    def setup_environment(self):
        """Optimiere TensorFlow für maximale Performance"""
        print("⚙️  Optimiere Umgebung...")
        
        # GPU-Konfiguration
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print(f"   🎮 GPU aktiviert: {len(gpus)} GPU(s)")
            except RuntimeError as e:
                print(f"   ⚠️  GPU-Konfiguration: {e}")
        
        # CPU-Optimierung
        tf.config.threading.set_intra_op_parallelism_threads(0)
        tf.config.threading.set_inter_op_parallelism_threads(0)
        
        print(f"   💻 CPU-Kerne: {os.cpu_count()}")
        print("   ✅ Umgebung optimiert")
    
    def load_and_engineer_features(self):
        """Ultimate Feature Engineering"""
        print("\n📊 ULTIMATE FEATURE ENGINEERING")
        print("-" * 50)
        
        # Daten laden
        df = pd.read_csv(CONFIG['data_file'])
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        print(f"📈 Rohdaten: {len(df)} Punkte")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
        
        # Basis OHLCV
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # === PREIS-FEATURES ===
        print("   🔧 Erstelle Preis-Features...")
        
        # Returns
        features['returns'] = df['close'].pct_change()
        features['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # Preis-Ratios
        features['hl_ratio'] = df['high'] / df['low']
        features['co_ratio'] = df['close'] / df['open']
        features['body'] = abs(df['close'] - df['open'])
        features['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
        features['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
        
        # === MOVING AVERAGES ===
        print("   📈 Erstelle Moving Averages...")
        
        ma_periods = [5, 10, 20, 50, 100, 200]
        for period in ma_periods:
            if period <= len(df):
                features[f'sma_{period}'] = df['close'].rolling(period).mean()
                features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
                features[f'price_sma_{period}_ratio'] = df['close'] / features[f'sma_{period}']
                
                if period > 5:
                    features[f'sma_cross_{period}'] = (features['sma_5'] > features[f'sma_{period}']).astype(int)
        
        # === MOMENTUM INDIKATOREN ===
        print("   ⚡ Erstelle Momentum-Indikatoren...")
        
        # RSI
        for period in [14, 21]:
            delta = df['close'].diff()
            gain = delta.where(delta > 0, 0).rolling(period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            features[f'rsi_{period}'] = rsi
            features[f'rsi_{period}_overbought'] = (rsi > 70).astype(int)
            features[f'rsi_{period}_oversold'] = (rsi < 30).astype(int)
        
        # MACD
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        features['macd'] = ema_12 - ema_26
        features['macd_signal'] = features['macd'].ewm(span=9).mean()
        features['macd_histogram'] = features['macd'] - features['macd_signal']
        features['macd_cross'] = (features['macd'] > features['macd_signal']).astype(int)
        
        # Stochastic
        for period in [14, 21]:
            low_min = df['low'].rolling(period).min()
            high_max = df['high'].rolling(period).max()
            stoch_k = 100 * ((df['close'] - low_min) / (high_max - low_min))
            features[f'stoch_k_{period}'] = stoch_k
            features[f'stoch_d_{period}'] = stoch_k.rolling(3).mean()
        
        # === VOLATILITÄT ===
        print("   📊 Erstelle Volatilitäts-Indikatoren...")
        
        # Bollinger Bands
        for period in [20, 50]:
            sma = df['close'].rolling(period).mean()
            std = df['close'].rolling(period).std()
            features[f'bb_upper_{period}'] = sma + (std * 2)
            features[f'bb_lower_{period}'] = sma - (std * 2)
            features[f'bb_width_{period}'] = (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}']) / sma
            features[f'bb_position_{period}'] = (df['close'] - features[f'bb_lower_{period}']) / (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}'])
        
        # ATR
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        features['atr'] = true_range.rolling(14).mean()
        features['atr_percent'] = features['atr'] / df['close'] * 100
        
        # Realized Volatility
        for period in [10, 20]:
            features[f'realized_vol_{period}'] = features['log_returns'].rolling(period).std() * np.sqrt(24)
        
        # === VOLUMEN ===
        print("   📦 Erstelle Volumen-Indikatoren...")
        
        # OBV
        obv = [0]
        for i in range(1, len(df)):
            if df['close'].iloc[i] > df['close'].iloc[i-1]:
                obv.append(obv[-1] + df['volume'].iloc[i])
            elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                obv.append(obv[-1] - df['volume'].iloc[i])
            else:
                obv.append(obv[-1])
        features['obv'] = obv
        features['obv_ema'] = features['obv'].ewm(span=20).mean()
        
        # Volume Ratios
        for period in [10, 20]:
            vol_sma = df['volume'].rolling(period).mean()
            features[f'volume_sma_{period}'] = vol_sma
            features[f'volume_ratio_{period}'] = df['volume'] / vol_sma
            features[f'volume_spike_{period}'] = (features[f'volume_ratio_{period}'] > 2).astype(int)
        
        # VWAP
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        features['vwap'] = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        features['vwap_ratio'] = df['close'] / features['vwap']
        
        # === ZEIT-FEATURES ===
        print("   🕐 Erstelle Zeit-Features...")
        
        features['hour'] = df.index.hour
        features['day_of_week'] = df.index.dayofweek
        features['month'] = df.index.month
        
        # Trigonometrische Transformationen
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
        features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
        
        # Markt-Sessions
        features['asian_session'] = ((features['hour'] >= 0) & (features['hour'] < 8)).astype(int)
        features['european_session'] = ((features['hour'] >= 8) & (features['hour'] < 16)).astype(int)
        features['american_session'] = ((features['hour'] >= 16) & (features['hour'] < 24)).astype(int)
        features['weekend'] = (features['day_of_week'] >= 5).astype(int)
        
        # === STATISTISCHE FEATURES ===
        print("   📊 Erstelle statistische Features...")
        
        for period in [10, 20, 50]:
            features[f'skew_{period}'] = features['returns'].rolling(period).skew()
            features[f'kurt_{period}'] = features['returns'].rolling(period).kurt()
            features[f'zscore_{period}'] = (df['close'] - df['close'].rolling(period).mean()) / df['close'].rolling(period).std()
        
        # === CLEANUP ===
        features = features.dropna()
        
        n_features = len(features.columns)
        n_samples = len(features)
        
        print(f"   ✅ {n_features} Ultimate Features erstellt")
        print(f"   📊 {n_samples} saubere Datenpunkte")
        
        return features
    
    def create_sequences(self, data, target, sequence_length):
        """Erstelle Sequenzen für Zeitreihen-Modelle"""
        X, y = [], []
        for i in range(sequence_length, len(data)):
            X.append(data[i-sequence_length:i])
            y.append(target[i])
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)
    
    def prepare_data(self, features):
        """Bereite Daten für Training vor"""
        print("\n🔄 DATENAUFBEREITUNG")
        print("-" * 30)
        
        # Features und Target trennen
        X = features.drop('close', axis=1)
        y = features['close'].values
        
        print(f"📊 Features: {X.shape[1]}")
        print(f"📊 Samples: {len(y)}")
        
        # Skalierung
        print("   🔧 Skaliere Features...")
        feature_scaler = RobustScaler()
        target_scaler = StandardScaler()
        
        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        
        # Sequenzen erstellen
        print(f"   📦 Erstelle Sequenzen (Länge: {CONFIG['sequence_length']})...")
        X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, CONFIG['sequence_length'])
        
        print(f"   ✅ {len(X_seq)} Sequenzen erstellt")
        
        # Speichere Scaler
        self.scalers['feature'] = feature_scaler
        self.scalers['target'] = target_scaler
        
        return X_seq, y_seq, X.columns.tolist()
    
    def split_data(self, X, y):
        """Datenaufteilung für Zeitreihen"""
        print("\n✂️  DATENAUFTEILUNG")
        print("-" * 25)
        
        total_size = len(X)
        train_size = int(total_size * 0.7)
        val_size = int(total_size * 0.15)
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        X_val = X[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        X_test = X[train_size+val_size:]
        y_test = y[train_size+val_size:]
        
        print(f"📊 Training: {len(X_train)} ({len(X_train)/total_size*100:.1f}%)")
        print(f"📊 Validation: {len(X_val)} ({len(X_val)/total_size*100:.1f}%)")
        print(f"📊 Test: {len(X_test)} ({len(X_test)/total_size*100:.1f}%)")
        
        return (X_train, y_train), (X_val, y_val), (X_test, y_test)
