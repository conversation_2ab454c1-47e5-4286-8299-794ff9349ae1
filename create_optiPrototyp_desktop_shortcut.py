#!/usr/bin/env python3
"""
OPTI-PROTOTYP DESKTOP-VERKNÜPFUNG ERSTELLER
==========================================
Erstellt eine Desktop-Verknüpfung für den OptiPrototyp Bitcoin Launcher Ultimate
- Automatische Desktop-Verknüpfung
- Windows-kompatibel
- Icon-Integration
- Vollständige Pfad-Erkennung

DESKTOP-VERKNÜPFUNG FÜR OPTI-PROTOTYP!
"""

import os
import sys
import winshell
from win32com.client import Dispatch
import tkinter as tk
from tkinter import messagebox
import subprocess

class OptiPrototypDesktopShortcutCreator:
    """
    OPTI-PROTOTYP DESKTOP-VERKNÜPFUNG ERSTELLER
    ==========================================
    Erstellt Desktop-Verknüpfung für den OptiPrototyp Launcher
    """
    
    def __init__(self):
        self.script_directory = os.getcwd()
        self.launcher_file = "optiPrototyp_bitcoin_launcher_ultimate.py"
        self.launcher_path = os.path.join(self.script_directory, self.launcher_file)
        
        print("OPTI-PROTOTYP Desktop-Verknüpfung Ersteller initialisiert")
        print(f"Script-Verzeichnis: {self.script_directory}")
        print(f"Launcher-Datei: {self.launcher_file}")
    
    def check_launcher_exists(self):
        """Prüfe ob Launcher-Datei existiert"""
        if os.path.exists(self.launcher_path):
            print(f"✅ OptiPrototyp Launcher gefunden: {self.launcher_path}")
            return True
        else:
            print(f"❌ OptiPrototyp Launcher nicht gefunden: {self.launcher_path}")
            return False
    
    def create_desktop_shortcut(self):
        """Erstelle Desktop-Verknüpfung"""
        try:
            # Desktop-Pfad ermitteln
            desktop = winshell.desktop()
            shortcut_path = os.path.join(desktop, "OptiPrototyp Bitcoin Launcher Ultimate.lnk")
            
            # Shell-Objekt erstellen
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(shortcut_path)
            
            # Python-Interpreter-Pfad
            python_exe = sys.executable
            
            # Verknüpfungs-Eigenschaften setzen
            shortcut.Targetpath = python_exe
            shortcut.Arguments = f'"{self.launcher_path}"'
            shortcut.WorkingDirectory = self.script_directory
            shortcut.Description = "OptiPrototyp Bitcoin Launcher Ultimate - Vollständig überarbeiteter Trading Launcher"
            
            # Icon setzen (Python-Icon als Standard)
            shortcut.IconLocation = python_exe
            
            # Verknüpfung speichern
            shortcut.save()
            
            print(f"✅ Desktop-Verknüpfung erfolgreich erstellt: {shortcut_path}")
            return True
            
        except Exception as e:
            print(f"❌ Fehler beim Erstellen der Desktop-Verknüpfung: {e}")
            return False
    
    def create_start_menu_shortcut(self):
        """Erstelle Start-Menü-Verknüpfung"""
        try:
            # Start-Menü-Pfad ermitteln
            start_menu = winshell.start_menu()
            programs_folder = os.path.join(start_menu, "Programs")
            
            # OptiPrototyp-Ordner erstellen
            opti_folder = os.path.join(programs_folder, "OptiPrototyp Bitcoin Trading")
            if not os.path.exists(opti_folder):
                os.makedirs(opti_folder)
            
            shortcut_path = os.path.join(opti_folder, "OptiPrototyp Bitcoin Launcher Ultimate.lnk")
            
            # Shell-Objekt erstellen
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(shortcut_path)
            
            # Python-Interpreter-Pfad
            python_exe = sys.executable
            
            # Verknüpfungs-Eigenschaften setzen
            shortcut.Targetpath = python_exe
            shortcut.Arguments = f'"{self.launcher_path}"'
            shortcut.WorkingDirectory = self.script_directory
            shortcut.Description = "OptiPrototyp Bitcoin Launcher Ultimate - 6-Modell-Ensemble mit 350+ Features"
            
            # Icon setzen
            shortcut.IconLocation = python_exe
            
            # Verknüpfung speichern
            shortcut.save()
            
            print(f"✅ Start-Menü-Verknüpfung erfolgreich erstellt: {shortcut_path}")
            return True
            
        except Exception as e:
            print(f"❌ Fehler beim Erstellen der Start-Menü-Verknüpfung: {e}")
            return False
    
    def test_shortcut(self):
        """Teste die erstellte Verknüpfung"""
        try:
            print("🧪 Teste OptiPrototyp Launcher...")
            
            # Teste ob der Launcher startet
            result = subprocess.run([sys.executable, self.launcher_path], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ OptiPrototyp Launcher-Test erfolgreich")
                return True
            else:
                print(f"❌ OptiPrototyp Launcher-Test fehlgeschlagen: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("✅ OptiPrototyp Launcher gestartet (Timeout nach 10s - normal)")
            return True
        except Exception as e:
            print(f"❌ Fehler beim Testen des Launchers: {e}")
            return False
    
    def show_gui_confirmation(self):
        """Zeige GUI-Bestätigung"""
        root = tk.Tk()
        root.withdraw()  # Hauptfenster verstecken
        
        # Bestätigungsdialog
        result = messagebox.askyesno(
            "OptiPrototyp Desktop-Verknüpfung",
            "Möchten Sie Desktop- und Start-Menü-Verknüpfungen für den\n"
            "OptiPrototyp Bitcoin Launcher Ultimate erstellen?\n\n"
            "Dies erstellt:\n"
            "• Desktop-Verknüpfung\n"
            "• Start-Menü-Verknüpfung\n"
            "• Automatische Pfad-Erkennung",
            icon='question'
        )
        
        root.destroy()
        return result
    
    def run_creation_process(self):
        """Führe kompletten Erstellungsprozess durch"""
        print("\n" + "="*80)
        print("OPTI-PROTOTYP DESKTOP-VERKNÜPFUNG ERSTELLER")
        print("="*80)
        
        # 1. Prüfe Launcher-Existenz
        if not self.check_launcher_exists():
            print("\n❌ FEHLER: OptiPrototyp Launcher nicht gefunden!")
            print("Stellen Sie sicher, dass 'optiPrototyp_bitcoin_launcher_ultimate.py' im aktuellen Verzeichnis ist.")
            return False
        
        # 2. GUI-Bestätigung
        if not self.show_gui_confirmation():
            print("\n🚫 Verknüpfungs-Erstellung abgebrochen")
            return False
        
        print("\n🚀 Starte Verknüpfungs-Erstellung...")
        
        # 3. Desktop-Verknüpfung erstellen
        desktop_success = self.create_desktop_shortcut()
        
        # 4. Start-Menü-Verknüpfung erstellen
        start_menu_success = self.create_start_menu_shortcut()
        
        # 5. Launcher testen
        test_success = self.test_shortcut()
        
        # 6. Ergebnis anzeigen
        print("\n" + "="*80)
        print("ERGEBNIS DER VERKNÜPFUNGS-ERSTELLUNG")
        print("="*80)
        
        if desktop_success:
            print("✅ Desktop-Verknüpfung: ERFOLGREICH erstellt")
        else:
            print("❌ Desktop-Verknüpfung: FEHLGESCHLAGEN")
        
        if start_menu_success:
            print("✅ Start-Menü-Verknüpfung: ERFOLGREICH erstellt")
        else:
            print("❌ Start-Menü-Verknüpfung: FEHLGESCHLAGEN")
        
        if test_success:
            print("✅ Launcher-Test: ERFOLGREICH")
        else:
            print("❌ Launcher-Test: FEHLGESCHLAGEN")
        
        # Erfolgs-GUI
        if desktop_success or start_menu_success:
            root = tk.Tk()
            root.withdraw()
            
            success_message = "OptiPrototyp Desktop-Verknüpfungen erfolgreich erstellt!\n\n"
            
            if desktop_success:
                success_message += "✅ Desktop-Verknüpfung erstellt\n"
            if start_menu_success:
                success_message += "✅ Start-Menü-Verknüpfung erstellt\n"
            
            success_message += "\nSie können jetzt den OptiPrototyp Bitcoin Launcher Ultimate\n"
            success_message += "direkt vom Desktop oder Start-Menü starten!"
            
            messagebox.showinfo("Erfolg", success_message, icon='info')
            root.destroy()
            
            print("\n🎉 OPTI-PROTOTYP DESKTOP-VERKNÜPFUNGEN ERFOLGREICH ERSTELLT!")
            return True
        else:
            root = tk.Tk()
            root.withdraw()
            
            messagebox.showerror(
                "Fehler",
                "Fehler beim Erstellen der Desktop-Verknüpfungen!\n\n"
                "Überprüfen Sie die Konsolen-Ausgabe für Details.",
                icon='error'
            )
            root.destroy()
            
            print("\n❌ FEHLER beim Erstellen der Desktop-Verknüpfungen")
            return False

def main():
    """Hauptfunktion"""
    try:
        creator = OptiPrototypDesktopShortcutCreator()
        creator.run_creation_process()
    except Exception as e:
        print(f"FEHLER: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
