2025-07-03 00:23:01,817 - INFO - ML-Engine initialisiert mit 8 Modellen
2025-07-03 00:23:01,817 - INFO - Trading System V10.0_REVOLUTIONARY initialisiert
2025-07-03 00:23:01,817 - INFO - Starte Ultimate Analyse V10...
2025-07-03 00:23:02,518 - INFO - Marktdaten gesammelt: 366 Datenpunkte von 5 Quellen
2025-07-03 00:23:02,568 - INFO - Features erstellt: 56 Features f�r 366 Samples
2025-07-03 00:23:03,188 - INFO - random_forest: Genauigkeit = 0.429 � 0.104
2025-07-03 00:23:06,733 - INFO - gradient_boosting: Genauigkeit = 0.451 � 0.110
2025-07-03 00:23:07,214 - INFO - extra_trees: Genauigkeit = 0.426 � 0.129
2025-07-03 00:23:07,252 - ERROR - <PERSON><PERSON> beim <PERSON> von sgd: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
5 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "E:\Dev\tf_env\Lib\site-packages\sklearn\model_selection\_validation.py", line 888, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "E:\Dev\tf_env\Lib\site-packages\sklearn\base.py", line 1466, in wrapper
    estimator._validate_params()
  File "E:\Dev\tf_env\Lib\site-packages\sklearn\base.py", line 666, in _validate_params
    validate_parameter_constraints(
  File "E:\Dev\tf_env\Lib\site-packages\sklearn\utils\_param_validation.py", line 95, in validate_parameter_constraints
    raise InvalidParameterError(
sklearn.utils._param_validation.InvalidParameterError: The 'loss' parameter of SGDClassifier must be a str among {'perceptron', 'hinge', 'squared_error', 'huber', 'modified_huber', 'squared_epsilon_insensitive', 'squared_hinge', 'epsilon_insensitive', 'log_loss'}. Got 'log' instead.

2025-07-03 00:23:07,338 - INFO - logistic: Genauigkeit = 0.468 � 0.100
2025-07-03 00:23:07,433 - INFO - svm: Genauigkeit = 0.410 � 0.114
2025-07-03 00:23:10,348 - INFO - mlp: Genauigkeit = 0.421 � 0.117
2025-07-03 00:23:10,718 - INFO - xgboost: Genauigkeit = 0.474 � 0.063
2025-07-03 00:23:10,718 - INFO - Ensemble-Training abgeschlossen: 8 Modelle
2025-07-03 00:23:10,757 - WARNING - Vorhersage-Fehler bei sgd: This SGDClassifier instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-07-03 00:37:15,211 - INFO - ML-Engine initialisiert mit 8 Modellen
2025-07-03 00:37:15,211 - INFO - Trading System V10.0_REVOLUTIONARY initialisiert
2025-07-03 00:37:15,211 - INFO - GUI V10.0_ENHANCED initialisiert
2025-07-03 00:37:16,704 - INFO - Enhanced Chart Layout mit 4 Tabs erstellt
2025-07-03 00:37:16,705 - INFO - Enhanced GUI erfolgreich erstellt
2025-07-03 00:37:16,705 - INFO - Enhanced GUI gestartet
2025-07-03 00:37:32,341 - INFO - Starte Ultimate Analyse V10...
2025-07-03 00:37:33,015 - INFO - Marktdaten gesammelt: 366 Datenpunkte von 5 Quellen
2025-07-03 00:37:33,066 - INFO - Features erstellt: 56 Features f�r 366 Samples
2025-07-03 00:37:33,691 - INFO - random_forest: Genauigkeit = 0.429 � 0.104
2025-07-03 00:37:37,231 - INFO - gradient_boosting: Genauigkeit = 0.451 � 0.110
2025-07-03 00:37:37,708 - INFO - extra_trees: Genauigkeit = 0.426 � 0.129
2025-07-03 00:37:37,724 - INFO - sgd: Genauigkeit = 0.382 � 0.129
2025-07-03 00:37:37,816 - INFO - logistic: Genauigkeit = 0.468 � 0.100
2025-07-03 00:37:37,906 - INFO - svm: Genauigkeit = 0.410 � 0.114
2025-07-03 00:37:40,771 - INFO - mlp: Genauigkeit = 0.421 � 0.117
2025-07-03 00:37:41,135 - INFO - xgboost: Genauigkeit = 0.474 � 0.063
2025-07-03 00:37:41,135 - INFO - Ensemble-Training abgeschlossen: 8 Modelle
2025-07-03 00:37:41,190 - ERROR - Chart-Update Fehler: 'EnhancedChartManager' object has no attribute '_update_volume_sentiment_chart'
2025-07-03 00:39:22,036 - INFO - Starte Ultimate Analyse V10...
2025-07-03 00:39:22,036 - INFO - Auto-Update gestartet
2025-07-03 00:39:22,531 - INFO - Marktdaten gesammelt: 366 Datenpunkte von 5 Quellen
2025-07-03 00:39:22,582 - INFO - Features erstellt: 56 Features f�r 366 Samples
2025-07-03 00:39:23,198 - INFO - random_forest: Genauigkeit = 0.429 � 0.104
2025-07-03 00:39:26,762 - INFO - gradient_boosting: Genauigkeit = 0.451 � 0.110
2025-07-03 00:39:27,300 - INFO - extra_trees: Genauigkeit = 0.426 � 0.129
2025-07-03 00:39:27,321 - INFO - sgd: Genauigkeit = 0.382 � 0.129
2025-07-03 00:39:27,419 - INFO - logistic: Genauigkeit = 0.468 � 0.100
2025-07-03 00:39:27,507 - INFO - svm: Genauigkeit = 0.410 � 0.114
2025-07-03 00:39:30,564 - INFO - mlp: Genauigkeit = 0.421 � 0.117
2025-07-03 00:39:30,961 - INFO - xgboost: Genauigkeit = 0.474 � 0.063
2025-07-03 00:39:30,961 - INFO - Ensemble-Training abgeschlossen: 8 Modelle
2025-07-03 00:39:31,036 - ERROR - Chart-Update Fehler: 'EnhancedChartManager' object has no attribute '_update_volume_sentiment_chart'
2025-07-03 00:39:42,037 - INFO - Auto-Update gestoppt
2025-07-03 00:40:12,756 - INFO - Auto-Update gestoppt
2025-07-03 00:40:12,756 - INFO - System zur�ckgesetzt
2025-07-03 00:40:18,236 - INFO - Starte Ultimate Analyse V10...
2025-07-03 00:40:18,706 - INFO - Marktdaten gesammelt: 366 Datenpunkte von 5 Quellen
2025-07-03 00:40:18,754 - INFO - Features erstellt: 56 Features f�r 366 Samples
2025-07-03 00:40:19,371 - INFO - random_forest: Genauigkeit = 0.429 � 0.104
2025-07-03 00:40:22,861 - INFO - gradient_boosting: Genauigkeit = 0.451 � 0.110
2025-07-03 00:40:23,353 - INFO - extra_trees: Genauigkeit = 0.426 � 0.129
2025-07-03 00:40:23,375 - INFO - sgd: Genauigkeit = 0.382 � 0.129
2025-07-03 00:40:23,461 - INFO - logistic: Genauigkeit = 0.468 � 0.100
2025-07-03 00:40:23,551 - INFO - svm: Genauigkeit = 0.410 � 0.114
2025-07-03 00:40:26,453 - INFO - mlp: Genauigkeit = 0.421 � 0.117
2025-07-03 00:40:26,812 - INFO - xgboost: Genauigkeit = 0.474 � 0.063
2025-07-03 00:40:26,812 - INFO - Ensemble-Training abgeschlossen: 8 Modelle
2025-07-03 00:40:26,873 - ERROR - Chart-Update Fehler: 'EnhancedChartManager' object has no attribute '_update_volume_sentiment_chart'
2025-07-03 00:40:46,936 - INFO - Enhanced GUI wird heruntergefahren...
2025-07-03 00:40:47,009 - INFO - Bereinige GUI...
2025-07-03 00:40:47,010 - INFO - GUI Cleanup abgeschlossen
2025-07-03 00:40:47,010 - INFO - Enhanced GUI erfolgreich heruntergefahren
2025-07-03 00:40:47,010 - INFO - Enhanced GUI wird heruntergefahren...
2025-07-03 00:40:47,010 - ERROR - Shutdown Fehler: can't invoke "destroy" command: application has been destroyed
2025-07-03 00:40:47,010 - INFO - Bereinige GUI...
2025-07-03 00:40:47,010 - INFO - GUI Cleanup abgeschlossen
2025-07-03 00:40:47,011 - INFO - Bereinige GUI...
2025-07-03 00:40:47,012 - INFO - GUI Cleanup abgeschlossen
