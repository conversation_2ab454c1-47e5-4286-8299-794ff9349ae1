�

    *^eh�=  �                   ��   � d Z ddlZddlZddlZddlZddlZddl	m	Z	m
Z
 ddlZ ej        d�  �         ddl
mZ ddlmZ ddlmZ  G d� d	�  �        Zd
� Zedk    r e�   �          dS dS )u`  
ULTIMATE BITCOIN TRADING SYSTEM - FINAL WORKING VERSION
=======================================================
EINFACH • FUNKTIONAL • EFFIZIENT • GETESTET
- Nur notwendige Abhängigkeiten
- Alle Funktionen vollständig implementiert
- Sofort lauffähig
- Keine komplexen Features die scheitern können

FINAL VERSION - FUNKTIONIERT GARANTIERT!
�    N)�datetime�	timedelta�ignore)�RobustScaler)�RandomForestRegressor)�r2_scorec                   �   � e Zd ZdZd� Zdefd�Zdej        fd�Z	dej        dej        fd�Z
dej        defd�Zdej        de
fd	�Zde
fd
�ZdS )�UltimateBitcoinTradingFinalz�
    ULTIMATE BITCOIN TRADING SYSTEM - FINAL VERSION
    ===============================================
    Einfach, funktional, effizient
    c                 �f  � d| _         d| _        ddd�| _        t          j        �   �         | _        d | _        t          �   �         | _        ddddd	�| _	        t          d
�  �         t          d| j         � ��  �         t          dt          | j        �  �        � d
��  �         t          d�  �         d S )N�Ultimate_Bitcoin_Trading_FINALzBTC-USDz:https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDTzKhttps://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd)�binance�	coingeckor   g        �HALTEN)�scans�accuracy�
last_price�last_signalz*Bitcoin Trading System FINAL initialisiertz	Version: zAPIs: z (nur funktionierende)u&   Status: BEREIT FÜR SOFORTIGEN EINSATZ)�VERSION�SYMBOL�
api_endpoints�pd�	DataFrame�market_data�ml_modelr   �scaler�stats�print�len)�selfs    �E:\Dev\bitcoin_trading_final.py�__init__z$UltimateBitcoinTradingFinal.__init__#   s�   � �7������ T�f�
� 
��� �<�>�>�����
�"�n�n��� ���#�	
� 
��
� 	�;�<�<�<�
�(�$�,�(�(�)�)�)�
�F�s�4�-�.�.�F�F�F�G�G�G�
�7�8�8�8�8�8�    �returnc                 ��  � 	 t          d�  �         g }	 t          j        | j        d         d��  �        }|j        dk    ra|�                    �   �         }t
          |d         �  �        }d|cxk    rdk    r+n n(|�                    |�  �         t          d	|d
���  �         n#  Y nxY w	 t          j        | j        d         d��  �        }|j        dk    rg|�                    �   �         }t
          |d         d
         �  �        }d|cxk    rdk    r+n n(|�                    |�  �         t          d|d
���  �         n#  Y nxY w|r)t          j	        |�  �        }t          d|d
���  �         |S 	 t          j        | j        �  �        }|j
        }|�                    dd�  �        }t          d|d
���  �         |S #  t          d�  �         Y dS xY w# t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)z0Hole Live Bitcoin-Preis (EINFACH und FUNKTIONAL)zHole Live Bitcoin-Preis...r
   �   )�timeout��   �pricei'  i � u   ✅ Binance: $�,.2fr   �bitcoin�usdu   ✅ CoinGecko: $u   💰 Live-Preis: $�regularMarketPrice�ȩ u   📈 Yahoo Fallback: $u   ⚠️ Fallback-Preis: $109,000u   ❌ Live-Preis Fehler: N)r   �requests�getr   �status_code�json�float�append�np�median�yf�Tickerr   �info�	Exception)	r   �prices�response�datar(   �consensus_price�btcr8   �es	            r    �get_live_pricez*UltimateBitcoinTradingFinal.get_live_price?   s`  � �0	��.�/�/�/��F�	
�#�<��(:�9�(E�q�Q�Q�Q���'�3�.�.�#�=�=�?�?�D�!�$�w�-�0�0�E���/�/�/�/��/�/�/�/�/��
�
�e�,�,�,��;�u�;�;�;�<�<�<���
������	
�#�<��(:�;�(G�QR�S�S�S���'�3�.�.�#�=�=�?�?�D�!�$�y�/�%�"8�9�9�E���/�/�/�/��/�/�/�/�/��
�
�e�,�,�,��=��=�=�=�>�>�>���
������ � 
"�"$�)�F�"3�"3���A�?�A�A�A�B�B�B�&�&�"��)�D�K�0�0�C��8�D� �H�H�%9�6�B�B�E��?�5�?�?�?�@�@�@� �L��"��;�<�<�<�!�6�6������ 	� 	� 	��/�A�/�/�0�0�0��6�6�6�6�6�����	���sa   �G �B
B" �!G �"B&�$G �*BD> �=G �>E� /G �1A
F< �<G�
G �G �
G:�G5�5G:c                 �*  � 	 t          d�  �         t          j        | j        �  �        }|�                    dd��  �        }|j        rt
          d�  �        �| �                    �   �         }t          j	        �   �         }|d         j
        d         }|t          ||�  �        t          ||�  �        ||d         j
        d	d
�         �
                    �   �         d�}||j        t          j        |�  �        <   |�                    �   �         }||d         dk             }|�                    �   �         }t          d
t'          |�  �        � d��  �         || _        |S # t          $ r/}t          d|� ��  �         t          j        �   �         cY d
}~S d
}~ww xY w)z(Hole Marktdaten (EINFACH und FUNKTIONAL)zHole Marktdaten...�30d�1h)�period�intervalzKeine Daten von Yahoo Finance�Close������Volumei����N)�Open�High�LowrF   rH   i�  zMarktdaten: � Datenpunkteu   ❌ Marktdaten Fehler: )r   r6   r7   r   �history�emptyr9   r@   r   �now�iloc�max�min�mean�locr   �	Timestamp�dropna�
sort_indexr   r   r   )r   r>   �hist�
live_price�current_time�
last_close�new_rowr?   s           r    �get_market_dataz+UltimateBitcoinTradingFinal.get_market_datas   s�  � �&	"��&�'�'�'� �)�D�K�(�(�C��;�;�e�d�;�;�;�D��z� 
A�� ?�@�@�@� �,�,�.�.�J� $�<�>�>�L��g��+�B�/�J� #��J�
�3�3��:�z�2�2�#��x�.�-�c�d�d�3�8�8�:�:�� �G� 4;�D�H�R�\�,�/�/�0� �;�;�=�=�D���W�
��,�-�D��?�?�$�$�D��8��T���8�8�8�9�9�9�#�D���K��� 	"� 	"� 	"��/�A�/�/�0�0�0��<�>�>�!�!�!�!�!�!�����	"���s   �EE �
F�#$F
�F�
F�dfc                 �  � 	 |j         st          |�  �        dk     r|S t          d�  �         |d         �                    d�  �        �                    �   �         |d<   |d         �                    d�  �        �                    �   �         |d<   |d         �                    d��  �        �                    �   �         |d	<   |d         �                    �   �         }|�                    |d
k    d
�  �        �                    d��  �        �                    �   �         }|�                    |d
k     d
�  �         �                    d��  �        �                    �   �         }||d
z   z  }ddd|z   z  z
  |d<   |d         �                    d�  �        �	                    d
d�  �        |d<   |d         �                    d��  �        �                    �   �         }|d         �                    d��  �        �                    �   �         }||z
  |d<   |d         �                    d��  �        �                    �   �         |d<   |d         �                    d�  �        �                    �   �         }|d         �                    d�  �        �
                    �   �         }	||	dz  z   |d<   ||	dz  z
  |d<   |d         �                    �   �         �                    d�  �        �
                    �   �         |d<   |�                    d��  �        �                    d��  �        }t          dt          |�  �        � d��  �         |S # t          $ r}
t          d|
� ��  �         |cY d }
~
S d }
~
ww xY w)!z4Berechne technische Indikatoren (EINFACH und ROBUST)�   z"Berechne technische Indikatoren...rF   �
   �SMA_10�SMA_20�   )�span�EMA_12r   �   )�windowg�����|�=�d   �   �RSI�2   �   �MACD�	   �MACD_Signal�   �BB_Upper�BB_Lower�
Volatility�ffill)�method�bfillu&   Technische Indikatoren berechnet für rL   u   ❌ Indikatoren Fehler: N)
rN   r   r   �rollingrS   �ewm�diff�where�fillna�clip�std�
pct_changer9   )r   r^   �delta�gain�loss�rs�ema_12�ema_26�	bb_middle�bb_stdr?   s              r    �calculate_indicatorsz0UltimateBitcoinTradingFinal.calculate_indicators�   s*  � �*	��x� 
�3�r�7�7�R�<�<��	��6�7�7�7� �g�;�.�.�r�2�2�7�7�9�9�B�x�L��g�;�.�.�r�2�2�7�7�9�9�B�x�L��g�;�?�?��?�3�3�8�8�:�:�B�x�L� �w�K�$�$�&�&�E��K�K���	�1�-�-�6�6�b�6�A�A�F�F�H�H�D��[�[����A�.�.�.�7�7�r�7�B�B�G�G�I�I�D�����&�B��s�a�"�f�~�.�B�u�I��5�	�(�(��,�,�1�1�!�S�9�9�B�u�I� ��[�_�_�"�_�-�-�2�2�4�4�F���[�_�_�"�_�-�-�2�2�4�4�F��&��B�v�J� "�6�
���A�� 6� 6� ;� ;� =� =�B�}�� �7��+�+�B�/�/�4�4�6�6�I���[�(�(��,�,�0�0�2�2�F�&�&�1�*�5�B�z�N�&�&�1�*�5�B�z�N�  "�'�{�5�5�7�7�?�?��C�C�G�G�I�I�B�|�� ���'��*�*�1�1��1�A�A�B��P�3�r�7�7�P�P�P�Q�Q�Q��I��� 	� 	� 	��0�Q�0�0�1�1�1��I�I�I�I�I�I�����	���s#   �L4 �LL4 �4
M�>M�M�Mc                 �  � 	 |j         st          |�  �        dk     rt          d�  �         dS t          d�  �         t          j        �   �         }|d         �                    �   �         |d<   |d         |d         z  |d<   |d	         d
z  |d<   |d         |d
<   |d         |d<   |d         �                    d�  �        |d         k    �                    t          �  �        }|�	                    �   �         }|�	                    �   �         }t          t          |�  �        t          |�  �        �  �        }|j        d|�         }|j        d|�         }t          |�  �        dk     rt          d�  �         dS | j        �
                    |�  �        }t          ddd��  �        | _        | j        �                    ||�  �         | j        �                    |�  �        }t%          ||�  �        }t'          d|�  �        | j        d<   t          d|d���  �         dS # t*          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)z,Trainiere ML-Modell (EINFACH und FUNKTIONAL)rl   u&   Nicht genügend Daten für ML-TrainingFzTrainiere ML-Modell...rF   �price_changerc   �	sma_ratiork   ri   �rsirn   �macdrt   �
volatilityrG   N�   u    Nicht genügend bereinigte Datenra   �*   )�n_estimators�	max_depth�random_stater   r   u'   ✅ ML-Modell trainiert - Genauigkeit: �.1%Tu   ❌ ML-Training Fehler: )rN   r   r   r   r   r   �shift�astype�intrV   rR   rP   r   �
fit_transformr   r   �fit�predictr   rQ   r   r9   )	r   r^   �features�target�min_len�features_scaled�predictionsr   r?   s	            r    �train_modelz'UltimateBitcoinTradingFinal.train_model�   sN  � �5	��x� 
�3�r�7�7�R�<�<��>�?�?�?��u��*�+�+�+� �|�~�~�H�')�'�{�'=�'=�'?�'?�H�^�$�$&�w�K�"�X�,�$>�H�[�!� ��i�#�o�H�U�O�!�&�z�H�V��%'��%5�H�\�"� ��k�'�'��+�+�b��k�9�A�A�#�F�F�F�  ���(�(�H��]�]�_�_�F� �#�h�-�-��V���5�5�G��}�X�g�X�.�H��[��'��*�F��8�}�}�r�!�!��8�9�9�9��u� #�k�7�7��A�A�O� 2����� � �D�M� 
�M���o�v�6�6�6� �-�/�/��@�@�K����4�4�H�%(��H�%5�%5�D�J�z�"��J�H�J�J�J�K�K�K��4��� 	� 	� 	��0�Q�0�0�1�1�1��5�5�5�5�5�����	���s$   �)H �EH �5B!H �
H?�"H:�:H?c           	      �z  � 	 t          d�  �         |d         j        d         }|d         j        d         }|d         j        d         }|d         j        d         }|d         j        d         }d}|d	k     r|d
z
  }n|dk    r|d
z  }||k    r|dz
  }n|dz  }||k    r|dz
  }n|dz  }d}| j        r�	 t          j        �   �         }	|d         �                    �   �         j        d         g|	d
<   ||z  g|	d<   |dz  g|	d<   |g|	d<   |d         j        d         g|	d<   | j        �                    |	�  �        }
| j        �                    |
�  �        d         }n#  Y nxY w|dz  |dz  z   }|dk    rd}t          d|�  �        }
n3|dk     rd}t          dd|z
  �  �        }
nd}dt          |dz
  �  �        z   }
||
|||||t          j        �   �         �
                    �   �         d�}t          d|� d |
d!�d"��  �         |S # t          $ r0}t          d#|� ��  �         ddd$t          |�  �        d%�cY d&}~S d&}~ww xY w)'z,Erstelle Vorhersage (EINFACH und FUNKTIONAL)zErstelle Vorhersage...rF   rG   rk   rn   rp   rc   g      �?r�   g�������?�F   g�������?r�   r�   ri   r�   r�   rt   r�   r   g333333�?g�������?g�������?�KAUFENg�������?gffffff�?�	VERKAUFENrj   r   )�signal�
confidence�
current_price�
tech_score�ml_score�combined_scorer�   �	timestampu   🎯 Vorhersage: �
 (Konfidenz: r�   �)u   ❌ Vorhersage Fehler: r-   )r�   r�   r�   �errorN)r   rP   r   r   r   r   r   �	transformr�   rR   �absr   rO   �	isoformatr9   �str)r   r^   r�   r�   r�   �macd_signal�sma_20r�   r�   r�   r�   r�   r�   r�   �
predictionr?   s                   r    �make_predictionz+UltimateBitcoinTradingFinal.make_prediction  s�  � �P	��*�+�+�+��w�K�,�R�0�M� �U�)�.��$�C��f�:�?�2�&�D��]�+�0��4�K���\�&�r�*�F� �J��R�x�x��c�!�
�
��r����c�!�
��k�!�!��c�!�
�
��c�!�
��v�%�%��c�!�
�
��c�!�
� �H��}� 
��!�|�~�~�H�02�7��0F�0F�0H�0H�0M�b�0Q�/R�H�^�,�-:�V�-C�,D�H�[�)�'*�S�y�k�H�U�O�(,�v�H�V�$�.0��.>�.C�B�.G�-H�H�\�*�&*�k�&;�&;�H�&E�&E�O�#�}�4�4�_�E�E�a�H�H�H����D���� )�3�.�8�c�>�B�N� ��$�$�!�� ��n�5�5�
�
��$�&�&�$�� ��a�.�&8�9�9�
�
�!�� �3�~��';�#<�#<�<�
� !�(�!.�(�$�"0��%�\�^�^�5�5�7�7�	� 	�J� 
�L�f�L�L�:�L�L�L�L�M�M�M����� 	� 	� 	��/�A�/�/�0�0�0�"�!�!'��Q���	� � 
� 
� 
� 
� 
� 
�����	���s8   �B2H  �5B%E �H  �E�B"H  � 
H:�
%H5�/H:�5H:c                 �  � 	 t          d�  �         t          d�  �         t          d�  �         t          j        �   �         }| j        dxx         dz
  cc<   | �                    �   �         }|j        rt          d�  �        �| �                    |�  �        }| j        d         dz  dk    r| �                    |�  �         | �                    |�  �        }t          j        �   �         |z
  }| j        d         t          j
        �   �         �                    �   �         |t          |�  �        || j        d         | j
        d�}|d	         | j        d
<   |d         | j        d<   t          d
| j        d         � d|d�d��  �         t          d|d	         d���  �         t          d|d         � d|d         d�d��  �         t          d| j        d         d���  �         |S # t
          $ rc}t          d|� ��  �         | j        d         t          j
        �   �         �                    �   �         t          |�  �        | j
        d�cY d}~S d}~ww xY w)u,   Führe kompletten Scan durch (HAUPTFUNKTION)�<============================================================z+STARTE BITCOIN TRADING SCAN - FINAL VERSIONr   rj   u   Keine Marktdaten verfügbarr%   r   )�scan_idr�   �	scan_time�data_pointsr�   �model_accuracy�versionr�   r   r�   r   u
   ✅ Scan #z abgeschlossen in �.2f�s�   💰 Bitcoin-Preis: $r)   �
   📊 Signal: r�   r�   r�   r�   u   🎯 ML-Genauigkeit: u   ❌ SCAN FEHLER: )r�   r�   r�   r�   N)r   �timer   r]   rN   r9   r�   r�   r�   r   rO   r�   r   r   r�   )r   �
start_timer^   r�   r�   �resultr?   s          r    �run_scanz$UltimateBitcoinTradingFinal.run_scanX  s�  � �6	��(�O�O�O��?�@�@�@��(�O�O�O�����J��J�w����1�$���� �%�%�'�'�B��x� 
?�� =�>�>�>� �*�*�2�.�.�B� �z�'�"�Q�&�!�+�+�� � ��$�$�$� �-�-�b�1�1�J� �	���j�0�I�  �:�g�.�%�\�^�^�5�5�7�7�&�"�2�w�w�(�"&�*�Z�"8��<�� �F� (2�/�'B�D�J�|�$�(2�8�(<�D�J�}�%��V�t�z�'�2�V�V�i�V�V�V�V�W�W�W��L�*�_�*E�L�L�L�M�M�M��d�*�X�"6�d�d�Z�P\�E]�d�d�d�d�e�e�e��F�$�*�Z�*@�F�F�F�G�G�G��M��� 	� 	� 	��)�a�)�)�*�*�*��:�g�.�%�\�^�^�5�5�7�7��Q����<�	� � 
� 
� 
� 
� 
� 
�����	���s   �GG �
I
�'AI�?I
�I
N)�__name__�
__module__�__qualname__�__doc__r!   r2   r@   r   r   r]   r�   �boolr�   �dictr�   r�   � r"   r    r
   r
      s�   � � � � � �� �9� 9� 9�82�� 2� 2� 2� 2�h("��� ("� ("� ("� ("�T,�r�|� ,��� ,� ,� ,� ,�\7�b�l� 7�t� 7� 7� 7� 7�rR�"�,� R�4� R� R� R� R�h8�$� 8� 8� 8� 8� 8� 8r"   r
   c                  �4  � 	 t          d�  �         t          d�  �         t          �   �         } | �                    �   �         }t          d�  �         t          d�  �         t          d�  �         d|vr�|d         }t          d�  �         t          d|d	         � ��  �         t          d
|d         d���  �         t          d
|d         d���  �         t          d|�                    dd�  �        d���  �         t          d|d         d�d��  �         t          d|d         � ��  �         t          d|d         d���  �         nt          d|d         � ��  �         t          d�  �         |S # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w) u!   Hauptfunktion - SOFORT LAUFFÄHIGz/ULTIMATE BITCOIN TRADING SYSTEM - FINAL VERSIONr�   z=
============================================================zSCAN-ERGEBNIS:r�   r�   u   ✅ ERFOLGREICH!r�   r�   u   🎯 Konfidenz: r�   r�   r�   r�   r)   u
   📈 RSI: r�   r   z.1fu   ⏱️ Scan-Zeit: r�   r�   r�   u   📊 Datenpunkte: r�   u   🤖 ML-Genauigkeit: r�   u   ❌ FEHLER: u.   
🚀 FINAL VERSION - FUNKTIONIERT GARANTIERT!u   ❌ HAUPTFUNKTION FEHLER: N)r   r
   r�   r/   r9   )�systemr�   r�   r?   s       r    �mainr�   �  s�  � �!�
�?�@�@�@�
�h���� -�.�.�� ���"�"�� 	�o����
�����
�h�����&� � ���-�J��%�&�&�&��8�*�X�"6�8�8�9�9�9��C�Z��%=�C�C�C�D�D�D��L�*�_�*E�L�L�L�M�M�M��=�z�~�~�e�Q�7�7�=�=�=�>�>�>��A�v�k�':�A�A�A�A�B�B�B��>�v�m�'<�>�>�?�?�?��H�&�1A�*B�H�H�H�I�I�I�I��2����2�2�3�3�3�
�@�A�A�A��
��� � � �
�.�1�.�.�/�/�/��t�t�t�t�t��������s   �E-E0 �0
F�:F�F�__main__)r�   �yfinancer6   �pandasr   �numpyr4   r.   r�   r   r   �warnings�filterwarnings�sklearn.preprocessingr   �sklearn.ensembler   �sklearn.metricsr   r
   r�   r�   r�   r"   r    �<module>r�      s  ��
� 
� � � � � � � � � � � � � ���� ���� (� (� (� (� (� (� (� (� ���� �� �� !� !� !� /� .� .� .� .� .� 2� 2� 2� 2� 2� 2� $� $� $� $� $� $�t� t� t� t� t� t� t� t�n#� #� #�J �z����D�F�F�F�F�F� �r"   