#!/usr/bin/env python3
"""
ULTIMATE OPTIMIZED BITCOIN PREDICTION MODEL - KERAS 3 KOMPATIBEL
Kombiniert die besten Elemente aus allen Scripts für maximale Performance
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import (LSTM, GRU, Dense, Dropout, BatchNormalization, 
                                   Bidirectional, Input, Concatenate, Add, MultiHeadAttention, LayerNormalization)
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.optimizers import Adam, RMSprop  # FIXED: Entfernt .legacy
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import TimeSeriesSplit
import warnings
import time
import os
warnings.filterwarnings('ignore')

print("🚀 ULTIMATE OPTIMIZED BITCOIN PREDICTION MODEL - KERAS 3 FIXED")
print("=" * 70)
print("🎯 Kombiniert die BESTEN Elemente aus allen Scripts")
print("💻 Ziel: >95% Genauigkeit mit optimaler Performance")
print("=" * 70)

# ULTIMATE OPTIMIERTE KONFIGURATION
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.85,
    'validation_split': 0.1,
    'look_back': 48,
    'future_steps': 12,
    'batch_size': 64,
    'epochs': 100,
    'patience': 20,
    'n_features_select': 50,
    'ensemble_models': 5,
    'monte_carlo_sims': 1000,
    'target_accuracy': 0.95
}

# MAXIMALE CPU/GPU OPTIMIERUNG
print("⚙️  Optimiere Hardware...")
tf.config.threading.set_intra_op_parallelism_threads(0)
tf.config.threading.set_inter_op_parallelism_threads(0)
os.environ['OMP_NUM_THREADS'] = str(os.cpu_count())

# GPU-Optimierung
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"🎮 GPU aktiviert: {len(gpus)} GPU(s)")
    except RuntimeError as e:
        print(f"GPU-Konfiguration: {e}")

print(f"💻 CPU-Kerne: {os.cpu_count()}")

class UltimateOptimizedPredictor:
    """Ultimate Optimized Bitcoin Predictor - Keras 3 Kompatibel"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_selector = None
        self.selected_features = None
        self.performance_history = []
    
    def load_and_create_ultimate_features(self):
        """Ultimate Feature Engineering - Kompakt aber effektiv"""
        print("\n📊 ULTIMATE FEATURE ENGINEERING")
        print("-" * 50)
        
        df = pd.read_csv(CONFIG['data_file'])
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        print(f"📈 Daten: {len(df)} Punkte")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
        
        # Basis OHLCV
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # === PREIS-FEATURES ===
        print("   🔧 Erstelle Preis-Features...")
        features['returns'] = df['close'].pct_change()
        features['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        features['hl_ratio'] = df['high'] / df['low']
        features['co_ratio'] = df['close'] / df['open']
        features['body'] = abs(df['close'] - df['open'])
        
        # === MOVING AVERAGES ===
        print("   📈 Erstelle Moving Averages...")
        ma_periods = [5, 10, 20, 50]
        for period in ma_periods:
            if period <= len(df):
                features[f'sma_{period}'] = df['close'].rolling(period).mean()
                features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
                features[f'price_sma_{period}_ratio'] = df['close'] / features[f'sma_{period}']
        
        # === MOMENTUM ===
        print("   ⚡ Erstelle Momentum-Indikatoren...")
        # RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        features['macd'] = ema_12 - ema_26
        features['macd_signal'] = features['macd'].ewm(span=9).mean()
        features['macd_histogram'] = features['macd'] - features['macd_signal']
        
        # === VOLATILITÄT ===
        print("   📊 Erstelle Volatilitäts-Indikatoren...")
        # Bollinger Bands
        sma_20 = df['close'].rolling(20).mean()
        std_20 = df['close'].rolling(20).std()
        features['bb_upper'] = sma_20 + (std_20 * 2)
        features['bb_lower'] = sma_20 - (std_20 * 2)
        features['bb_width'] = (features['bb_upper'] - features['bb_lower']) / sma_20
        features['bb_position'] = (df['close'] - features['bb_lower']) / (features['bb_upper'] - features['bb_lower'])
        
        # ATR
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        features['atr'] = true_range.rolling(14).mean()
        
        # === VOLUMEN ===
        print("   📦 Erstelle Volumen-Indikatoren...")
        features['volume_sma'] = df['volume'].rolling(20).mean()
        features['volume_ratio'] = df['volume'] / features['volume_sma']
        
        # === ZEIT-FEATURES ===
        print("   🕐 Erstelle Zeit-Features...")
        features['hour'] = df.index.hour
        features['day_of_week'] = df.index.dayofweek
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
        features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
        
        # === CLEANUP ===
        features = features.dropna()
        
        print(f"   ✅ {len(features.columns)} Features erstellt")
        print(f"   📊 {len(features)} saubere Datenpunkte")
        
        return features
    
    def intelligent_feature_selection(self, features):
        """Intelligente Feature-Selektion"""
        print("\n🧠 INTELLIGENTE FEATURE-SELEKTION")
        print("-" * 40)
        
        X = features.drop('close', axis=1)
        y = features['close']
        
        print(f"📊 Vor Selektion: {X.shape[1]} Features")
        
        # SelectKBest
        self.feature_selector = SelectKBest(score_func=f_regression, k=min(CONFIG['n_features_select'], X.shape[1]))
        X_selected = self.feature_selector.fit_transform(X, y)
        
        # Selektierte Features
        selected_mask = self.feature_selector.get_support()
        self.selected_features = X.columns[selected_mask].tolist()
        
        print(f"✅ Nach Selektion: {len(self.selected_features)} Features")
        
        # Top Features
        feature_scores = self.feature_selector.scores_[selected_mask]
        top_features = sorted(zip(self.selected_features, feature_scores), key=lambda x: x[1], reverse=True)
        
        print(f"\n🏆 TOP 5 FEATURES:")
        for i, (feature, score) in enumerate(top_features[:5]):
            print(f"   {i+1}. {feature}: {score:.2f}")
        
        # DataFrame mit selektierten Features
        selected_features_df = pd.DataFrame(X_selected, columns=self.selected_features, index=features.index)
        selected_features_df['close'] = features['close']
        
        return selected_features_df
    
    def create_sequences(self, data, target, look_back):
        """Sequenz-Erstellung"""
        X, y = [], []
        for i in range(look_back, len(data)):
            X.append(data[i-look_back:i])
            y.append(target[i])
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)
    
    def prepare_data(self, features):
        """Datenaufbereitung"""
        print("\n🔄 DATENAUFBEREITUNG")
        print("-" * 25)
        
        # Feature Selection
        features_selected = self.intelligent_feature_selection(features)
        
        # Features und Target
        X = features_selected.drop('close', axis=1)
        y = features_selected['close'].values
        
        print(f"📊 Finale Features: {X.shape[1]}")
        print(f"📊 Samples: {len(y)}")
        
        # Skalierung
        print("   🔧 Skaliere Daten...")
        feature_scaler = RobustScaler()
        target_scaler = StandardScaler()
        
        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        
        # Sequenzen
        print(f"   📦 Erstelle Sequenzen...")
        X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, CONFIG['look_back'])
        
        print(f"   ✅ {len(X_seq)} Sequenzen erstellt")
        
        # Speichere Scaler
        self.scalers['feature'] = feature_scaler
        self.scalers['target'] = target_scaler
        
        return X_seq, y_seq, self.selected_features
    
    def split_data(self, X, y):
        """Datenaufteilung"""
        print("\n✂️  DATENAUFTEILUNG")
        print("-" * 20)
        
        total_size = len(X)
        train_size = int(total_size * CONFIG['train_split'])
        val_size = int(total_size * CONFIG['validation_split'])
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        X_val = X[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        X_test = X[train_size+val_size:]
        y_test = y[train_size+val_size:]
        
        print(f"📊 Training: {len(X_train)} ({len(X_train)/total_size*100:.1f}%)")
        print(f"📊 Validation: {len(X_val)} ({len(X_val)/total_size*100:.1f}%)")
        print(f"📊 Test: {len(X_test)} ({len(X_test)/total_size*100:.1f}%)")
        
        return (X_train, y_train), (X_val, y_val), (X_test, y_test)

    def build_lstm_model(self, input_shape):
        """Optimiertes LSTM Modell - Keras 3 Kompatibel"""
        print("🧠 Baue LSTM Modell...")

        model = Sequential(name='LSTMPredictor')

        # LSTM Layers
        model.add(LSTM(128, return_sequences=True, dropout=0.2, input_shape=input_shape))
        model.add(BatchNormalization())

        model.add(LSTM(64, return_sequences=True, dropout=0.2))
        model.add(BatchNormalization())

        model.add(LSTM(32, return_sequences=False, dropout=0.2))
        model.add(BatchNormalization())

        # Dense Layers
        model.add(Dense(64, activation='relu'))
        model.add(Dropout(0.3))

        model.add(Dense(32, activation='relu'))
        model.add(Dropout(0.2))

        model.add(Dense(1))

        # Kompiliere mit Keras 3 kompatiblem Optimizer
        optimizer = Adam(learning_rate=0.001)
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae'])

        print(f"   ✅ LSTM: {model.count_params():,} Parameter")
        return model

    def build_gru_model(self, input_shape):
        """Optimiertes GRU Modell - Keras 3 Kompatibel"""
        print("⚡ Baue GRU Modell...")

        model = Sequential(name='GRUPredictor')

        # GRU Layers
        model.add(GRU(128, return_sequences=True, dropout=0.2, input_shape=input_shape))
        model.add(BatchNormalization())

        model.add(GRU(64, return_sequences=True, dropout=0.2))
        model.add(BatchNormalization())

        model.add(GRU(32, return_sequences=False, dropout=0.2))
        model.add(BatchNormalization())

        # Dense Layers
        model.add(Dense(64, activation='relu'))
        model.add(Dropout(0.3))

        model.add(Dense(32, activation='relu'))
        model.add(Dropout(0.2))

        model.add(Dense(1))

        optimizer = RMSprop(learning_rate=0.001)
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae'])

        print(f"   ✅ GRU: {model.count_params():,} Parameter")
        return model

    def build_bidirectional_model(self, input_shape):
        """Bidirectional LSTM - Keras 3 Kompatibel"""
        print("🔄 Baue Bidirectional Modell...")

        model = Sequential(name='BidirectionalPredictor')

        # Bidirectional LSTM
        model.add(Bidirectional(LSTM(64, return_sequences=True, dropout=0.2), input_shape=input_shape))
        model.add(BatchNormalization())

        model.add(Bidirectional(LSTM(32, return_sequences=False, dropout=0.2)))
        model.add(BatchNormalization())

        # Dense Layers
        model.add(Dense(64, activation='relu'))
        model.add(Dropout(0.3))

        model.add(Dense(32, activation='relu'))
        model.add(Dropout(0.2))

        model.add(Dense(1))

        optimizer = Adam(learning_rate=0.0005)
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae'])

        print(f"   ✅ Bidirectional: {model.count_params():,} Parameter")
        return model

    def train_model(self, model, X_train, y_train, X_val, y_val, model_name):
        """Optimiertes Training"""
        print(f"\n🎯 Trainiere {model_name}...")

        # Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=CONFIG['patience'],
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=CONFIG['patience']//2,
                min_lr=1e-7,
                verbose=1
            )
        ]

        start_time = time.time()

        # Training
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            callbacks=callbacks,
            verbose=1
        )

        training_time = time.time() - start_time

        print(f"   ✅ {model_name} Training: {training_time:.1f}s")

        return model, history, training_time

    def evaluate_model(self, model, X_test, y_test, model_name):
        """Modell-Evaluation"""
        print(f"\n📊 Evaluiere {model_name}...")

        # Vorhersagen
        y_pred = model.predict(X_test, verbose=0)

        # Skalierung rückgängig
        y_test_orig = self.scalers['target'].inverse_transform(y_test.reshape(-1, 1)).flatten()
        y_pred_orig = self.scalers['target'].inverse_transform(y_pred).flatten()

        # Metriken
        r2 = r2_score(y_test_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
        mae = mean_absolute_error(y_test_orig, y_pred_orig)
        mape = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig)) * 100

        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(y_pred_orig) > 0
            direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            direction_acc = 0

        # Accuracy Bands
        accuracy_5pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.05) * 100

        results = {
            'model_name': model_name,
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'mape': mape,
            'direction_accuracy': direction_acc,
            'accuracy_5pct': accuracy_5pct,
            'y_test_orig': y_test_orig,
            'y_pred_orig': y_pred_orig
        }

        print(f"   📈 R²: {r2:.4f} ({r2*100:.1f}%)")
        print(f"   💰 RMSE: ${rmse:.2f}")
        print(f"   📊 MAPE: {mape:.2f}%")
        print(f"   🎯 Direction Acc: {direction_acc:.1f}%")
        print(f"   ✅ 5% Accuracy: {accuracy_5pct:.1f}%")

        return results

    def create_ensemble(self, models_results):
        """Ensemble-Erstellung"""
        print(f"\n🏆 ENSEMBLE aus {len(models_results)} Modellen")
        print("-" * 40)

        # Sammle Vorhersagen
        predictions = []
        weights = []

        for result in models_results:
            predictions.append(result['y_pred_orig'])
            # Gewicht basierend auf R²
            weight = max(0.1, result['r2'])
            weights.append(weight)
            print(f"   {result['model_name']}: R²={result['r2']:.4f}, Gewicht={weight:.3f}")

        # Normalisiere Gewichte
        weights = np.array(weights)
        weights = weights / np.sum(weights)

        # Gewichtete Vorhersage
        ensemble_pred = np.average(predictions, axis=0, weights=weights)

        # Evaluation
        y_test_orig = models_results[0]['y_test_orig']

        ensemble_r2 = r2_score(y_test_orig, ensemble_pred)
        ensemble_rmse = np.sqrt(mean_squared_error(y_test_orig, ensemble_pred))
        ensemble_mape = np.mean(np.abs((y_test_orig - ensemble_pred) / y_test_orig)) * 100

        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(ensemble_pred) > 0
            ensemble_direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            ensemble_direction_acc = 0

        ensemble_result = {
            'model_name': 'Ensemble',
            'r2': ensemble_r2,
            'rmse': ensemble_rmse,
            'mape': ensemble_mape,
            'direction_accuracy': ensemble_direction_acc,
            'weights': weights,
            'y_test_orig': y_test_orig,
            'y_pred_orig': ensemble_pred
        }

        print(f"\n🎯 ENSEMBLE PERFORMANCE:")
        print(f"   📈 R²: {ensemble_r2:.4f} ({ensemble_r2*100:.1f}%)")
        print(f"   💰 RMSE: ${ensemble_rmse:.2f}")
        print(f"   📊 MAPE: {ensemble_mape:.2f}%")
        print(f"   🎯 Direction Acc: {ensemble_direction_acc:.1f}%")

        return ensemble_result

    def plot_results(self, models_results, ensemble_result):
        """Visualisierung"""
        print(f"\n📈 VISUALISIERUNG")
        print("-" * 20)

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('🚀 ULTIMATE OPTIMIZED BITCOIN PREDICTION - RESULTS', fontsize=16, fontweight='bold')

        # 1. Performance Vergleich
        all_results = models_results + [ensemble_result]
        names = [r['model_name'] for r in all_results]
        r2_scores = [r['r2'] for r in all_results]

        axes[0, 0].bar(names, r2_scores, color=['blue', 'green', 'orange', 'gold'])
        axes[0, 0].set_title('R² Score Vergleich')
        axes[0, 0].set_ylabel('R² Score')
        axes[0, 0].tick_params(axis='x', rotation=45)
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].axhline(y=CONFIG['target_accuracy'], color='red', linestyle='--', label=f'Ziel: {CONFIG["target_accuracy"]*100:.0f}%')
        axes[0, 0].legend()

        # 2. RMSE Vergleich
        rmse_scores = [r['rmse'] for r in all_results]
        axes[0, 1].bar(names, rmse_scores, color='lightcoral')
        axes[0, 1].set_title('RMSE Vergleich')
        axes[0, 1].set_ylabel('RMSE ($)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        axes[0, 1].grid(True, alpha=0.3)

        # 3. Direction Accuracy
        direction_accs = [r['direction_accuracy'] for r in all_results]
        axes[0, 2].bar(names, direction_accs, color='lightgreen')
        axes[0, 2].set_title('Richtungsgenauigkeit')
        axes[0, 2].set_ylabel('Genauigkeit (%)')
        axes[0, 2].tick_params(axis='x', rotation=45)
        axes[0, 2].grid(True, alpha=0.3)

        # 4. Ensemble Vorhersage
        y_test = ensemble_result['y_test_orig'][:50]
        y_pred = ensemble_result['y_pred_orig'][:50]

        axes[1, 0].plot(y_test, 'g-', label='Actual', linewidth=2)
        axes[1, 0].plot(y_pred, 'r--', label='Ensemble', linewidth=2)
        axes[1, 0].set_title('Ensemble Vorhersage')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 5. Scatter Plot
        axes[1, 1].scatter(ensemble_result['y_test_orig'], ensemble_result['y_pred_orig'], alpha=0.6)
        min_val = min(ensemble_result['y_test_orig'].min(), ensemble_result['y_pred_orig'].min())
        max_val = max(ensemble_result['y_test_orig'].max(), ensemble_result['y_pred_orig'].max())
        axes[1, 1].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
        axes[1, 1].set_title('Scatter Plot')
        axes[1, 1].set_xlabel('Actual')
        axes[1, 1].set_ylabel('Predicted')
        axes[1, 1].grid(True, alpha=0.3)

        # 6. Summary
        axes[1, 2].axis('off')
        best_model = max(all_results, key=lambda x: x['r2'])
        target_reached = "✅ JA" if ensemble_result['r2'] >= CONFIG['target_accuracy'] else "❌ NEIN"

        summary_text = f"""
🏆 RESULTS SUMMARY

🎯 ZIEL ERREICHT: {target_reached}
   Target: {CONFIG['target_accuracy']*100:.0f}%
   Ensemble: {ensemble_result['r2']*100:.1f}%

🚀 BESTE PERFORMANCE:
   {best_model['model_name']}
   R²: {best_model['r2']*100:.1f}%

📊 FEATURES: {len(self.selected_features)}
🤖 MODELLE: {len(models_results)}
        """

        axes[1, 2].text(0.1, 0.5, summary_text, fontsize=12, verticalalignment='center',
                        bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()
        plt.show()

        print("   ✅ Visualisierung abgeschlossen")

def main():
    """ULTIMATE HAUPTFUNKTION - KERAS 3 KOMPATIBEL"""
    print("🚀 STARTE ULTIMATE OPTIMIZED SYSTEM")
    print("=" * 50)

    start_time = time.time()

    # System initialisieren
    predictor = UltimateOptimizedPredictor()

    # === DATENAUFBEREITUNG ===
    print(f"\n📊 DATENAUFBEREITUNG")
    print("=" * 30)

    # Features erstellen
    features = predictor.load_and_create_ultimate_features()

    # Daten vorbereiten
    X, y, selected_features = predictor.prepare_data(features)

    # Daten aufteilen
    (X_train, y_train), (X_val, y_val), (X_test, y_test) = predictor.split_data(X, y)

    print(f"\n✅ DATENAUFBEREITUNG ABGESCHLOSSEN!")
    print(f"   📊 {len(selected_features)} Features")
    print(f"   📦 {len(X)} Sequenzen")

    # === MODELL-TRAINING ===
    print(f"\n🤖 MODELL-TRAINING")
    print("=" * 25)

    input_shape = (X_train.shape[1], X_train.shape[2])
    models_results = []

    # 1. LSTM Model
    try:
        print(f"\n🔥 Modell 1/3: LSTM")
        lstm_model = predictor.build_lstm_model(input_shape)
        lstm_model, _, _ = predictor.train_model(
            lstm_model, X_train, y_train, X_val, y_val, 'LSTM'
        )
        lstm_results = predictor.evaluate_model(
            lstm_model, X_test, y_test, 'LSTM'
        )
        models_results.append(lstm_results)
        predictor.models['lstm'] = lstm_model
    except Exception as e:
        print(f"❌ LSTM Fehler: {e}")

    # 2. GRU Model
    try:
        print(f"\n🔥 Modell 2/3: GRU")
        gru_model = predictor.build_gru_model(input_shape)
        gru_model, _, _ = predictor.train_model(
            gru_model, X_train, y_train, X_val, y_val, 'GRU'
        )
        gru_results = predictor.evaluate_model(
            gru_model, X_test, y_test, 'GRU'
        )
        models_results.append(gru_results)
        predictor.models['gru'] = gru_model
    except Exception as e:
        print(f"❌ GRU Fehler: {e}")

    # 3. Bidirectional Model
    try:
        print(f"\n🔥 Modell 3/3: Bidirectional")
        bidirectional_model = predictor.build_bidirectional_model(input_shape)
        bidirectional_model, _, _ = predictor.train_model(
            bidirectional_model, X_train, y_train, X_val, y_val, 'Bidirectional'
        )
        bidirectional_results = predictor.evaluate_model(
            bidirectional_model, X_test, y_test, 'Bidirectional'
        )
        models_results.append(bidirectional_results)
        predictor.models['bidirectional'] = bidirectional_model
    except Exception as e:
        print(f"❌ Bidirectional Fehler: {e}")

    # === ENSEMBLE ===
    if models_results:
        ensemble_result = predictor.create_ensemble(models_results)

        # === FINALE ANALYSE ===
        print(f"\n🏆 FINALE ANALYSE")
        print("=" * 30)

        total_time = time.time() - start_time

        # Sortiere Modelle
        sorted_results = sorted(models_results, key=lambda x: x['r2'], reverse=True)

        print(f"\n📊 MODELL-RANKING:")
        for i, result in enumerate(sorted_results):
            print(f"   {i+1}. {result['model_name']}: {result['r2']*100:.1f}% R²")

        print(f"\n🎯 ENSEMBLE: {ensemble_result['r2']*100:.1f}% R²")

        # Ziel-Check
        if ensemble_result['r2'] >= CONFIG['target_accuracy']:
            print(f"\n🎉🎉🎉 ZIEL ERREICHT! 🎉🎉🎉")
            print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
        else:
            print(f"\n💪 STARKE PERFORMANCE!")
            print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
            gap = (CONFIG['target_accuracy'] - ensemble_result['r2']) * 100
            print(f"Noch {gap:.1f} Prozentpunkte bis zum Ziel")

        # Performance-Details
        print(f"\n📈 PERFORMANCE:")
        print(f"   R²: {ensemble_result['r2']:.4f} ({ensemble_result['r2']*100:.1f}%)")
        print(f"   RMSE: ${ensemble_result['rmse']:.2f}")
        print(f"   MAPE: {ensemble_result['mape']:.2f}%")
        print(f"   Direction Acc: {ensemble_result['direction_accuracy']:.1f}%")

        # System-Stats
        print(f"\n⚡ SYSTEM:")
        print(f"   Zeit: {total_time:.1f}s")
        print(f"   Modelle: {len(models_results)}")
        print(f"   Features: {len(selected_features)}")
        print(f"   CPU-Kerne: {os.cpu_count()}")

        # Visualisierung
        predictor.plot_results(models_results, ensemble_result)

        # Finale Bewertung
        if ensemble_result['r2'] >= 0.95:
            print(f"\n🎉🎉🎉 EXZELLENT! ZIEL ERREICHT! 🎉🎉🎉")
        elif ensemble_result['r2'] >= 0.90:
            print(f"\n🔥🔥 HERVORRAGEND! 🔥🔥")
        elif ensemble_result['r2'] >= 0.80:
            print(f"\n💪💪 SEHR GUT! 💪💪")
        else:
            print(f"\n✅ GUTE BASIS!")

        print(f"\n✅ ULTIMATE OPTIMIZED SYSTEM ABGESCHLOSSEN!")

        return ensemble_result

    else:
        print(f"\n❌ Keine Modelle erfolgreich trainiert!")
        return None

if __name__ == "__main__":
    result = main()

    if result and result['r2'] >= CONFIG['target_accuracy']:
        print(f"\n🎯 MISSION ACCOMPLISHED! 🎯")
    else:
        print(f"\n🔧 WEITERE OPTIMIERUNG MÖGLICH")
