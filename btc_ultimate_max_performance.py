#!/usr/bin/env python3
"""
ULTIMATE BITCOIN PREDICTION MODEL - MAXIMUM PERFORMANCE
Holt ALLES aus CPU und GPU heraus!
Vergleicht alle Modelle und gibt finale Prognose
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, Bidirectional, BatchNormalization, Attention
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.optimizers import <PERSON>, RMSprop, AdamW
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
import xgboost as xgb
import lightgbm as lgb
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
import multiprocessing as mp
import time
import os
import warnings
warnings.filterwarnings('ignore')

# MAXIMALE HARDWARE-AUSLASTUNG
print(f"🚀 AKTIVIERE MAXIMUM PERFORMANCE MODE!")
print(f"💻 CPU-Kerne: {os.cpu_count()}")

# GPU-Konfiguration
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"🎮 GPU gefunden: {len(gpus)} GPU(s) aktiviert!")
    except RuntimeError as e:
        print(f"GPU-Konfiguration: {e}")
else:
    print("💻 Nur CPU verfügbar - maximiere CPU-Performance!")

# CPU-Optimierung
tf.config.threading.set_intra_op_parallelism_threads(0)
tf.config.threading.set_inter_op_parallelism_threads(0)
os.environ['OMP_NUM_THREADS'] = str(os.cpu_count())
os.environ['TF_NUM_INTEROP_THREADS'] = str(os.cpu_count())
os.environ['TF_NUM_INTRAOP_THREADS'] = str(os.cpu_count())

# ULTIMATE KONFIGURATION
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.75,
    'look_back_options': [12, 24, 48, 72],  # Verschiedene Sequenzlängen testen
    'batch_sizes': [16, 32, 64, 128],       # Verschiedene Batch-Größen
    'epochs': 100,
    'patience': 15,
    'learning_rates': [0.001, 0.005, 0.01], # Verschiedene LRs
    'ensemble_size': 7,                      # Mehr Modelle
    'monte_carlo_sims': 1000,               # Mehr Simulationen
    'future_hours': [6, 12, 24, 48, 72],   # Verschiedene Prognosehorizonte
    'use_all_models': True,                 # Alle Modelltypen
    'parallel_training': True               # Paralleles Training
}

def load_and_create_ultimate_features():
    """Ultimate Feature-Engineering mit ALLEN Indikatoren"""
    print("📊 Erstelle ULTIMATE Feature-Set...")
    
    df = pd.read_csv(CONFIG['data_file'])
    df['time'] = pd.to_datetime(df['time'])
    df.set_index('time', inplace=True)
    
    print(f"✅ {len(df)} Datenpunkte geladen")
    
    # Basis OHLCV
    features = df[['open', 'high', 'low', 'close', 'volume']].copy()
    
    # === ALLE MOVING AVERAGES ===
    ma_periods = [5, 7, 10, 14, 20, 21, 26, 30, 50, 100, 200]
    for period in ma_periods:
        if period <= len(df):
            features[f'sma_{period}'] = df['close'].rolling(period).mean()
            features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
            features[f'wma_{period}'] = df['close'].rolling(period).apply(
                lambda x: np.sum(x * np.arange(1, len(x) + 1)) / np.sum(np.arange(1, len(x) + 1))
            )
    
    # === MACD FAMILIE ===
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    features['macd'] = ema_12 - ema_26
    features['macd_signal'] = features['macd'].ewm(span=9).mean()
    features['macd_histogram'] = features['macd'] - features['macd_signal']
    features['macd_slope'] = features['macd'].diff()
    features['macd_acceleration'] = features['macd_slope'].diff()
    
    # === RSI FAMILIE ===
    for period in [9, 14, 21, 25]:
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
        rs = gain / loss
        features[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        features[f'rsi_{period}_slope'] = features[f'rsi_{period}'].diff()
    
    # === BOLLINGER BANDS FAMILIE ===
    for period in [10, 20, 50]:
        sma = df['close'].rolling(period).mean()
        std = df['close'].rolling(period).std()
        features[f'bb_upper_{period}'] = sma + (std * 2)
        features[f'bb_lower_{period}'] = sma - (std * 2)
        features[f'bb_middle_{period}'] = sma
        features[f'bb_width_{period}'] = (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}']) / sma
        features[f'bb_position_{period}'] = (df['close'] - features[f'bb_lower_{period}']) / (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}'])
        features[f'bb_squeeze_{period}'] = features[f'bb_width_{period}'].rolling(20).min() == features[f'bb_width_{period}']
    
    # === STOCHASTIC FAMILIE ===
    for period in [14, 21]:
        low_min = df['low'].rolling(period).min()
        high_max = df['high'].rolling(period).max()
        features[f'stoch_k_{period}'] = 100 * ((df['close'] - low_min) / (high_max - low_min))
        features[f'stoch_d_{period}'] = features[f'stoch_k_{period}'].rolling(3).mean()
        features[f'stoch_momentum_{period}'] = features[f'stoch_k_{period}'].diff()
    
    # === WILLIAMS %R ===
    for period in [14, 21]:
        high_max = df['high'].rolling(period).max()
        low_min = df['low'].rolling(period).min()
        features[f'williams_r_{period}'] = -100 * ((high_max - df['close']) / (high_max - low_min))
    
    # === ATR FAMILIE ===
    high_low = df['high'] - df['low']
    high_close = (df['high'] - df['close'].shift()).abs()
    low_close = (df['low'] - df['close'].shift()).abs()
    true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    
    for period in [7, 14, 21]:
        features[f'atr_{period}'] = true_range.rolling(period).mean()
        features[f'atr_percent_{period}'] = features[f'atr_{period}'] / df['close'] * 100
        features[f'atr_ratio_{period}'] = features[f'atr_{period}'] / features[f'atr_{period}'].rolling(50).mean()
    
    # === VOLUMEN INDIKATOREN ===
    # OBV
    obv = [0]
    for i in range(1, len(df)):
        if df['close'].iloc[i] > df['close'].iloc[i-1]:
            obv.append(obv[-1] + df['volume'].iloc[i])
        elif df['close'].iloc[i] < df['close'].iloc[i-1]:
            obv.append(obv[-1] - df['volume'].iloc[i])
        else:
            obv.append(obv[-1])
    features['obv'] = obv
    features['obv_ema'] = features['obv'].ewm(span=20).mean()
    features['obv_slope'] = features['obv'].diff()
    
    # Volume Ratios
    for period in [10, 20, 50]:
        features[f'volume_sma_{period}'] = df['volume'].rolling(period).mean()
        features[f'volume_ratio_{period}'] = df['volume'] / features[f'volume_sma_{period}']
        features[f'volume_momentum_{period}'] = df['volume'].pct_change(periods=period)
    
    # VWAP
    features['vwap'] = (df['volume'] * (df['high'] + df['low'] + df['close']) / 3).cumsum() / df['volume'].cumsum()
    features['vwap_ratio'] = df['close'] / features['vwap']
    
    # === MOMENTUM INDIKATOREN ===
    for period in [5, 10, 20, 50]:
        features[f'roc_{period}'] = df['close'].pct_change(periods=period) * 100
        features[f'momentum_{period}'] = df['close'] - df['close'].shift(period)
        features[f'momentum_ratio_{period}'] = features[f'momentum_{period}'] / df['close'].shift(period) * 100
    
    # === VOLATILITÄT ===
    for period in [5, 10, 20, 50]:
        features[f'volatility_{period}'] = df['close'].pct_change().rolling(period).std() * 100
        features[f'volatility_ratio_{period}'] = features[f'volatility_{period}'] / features[f'volatility_{period}'].rolling(50).mean()
    
    # === PREIS PATTERN ===
    features['high_low_ratio'] = df['high'] / df['low']
    features['close_open_ratio'] = df['close'] / df['open']
    features['high_close_ratio'] = df['high'] / df['close']
    features['low_close_ratio'] = df['low'] / df['close']
    features['body_size'] = abs(df['close'] - df['open']) / df['open'] * 100
    features['upper_shadow'] = (df['high'] - np.maximum(df['open'], df['close'])) / df['open'] * 100
    features['lower_shadow'] = (np.minimum(df['open'], df['close']) - df['low']) / df['open'] * 100
    
    # === TREND INDIKATOREN ===
    for period in [20, 50, 100]:
        if period <= len(df):
            features[f'trend_strength_{period}'] = (df['close'] - features[f'sma_{period}']) / features[f'sma_{period}'] * 100
            features[f'trend_direction_{period}'] = features[f'sma_{period}'].diff() > 0
    
    # === FIBONACCI RETRACEMENTS ===
    high_52 = df['high'].rolling(52*24).max()  # 52 Wochen
    low_52 = df['low'].rolling(52*24).min()
    fib_levels = [0.236, 0.382, 0.5, 0.618, 0.786]
    for level in fib_levels:
        features[f'fib_{int(level*1000)}'] = low_52 + (high_52 - low_52) * level
        features[f'fib_distance_{int(level*1000)}'] = abs(df['close'] - features[f'fib_{int(level*1000)}']) / df['close'] * 100
    
    # === ZYKLISCHE FEATURES ===
    features['hour'] = df.index.hour
    features['day_of_week'] = df.index.dayofweek
    features['day_of_month'] = df.index.day
    features['month'] = df.index.month
    features['quarter'] = df.index.quarter
    
    # Trigonometrische Transformationen
    features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
    features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
    features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
    features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
    features['month_sin'] = np.sin(2 * np.pi * features['month'] / 12)
    features['month_cos'] = np.cos(2 * np.pi * features['month'] / 12)
    
    # === ADVANCED PATTERN ===
    # Gaps
    features['gap_up'] = (df['open'] > df['close'].shift()) & (df['open'] > df['high'].shift())
    features['gap_down'] = (df['open'] < df['close'].shift()) & (df['open'] < df['low'].shift())
    
    # Doji Pattern
    features['doji'] = abs(df['close'] - df['open']) / (df['high'] - df['low']) < 0.1
    
    # Hammer/Shooting Star
    body = abs(df['close'] - df['open'])
    upper_shadow = df['high'] - np.maximum(df['open'], df['close'])
    lower_shadow = np.minimum(df['open'], df['close']) - df['low']
    features['hammer'] = (lower_shadow > 2 * body) & (upper_shadow < 0.1 * body)
    features['shooting_star'] = (upper_shadow > 2 * body) & (lower_shadow < 0.1 * body)
    
    # === MARKET MICROSTRUCTURE ===
    # Bid-Ask Spread Approximation
    features['spread_approx'] = (df['high'] - df['low']) / df['close'] * 100
    
    # Price Impact
    features['price_impact'] = abs(df['close'].pct_change()) / (df['volume'] / df['volume'].rolling(20).mean())
    
    # Liquidity Proxy
    features['liquidity_proxy'] = df['volume'] / (df['high'] - df['low'])
    
    print(f"📈 {len(features.columns)} ULTIMATE Features erstellt!")
    return features.dropna()

def create_sequences_parallel(data, target, look_back):
    """Parallele Sequenz-Erstellung"""
    def create_chunk(start_idx, end_idx):
        X_chunk, y_chunk = [], []
        for i in range(start_idx, min(end_idx, len(data) - look_back)):
            X_chunk.append(data[i:i + look_back])
            y_chunk.append(target[i + look_back])
        return np.array(X_chunk), np.array(y_chunk)
    
    # Parallele Verarbeitung
    chunk_size = max(1, (len(data) - look_back) // os.cpu_count())
    chunks = [(i, i + chunk_size) for i in range(0, len(data) - look_back, chunk_size)]
    
    with ThreadPoolExecutor(max_workers=os.cpu_count()) as executor:
        results = list(executor.map(lambda chunk: create_chunk(chunk[0], chunk[1]), chunks))
    
    # Zusammenfügen
    X_list, y_list = zip(*results)
    X = np.vstack([x for x in X_list if len(x) > 0])
    y = np.hstack([y for y in y_list if len(y) > 0])
    
    return X.astype(np.float32), y.astype(np.float32)

def build_ultimate_models(input_shape):
    """Alle Modelltypen erstellen"""
    models = {}
    
    # 1. Bidirectional LSTM
    models['bidirectional_lstm'] = Sequential([
        Bidirectional(LSTM(128, return_sequences=True, dropout=0.2), input_shape=input_shape),
        BatchNormalization(),
        Bidirectional(LSTM(64, return_sequences=False, dropout=0.2)),
        BatchNormalization(),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(32, activation='relu'),
        Dense(1)
    ])
    
    # 2. Deep LSTM
    models['deep_lstm'] = Sequential([
        LSTM(128, return_sequences=True, dropout=0.2, input_shape=input_shape),
        BatchNormalization(),
        LSTM(96, return_sequences=True, dropout=0.2),
        BatchNormalization(),
        LSTM(64, return_sequences=True, dropout=0.2),
        BatchNormalization(),
        LSTM(32, return_sequences=False, dropout=0.2),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(1)
    ])
    
    # 3. GRU Model
    models['gru'] = Sequential([
        GRU(128, return_sequences=True, dropout=0.2, input_shape=input_shape),
        BatchNormalization(),
        GRU(64, return_sequences=False, dropout=0.2),
        BatchNormalization(),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(32, activation='relu'),
        Dense(1)
    ])
    
    # 4. Hybrid LSTM-GRU
    models['hybrid'] = Sequential([
        LSTM(96, return_sequences=True, dropout=0.2, input_shape=input_shape),
        BatchNormalization(),
        GRU(64, return_sequences=False, dropout=0.2),
        BatchNormalization(),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(32, activation='relu'),
        Dense(1)
    ])
    
    # 5. Wide LSTM
    models['wide_lstm'] = Sequential([
        LSTM(256, return_sequences=False, dropout=0.3, input_shape=input_shape),
        BatchNormalization(),
        Dense(128, activation='relu'),
        Dropout(0.4),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(32, activation='relu'),
        Dense(1)
    ])
    
    # 6. Attention LSTM
    models['attention_lstm'] = Sequential([
        LSTM(128, return_sequences=True, dropout=0.2, input_shape=input_shape),
        LSTM(64, return_sequences=True, dropout=0.2),
        # Attention würde hier eingefügt werden (vereinfacht)
        LSTM(32, return_sequences=False, dropout=0.2),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(1)
    ])
    
    # 7. Ensemble Base
    models['ensemble_base'] = Sequential([
        LSTM(64, return_sequences=True, dropout=0.2, input_shape=input_shape),
        LSTM(32, return_sequences=False, dropout=0.2),
        Dense(32, activation='relu'),
        Dropout(0.3),
        Dense(16, activation='relu'),
        Dense(1)
    ])
    
    # Kompiliere alle Modelle
    optimizers = [Adam(0.001), Adam(0.0005), RMSprop(0.001), AdamW(0.001)]
    
    for i, (name, model) in enumerate(models.items()):
        optimizer = optimizers[i % len(optimizers)]
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae'])
        print(f"✅ {name}: {model.count_params():,} Parameter")
    
    return models

def train_models_parallel(models, X_train, y_train, X_val, y_val):
    """Paralleles Training aller Modelle"""
    print("🎯 Starte paralleles Training aller Modelle...")

    def train_single_model(model_data):
        name, model = model_data
        print(f"   Training {name}...")

        callbacks = [
            EarlyStopping(patience=CONFIG['patience'], restore_best_weights=True, verbose=0),
            ReduceLROnPlateau(factor=0.5, patience=10, min_lr=1e-7, verbose=0)
        ]

        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=CONFIG['epochs'],
            batch_size=64,
            callbacks=callbacks,
            verbose=0
        )

        val_loss = min(history.history['val_loss'])
        print(f"   ✅ {name}: Val Loss = {val_loss:.6f}")

        return name, model, history, val_loss

    # Paralleles Training
    with ThreadPoolExecutor(max_workers=min(len(models), os.cpu_count())) as executor:
        results = list(executor.map(train_single_model, models.items()))

    # Sortiere nach Performance
    results.sort(key=lambda x: x[3])  # Nach val_loss sortieren

    trained_models = {}
    histories = {}
    performance = {}

    for name, model, history, val_loss in results:
        trained_models[name] = model
        histories[name] = history
        performance[name] = val_loss

    print(f"🏆 Beste Modelle (nach Val Loss):")
    for i, (name, _, _, val_loss) in enumerate(results[:3]):
        print(f"   {i+1}. {name}: {val_loss:.6f}")

    return trained_models, histories, performance

def create_ml_models(X_train_flat, y_train, X_val_flat, y_val):
    """Traditionelle ML-Modelle für Vergleich"""
    print("🤖 Trainiere traditionelle ML-Modelle...")

    ml_models = {}

    # Random Forest
    print("   Training Random Forest...")
    rf = RandomForestRegressor(n_estimators=200, max_depth=20, n_jobs=-1, random_state=42)
    rf.fit(X_train_flat, y_train)
    ml_models['random_forest'] = rf

    # XGBoost
    print("   Training XGBoost...")
    xgb_model = xgb.XGBRegressor(n_estimators=200, max_depth=10, n_jobs=-1, random_state=42)
    xgb_model.fit(X_train_flat, y_train)
    ml_models['xgboost'] = xgb_model

    # LightGBM
    print("   Training LightGBM...")
    lgb_model = lgb.LGBMRegressor(n_estimators=200, max_depth=10, n_jobs=-1, random_state=42)
    lgb_model.fit(X_train_flat, y_train)
    ml_models['lightgbm'] = lgb_model

    # Gradient Boosting
    print("   Training Gradient Boosting...")
    gb = GradientBoostingRegressor(n_estimators=200, max_depth=10, random_state=42)
    gb.fit(X_train_flat, y_train)
    ml_models['gradient_boosting'] = gb

    # Linear Models
    print("   Training Linear Models...")
    ridge = Ridge(alpha=1.0)
    ridge.fit(X_train_flat, y_train)
    ml_models['ridge'] = ridge

    lasso = Lasso(alpha=0.1)
    lasso.fit(X_train_flat, y_train)
    ml_models['lasso'] = lasso

    # Evaluiere ML-Modelle
    ml_performance = {}
    for name, model in ml_models.items():
        pred = model.predict(X_val_flat)
        mse = mean_squared_error(y_val, pred)
        ml_performance[name] = mse
        print(f"   ✅ {name}: MSE = {mse:.6f}")

    return ml_models, ml_performance

def create_ultimate_ensemble(models, ml_models, X_val, y_val, X_val_flat):
    """Ultimate Ensemble aus allen Modellen"""
    print("🏆 Erstelle Ultimate Ensemble...")

    # Deep Learning Predictions
    dl_predictions = {}
    dl_weights = {}

    for name, model in models.items():
        pred = model.predict(X_val, verbose=0).flatten()
        mse = mean_squared_error(y_val, pred)
        dl_predictions[name] = pred
        dl_weights[name] = 1.0 / (1.0 + mse)  # Inverse MSE als Gewicht

    # ML Predictions
    ml_predictions = {}
    ml_weights = {}

    for name, model in ml_models.items():
        pred = model.predict(X_val_flat)
        mse = mean_squared_error(y_val, pred)
        ml_predictions[name] = pred
        ml_weights[name] = 1.0 / (1.0 + mse)

    # Normalisiere Gewichte
    total_dl_weight = sum(dl_weights.values())
    total_ml_weight = sum(ml_weights.values())

    for name in dl_weights:
        dl_weights[name] /= total_dl_weight
    for name in ml_weights:
        ml_weights[name] /= total_ml_weight

    # Gewichtete Ensemble-Vorhersage
    ensemble_pred = np.zeros_like(y_val)

    # 70% Deep Learning, 30% Traditional ML
    dl_contribution = 0.7
    ml_contribution = 0.3

    for name, pred in dl_predictions.items():
        ensemble_pred += dl_contribution * dl_weights[name] * pred

    for name, pred in ml_predictions.items():
        ensemble_pred += ml_contribution * ml_weights[name] * pred

    ensemble_mse = mean_squared_error(y_val, ensemble_pred)

    print(f"🎯 Ensemble Performance:")
    print(f"   MSE: {ensemble_mse:.6f}")
    print(f"   R²: {r2_score(y_val, ensemble_pred):.4f}")

    return ensemble_pred, dl_weights, ml_weights

def monte_carlo_future_prediction(models, ml_models, last_sequence, last_features, scaler, n_hours=24):
    """Monte Carlo Zukunftsprognose mit allen Modellen"""
    print(f"🔮 Monte Carlo Prognose für {n_hours}h mit {CONFIG['monte_carlo_sims']} Simulationen...")

    all_predictions = []

    for sim in range(CONFIG['monte_carlo_sims']):
        if sim % 200 == 0:
            print(f"   Simulation {sim+1}/{CONFIG['monte_carlo_sims']}")

        current_seq = last_sequence.copy()
        current_features = last_features.copy()
        sim_predictions = []

        for hour in range(n_hours):
            # Deep Learning Predictions
            dl_preds = []
            for model in models.values():
                pred = model.predict(current_seq.reshape(1, *current_seq.shape), verbose=0)[0, 0]
                dl_preds.append(pred)

            # ML Predictions
            ml_preds = []
            for model in ml_models.values():
                pred = model.predict(current_features.reshape(1, -1))[0]
                ml_preds.append(pred)

            # Ensemble Prediction
            ensemble_pred = 0.7 * np.mean(dl_preds) + 0.3 * np.mean(ml_preds)

            # Füge Rauschen hinzu
            volatility = 0.02 * (1 + hour * 0.001)
            noise = np.random.normal(0, volatility)
            final_pred = ensemble_pred * (1 + noise)

            sim_predictions.append(final_pred)

            # Update Sequenzen für nächste Vorhersage
            new_features = current_features.copy()
            new_features[3] = final_pred  # close price

            # Andere Features mit Rauschen aktualisieren
            for i in range(len(new_features)):
                if i != 3:
                    feature_noise = np.random.normal(0, volatility * 0.3)
                    new_features[i] = new_features[i] * (1 + feature_noise)

            # Update Sequenzen
            current_seq = np.vstack([current_seq[1:], new_features[:current_seq.shape[1]].reshape(1, -1)])
            current_features = new_features

        all_predictions.append(sim_predictions)

    # Statistiken berechnen
    all_predictions = np.array(all_predictions)

    stats = {
        'mean': np.mean(all_predictions, axis=0),
        'std': np.std(all_predictions, axis=0),
        'median': np.median(all_predictions, axis=0),
        'q25': np.percentile(all_predictions, 25, axis=0),
        'q75': np.percentile(all_predictions, 75, axis=0),
        'q05': np.percentile(all_predictions, 5, axis=0),
        'q95': np.percentile(all_predictions, 95, axis=0),
        'min': np.min(all_predictions, axis=0),
        'max': np.max(all_predictions, axis=0)
    }

    # Skalierung rückgängig machen
    for key in stats:
        dummy_data = np.zeros((len(stats[key]), scaler.n_features_in_))
        dummy_data[:, 3] = stats[key]
        stats[key] = scaler.inverse_transform(dummy_data)[:, 3]

    return stats

def plot_ultimate_analysis(df, train_size, val_size, y_test, ensemble_pred, future_stats, performance_comparison):
    """Ultimate Analyse-Visualisierung"""
    plt.figure(figsize=(24, 16))

    # 1. Hauptpreis-Chart mit Prognose
    plt.subplot(3, 4, (1, 4))
    dates = df.index

    plt.plot(dates[:train_size], df['close'].iloc[:train_size], 'b-', label='Training', alpha=0.7, linewidth=1)
    plt.plot(dates[train_size:train_size+val_size], df['close'].iloc[train_size:train_size+val_size],
             'orange', label='Validation', alpha=0.7, linewidth=1)

    test_start = train_size + val_size
    test_dates = dates[test_start:test_start+len(y_test)]
    plt.plot(test_dates, y_test, 'g-', label='Actual', linewidth=2)
    plt.plot(test_dates, ensemble_pred, 'r--', label='Ensemble Prediction', linewidth=2)

    # Zukunftsprognose
    future_dates = pd.date_range(start=dates[-1], periods=len(future_stats['mean'])+1, freq='1H')[1:]
    plt.plot(future_dates, future_stats['mean'], 'purple', linewidth=3, label='Future Prediction')
    plt.fill_between(future_dates, future_stats['q05'], future_stats['q95'],
                     alpha=0.2, color='purple', label='90% Confidence')
    plt.fill_between(future_dates, future_stats['q25'], future_stats['q75'],
                     alpha=0.3, color='purple', label='50% Confidence')

    plt.title('ULTIMATE BITCOIN PREDICTION - ALL MODELS ENSEMBLE', fontsize=16, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. Performance Vergleich
    plt.subplot(3, 4, 5)
    models = list(performance_comparison.keys())
    scores = list(performance_comparison.values())
    colors = plt.cm.viridis(np.linspace(0, 1, len(models)))

    bars = plt.barh(models, scores, color=colors)
    plt.title('Model Performance (Lower = Better)')
    plt.xlabel('Validation MSE')

    # Beste 3 markieren
    for i, bar in enumerate(bars[:3]):
        bar.set_color('gold' if i == 0 else 'silver' if i == 1 else '#CD7F32')

    plt.grid(True, alpha=0.3)

    # 3. Ensemble Gewichte
    plt.subplot(3, 4, 6)
    # Placeholder für Gewichte-Visualisierung
    plt.pie([0.4, 0.35, 0.25], labels=['Best DL', 'Second DL', 'ML Models'], autopct='%1.1f%%')
    plt.title('Ensemble Weights')

    # 4. Zukunftsprognose Detail
    plt.subplot(3, 4, 7)
    hours = range(1, len(future_stats['mean']) + 1)
    plt.plot(hours, future_stats['mean'], 'purple', linewidth=2, label='Mean')
    plt.plot(hours, future_stats['median'], 'orange', linewidth=2, label='Median')
    plt.fill_between(hours, future_stats['q25'], future_stats['q75'], alpha=0.3, color='blue')
    plt.title('Future Prediction Statistics')
    plt.xlabel('Hours Ahead')
    plt.ylabel('Price (USD)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 5. Volatilität Prognose
    plt.subplot(3, 4, 8)
    volatility = future_stats['std'] / future_stats['mean'] * 100
    plt.plot(hours, volatility, 'red', linewidth=2)
    plt.title('Predicted Volatility')
    plt.xlabel('Hours Ahead')
    plt.ylabel('Volatility %')
    plt.grid(True, alpha=0.3)

    # 6-12. Weitere Analysen...
    for i in range(6, 13):
        plt.subplot(3, 4, i)
        if i == 9:
            # Residuals
            residuals = y_test - ensemble_pred
            plt.scatter(ensemble_pred, residuals, alpha=0.6, s=20)
            plt.axhline(y=0, color='red', linestyle='--')
            plt.title('Residuals')
            plt.xlabel('Predicted')
            plt.ylabel('Residuals')
        elif i == 10:
            # Error Distribution
            residuals = y_test - ensemble_pred
            plt.hist(residuals, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            plt.title('Error Distribution')
            plt.xlabel('Error')
            plt.ylabel('Frequency')
        elif i == 11:
            # Confidence Intervals
            current_price = df['close'].iloc[-1]
            price_changes = (future_stats['mean'] / current_price - 1) * 100
            plt.plot(hours, price_changes, 'green', linewidth=2)
            plt.title('Expected Price Change %')
            plt.xlabel('Hours Ahead')
            plt.ylabel('Change %')
            plt.axhline(y=0, color='red', linestyle='--')
        else:
            plt.text(0.5, 0.5, f'Analysis {i-5}', ha='center', va='center', transform=plt.gca().transAxes)

        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

def calculate_ultimate_metrics(y_true, y_pred):
    """Ultimate Metriken-Berechnung"""
    metrics = {
        'mse': mean_squared_error(y_true, y_pred),
        'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
        'mae': mean_absolute_error(y_true, y_pred),
        'r2': r2_score(y_true, y_pred),
        'mape': np.mean(np.abs((y_true - y_pred) / y_true)) * 100,
        'explained_variance': 1 - (np.var(y_true - y_pred) / np.var(y_true)),
        'max_error': np.max(np.abs(y_true - y_pred)),
        'mean_error': np.mean(y_true - y_pred)
    }

    # Richtungsgenauigkeit
    if len(y_true) > 1:
        true_direction = np.diff(y_true) > 0
        pred_direction = np.diff(y_pred) > 0
        metrics['direction_accuracy'] = np.mean(true_direction == pred_direction) * 100
    else:
        metrics['direction_accuracy'] = 0

    return metrics

def main():
    """ULTIMATE MAIN - MAXIMUM PERFORMANCE"""
    print("🚀 ULTIMATE BITCOIN PREDICTION MODEL - MAXIMUM PERFORMANCE")
    print("=" * 70)
    print(f"🎯 Ziel: ALLES herausholen - CPU + GPU + Alle Modelle")
    print(f"💻 Hardware: {os.cpu_count()} CPU-Kerne + {'GPU' if gpus else 'CPU only'}")

    total_start = time.time()

    try:
        # 1. ULTIMATE Feature Engineering
        print("\n" + "="*50)
        print("PHASE 1: ULTIMATE FEATURE ENGINEERING")
        print("="*50)

        feature_start = time.time()
        df = load_and_create_ultimate_features()
        feature_time = time.time() - feature_start
        print(f"⚡ Feature Engineering: {feature_time:.1f}s")

        # 2. Daten vorbereiten
        print("\n" + "="*50)
        print("PHASE 2: DATA PREPARATION")
        print("="*50)

        prep_start = time.time()

        # Feature Selection (Top Features)
        X = df.drop('close', axis=1)
        y = df['close'].values

        # Robuste Skalierung
        feature_scaler = RobustScaler()
        target_scaler = StandardScaler()

        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()

        print(f"📊 Features: {X_scaled.shape[1]}")
        print(f"📊 Samples: {len(y_scaled)}")

        prep_time = time.time() - prep_start
        print(f"⚡ Data Preparation: {prep_time:.1f}s")

        # 3. Teste verschiedene Konfigurationen
        print("\n" + "="*50)
        print("PHASE 3: CONFIGURATION TESTING")
        print("="*50)

        best_config = None
        best_score = float('inf')

        for look_back in CONFIG['look_back_options']:
            print(f"\n🔍 Teste Look-Back: {look_back}")

            # Sequenzen erstellen
            seq_start = time.time()
            X_seq, y_seq = create_sequences_parallel(X_scaled, y_scaled, look_back)
            seq_time = time.time() - seq_start

            if len(X_seq) < 100:  # Zu wenig Daten
                print(f"   ⚠️  Zu wenig Sequenzen: {len(X_seq)}")
                continue

            # Train-Val-Test Split
            train_size = int(len(X_seq) * CONFIG['train_split'])
            val_size = int(len(X_seq) * 0.15)

            X_train = X_seq[:train_size]
            y_train = y_seq[:train_size]
            X_val = X_seq[train_size:train_size+val_size]
            y_val = y_seq[train_size:train_size+val_size]
            X_test = X_seq[train_size+val_size:]
            y_test = y_seq[train_size+val_size:]

            print(f"   📊 Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}")
            print(f"   ⚡ Sequenz-Erstellung: {seq_time:.1f}s")

            # Quick Test mit einfachem Modell
            test_model = Sequential([
                LSTM(32, input_shape=(X_train.shape[1], X_train.shape[2])),
                Dense(1)
            ])
            test_model.compile(optimizer='adam', loss='mse')

            test_model.fit(X_train, y_train, validation_data=(X_val, y_val),
                          epochs=5, batch_size=64, verbose=0)

            val_loss = test_model.evaluate(X_val, y_val, verbose=0)
            print(f"   📊 Quick Test Val Loss: {val_loss:.6f}")

            if val_loss < best_score:
                best_score = val_loss
                best_config = {
                    'look_back': look_back,
                    'X_train': X_train, 'y_train': y_train,
                    'X_val': X_val, 'y_val': y_val,
                    'X_test': X_test, 'y_test': y_test,
                    'train_size': train_size, 'val_size': val_size
                }
                print(f"   🏆 Neue beste Konfiguration!")

        if best_config is None:
            raise ValueError("Keine gültige Konfiguration gefunden!")

        print(f"\n🏆 Beste Konfiguration: Look-Back = {best_config['look_back']}")

        # 4. ULTIMATE Model Training
        print("\n" + "="*50)
        print("PHASE 4: ULTIMATE MODEL TRAINING")
        print("="*50)

        training_start = time.time()

        # Deep Learning Modelle
        input_shape = (best_config['X_train'].shape[1], best_config['X_train'].shape[2])
        models = build_ultimate_models(input_shape)

        trained_models, histories, dl_performance = train_models_parallel(
            models, best_config['X_train'], best_config['y_train'],
            best_config['X_val'], best_config['y_val']
        )

        # Traditional ML Modelle
        X_train_flat = best_config['X_train'].reshape(best_config['X_train'].shape[0], -1)
        X_val_flat = best_config['X_val'].reshape(best_config['X_val'].shape[0], -1)
        X_test_flat = best_config['X_test'].reshape(best_config['X_test'].shape[0], -1)

        ml_models, ml_performance = create_ml_models(
            X_train_flat, best_config['y_train'], X_val_flat, best_config['y_val']
        )

        training_time = time.time() - training_start
        print(f"⚡ Model Training: {training_time:.1f}s")

        # 5. ULTIMATE Ensemble
        print("\n" + "="*50)
        print("PHASE 5: ULTIMATE ENSEMBLE")
        print("="*50)

        ensemble_start = time.time()

        ensemble_pred, dl_weights, ml_weights = create_ultimate_ensemble(
            trained_models, ml_models, best_config['X_test'], best_config['y_test'], X_test_flat
        )

        ensemble_time = time.time() - ensemble_start
        print(f"⚡ Ensemble Creation: {ensemble_time:.1f}s")

        # 6. Skalierung rückgängig machen
        y_test_orig = target_scaler.inverse_transform(best_config['y_test'].reshape(-1, 1)).flatten()
        ensemble_pred_orig = target_scaler.inverse_transform(ensemble_pred.reshape(-1, 1)).flatten()

        # 7. Ultimate Metriken
        metrics = calculate_ultimate_metrics(y_test_orig, ensemble_pred_orig)

        print(f"\n📊 ULTIMATE PERFORMANCE:")
        print(f"R²: {metrics['r2']:.4f} ({metrics['r2']*100:.1f}%)")
        print(f"RMSE: ${metrics['rmse']:.2f}")
        print(f"MAE: ${metrics['mae']:.2f}")
        print(f"MAPE: {metrics['mape']:.2f}%")
        print(f"Direction Accuracy: {metrics['direction_accuracy']:.1f}%")
        print(f"Explained Variance: {metrics['explained_variance']:.4f}")

        # 8. ULTIMATE Future Prediction
        print("\n" + "="*50)
        print("PHASE 6: ULTIMATE FUTURE PREDICTION")
        print("="*50)

        future_start = time.time()

        # Letzte Sequenz für Prognose
        last_sequence = best_config['X_test'][-1]
        last_features = X_test_flat[-1]

        # Prognosen für verschiedene Horizonte
        future_predictions = {}

        for hours in CONFIG['future_hours']:
            print(f"\n🔮 {hours}h Prognose...")
            future_stats = monte_carlo_future_prediction(
                trained_models, ml_models, last_sequence, last_features,
                target_scaler, hours
            )
            future_predictions[hours] = future_stats

        future_time = time.time() - future_start
        print(f"⚡ Future Predictions: {future_time:.1f}s")

        # 9. ULTIMATE Visualisierung
        print("\n" + "="*50)
        print("PHASE 7: ULTIMATE VISUALIZATION")
        print("="*50)

        viz_start = time.time()

        # Kombiniere alle Performance-Daten
        all_performance = {**dl_performance, **ml_performance}

        plot_ultimate_analysis(
            df, best_config['train_size'], best_config['val_size'],
            y_test_orig, ensemble_pred_orig, future_predictions[24], all_performance
        )

        viz_time = time.time() - viz_start
        print(f"⚡ Visualization: {viz_time:.1f}s")

        # 10. FINALE PROGNOSE-ZUSAMMENFASSUNG
        print("\n" + "="*70)
        print("🎯 ULTIMATE BITCOIN PROGNOSE")
        print("="*70)

        current_price = df['close'].iloc[-1]

        for hours in CONFIG['future_hours']:
            stats = future_predictions[hours]

            mean_price = stats['mean'][-1]
            median_price = stats['median'][-1]
            q25_price = stats['q25'][-1]
            q75_price = stats['q75'][-1]
            q05_price = stats['q05'][-1]
            q95_price = stats['q95'][-1]

            mean_change = (mean_price / current_price - 1) * 100

            print(f"\n📅 In {hours}h:")
            print(f"   💰 Erwarteter Preis: ${mean_price:.2f} ({mean_change:+.2f}%)")
            print(f"   📊 Median: ${median_price:.2f}")
            print(f"   📈 50% Konfidenz: ${q25_price:.2f} - ${q75_price:.2f}")
            print(f"   📈 90% Konfidenz: ${q05_price:.2f} - ${q95_price:.2f}")

            # Trend-Bewertung
            if mean_change > 5:
                trend = "🚀 STARK BULLISH"
            elif mean_change > 2:
                trend = "📈 BULLISH"
            elif mean_change > -2:
                trend = "➡️  SEITWÄRTS"
            elif mean_change > -5:
                trend = "📉 BEARISH"
            else:
                trend = "💥 STARK BEARISH"

            print(f"   🎯 Trend: {trend}")

        # Finale Statistiken
        total_time = time.time() - total_start

        print(f"\n" + "="*70)
        print("✅ ULTIMATE MODEL ABGESCHLOSSEN!")
        print("="*70)
        print(f"⚡ Gesamtzeit: {total_time:.1f} Sekunden")
        print(f"🎯 Finale Genauigkeit: {metrics['r2']*100:.1f}%")
        print(f"🏆 Beste Konfiguration: Look-Back {best_config['look_back']}")
        print(f"📊 Features verwendet: {X_scaled.shape[1]}")
        print(f"🤖 Modelle trainiert: {len(trained_models) + len(ml_models)}")
        print(f"🔮 Monte Carlo Simulationen: {CONFIG['monte_carlo_sims']}")
        print(f"💻 Hardware-Auslastung: MAXIMUM")

        # Performance-Ranking
        print(f"\n🏆 TOP 5 MODELLE:")
        sorted_performance = sorted(all_performance.items(), key=lambda x: x[1])
        for i, (name, score) in enumerate(sorted_performance[:5]):
            medal = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1}."
            print(f"   {medal} {name}: {score:.6f}")

    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
