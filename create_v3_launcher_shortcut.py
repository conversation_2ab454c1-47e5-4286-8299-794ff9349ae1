#!/usr/bin/env python3
"""
🖥️ DESKTOP-VERKNÜPFUNG FÜR ULTIMATE GUI-LAUNCHER V3 🖥️
======================================================
🏆 AUTOMATISCHE DESKTOP-VERKNÜPFUNG FÜR V3 LAUNCHER 🏆
✅ Erstellt Desktop-Verknüpfung für Ultimate GUI-Launcher V3
✅ Alle 3 Modelle + Gesamtprognose + Maximale API-Nutzung
✅ Windows-kompatibel mit professionellem Icon
✅ Ein-Klick-Start für revolutionäres Bitcoin Trading

💡 EINFACH AUSFÜHREN UND V3 LAUNCHER IST VOM DESKTOP STARTBAR!
"""

import os
import sys

def create_v3_launcher_shortcut():
    """Erstelle Desktop-Verknüpfung für Ultimate GUI-Launcher V3"""
    
    print("🖥️ Erstelle Desktop-Verknüpfung für Ultimate GUI-Launcher V3...")
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        # Pfade ermitteln
        current_dir = os.getcwd()
        launcher_path = os.path.join(current_dir, "ultimate_gui_launcher_v3.py")
        python_path = sys.executable
        
        # Desktop-Pfad ermitteln
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "Ultimate Bitcoin Trading Launcher V3.lnk")
        
        # Prüfe ob V3 Launcher existiert
        if not os.path.exists(launcher_path):
            print(f"❌ FEHLER: ultimate_gui_launcher_v3.py nicht gefunden!")
            print(f"💡 Stellen Sie sicher, dass der V3 Launcher im aktuellen Ordner ist.")
            return False
        
        # Erstelle Verknüpfung
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        
        # Verknüpfungs-Eigenschaften
        shortcut.Targetpath = python_path
        shortcut.Arguments = f'"{launcher_path}"'
        shortcut.WorkingDirectory = current_dir
        shortcut.Description = "Ultimate Bitcoin Trading Launcher V3 - Alle 3 Modelle + Gesamtprognose + Maximale API-Nutzung"
        shortcut.IconLocation = python_path + ",0"  # Python-Icon verwenden
        
        # Speichere Verknüpfung
        shortcut.save()
        
        print(f"✅ V3 Desktop-Verknüpfung erfolgreich erstellt!")
        print(f"📍 Speicherort: {shortcut_path}")
        print(f"🎯 Ziel: {launcher_path}")
        print(f"💡 Sie können jetzt den V3 Launcher vom Desktop starten!")
        
        return True
        
    except ImportError as e:
        print(f"💡 Module sind bereits installiert - verwende Batch-Alternative")
        return create_v3_batch_launcher()
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Verknüpfung: {e}")
        return create_v3_batch_launcher()

def create_v3_batch_launcher():
    """Erstelle/Aktualisiere Batch-Datei für V3 Launcher"""
    
    print("🔧 Erstelle/Aktualisiere Batch-Launcher für V3 Launcher...")
    
    try:
        current_dir = os.getcwd()
        launcher_path = os.path.join(current_dir, "ultimate_gui_launcher_v3.py")
        
        # Desktop-Pfad ermitteln
        try:
            import winshell
            desktop = winshell.desktop()
        except:
            # Fallback für Desktop-Pfad
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        
        batch_path = os.path.join(desktop, "Ultimate Bitcoin Trading Launcher V3.bat")
        
        # Batch-Inhalt
        batch_content = f'''@echo off
title Ultimate Bitcoin Trading Launcher V3 - Alle 3 Modelle + Gesamtprognose
color 0A
echo.
echo ========================================
echo    ULTIMATE BITCOIN TRADING LAUNCHER V3
echo ========================================
echo    Alle 3 Modelle + Gesamtprognose
echo    Maximale API-Nutzung + Echtzeit-Monitoring
echo ========================================
echo.
cd /d "{current_dir}"
python "{launcher_path}"
if errorlevel 1 (
    echo.
    echo ❌ Fehler beim Starten des V3 Launchers
    echo 💡 Stellen Sie sicher, dass Python installiert ist
    echo 📄 Launcher-Datei: {launcher_path}
    echo.
    pause
) else (
    echo.
    echo ✅ V3 Launcher erfolgreich beendet
    echo 👋 Bis zum naechsten Mal!
    echo.
    timeout /t 3 >nul
)
'''
        
        # Schreibe Batch-Datei auf Desktop
        with open(batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"✅ Desktop-Batch-Launcher erstellt: {batch_path}")
        print(f"💡 Sie können diese .bat-Datei vom Desktop aus starten!")
        
        # Erstelle auch eine Kopie im aktuellen Verzeichnis
        local_batch_path = os.path.join(current_dir, "Ultimate Bitcoin Trading Launcher V3.bat")
        with open(local_batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"✅ Lokale Batch-Datei aktualisiert: {local_batch_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Batch-Datei: {e}")
        return False

def check_v3_launcher_files():
    """Prüfe verfügbare V3 Launcher-Dateien"""
    
    print("📄 Prüfe verfügbare V3 Launcher-Dateien...")
    
    current_dir = os.getcwd()
    launcher_files = [
        "ultimate_gui_launcher_v3.py",
        "ultimate_bitcoin_trading_complete_v3.py",
        "bitcoin_launcher_working.py",
        "ultimate_complete_bitcoin_trading_FAVORITE.py"
    ]
    
    available_files = []
    
    for file in launcher_files:
        file_path = os.path.join(current_dir, file)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            available_files.append((file, file_size))
            print(f"✅ {file} - {file_size:,} Bytes")
        else:
            print(f"❌ {file} - Nicht gefunden")
    
    if available_files:
        print(f"\n💡 {len(available_files)} V3 Launcher-Dateien verfügbar")
        return True
    else:
        print(f"\n❌ Keine V3 Launcher-Dateien gefunden!")
        return False

def main():
    """Hauptfunktion"""
    
    print("🖥️ DESKTOP-VERKNÜPFUNG FÜR ULTIMATE GUI-LAUNCHER V3")
    print("=" * 80)
    print("🚀 Erstelle Desktop-Verknüpfung für ultimate_gui_launcher_v3.py...")
    print("")
    
    # Prüfe verfügbare Dateien
    files_available = check_v3_launcher_files()
    
    if not files_available:
        print("❌ Keine V3 Launcher-Dateien gefunden!")
        print("💡 Stellen Sie sicher, dass Sie im richtigen Verzeichnis sind.")
        return
    
    print("")
    
    # Versuche Desktop-Verknüpfung zu erstellen
    shortcut_success = create_v3_launcher_shortcut()
    
    print("\n" + "=" * 80)
    
    if shortcut_success:
        print("🎉 ERFOLGREICH!")
        print("✅ Desktop-Verknüpfung wurde erstellt")
        print("🖱️ Schauen Sie auf Ihren Desktop nach:")
        print("   📄 'Ultimate Bitcoin Trading Launcher V3.bat'")
        print("   📄 'Ultimate Bitcoin Trading Launcher V3.lnk' (falls verfügbar)")
    else:
        print("❌ FEHLER!")
        print("💡 Starten Sie den V3 Launcher manuell mit:")
        print("   python ultimate_gui_launcher_v3.py")
    
    print("\n💡 ULTIMATE GUI-LAUNCHER V3 FEATURES:")
    print("🏅 Alle 3 Bitcoin Trading Modelle integriert:")
    print("   1. FAVORIT - Bewährt & Getestet (Session #14+)")
    print("   2. FINALE V3 - Optimiert & Effizient (300+ Features)")
    print("   3. KI-SYSTEM V3 - Innovativ & Selbstlernend (6 AI Capabilities)")
    print("🎯 Gesamtprognose-Button aus allen 3 Berechnungen")
    print("🔄 Kontinuierliche Berechnung für bessere Ergebnisse")
    print("📊 Echtzeit-Fehlerbeschreibung und Live-Status-Updates")
    print("🌐 Maximale API-Nutzung für genaueste Berechnungen")
    print("🛑 Automatischer Script-Stop beim Schließen")
    
    print("\n🚀 VERWENDUNG:")
    print("1. Doppelklick auf Desktop-Verknüpfung")
    print("2. Klicken Sie auf 'Starten' für gewünschte Modelle")
    print("3. Aktivieren Sie 'Kontinuierliche Berechnung' für beste Ergebnisse")
    print("4. Klicken Sie auf 'GESAMTPROGNOSE BERECHNEN' für Ensemble-Vorhersage")
    print("5. Überwachen Sie Live-Status und Echtzeit-Logs")
    print("6. Schließen Sie den Launcher - alle Prozesse werden automatisch gestoppt")
    
    print("\n🎯 GESAMTPROGNOSE-FUNKTIONALITÄT:")
    print("✅ Kombiniert alle 3 Modell-Ergebnisse intelligent")
    print("✅ Gewichtete Ensemble-Vorhersage nach Konfidenz und Genauigkeit")
    print("✅ Konsens-Stärke-Bewertung für Zuverlässigkeit")
    print("✅ Echtzeit-Aktualisierung bei neuen Modell-Ergebnissen")
    
    print("\n👋 V3 Desktop-Verknüpfung Setup abgeschlossen!")

if __name__ == "__main__":
    main()
