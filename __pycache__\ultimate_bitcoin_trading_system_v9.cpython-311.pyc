�

    �[eh��  �                   ��  � d Z ddlZddlZddlZddlZddlZddl	m	Z	m
Z
 ddlZddlZddl
Z
ddlZddlZddlmZmZmZmZ  e
j        d�  �         ddlmZ ddlmZ ddlmZmZ dd	lmZ 	 ddlZ d
Z! e"d�  �         n# e#$ r dZ! e"d
�  �         Y nw xY wddl$m%Z& ddl'Z(	 ddl)m*Z* d
Z+ e"d�  �         n# e#$ r dZ+ e"d�  �         Y nw xY w G d� d�  �        Z,d� Z-e.dk    r e-�   �          dS dS )u�  
ULTIMATE BITCOIN TRADING SYSTEM V9.0 - STABLE EDITION
======================================================
FUNKTIONAL GETESTETE VERSION MIT GUTEN FEATURES
- Basiert auf funktionierender V6.0
- Erweiterte Features aber stabile Basis
- Vollständige Funktionalitätsprüfung
- Robuste Error-Handling
- Optimierte Performance
- Production-Ready Code

ULTIMATE TRADING SYSTEM V9.0 - STABIL UND FUNKTIONAL!
�    N)�datetime�	timedelta)�Dict�List�Tuple�Optional�ignore)�RobustScaler)�RandomForestRegressor)�mean_squared_error�r2_score)�TimeSeriesSplitTu   ✅ XGBoost verfügbarFu7   ⚠️ XGBoost nicht verfügbar - verwende RandomForest)�statsu   ✅ SciPy verfügbaru:   ⚠️ SciPy nicht verfügbar - verwende NumPy Statistikenc                   �  � e Zd ZdZd� Zdefd�Zdee         defd�Z	de
j        fd�Zdefd�Z
de
j        fd	�Zd
e
j        dede
j        fd�Zd
e
j        de
j        fd�Zd
e
j        de
j        fd�Zd
e
j        defd�Zd
e
j        de
j        fd�Zd
e
j        de
j        fd�Zdefd�Zd
e
j        defd�Zd
e
j        defd�Zdefd�Zde
j        fd�ZdS )�UltimateBitcoinTradingSystemV9z�
    ULTIMATE BITCOIN TRADING SYSTEM V9.0 - STABLE EDITION
    ======================================================
    Funktional getestete Version mit guten Features
    c                 �(  � d| _         d| _        t          j        �   �         | _        ddddd�| _        t
          j        �   �         | _        i | _	        d | _
        d| _        i | _        i | _
        t          �   �         | _        d	d
d
t           d
d
d�| _        d	| _        d	| _        d| _        d | _        d | _        g | _        d
| _        d | _        g | _        ddd�ddd�ddd�d�| _        i | _        | j        �                    �   �         d
d
ddd
dddd
t=          | j        �                    �   �         �  �        d�| _         tC          d�  �         tC          d| j         � ��  �         tC          d| j        �"                    d�  �        � ��  �         tC          dtG          | j        �  �        � d��  �         tC          d| j         d         � d tG          | j        �  �        � d!��  �         tC          d"t           � d#tH          � ��  �         tC          d$�  �         d S )%Nz#Ultimate_Trading_System_v9.0_StablezBTC-USDz:https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDTz7https://api.coinbase.com/v2/exchange-rates?currency=BTCzKhttps://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usdz2https://api.kraken.com/0/public/Ticker?pair=XBTUSD)�binance�coinbase�	coingecko�kraken�<   FT)�sentiment_analysis�technical_indicators_extended�multi_timeframe_analysis�ensemble_predictions�volatility_modeling�trend_detectioni  r   �7d�1h��period�interval�30d�4h�90d�1d)r   r$   r&   �        )�script_start_time�total_scans�successful_scans�current_accuracy�
best_accuracy�api_calls_count�live_data_quality�prediction_accuracy�total_analysis_time�errors_count�features_enabledz2Ultimate Bitcoin Trading System V9.0 initialisiertz	Version: zStart-Zeit: z%d.%m.%Y %H:%M:%SzGetestete APIs: z QuellenzErweiterte Features: r2   �/z
 aktiviertu   ML-Verfügbarkeit: XGBoost=z, SciPy=u1   Funktionalität: Vollständig getestet und stabil)%�VERSION�SYMBOLr   �nowr(   �
api_endpoints�pd�	DataFrame�market_data�live_price_cache�last_cache_time�cache_duration�	ml_models�model_performancer
   �scaler�XGBOOST_AVAILABLE�advanced_features�auto_training_enabled�auto_training_active�training_interval�last_training_time�training_thread�scan_results�scan_counter�last_scan_result�prediction_visualizations�
timeframes�multi_timeframe_data�	isoformat�sum�values�
session_stats�print�strftime�len�SCIPY_AVAILABLE)�selfs    �,E:\Dev\ultimate_bitcoin_trading_system_v9.py�__init__z'UltimateBitcoinTradingSystemV9.__init__A   sh  � �<������ "*������ T�Q�f�J�	
� 
��� �<�>�>��� "���#��� ��� ���!#���"�n�n��� #(�-1�(,�$5�#'�#�
"
� "
��� &+��"�$)��!�!%���"&���#��� ������ $���)+��&� "�t�4�4�"��5�5�"��5�5�
� 
���
 %'��!� "&�!7�!A�!A�!C�!C�� !� #� � �!$�#&�#&�� #�D�$:�$A�$A�$C�$C� D� D�
� 
��� 	�C�D�D�D�
�(�$�,�(�(�)�)�)�
�S�T�3�<�<�=P�Q�Q�S�S�T�T�T�
�B��T�%7�!8�!8�B�B�B�C�C�C�
�v�d�&8�9K�&L�v�v�s�SW�Si�Oj�Oj�v�v�v�w�w�w�
�X�,=�X�X��X�X�Y�Y�Y�
�B�C�C�C�C�C�    �returnc                 �  �� 	 t          d�  �         t          j        �   �         }i }i }d}g }	 t          j        �   �         }t          j        | j        d         d��  �        }t          j        �   �         |z
  }|j        dk    r{|�                    �   �         }	t          |	d         �  �        }
d|
cxk    rd	k    r+n n(|
|d<   ||d<   |d
z
  }t          d|
d�d
|d�d��  �         n7|�                    d|
d���  �         n|�                    d|j        � ��  �         nD# t          $ r7}|�                    dt          |�  �        dd�         � ��  �         Y d}~nd}~ww xY w	 t          j        �   �         }t          j        | j        d         d��  �        }t          j        �   �         |z
  }|j        dk    r�|�                    �   �         }	t          |	d         d         d         �  �        }
d|
cxk    rd	k    r+n n(|
|d<   ||d<   |d
z
  }t          d|
d�d
|d�d��  �         n7|�                    d|
d���  �         n|�                    d|j        � ��  �         nD# t          $ r7}|�                    dt          |�  �        dd�         � ��  �         Y d}~nd}~ww xY w	 t          j        �   �         }t          j        | j        d         d��  �        }t          j        �   �         |z
  }|j        dk    r�|�                    �   �         }	t          |	d         d         �  �        }
d|
cxk    rd	k    r+n n(|
|d<   ||d<   |d
z
  }t          d |
d�d
|d�d��  �         n7|�                    d!|
d���  �         n|�                    d"|j        � ��  �         nD# t          $ r7}|�                    d#t          |�  �        dd�         � ��  �         Y d}~nd}~ww xY w	 t          j        �   �         }t          j        | j        d$         d��  �        }t          j        �   �         |z
  }|j        dk    r�|�                    �   �         }	d%|	v r�|	d%         r�t          |	d%         �                    �   �         �  �        d         }t          |d&         d         �  �        }
d|
cxk    rd	k    r+n n(|
|d$<   ||d$<   |d
z
  }t          d'|
d�d
|d�d��  �         nM|�                    d(|
d���  �         n3|�                    d)�  �         n|�                    d*|j        � ��  �         nD# t          $ r7}|�                    d+t          |�  �        dd�         � ��  �         Y d}~nd}~ww xY w|�r�t          |�                    �   �         �  �        }
t          j        |
�  �        }t          j        |
�  �        }t          j        |
�  �        }t#          |
�  �        t%          |
�  �        z
  }t&          r�t)          |
�  �        d,k    r�	 t          j        t-          j        |
�  �        �  �        ��fd-�t1          |
�  �        D �   �         }|r4t          j        |�  �        }t)          |
�  �        t)          |�  �        z
  }n|}d}n#  |}d}Y nxY w|}d}|t)          | j        �  �        z  }|dk    rt#          d.d/||z  z
  �  �        nd.}|rJt          j        t          |�                    �   �         �  �        �  �        }t#          d.d/|d0z  z
  �  �        }nd.}|d1z  |d2z  z   |d3z  z   }t          j        �   �         |z
  }|||||t)          | j        �  �        |||d4�||||d5�|||t3          j        �   �         �                    �   �         d6d7�
}| j        d8xx         |z
  cc<   || j        d9<   t          d:|d���  �         t          d;|d<�d
|� d=t)          | j        �  �        � d>��  �         t          d?|d<���  �         t          |rd@|d�dA�ndB�  �         |dk    rt          dC|� ��  �         |r>t          dDt)          |�  �        � ��  �         |dd,�         D ]}t          dE|� ��  �         �|S t          dF�  �        �# t          $ rY}t          dG|� ��  �         | j        dHxx         d
z
  cc<   | �                    |dIt=          �   �         v r|ng �  �        cY d}~S d}~ww xY w)Jz�
        ERWEITERTE LIVE-DATEN V9.0
        ==========================
        Sammelt Live-Daten mit erweiterten Features aber stabiler Basis
        z$Sammle erweiterte Live-Daten V9.0...r   r   �
   )�timeout��   �price�'  � � �   u   ✅ Binance: $�,.2fz (�.3f�s)z Binance: Unrealistischer Preis $zBinance: HTTP z	Binance: N�2   r   �data�rates�USDu   ✅ Coinbase: $z!Coinbase: Unrealistischer Preis $zCoinbase: HTTP z
Coinbase: r   �bitcoin�usdu   ✅ CoinGecko: $z"CoinGecko: Unrealistischer Preis $zCoinGecko: HTTP zCoinGecko: r   �result�cu
   ✅ Kraken: $zKraken: Unrealistischer Preis $zKraken: Keine Daten in Responsez
Kraken: HTTP zKraken: �   c                 �2   �� g | ]\  }}�|         d k     �|��S )rn   � )�.0�i�p�z_scoress      �rW   �
<listcomp>zLUltimateBitcoinTradingSystemV9.get_enhanced_live_data_v9.<locals>.<listcomp>  s/   �� �'`�'`�'`�d�a��PX�YZ�P[�^_�P_�P_��P_�P_�P_rY   r'   �      �?g      @�      �?�333333�?皙�����?)�std�range�outliers_removed)�overall_quality�api_success_rate�price_consistency�response_time_quality�v9.0_stable)
�consensus_price�
raw_consensus�
mean_price�individual_prices�successful_apis�
total_apis�price_statistics�quality_metrics�api_response_times�
api_errors�
fetch_time�	timestamp�versionr-   r.   u   ✅ Erweiterte Live-Daten: $u   📊 Datenqualität: �.1%r3   z APIs)u   📈 Preis-Konsistenz: u    ⚡ Durchschnittliche API-Zeit: �su   ⚡ Keine API-Zeiten verfügbaru   🔍 Ausreißer entfernt: u   ⚠️ API-Fehler: z   - z#Keine Live-Preise von APIs erhaltenu'   ❌ FEHLER bei erweiterten Live-Daten: r1   r�   )rR   �time�requests�getr7   �status_code�json�float�append�	Exception�str�listrP   �np�median�meanrz   �max�minrU   rT   �absr   �zscore�	enumerater   r6   rN   rQ   �_get_stable_fallback_data_v9�locals)rV   �
start_time�live_pricesr�   r�   r�   �	api_start�response�api_timerg   r_   �e�ticker_data�prices_listr�   r�   �	price_std�price_range�clean_prices�clean_consensusr|   r~   r   �avg_response_timer�   r}   r�   rl   �errorrt   s                                @rW   �get_enhanced_live_data_v9z8UltimateBitcoinTradingSystemV9.get_enhanced_live_data_v9�   s�
  �� �E	h��8�9�9�9�����J��K�!#���O��J�
=� �I�K�K�	�#�<��(:�9�(E�r�R�R�R���9�;�;��2���'�3�.�.�#�=�=�?�?�D�!�$�w�-�0�0�E���/�/�/�/��/�/�/�/�/�16��I�.�8@�*�9�5�'�1�,���M�u�M�M�M�X�M�M�M�M�N�N�N�N�"�)�)�*Y�U�*Y�*Y�*Y�Z�Z�Z�Z��%�%�&M�x�7K�&M�&M�N�N�N���� 
=� 
=� 
=��!�!�";�c�!�f�f�S�b�S�k�";�";�<�<�<�<�<�<�<�<�����
=����
>� �I�K�K�	�#�<��(:�:�(F�PR�S�S�S���9�;�;��2���'�3�.�.�#�=�=�?�?�D�!�$�v�,�w�"7��">�?�?�E���/�/�/�/��/�/�/�/�/�27��J�/�9A�*�:�6�'�1�,���N��N�N�N�h�N�N�N�N�O�O�O�O�"�)�)�*Z�e�*Z�*Z�*Z�[�[�[�[��%�%�&N��8L�&N�&N�O�O�O���� 
>� 
>� 
>��!�!�"<�s�1�v�v�c�r�c�{�"<�"<�=�=�=�=�=�=�=�=�����
>����
?� �I�K�K�	�#�<��(:�;�(G�QS�T�T�T���9�;�;��2���'�3�.�.�#�=�=�?�?�D�!�$�y�/�%�"8�9�9�E���/�/�/�/��/�/�/�/�/�38��K�0�:B�*�;�7�'�1�,���O��O�O�O�x�O�O�O�O�P�P�P�P�"�)�)�*[�u�*[�*[�*[�\�\�\�\��%�%�&O��9M�&O�&O�P�P�P���� 
?� 
?� 
?��!�!�"=��A���s��s��"=�"=�>�>�>�>�>�>�>�>�����
?����
<� �I�K�K�	�#�<��(:�8�(D�b�Q�Q�Q���9�;�;��2���'�3�.�.�#�=�=�?�?�D��4�'�'�D��N�'�&*�4��>�+@�+@�+B�+B�&C�&C�A�&F�� %�k�#�&6�q�&9� :� :�� �E�3�3�3�3�V�3�3�3�3�3�49�K��1�;C�.�x�8�+�q�0�O�!�"P�%�"P�"P�"P��"P�"P�"P�"P�Q�Q�Q�Q�&�-�-�.\�PU�.\�.\�.\�]�]�]�]�"�)�)�*K�L�L�L�L��%�%�&L�h�6J�&L�&L�M�M�M���� 
<� 
<� 
<��!�!�":�S��V�V�C�R�C�[�":�":�;�;�;�;�;�;�;�;�����
<���� � \
G�"�;�#5�#5�#7�#7�8�8�� #%�)�K�"8�"8���W�[�1�1�
��F�;�/�/�	�!�+�.�.��[�1A�1A�A�� #� )�s�;�'7�'7�!�';�';�-�#%�6�%�,�{�*C�*C�#D�#D��'`�'`�'`�'`�i��6L�6L�'`�'`�'`��'� 1�.0�i��.E�.E�O�/2�;�/?�/?�#�l�BS�BS�/S�,�,�.=�O�/0�,���-�*9��+,�(�(�(����&5�O�'(�$� $3�S��9K�5L�5L�#L� �Ud�gh�Uh�Uh�C��S�I��4O�-P�$Q�$Q�$Q�nq�!� &� 0�(*���5G�5N�5N�5P�5P�0Q�0Q�(R�(R�%�,/��S�<M�PS�<S�5T�,U�,U�)�)�,/�)� %�s�*�%��+�,�)�C�/�0�  � "�Y�[�[�:�5�
� (7�%4�",�)4�'6�"%�d�&8�"9�"9�(�!,�,<�)� )� ,;�,<�->�1F�	(� (� +=�",�",�!)����!9�!9�!;�!;�,�-� ��4 �"�#4�5�5�5��H�5�5�5�:I��"�#6�7��K�_�K�K�K�L�L�L��v�o�v�v�v�_�v�v�WZ�[_�[m�Wn�Wn�v�v�v�w�w�w��G�0A�G�G�G�H�H�H��Ug�  O�Q�9J�Q�Q�Q�Q�Q�  nO�  P�  P�  P�#�a�'�'��I�7G�I�I�J�J�J�� /��A��J���A�A�B�B�B�!+�B�Q�B�� /� /���o�e�o�o�.�.�.�.��
�  � E�F�F�F��� 	h� 	h� 	h��?�A�?�?�@�@�@���~�.�.�.�!�3�.�.�.� �4�4�Q�l�V\�V^�V^�F^�F^�
�
�df�g�g�g�g�g�g�g�g�����	h���s�   �*a/ �C-D �a/ �
E�&-E�a/ �E�a/ �!C9I �a/ �
J�%-J�a/ �J�a/ � C3N �a/ �
O�-O�a/ �O�a/ �ET �a/ �
U�&-U�a/ �U�Ba/ �:A;Y6 �5a/ �6Y>�<G#a/ � a/ �/
c�9Ac
�c�
cr�   c           
      �T  � 	 t          d�  �         	 t          d�  �         t          j        | j        �  �        }|j        }|�                    dd�  �        }|red|cxk    rdk    rXn nUt          d|d���  �         |d	|id
d
ddid
t
          j        �   �         �                    �   �         d	t          |�  �        |dd�S n)# t          $ r}t          d|� ��  �         Y d}~nd}~ww xY w| j        j        s�	 | j        d         j
        d         }t          j        �                    dd�  �        }t#          dt%          d|�  �        �  �        }|d
|z   z  }	t          d|	d���  �         |	d|	idt'          | j        �  �        ddidt
          j        �   �         �                    �   �         dt          |�  �        |dd�S # t          $ r}
t          d|
� ��  �         Y d}
~
nd}
~
ww xY wdt          j        �                    dd�  �        z   }t#          dt%          d |�  �        �  �        }t          d!|d���  �         |d"|idt'          | j        �  �        dd#idt
          j        �   �         �                    �   �         d$t          |�  �        |dd�S # t          $ r�}t          d%|� ��  �         | j        d&xx         d
z
  cc<   dd'didt'          | j        �  �        dd(idt
          j        �   �         �                    �   �         d)t          |�  �        t          |�  �        d*d+�cY d}~S d}~ww xY w),zStabiles Fallback System V9.0z*Aktiviere stabiles Fallback System V9.0...z"Versuche Yahoo Finance Fallback...�regularMarketPricer   r`   ra   u   ✅ Yahoo Finance Fallback: $rc   �
yahoo_financerb   r}   皙�����?rv   z
v9.0_fallback)r�   r�   r�   r�   r�   r�   r�   �fallback�original_errorr�   r�   u#   ❌ Yahoo Finance Fallback Fehler: N�Close����������Mb`?�{�G�zt��{�G�zt?u   ✅ Historischer Fallback: $�historical_fallback�ffffff�?皙�����?�
historicalu"   ❌ Historischer Fallback Fehler: �ȩ i�  iP�  i@
 u%   ⚠️ Stabiler Emergency Fallback: $�emergency_fallbackrw   �	emergencyu*   ❌ KRITISCHER FEHLER im Fallback System: r1   �critical_fallbackrx   �criticalz
v9.0_critical)r�   r�   r�   r�   r�   r�   r�   r�   r�   �fallback_errorr�   )rR   �yf�Tickerr5   �infor�   r   r6   rN   r�   r�   r:   �empty�ilocr�   �random�normalr�   r�   rT   r7   rQ   )
rV   r�   r�   �btcr�   �
current_price�yf_error�
last_price�realistic_change�fallback_price�
hist_error�emergency_pricer�   s
                rW   r�   z;UltimateBitcoinTradingSystemV9._get_stable_fallback_data_v9_  s�  � �[	��>�?�?�?�
H��:�;�;�;��i���,�,���x�� $���)=�q� A� A�
� � �U�m�%E�%E�%E�%E�v�%E�%E�%E�%E�%E��N�-�N�N�N�O�O�O�+8�.=�}�-M�+,�&'�,=�s�+C�&)�%-�\�^�^�%=�%=�%?�%?�$3�*-�e�*�*�&0�#2�� � ��� � 
H� 
H� 
H��F�H�F�F�G�G�G�G�G�G�G�G�����
H���� �#�)� 
M�M�!%�!1�'�!:�!?��!C�J�')�y�'7�'7��5�'A�'A�$�'*�6�3�u�>N�3O�3O�'P�'P�$�%/�1�7G�3G�%H�N��N��N�N�N�O�O�O�+9�.C�^�-T�+,�&)�$�*<�&=�&=�,=�s�+C�&)�%-�\�^�^�%=�%=�%?�%?�$0�*-�e�*�*�&0�#2�� � �� !� M� M� M��K�z�K�K�L�L�L�L�L�L�L�L�����M����
 %�r�y�'7�'7��3�'?�'?�?�O�!�%��V�_�)E�)E�F�F�O��P�/�P�P�P�Q�Q�Q� $3�&:�O�%L�#$�!�$�"4�5�5�$5�s�#;�!�%�\�^�^�5�5�7�7�'�"%�e�*�*�(�*�� � 
�� � 	� 	� 	��O�~�O�O�P�P�P���~�.�.�.�!�3�.�.�.� $*�&9�6�%B�#$�!�$�"4�5�5�$5�s�#;�!�%�\�^�^�5�5�7�7�&�"%�e�*�*�"%�n�"5�"5�*�� � 
� 
� 
� 
� 
� 
�����		���sn   �J �B+B? �>J �?
C%�	C �J � C%�%J �5CF: �:
G �G�J �G � B*J �
L'�BL"�L'�"L'c                 �z  � 	 t          d�  �         t          j        �   �         }| j        rvt          j        �   �         | j        z
  t          | j        ��  �        k     rD| j        j        s8t          dt          j        �   �         | j        z
  j	        � d��  �         | j        S | j
        d         rX| �                    �   �         }|r-d|v r)|d         }t          dt          |�  �        � d��  �         n)| �
                    �   �         }n| �
                    �   �         }	 | �                    �   �         }|d	         }|r:d
|cxk    rdk    r-n n*| �                    ||�  �        }t          d|d
���  �         nt          d|d
���  �         n)# t           $ r}t          d|� ��  �         Y d}~nd}~ww xY w| �                    |�  �        }| �                    |�  �        }|| _        t          j        �   �         | _        t          j        �   �         |z
  }t          dt          |�  �        � d|d�d��  �         |S # t           $ r{}t          d|� ��  �         | j        dxx         dz
  cc<   | j        j        st          d�  �         | j        cY d}~S t          d�  �         | �                    �   �         cY d}~S d}~ww xY w)z�
        STABILE MARKTDATEN V9.0
        =======================
        Sammelt stabile historische und Live-Daten mit erweiterten Features
        z!Sammle stabile Marktdaten V9.0...)�secondszVerwende Cache-Daten (Alter: re   r   r   zMulti-Timeframe Daten: z Datenpunkte (1h)r�   r`   ra   zLive-Preis integriert: $rc   u5   ⚠️ Live-Preis außerhalb realistischer Grenzen: $�Live-Preis Integration Fehler: NzStabile Marktdaten V9.0: z Datenpunkte in �.2fr�   u)   ❌ FEHLER bei stabilen Marktdaten V9.0: r1   rb   z*Verwende vorherige Marktdaten als Fallbackz Generiere stabile Fallback-Daten)rR   r�   r<   r   r6   r   r=   r:   r�   r�   rB   �_get_multi_timeframe_data_v9rT   �_get_standard_yahoo_data_v9r�   �_integrate_live_price_v9r�   �_stable_data_cleaning_v9�_calculate_stable_indicators_v9rQ   �!_generate_stable_fallback_data_v9)	rV   r�   �
multi_data�hist�	live_datar�   r�   �dfr�   s	            rW   �get_stable_market_data_v9z8UltimateBitcoinTradingSystemV9.get_stable_market_data_v9�  sF  � �@	@��5�6�6�6�����J� �$� 
(������!5�5�	�$�J]�8^�8^�8^�^�^��$�*� _��i�x�|�~�~��H\�7\�6e�i�i�i�j�j�j��'�'� �%�&@�A� 

:�!�>�>�@�@�
�� >�$�*�"4�"4�%�d�+�D��P�C��I�I�P�P�P�Q�Q�Q�Q�  �;�;�=�=�D�D� �7�7�9�9��
=� �:�:�<�<�	� )�*;� <�
� � h�U�m�%E�%E�%E�%E�v�%E�%E�%E�%E�%E��8�8��}�M�M�D��I�]�I�I�I�J�J�J�J��f�R_�f�f�f�g�g�g���� 
=� 
=� 
=��;��;�;�<�<�<�<�<�<�<�<�����
=���� �.�.�t�4�4�B� �5�5�b�9�9�B�  "�D��#+�<�>�>�D� �����z�1�J��X�c�"�g�g�X�X�z�X�X�X�X�Y�Y�Y��I��� 
	@� 
	@� 
	@��A�a�A�A�B�B�B���~�.�.�.�!�3�.�.�.� �#�)� 
@��B�C�C�C��'�'�'�'�'�'�'��8�9�9�9��=�=�?�?�?�?�?�?�?�?�����
	@���s^   �BH5 �!A9H5 �A+F �H5 �
F-�F(�#H5 �(F-�-BH5 �5
J:�?AJ5�J:�
"J5�/J:�5J:c           	      �  � 	 t          d�  �         i }| j        �                    �   �         D �]!\  }}	 t          j        | j        �  �        }|�                    |d         |d         ��  �        }|j        s�|�                    �   �         }||d         dk             }||d         dk             }t          |�  �        d	k    r)|||<   t          d
|� dt          |�  �        � d��  �         n7t          d
|� dt          |�  �        � d��  �         nt          d|� d��  �         ��# t          $ r }t          d|� d|� ��  �         Y d}~��d}~ww xY wd|v r|| _        |d         S d|v rt          d�  �         |d         S d|v rt          d�  �         |d         S t          d�  �        �# t          $ r}t          d|� ��  �         i cY d}~S d}~ww xY w)z9Sammle Multi-Timeframe Daten V9.0 (ERWEITERT aber STABIL)zSammle Multi-Timeframe Daten...r!   r"   r    r�   i�  �Volumer   r\   u   ✅ z: � Datenpunkteu   ⚠️ u   : Nicht genügend Daten (�)u   ❌ z: Keine Daten erhaltenz	 Fehler: Nr   r$   zFallback zu 4h Datenr&   zFallback zu 1d Datenu&   Keine Multi-Timeframe Daten verfügbarzMulti-Timeframe Fehler: )rR   rL   �itemsr�   r�   r5   �historyr�   �dropnarT   r�   rM   )rV   r�   �tf_name�	tf_configr�   r�   r�   s          rW   r�   z;UltimateBitcoinTradingSystemV9._get_multi_timeframe_data_v9  sG  � �)	��3�4�4�4��J�&*�o�&;�&;�&=�&=� 
8� 
8�"���8��)�D�K�0�0�C��;�;�i��.A�I�V`�La�;�b�b�D��:� F�#�{�{�}�}��#�D��M�D�$8�9��#�D��N�Q�$6�7���t�9�9��?�?�26�J�w�/�!�"K��"K�"K�C��I�I�"K�"K�"K�L�L�L�L�!�"Z�G�"Z�"Z�c�RV�i�i�"Z�"Z�"Z�[�[�[�[��D�W�D�D�D�E�E�E��� � 8� 8� 8��6��6�6�1�6�6�7�7�7�7�7�7�7�7�����8���� �z�!�!�,6��)�!�$�'�'���#�#��,�-�-�-�!�$�'�'���#�#��,�-�-�-�!�$�'�'�� H�I�I�I��� 	� 	� 	��0�Q�0�0�1�1�1��I�I�I�I�I�I�����	���sY   �0F' �C.D"�!F' �"
E�,E�F' �E�F' �"F' �=F' �F' �'
G�1G
�G�
Gc                 �>  � 	 t          d�  �         t          j        | j        �  �        }|�                    dd��  �        }|j        rt
          d�  �        �t          dt          |�  �        � d��  �         |S # t          $ r}t          d|� ��  �         |�d	}~ww xY w)
z3Sammle Standard Yahoo Finance Daten V9.0 (GETESTET)z&Sammle Standard Yahoo Finance Daten...r   r   r    z"Keine Yahoo Finance Daten erhaltenzYahoo Finance Daten: r�   zYahoo Finance Fehler: N)rR   r�   r�   r5   r�   r�   r�   rT   )rV   r�   r�   r�   s       rW   r�   z:UltimateBitcoinTradingSystemV9._get_standard_yahoo_data_v93  s�   � �
	��:�;�;�;��)�D�K�(�(�C��;�;�d�T�;�:�:�D��z� 
F�� D�E�E�E��A�#�d�)�)�A�A�A�B�B�B��K��� 	� 	� 	��.�1�.�.�/�/�/��G�����	���s   �A6A9 �9
B�B�Br�   r�   c           
      �f  � 	 |j         r|S t          j        �   �         }|d         j        d         }||z
  |z  }t	          dt          d|�  �        �  �        }|t	          ||dt
          |�  �        dz  z   z  �  �        t          ||dt
          |�  �        dz  z
  z  �  �        ||d         j        dd	�         �                    �   �         d
�}t          j	        |�  �        }|�
                    �   �         D ]\  }}	|	|j        ||f<   �|S # t          $ r}
t          d|
� ��  �         |cY d	}
~
S d	}
~
ww xY w)z#Integriere Live-Preis V9.0 (STABIL)r�   r�   g���Q���g���Q��?rb   rw   r�   i����N��Open�High�Lowr�   r�   r�   )r�   r   r6   r�   r�   r�   r�   r�   r8   �	Timestampr�   �locr�   rR   )rV   r�   r�   �current_time�
last_close�price_change�new_row�	new_index�col�valuer�   s              rW   r�   z7UltimateBitcoinTradingSystemV9._integrate_live_price_v9D  sb  � �	��z� 
���#�<�>�>�L��g��+�B�/�J� *�J�6�*�D�L��u�c�$��&=�&=�>�>�L� #��M�:��S��=N�=N�QT�=T�9T�+U�V�V��=�*��C��<M�<M�PS�<S�8S�*T�U�U�&��x�.�-�c�d�d�3�8�8�:�:�� �G� ��\�2�2�I�%�m�m�o�o� 
1� 
1�
��U�+0����C��(�(��K��� 	� 	� 	��7�A�7�7�8�8�8��K�K�K�K�K�K�����	���s#   �D �C<D �
D0�D+�%D0�+D0r�   c                 �  �� 	 �j         r�S t          d�  �         t          ��  �        }��                    �   �         �dD ]L}|�j        v rA��|         dk             ���|         dk              ��|         �                    �   �         �|<   �Mt
          �fd�dD �   �         �  �        rB�g d�         �                    d��  �        �d<   �g d�         �                    d��  �        �d	<   d
�j        v re�d
         �                    �   �         �d
<   ��d
         dk             ��d
         �	                    �   �         }|dk    r|�j
        �d
         dk    d
f<   t          rvt          ��  �        dk    rc	 t          j        t          j        �d
         �  �        �  �        }�|dk              �n)# t          $ r}t          d|� ��  �         Y d}~nd}~ww xY w��                    �   �         ���j        �                    d��  �                  �t          ��  �        }||z
  }t          d|� d|� d��  �         t          d|� d��  �         �S # t          $ r3}t          d|� ��  �         | j        dxx         dz
  cc<   �cY d}~S d}~ww xY w)zStabile Datenbereinigung V9.0u(   Führe stabile Datenbereinigung durch...)r�   r�   r�   r�   �  i@B c              3   �*   �K  � | ]
}|�j         v V � �d S �N��columns�rq   r�   r�   s     �rW   �	<genexpr>zJUltimateBitcoinTradingSystemV9._stable_data_cleaning_v9.<locals>.<genexpr>y  �*   �� � � �Q�Q��3�"�*�$�Q�Q�Q�Q�Q�QrY   rb   ��axisr�   r�   r�   r   �   r�   �   u#   ⚠️ Z-Score Bereinigung Fehler: N�last)�keepzDatenbereinigung: z von z Datenpunkten entferntzBereinigte Daten: r�   z&FEHLER bei stabiler Datenbereinigung: r1   )r�   rR   rT   r�   r  r�   �allr�   r�   r�   r�   rU   r�   r   r�   r�   �
sort_index�index�
duplicatedrQ   )	rV   r�   �original_lengthr�   �
volume_medianrt   r�   �cleaned_length�
removed_counts	    `       rW   r�   z7UltimateBitcoinTradingSystemV9._stable_data_cleaning_v9d  s  �� �:	��x� 
��	��<�=�=�=�!�"�g�g�O� �����B� 8� 
,� 
,���"�*�$�$��B�s�G�d�N�+�B��B�s�G�g�-�.�B� ��g�k�k�m�m�B�s�G�� �Q�Q�Q�Q�0P�Q�Q�Q�Q�Q� 
M�� @� @� @�A�E�E�1�E�M�M��6�
��?�?�?�@�D�D�!�D�L�L��5�	� �2�:�%�%�!�(�|�/�/�1�1��8����8���)�*�� !#�8�� 3� 3� 5� 5�
� �1�$�$�:G�B�F�2�h�<�1�,�h�6�7� � 
E�3�r�7�7�R�<�<�E�!�v�e�l�2�g�;�&?�&?�@�@�H��H�q�L�)�B�B�� � E� E� E��C��C�C�D�D�D�D�D�D�D�D�����E���� �����B��R�X�(�(�f�(�5�5�5�6�B� ��W�W�N�+�n�<�M��b�}�b�b�?�b�b�b�c�c�c��C�~�C�C�C�D�D�D��I��� 	� 	� 	��>�1�>�>�?�?�?���~�.�.�.�!�3�.�.�.��I�I�I�I�I�I�����	���sN   �I
 �E&I
 �38F, �+I
 �,
G�6G
�I
 �
G�A7I
 �

J�(J�<J�Jc                 ��  � 	 |j         st          |�  �        dk     rt          d�  �         |S t          d�  �         |d         �                    �   �         |d<   t	          j        |d         |d         �                    d�  �        z  �  �        |d<   dD ]H}t          |�  �        |k    r3|d         �                    |�  �        �                    �   �         |d	|� �<   �Id
D ]|}t          |�  �        |k    rg|d         �                    |�  �        �	                    �   �         |d|� �<   |d         �
                    |��  �        �	                    �   �         |d
|� �<   �}t          |�  �        dk    r�|d         �                    �   �         }|�                    |dk    d�  �        �                    d��  �        �	                    �   �         }|�                    |dk     d�  �         �                    d��  �        �	                    �   �         }||dz   z  }ddd|z   z  z
  |d<   |d         �
                    d�  �        �                    dd�  �        |d<   t          |�  �        dk    r�|d         �
                    d��  �        �	                    �   �         }|d         �
                    d��  �        �	                    �   �         }	||	z
  |d<   |d         �
                    d��  �        �	                    �   �         |d<   |d         |d         z
  |d<   t          |�  �        dk    r�d}
|d         �                    |
�  �        �	                    �   �         }|d         �                    |
�  �        �                    �   �         }||dz  z   |d<   ||d<   ||dz  z
  |d<   |d         |d         z
  }
|d         |d         z
  |
dz   z  |d<   |d         �
                    d �  �        �                    dd�  �        |d<   | j        d!         �r�t          |�  �        dk    r�|d"         |d#         z
  }t	          j        |d"         |d         �                    d�  �        z
  �  �        }t	          j        |d#         |d         �                    d�  �        z
  �  �        }t#          j        |||gd�$�  �        �                    d�$�  �        }|�                    d�  �        �	                    �   �         |d%<   t          |�  �        dk    r�|d#         �                    d�  �        �                    �   �         }|d"         �                    d�  �        �                    �   �         }d|d         |z
  ||z
  dz   z  z  }|�
                    d�  �        �                    dd�  �        |d&<   |d&         �                    d'�  �        �	                    �   �         |d(<   | j        d)         r�d*D ]�}t          |�  �        |k    rp|d         |d         �                    |�  �        k    �                    t,          �  �        |d+|� �<   |d         |d         �                    |�  �        z  dz
  |d,|� �<   ��| j        d-         r�t          |�  �        d.k    r�|d         dz  }|�                    d�  �        �	                    �   �         �                    t          j        �  �        |d/<   |d/         �                    �   �         }|d/         |k    �                    t,          �  �        |d0<   d1|j        v r�dD ]H}t          |�  �        |k    r3|d1         �                    |�  �        �	                    �   �         |d2|� �<   �Id3|j        v rI|d1         |d3         dz   z  |d4<   |d4         �
                    d5�  �        �                    d6d7�  �        |d4<   |�                    t          j        g�8�  �        j        }|D �]�}||         �                    �   �         �                    �   �         �r`d|v sd9|v r||         �
                    d�  �        ||<   �Wd|v r||         �
                    d �  �        ||<   �zd4|v r||         �
                    d5�  �        ||<   ��d+|v r||         �
                    d�  �        ||<   ��d0|v r||         �
                    d�  �        ||<   ��||         �
                    d:�;�  �        �
                    d<�;�  �        ||<   ||         �                    �   �         �                    �   �         rN||         �                    �   �         }t#          j        |�  �        rd}||         �
                    |�  �        ||<   ���t          d=� |j        D �   �         �  �        }t          d>|� d?t          |�  �        � d@��  �         |S # t>          $ r3}t          dA|� ��  �         | j         dBxx         dz
  cc<   |cY dC}~S dC}~ww xY w)Dz,Berechne stabile technische Indikatoren V9.0r
  u1   Nicht genügend Daten für technische Indikatorenz/Berechne stabile technische Indikatoren V9.0...r�   �Returnsrb   �Log_Returns)r\   r
  �Volatility_)r\   r
  rf   �SMA_)�span�EMA_�   r   )�windowg�����|�=�d   �RSIrf   �   �   �MACD�	   �MACD_Signal�MACD_Histogramrn   �BB_Upper�	BB_Middle�BB_Lower�BB_Positionrw   r   r�   r�   r  �ATR�Stoch_Kr  �Stoch_Dr   ��   r\   r
  �Trend_�	Momentum_r   �   �	GARCH_Vol�
Vol_Regimer�   �Volume_SMA_�
Volume_SMA_20�Volume_Ratiorv   r�   g      $@)�include�Stoch�ffill)�method�bfillc                 �   � g | ]}|d v�|��	S )r�   rp   )rq   r�   s     rW   ru   zRUltimateBitcoinTradingSystemV9._calculate_stable_indicators_v9.<locals>.<listcomp>%  s#   � �"v�"v�"v�3��Ku�@u�@u�3�@u�@u�@urY   z*Stabile technische Indikatoren berechnet: u    Indikatoren für r�   z-FEHLER bei stabilen technischen Indikatoren: r1   N)!r�   rT   rR   �
pct_changer�   �log�shift�rollingrz   r�   �ewm�diff�where�fillna�cliprB   r�   r8   �concatr�   r�   �astype�int�apply�sqrtr�   r  �
select_dtypes�number�isna�anyr�   rQ   )rV   r�   r  r!   �delta�gain�loss�rs�ema_12�ema_26�	bb_period�	bb_middle�
bb_std_dev�bb_range�high_low�
high_close�	low_close�
true_range�
lowest_low�highest_high�	k_percent�returns_squared�
vol_median�numeric_columnsr�   �
median_val�indicator_countr�   s                               rW   r�   z>UltimateBitcoinTradingSystemV9._calculate_stable_indicators_v9�  s�	  � �I	��x� 
�3�r�7�7�R�<�<��I�J�J�J��	��C�D�D�D� �w�K�2�2�4�4�B�y�M� "��r�'�{�R��[�5F�5F�q�5I�5I�'I� J� J�B�}�� #� 
U� 
U���r�7�7�f�$�$�13�I��1F�1F�v�1N�1N�1R�1R�1T�1T�B�-�V�-�-�.�� '� 
N� 
N���r�7�7�f�$�$�*,�W�+�*=�*=�f�*E�*E�*J�*J�*L�*L�B��f���'�*,�W�+�/�/�v�/�*F�*F�*K�*K�*M�*M�B��f���'�� �2�w�w�"�}�}��7��(�(�*�*�����E�A�I�q�1�1�:�:�"�:�E�E�J�J�L�L�����U�Q�Y��2�2�2�;�;�2�;�F�F�K�K�M�M�� �T�E�\�*���3�!�b�&�>�2��5�	� �u�I�,�,�R�0�0�5�5�a��=�=��5�	� �2�w�w�"�}�}��G����b��1�1�6�6�8�8���G����b��1�1�6�6�8�8��#�f�_��6�
�$&�v�J�N�N��N�$:�$:�$?�$?�$A�$A��=�!�')�&�z�B�}�4E�'E��#�$� �2�w�w�"�}�}��	��w�K�/�/�	�:�:�?�?�A�A�	���[�0�0��;�;�?�?�A�A�
�!*�j�1�n�!=��:��"+��;��!*�j�1�n�!=��:�� �j�>�B�z�N�:��%'��[�2�j�>�%A�h�QV�FV�$W��=�!�$&�}�$5�$<�$<�S�$A�$A�$F�$F�q�!�$L�$L��=�!� �%�&E�F� 
D��r�7�7�b�=�=�!�&�z�B�u�I�5�H�!#���6�
�R��[�5F�5F�q�5I�5I�(I�!J�!J�J� "��r�%�y�2�g�;�3D�3D�Q�3G�3G�'G� H� H�I�!#��H�j�)�+L�ST�!U�!U�!U�!Y�!Y�_`�!Y�!a�!a�J� *� 2� 2�2� 6� 6� ;� ;� =� =�B�u�I� �r�7�7�b�=�=�!#�E��!2�!2�2�!6�!6�!:�!:�!<�!<�J�#%�f�:�#5�#5�b�#9�#9�#=�#=�#?�#?�L� #��7��j�(@�\�T^�E^�af�Ef�'g� h�I�$-�$4�$4�R�$8�$8�$=�$=�a��$E�$E�B�y�M�$&�y�M�$9�$9�!�$<�$<�$A�$A�$C�$C�B�y�M� �%�&7�8� 
_�)� _� _�F��2�w�w�&�(�(�13�G��r�'�{�?P�?P�QW�?X�?X�1X�0`�0`�ad�0e�0e��,�F�,�,�-�35�g�;��G��AR�AR�SY�AZ�AZ�3Z�]^�3^��/�v�/�/�0�� �%�&;�<� 
R��r�7�7�b�=�=�&(��m�q�&8�O�&5�&=�&=�b�&A�&A�&F�&F�&H�&H�&N�&N�r�w�&W�&W�B�{�O� "$�K��!7�!7�!9�!9�J�(*�;��*�(D�'L�'L�S�'Q�'Q�B�|�$� �2�:�%�%�&� Y� Y�F��2�w�w�&�(�(�57��\�5I�5I�&�5Q�5Q�5V�5V�5X�5X��1��1�1�2�� #�b�j�0�0�)+�H���O�9L�u�9T�)U�B�~�&�)+�N�);�)B�)B�3�)G�)G�)L�)L�S�RV�)W�)W�B�~�&� !�.�.��	�{�.�C�C�K�O�&� 
A� 
A���c�7�<�<�>�>�%�%�'�'� A���|�|�w�#�~�~�"$�S�'�.�.��"4�"4��3���&�#�-�-�"$�S�'�.�.��"5�"5��3���'�3�.�.�"$�S�'�.�.��"5�"5��3���!�S���"$�S�'�.�.��"3�"3��3���%��,�,�"$�S�'�.�.��"3�"3��3��� #%�S�'�.�.��.�"@�"@�"G�"G�w�"G�"W�"W��3���c�7�<�<�>�>�-�-�/�/� A�)+�C����)9�)9�J�!�w�z�2�2� /�-.�
�&(��g�n�n�Z�&@�&@�B�s�G��!�"v�"v�"�*�"v�"v�"v�w�w�O��w��w�w�be�fh�bi�bi�w�w�w�x�x�x��I��� 	� 	� 	��E�!�E�E�F�F�F���~�.�.�.�!�3�.�.�.��I�I�I�I�I�I�����	���s#   �*f5 �ff5 �5
g2�?(g-�'g2�-g2c           
      ��
  � 	 t          d�  �         t          j        �   �         }t          |�  �        dk     r"t          dt          |�  �        � d��  �         dS | �                    |�  �        }|j        rt          d�  �         dS | �                    |�  �        }|j        rt          d�  �         dS t
          t          |�  �        t          |�  �        �  �        }|j        d|�         }|j        d|�         }t          |�  �        d	k     rt          d
�  �         dS | j        �	                    |�  �        }t          d��  �        }	 t          d
�  �         t          dddddd��  �        }g }	|�                    |�  �        D ]\  }
}||
         ||         }
}|j        |
         |j        |         }}|�
                    ||�  �         |�                    |
�  �        }t          ||�  �        }|	�                    |�  �         ��|�
                    ||�  �         || j        d<   |	t%          j        |	�  �        t%          j        |	�  �        |j        dd�}|| j        d<   t          dt%          j        |	�  �        d�dt%          j        |	�  �        d���  �         n)# t.          $ r}t          d|� ��  �         Y d}~nd}~ww xY wt0          �r�| j        d         �r�	 t          d�  �         t5          j        dddd d ddd!�"�  �        }g }|�                    |�  �        D ]�\  }
}||
         ||         }
}|j        |
         |j        |         }}|�
                    |||
|fgd�#�  �         |�                    |
�  �        }t          ||�  �        }|�                    |�  �         ��|�
                    ||�  �         || j        d$<   |t%          j        |�  �        t%          j        |�  �        |j        d%d�}|| j        d$<   t          d&t%          j        |�  �        d�dt%          j        |�  �        d���  �         n)# t.          $ r}t          d'|� ��  �         Y d}~nd}~ww xY w| j        r�g }| j        �                    �   �         D ]$\  }}d(|v r|�                    |d(         �  �         �%|r]t%          j        |�  �        }t;          |�  �        }|| j        d)<   || j        d*<   t          d+|d,���  �         t          d-|d,���  �         t          j        �   �         |z
  }t          d.|d/�d0��  �         d1S # t.          $ r2}t          d2|� ��  �         | j        d3xx         dz
  cc<   Y d}~dS d}~ww xY w)4z�
        STABILES ML-MODELL TRAINING V9.0
        ================================
        Trainiert stabile ML-Modelle mit erweiterten Features
        z$Trainiere stabiles ML-Modell V9.0...rf   u(   Nicht genügend Daten für ML-Training: z < 50Fu*   Keine Features für ML-Training verfügbaru'   Kein Target für ML-Training verfügbarNr2  u.   Nicht genügend aligned Daten für ML-Trainingr  )�n_splitszTrainiere Random Forest...r  �   r/  rn   �*   rb   )�n_estimators�	max_depth�min_samples_split�min_samples_leaf�random_state�n_jobs�
random_forest�RandomForest)�	cv_scores�
mean_cv_score�std_cv_score�feature_importance�
model_typeu   ✅ Random Forest: CV Score = rd   u    ± u#   ❌ Random Forest Training Fehler: r   zTrainiere XGBoost...�   r�   r�   r   )rj  rk  �
learning_rate�	subsample�colsample_bytreern  ro  �	verbosity)�eval_set�verbose�xgboost�XGBoostu   ✅ XGBoost: CV Score = u   ❌ XGBoost Training Fehler: rs  r+   r,   u   🎯 Ensemble-Genauigkeit: r�   u   🏆 Beste Modell-Genauigkeit: u)   ✅ Stabiles ML-Modell V9.0 trainiert in r�   r�   Tu&   ❌ FEHLER beim stabilen ML-Training: r1   )rR   r�   rT   �_create_stable_features_v9r�   �_create_stable_target_v9r�   r�   r@   �
fit_transformr   r   �split�fit�predictr
   r�   r>   r�   r�   rz   �feature_importances_r?   r�   rA   rB   �xgb�XGBRegressorr�   r�   rQ   )rV   r�   r�   �features�target�
min_length�features_scaled�tscv�rf_model�	rf_scores�	train_idx�val_idx�X_train�X_val�y_train�y_val�y_pred�score�rf_performancer�   �	xgb_model�
xgb_scores�xgb_performance�
all_scores�
model_name�perf�ensemble_accuracyr,   �
training_times                                rW   �train_stable_ml_model_v9z7UltimateBitcoinTradingSystemV9.train_stable_ml_model_v9/  sh  � �Q	��8�9�9�9�����J��2�w�w��|�|��O��R���O�O�O�P�P�P��u� �6�6�r�:�:�H��~� 
��B�C�C�C��u� �2�2�2�6�6�F��|� 
��?�@�@�@��u� �S��]�]�C��K�K�8�8�J��}�[�j�[�1�H��[��*��-�F��8�}�}�r�!�!��F�G�G�G��u� #�k�7�7��A�A�O� #�A�.�.�.�D�&
A��2�3�3�3�0�!$� �&'�%&�!#��
� � �� �	�*.�*�*�_�*E�*E� ,� ,�&�I�w�%4�Y�%?��QX�AY�U�G�%+�[��%;�V�[��=Q�U�G��L�L��'�2�2�2�%�-�-�e�4�4�F�$�U�F�3�3�E��$�$�U�+�+�+�+� ���_�f�5�5�5�2:����/� "+�%'�W�Y�%7�%7�$&�F�9�$5�$5�*2�*G�"0�"� "�� ;I��&��7��j�r�w�y�7I�7I�j�j�j�SU�SY�Zc�Sd�Sd�j�j�j�k�k�k�k��� 
A� 
A� 
A��?�A�?�?�@�@�@�@�@�@�@�@�����
A���� !� )
?�T�%;�<R�%S� )
?�(?��0�1�1�1� #� 0�%(�"#�&)�"%�),�%'� �"#�	!� 	!� 	!�I� "$�J�.2�j�j��.I�.I� 1� 1�*�	�7�)8��)C�_�U\�E]���)/��Y�)?���W�AU���!�
�
�g�w�5�%�.�AQ�[`�
�a�a�a�!*�!2�!2�5�!9�!9�� (��� 7� 7��"�)�)�%�0�0�0�0� �M�M�/�6�:�:�:�09�D�N�9�-� &0�)+���)<�)<�(*��z�(:�(:�.7�.L�&/�'� '�O� 9H�D�*�9�5��j�R�W�Z�5H�5H�j�j�j�RT�RX�Yc�Rd�Rd�j�j�j�k�k�k�k�� � ?� ?� ?��=�!�=�=�>�>�>�>�>�>�>�>�����?���� �%� 
Q��
�(,�(>�(D�(D�(F�(F� A� A�$�J��&�$�.�.�"�)�)�$��*?�@�@�@��� Q�(*��
�(;�(;�%�$'�
�O�O�M�=N�D�&�'9�:�:G�D�&��7��O�8I�O�O�O�P�P�P��O�M�O�O�O�P�P�P� �I�K�K�*�4�M��R�m�R�R�R�R�S�S�S��4��� 	� 	� 	��>�1�>�>�?�?�?���~�.�.�.�!�3�.�.�.��5�5�5�5�5�����	���s�   �AT6 �+T6 �+T6 �3A*T6 �*T6 �
EJ �T6 �
K�&J=�8T6 �=K�T6 �EP; �:T6 �;
Q!�Q�T6 �Q!�!CT6 �6
U2� 'U-�-U2c                 �H  �� 	 t          j        �j        ��  �        }d�j        v r��d         �d         �                    d�  �        �                    �   �         z  |d<   �d         �                    �   �         |d<   �d         �                    d�  �        |d<   �d         �                    d�  �        |d	<   t          �fd
�dD �   �         �  �        rQ�d         �d
         z  |d<   �d         �d         z  |d<   �d         �d         z   �d
         z   �d         z   dz  |d<   g d�}|D ]}|�j        v r�|         |d|� �<   �g d�}|D ]}|�j        v r�|         |d|� �<   �d�j        v rM�d         �d         �                    d�  �        �                    �   �         z  |d<   d�j        v r�d         |d<   | j        d         r g d�}|D ]}|�j        v r�|         |d|� �<   �t          �j        d�  �        rI�j        j
        |d<   �j        j        |d<   �j        j        d k    �                    t          �  �        |d!<   d�j        v r&d"D ]#}�d         �                    |�  �        |d#|� �<   �$|�                    �   �         }|j        r"t#          d$�  �         t          j        �   �         S t#          d%t%          |j        �  �        � d&t%          |�  �        � d'��  �         |S # t&          $ r/}t#          d(|� ��  �         t          j        �   �         cY d)}~S d)}~ww xY w)*zErstelle stabile Features V9.0�r  r�   rf   �price_normalized�price_change_1h�   �price_change_4h�   �price_change_24hc              3   �*   �K  � | ]
}|�j         v V � �d S r  r  r  s     �rW   r  zLUltimateBitcoinTradingSystemV9._create_stable_features_v9.<locals>.<genexpr>�  r  rY   )r�   r�   r�   r�   r�   r�   �high_low_ratior�   �close_open_ratio�ohlc_avg)�SMA_10�SMA_20�EMA_10�EMA_20r   r#  r%  r'  r(  r)  r*  �
indicator_)�
Volatility_10�
Volatility_20r+  �vol_r�   r
  �volume_normalizedr7  �volume_ratior   )�Trend_5�Trend_10�Trend_20�
Momentum_5�Momentum_10�Momentum_20�trend_�hour�day_of_weekr/  �
is_weekend)rb   rn   r  �
close_lag_u)   ⚠️ Keine Features nach NaN-EntfernungzStabile Features erstellt: z Features, � SampleszFEHLER bei stabilen Features: N)r8   r9   r  r  rA  r�   r>  r  rB   �hasattrr�  �	dayofweekrH  rI  r@  r�   r�   rR   rT   r�   )	rV   r�   r�  �tech_indicators�	indicator�vol_indicators�trend_indicators�lagr�   s	    `       rW   r�  z9UltimateBitcoinTradingSystemV9._create_stable_features_v9�  s�  �� �B	"��|�"�(�3�3�3�H� �"�*�$�$�/1�'�{�R��[�=P�=P�QS�=T�=T�=Y�=Y�=[�=[�/[��+�,�.0��k�.D�.D�.F�.F��*�+�.0��k�.D�.D�Q�.G�.G��*�+�/1�'�{�/E�/E�b�/I�/I��+�,��Q�Q�Q�Q�0P�Q�Q�Q�Q�Q� 
_�-/��Z�"�U�)�-C��)�*�/1�'�{�R��Z�/G��+�,�(*�6�
�R��Z�(?�"�U�)�(K�b�QX�k�(Y�]^�'^���$�Q� Q� Q�O� -� 
G� 
G�	���
�*�*�9;�I��H�5�)�5�5�6�� G�F�F�N�+� 
A� 
A�	���
�*�*�35�i�=�H�/�I�/�/�0�� �2�:�%�%�02�8��r�(�|�?S�?S�TV�?W�?W�?\�?\�?^�?^�0^��,�-�!�R�Z�/�/�/1�.�/A�H�^�,� �%�&7�8� 
G�#r�#r�#r� �!1� G� G�I� �B�J�.�.�9;�I���!5�)�!5�!5�6�� �r�x��(�(� 
O�#%�8�=��� �*,�(�*<���'�*,�(�*<��*A�)I�)I�#�)N�)N���&� �"�*�$�$�$� J� J�C�35�g�;�3D�3D�S�3I�3I�H�/�#�/�/�0�0�  ���(�(�H� �~� 
&��A�B�B�B��|�~�~�%��i��H�4D�0E�0E�i�i�RU�V^�R_�R_�i�i�i�j�j�j��O��� 	"� 	"� 	"��6�1�6�6�7�7�7��<�>�>�!�!�!�!�!�!�����	"���s$   �J-K( �16K( �(
L!�2$L�L!�L!c                 �  � 	 d|j         vrt          j        �   �         S |d         �                    d�  �        }|d         }||k    �                    t
          �  �        }|�                    �   �         }t          dt          |�  �        � d��  �         |S # t          $ r/}t          d|� ��  �         t          j        �   �         cY d}~S d}~ww xY w)zErstelle stabiles Target V9.0r�   r�   zStabiles Target erstellt: r�  zFEHLER bei stabilem Target: N)
r  r8   �Seriesr@  rH  rI  r�   rR   rT   r�   )rV   r�   �future_pricer�   r�  r�   s         rW   r�  z7UltimateBitcoinTradingSystemV9._create_stable_target_v9  s�   � �	��b�j�(�(��y�{�{�"� �g�;�,�,�R�0�0�L��w�K�M� #�]�2�:�:�3�?�?�F� �]�]�_�_�F��D�s�6�{�{�D�D�D�E�E�E��M��� 	� 	� 	��4��4�4�5�5�5��9�;�;�����������	���s#   �B �A6B �
C�$C	�C�	Cc                 ��  � 	 t          d�  �         t          d�  �         t          d�  �         t          d�  �         t          j        �   �         }| xj        dz
  c_        | �                    �   �         }|j        rt          d�  �        �|d         j        d         }t          dt          |�  �        � d	|d
���  �         t          | j        �  �        dk    s| j        dz  dk    r+| �	                    |�  �        }t          d
|rdnd� ��  �         | �
                    |�  �        }t          j        �   �         |z
  }| j        t          j        �   �         �
                    �   �         |t          |�  �        || j        �                    dd�  �        || �                    �   �         t#          | j        �                    �   �         �  �        dd�
}| j        �                    |�  �         || _        | j        dxx         dz
  cc<   | j        dxx         dz
  cc<   | j        dxx         |z
  cc<   |�                    dd�  �        dk    r4t/          d| j        �                    dd�  �        dz   �  �        | j        d<   t          d| j        � d|d�d ��  �         t          d!|�                    d"d#�  �        � d$|�                    dd�  �        d%�d&��  �         t          d'|d
���  �         t          d(| j        �                    dd�  �        d%���  �         t          d)|d*         � d+t          | j        �  �        � ��  �         |S # t
          $ r�}t          d,|� ��  �         | j        d-xx         dz
  cc<   | j        t          j        �   �         �
                    �   �         t1          |�  �        d.t3          �   �         v rt          j        �   �         |z
  nddd/d0dd1�d2d3�cY d4}~S d4}~ww xY w)5u�   
        STABILER PROGNOSE-SCAN V9.0
        ===========================
        Führt stabilen Prognose-Scan mit erweiterten Features durch
        z<============================================================z%STARTE STABILEN PROGNOSE-SCAN V9.0...u0   Erweiterte Features mit stabiler Funktionalitätrb   u.   Keine stabilen Marktdaten für Scan verfügbarr�   r�   zAktuelle Daten: z Punkte, Preis: $rc   r   r/  z
ML-Training: u   ✅ Erfolgreichu   ❌ Fehlgeschlagenr.   r'   r�   )
�scan_idr�   �	scan_time�data_pointsr�   r.   �
predictionr?   �advanced_features_usedr�   r)   r*   r0   �
confidencer�   gffffff�?r/   g      �?g{�G�z�?u   ✅ Stabiler Prognose-Scan #z abgeschlossen in r�   r�   u   📊 PROGNOSE: �signal�N/A�
 (Konfidenz: r�   r�   u   💰 AKTUELLER PREIS: $u   📈 DATENQUALITÄT: u   🎯 ERWEITERTE FEATURES: r�  r3   u-   ❌ FEHLER beim stabilen Prognose-Scan V9.0: r1   �scan_start_timer�   �FEHLER)r�  r�  z
v9.0_error)r�  r�   r�   r�  r�  r�   r�  r�   N)rR   r�   rI   r�   r�   r�   r�   rT   r>   r�  �_calculate_stable_prediction_v9r   r6   rN   rQ   r�   �!_get_model_performance_summary_v9rO   rB   rP   rH   r�   rJ   r�   r�   r�   )	rV   r�  r�   r�   �training_success�prediction_resultr�  �scan_resultr�   s	            rW   �run_stable_prediction_scan_v9z<UltimateBitcoinTradingSystemV9.run_stable_prediction_scan_v9%  s�  � �M	��(�O�O�O��9�:�:�:��D�E�E�E��(�O�O�O�"�i�k�k�O�����"��� �/�/�1�1�B��x� 
R�� P�Q�Q�Q��w�K�,�R�0�M��S�S��W�W�S�S�}�S�S�S�T�T�T� �4�>�"�"�a�'�'�4�+<�q�+@�A�+E�+E�#'�#@�#@��#D�#D� ��g�;K�&e�&7�&7�Qe�g�g�h�h�h� !%� D� D�R� H� H�� �	���o�5�I�  �,�%�\�^�^�5�5�7�7�&�"�2�w�w�!.�%)�%7�%;�%;�<O�QT�%U�%U�/�%)�%K�%K�%M�%M�*-�d�.D�.K�.K�.M�.M�*N�*N�(�� �K� 
��$�$�[�1�1�1�$/�D�!� 
��}�-�-�-��2�-�-�-���1�2�2�2�a�7�2�2�2���4�5�5�5��B�5�5�5� !�$�$�\�1�5�5��;�;�<?���&�*�*�+@�$�G�G�$�N�=P� =P��"�#8�9� 
�f��1B�f�f�V_�f�f�f�f�g�g�g��  G�$5�$9�$9�(�E�$J�$J�  G�  G�Yj�Yn�Yn�o{�}~�Y�Y�  G�  G�  G�  G�  
H�  
H�  
H��@�M�@�@�@�A�A�A��^�$�*<�*@�*@�AT�VW�*X�*X�^�^�^�_�_�_��t�{�;S�/T�t�t�WZ�[_�[q�Wr�Wr�t�t�u�u�u����� 	� 	� 	��E�!�E�E�F�F�F���~�.�.�.�!�3�.�.�.�  �,�%�\�^�^�5�5�7�7��Q���>O�SY�S[�S[�>[�>[�T�Y�[�[�?�:�:�ab� �!'�)1��E�E�'�	� 	� 	
� 	
� 	
� 	
� 	
� 	
�����	���s   �MM �
O)�BO$�O)�$O)c                 �V  � 	 t          d�  �         d}d}| j        �r	 d}d}| j        �                    �   �         D ].\  }}|| j        v r |�                    dd�  �        }||k    r|}|}�/|r�| j        |         }	| �                    |�  �        }
|
j        sb|
j        dd�         j        }| j	        �
                    |�  �        }|	�                    |�  �        d         }|}t          d|d	�d
|� d��  �         n)# t          $ r}
t          d|
� ��  �         Y d}
~
nd}
~
ww xY w| �
                    |�  �        }| j        r|d
z  |dz  z   }n|}|dk    rd}t          dd|dz
  dz  z   �  �        }n<|dk     rd}t          ddd|z
  dz  z   �  �        }nd}dt          |dz
  �  �        dz  z   }| j        �                    dd�  �        }||z  }||||||t#          j        �   �         �                    �   �         |d         j        d         t)          | j        �                    �   �         �  �        d�	}t          d|� d|d�d��  �         |S # t          $ rX}
t          d|
� ��  �         ddddddt#          j        �   �         �                    �   �         dt-          |
�  �        d �	cY d}
~
S d}
~
ww xY w)!z Berechne stabile Vorhersage V9.0z#Berechne stabile Vorhersage V9.0...rw   zTechnische AnalyseNr�   rs  r   zML-Vorhersage: rd   z
 (Modell: r�   zML-Vorhersage Fehler: r�   rx   g�������?�KAUFENg�������?g333333�?gffffff�?�	VERKAUFEN�HALTENr�   r.   r�   )	r�  r�  �
ml_prediction�technical_score�
model_used�data_qualityr�   r�   �advanced_features_activezStabile Vorhersage: r�  r�   z FEHLER bei stabiler Vorhersage: �Fallbackr�   )	r�  r�  r�  r�  r�  r�  r�   r�   r�   )rR   r>   r?   r�   r�   r�  r�   r�   rP   r@   �	transformr�  r�   �_calculate_technical_score_v9r�   r�   rQ   r   r6   rN   rO   rB   r�   )rV   r�   r�  r�  �best_model_name�
best_scorer�  �performancer�  �modelr�  �latest_featuresr�  r�   r�  �combined_predictionr�  r�  r�  r�  s                       rW   r�  z>UltimateBitcoinTradingSystemV9._calculate_stable_prediction_v9z  s�  � �W	��7�8�8�8�  �M�-�J��~� 
8�8�&*�O�!#�J�37�3I�3O�3O�3Q�3Q� =� =�/�
�K�%���7�7�$/�O�O�O�Q�$G�$G�E�$�z�1�1�-2�
�2<���&� e� $��� ?�� $(�#B�#B�2�#F�#F��'�~� e�.6�m�B�C�C�.@�.G�O�.2�k�.C�.C�O�.T�.T�O�,1�M�M�/�,J�,J�1�,M�M�)8�J�!�"c�M�"c�"c�"c�Q`�"c�"c�"c�d�d�d��� � 8� 8� 8��6�1�6�6�7�7�7�7�7�7�7�7�����8���� #�@�@��D�D�O� �~� 
6�'4�s�':��QT�?T�&U�#�#�&5�#� #�T�)�)�!�� ��c�-@�3�-F�#�,M�&M�N�N�
�
�$�t�+�+�$�� ��c�S�3F�-F�#�,M�&M�N�N�
�
�!�� �3�':�S�'@�#A�#A�C�#G�G�
�  �-�1�1�2E�s�K�K�L��,�&�J� !�(�!4�#2�(� ,�%�\�^�^�5�5�7�7�!#�G��!1�"�!5�,/��0F�0M�0M�0O�0O�,P�,P�
� 
�J� 
�O��O�O�j�O�O�O�O�P�P�P����� 	� 	� 	��8�Q�8�8�9�9�9�"�!�!$�#&�(� #�%�\�^�^�5�5�7�7�!'��Q���
� 
� 

� 

� 

� 

� 

� 

�����	���sI   �I �CC8 �7I �8
D�D�I �D�D'I �
J(�A
J#�J(�#J(c                 ��  � 	 d}d|j         v r@|d         j        d         }|dk    r|dz  }n!|dk     r|dz
  }nd|cxk    rdk    rn n|d	z
  }d
|j         v r@d|j         v r7|d
         j        d         }|d         j        d         }||k    r|dz
  }n|dz  }d
|j         v r*|d
         j        d         }|dk    r|dz  }n|dk     r|dz
  }| j        d         rHd}d}dD ]+}	d|	� �}
|
|j         v r|||
         j        d         z
  }|dz
  }�,|dk    r||z  }||dz
  dz  z
  }t          dt	          d|�  �        �  �        S # t
          $ r}t
          d|� ��  �         Y d}~dS d}~ww xY w)zBerechne technischen Score V9.0rw   r   r�   �F   g333333�?r2  �(   r   皙�����?r#  r%  r�   r*  r�   ry   r   r   r.  r0  rb   r'   rv   zFEHLER bei technischem Score: N)r  r�   rB   r�   r�   r�   rR   )
rV   r�   r�  �rsi�macd�macd_signal�bb_position�trend_score�trend_countr!   �	trend_col�	avg_trendr�   s
                rW   r�  z<UltimateBitcoinTradingSystemV9._calculate_technical_score_v9�  s  � �1	��E� ��
�"�"���i�n�R�(����8�8��T�M�E�E��2�X�X��T�M�E�E��3�_�_�_�_�"�_�_�_�_�_��T�M�E� ���#�#�
���(C�(C��&�z��r�*�� ��/�4�R�8���+�%�%��S�L�E�E��S�L�E� ��
�*�*� ��/�4�R�8����$�$��S�L�E�E� �3�&�&��S�L�E� �%�&7�8� 
5�����)� )� )�F� 1�� 1� 1�I� �B�J�.�.�#�r�)�}�'9�"�'=�=��#�q�(�����?�?� +�k� 9�I��i�#�o��4�4�E��s�C��U�O�O�,�,�,��� 	� 	� 	��6�1�6�6�7�7�7��3�3�3�3�3�����	���s   �D9D< �<
E#�E�E#c                 ��  � � 	 � j         sddd�S t          � j         �                    �   �         � fd���  �        }t          � j         �  �        |� j         |         �                    dd�  �        t          j        d� � j         �                    �   �         D �   �         �  �        t          � j         �                    �   �         �  �        d�}|S # t          $ r}ddt          |�  �        d	�cY d
}~S d
}~ww xY w)z,Hole Modell-Performance Zusammenfassung V9.0r   r'   )�models_countr,   c                 �F   �� �j         |          �                    dd�  �        S )Nrs  r   )r?   r�   )�krV   s    �rW   �<lambda>zRUltimateBitcoinTradingSystemV9._get_model_performance_summary_v9.<locals>.<lambda>  s    �� ��)?��)B�)F�)F��XY�)Z�)Z� rY   )�keyrs  c                 �:   � g | ]}|�                     d d�  �        ��S )rs  r   )r�   )rq   �ms     rW   ru   zTUltimateBitcoinTradingSystemV9._get_model_performance_summary_v9.<locals>.<listcomp>  s&   � �,p�,p�,p�1�Q�U�U�?�A�-F�-F�,p�,p�,prY   )r�  �
best_modelr,   �average_accuracy�models_available)r�  r,   r�   N)r?   r�   �keysrT   r�   r�   r�   rP   r�   r�   r�   )rV   r  �summaryr�   s   `   rW   r�  z@UltimateBitcoinTradingSystemV9._get_model_performance_summary_v9
  s'  �� �	N��)� 
A�()�C�@�@�@��T�3�8�8�:�:�Z�Z�Z�Z�\� \� \�J� !$�D�$:� ;� ;�(�!%�!7�
�!C�!G�!G��Y\�!]�!]�$&�G�,p�,p�PT�Pf�Pm�Pm�Po�Po�,p�,p�,p�$q�$q�$(��)?�)D�)D�)F�)F�$G�$G�� �G� �N��� 	N� 	N� 	N�$%��c�!�f�f�M�M�M�M�M�M�M�M�����	N���s#   �C �B<C �
C3�C.�(C3�.C3c                 �N  � 	 t          d�  �         t          j        t          j        �   �         t          d��  �        z
  t          j        �   �         d��  �        }d}g }|dz  }t
          |�  �        D �]>\  }}|t          |�  �        dz
  k    r|}nV||z
  |z  d	z  }t          j	        �
                    d
d�  �        }||z   }	t          dt          d
|	�  �        �  �        }	|d|	z   z  }t          j	        �
                    dd�  �        }
|d|
z   z  }|d|
z
  z  }|dt          j	        �
                    d
d�  �        z   z  }
|�                    t          |t          ||
�  �        �  �        t          |||
�  �        t          |||
�  �        |t          j	        �
                    dd�  �        d��  �         ��@t          j        ||��  �        }t          dt          |�  �        � d��  �         |S # t           $ r/}t          d|� ��  �         t          j        �   �         cY d}~S d}~ww xY w)z%Generiere stabile Fallback-Daten V9.0z(Generiere stabile Fallback-Daten V9.0...�   )�days�H)�start�end�freqr�   gףp=
��?rb   r�  r   g����MbP?r�   r�   g����Mb@?r�   g-C��6*?i�  r   r�   r�  z"Stabile Fallback-Daten generiert: r�   z$FEHLER bei stabilen Fallback-Daten: N)rR   r8   �
date_ranger   r6   r   r�   rT   r�   r�   r�   r�   r�   �uniformr�   r9   r�   )rV   �dates�
base_price�
price_datar�   rr   �date�mean_reversion�
random_change�total_change�
volatility�high�low�
open_pricer�   r�   s                   rW   r�   z@UltimateBitcoinTradingSystemV9._generate_stable_fallback_data_v9   sV  � �.	"��<�=�=�=� �M�������9J�9J�9J�(J�%-�\�^�^�#�?� ?� ?�E�  �J� �J�&��.�M�$�U�+�+� 
� 
���4���E�
�
�Q��&�&�$.�M�M� '1�=�&@�J�%N�QU�%U�N�$&�I�$4�$4�Q��$>�$>�M�#1�M�#A�L�#&�v�s�5�,�/G�/G�#H�#H�L�!�a�,�&6�7�M�  �Y�.�.�v�u�=�=�
�$��J��7��#�q�:�~�6��*�a�"�)�2B�2B�1�f�2M�2M�.M�N�
��!�!���S��z�%:�%:�;�;���m�Z�@�@��s�M�:�>�>�*� �i�/�/��d�;�;�#� #� � � � � ��j��6�6�6�B��L�s�2�w�w�L�L�L�M�M�M��I��� 	"� 	"� 	"��<��<�<�=�=�=��<�>�>�!�!�!�!�!�!�����	"���s   �G(G+ �+
H$�5$H�H$�H$N)�__name__�
__module__�__qualname__�__doc__rX   �dictr�   r   r�   r�   r8   r9   r�   r�   r�   r�   r�   r�   r�   �boolr�  r�  r�  r�  r�  r�  r�  r�  r�   rp   rY   rW   r   r   :   s�  � � � � � �� �OD� OD� OD�bKh�4� Kh� Kh� Kh� Kh�Z]�d�3�i� ]�D� ]� ]� ]� ]�~F@�2�<� F@� F@� F@� F@�P+�d� +� +� +� +�Z�R�\� � � � �"�R�\� �%� �TV�T`� � � � �@<�2�<� <�B�L� <� <� <� <�|K�"�,� K�2�<� K� K� K� K�ZW�2�<� W�D� W� W� W� W�rD"�R�\� D"�b�l� D"� D"� D"� D"�L�2�<� �B�I� � � � �.S�t� S� S� S� S�jY�"�,� Y�4� Y� Y� Y� Y�v3��� 3�� 3� 3� 3� 3�jN�4� N� N� N� N�,0"�2�<� 0"� 0"� 0"� 0"� 0"� 0"rY   r   c                  ��  � t          d�  �         t          d�  �         t          d�  �         t          d�  �         	 t          �   �         } | �                    �   �         }t          d�  �         t          d�  �         t          d�  �         t          d�  �         t          d|�                    dd	�  �        d
���  �         t          d|�                    dd	�  �        d
���  �         t          d|�                    dd	�  �        � ��  �         t          d|�                    dd	�  �        d�d��  �         |�                    di �  �        }t          d�  �         t          d|�                    dd�  �        � ��  �         t          d|�                    dd	�  �        d
���  �         t          d|�                    dd	�  �        d���  �         t          d|�                    dd�  �        � ��  �         |�                    d i �  �        }t          d!�  �         t          d"|�                    d#d	�  �        � ��  �         t          d$|�                    d%d	�  �        d
���  �         t          d&|�                    d'd	�  �        d
���  �         t          d(�  �         t          d)|�                    d*d	�  �        � d+t	          | j        �  �        � ��  �         | j        �                    �   �         D ] \  }}|rd,nd-}t          d.|� d/|� ��  �         �!t          d0�  �         |S # t          $ r5}t          d1|� ��  �         d	d2l}|�	                    �   �          Y d2}~d2S d2}~ww xY w)3u7   Hauptfunktion für Ultimate Bitcoin Trading System V9.0zP================================================================================z5ULTIMATE BITCOIN TRADING SYSTEM V9.0 - STABLE EDITIONu=   FUNKTIONAL GETESTET • ERWEITERTE FEATURES • STABILE BASISzQ
================================================================================z6ULTIMATE BITCOIN TRADING SYSTEM V9.0 - SCAN-ERGEBNISSEz
STABILE MARKTDATEN:z   Bitcoin-Preis: $r�   r   rc   u      Datenqualität: r.   r�   z   Datenpunkte: r�  z   Scan-Zeit: r�  r�   r�   r�  z
STABILE PROGNOSE:z   Signal: r�  r�  z   Konfidenz: r�  z   ML-Prediction: r�  rd   z   Modell: r�  r?   z
MODELL-PERFORMANCE:z   Modelle: r�  z   Beste Genauigkeit: r,   z   Durchschnitt: r  z
ERWEITERTE FEATURES:z   Features aktiv: r�  r3   u   ✅u   ⚠️z   � uH   
🏆 ULTIMATE BITCOIN TRADING SYSTEM V9.0 - STABLE EDITION ERFOLGREICH!z2FEHLER beim Ultimate Bitcoin Trading System V9.0: N)
rR   r   r�  r�   rT   rB   r�   r�   �	traceback�	print_exc)	�systemrl   r�  �
model_perf�feature�active�statusr�   r'  s	            rW   �&run_ultimate_bitcoin_trading_system_v9r.  S  s�  � �	�(�O�O�O�	�
A�B�B�B�	�
I�J�J�J�	�(�O�O�O�-�/�1�1�� �5�5�7�7�� 	�o����
�F�G�G�G�
�h����
�&�'�'�'�
�I�F�J�J���$B�$B�I�I�I�J�J�J�
�L�F�J�J�/B�A�$F�$F�L�L�L�M�M�M�
�?����M�1�!=�!=�?�?�@�@�@�
�@�v�z�z�+�q�9�9�@�@�@�@�A�A�A��Z�Z��b�1�1�
�
�$�%�%�%�
�=�J�N�N�8�U�;�;�=�=�>�>�>�
�D�z�~�~�l�A�>�>�D�D�D�E�E�E�
�K�:�>�>�/�1�#E�#E�K�K�K�L�L�L�
�A�J�N�N�<��?�?�A�A�B�B�B��Z�Z� 3�R�8�8�
�
�&�'�'�'�
�@�Z�^�^�N�A�>�>�@�@�A�A�A�
�O�z�~�~�o�q�'I�'I�O�O�O�P�P�P�
�M�*�.�.�1C�Q�"G�"G�M�M�M�N�N�N�
�'�(�(�(�
�m�F�J�J�/G��$K�$K�m�m�c�RX�Rj�Nk�Nk�m�m�n�n�n�%�7�=�=�?�?� 	,� 	,�O�G�V�$�2�U�U�(�F��*��*�*��*�*�+�+�+�+�
�Z�[�[�[��
��� � � �
�F�1�F�F�G�G�G������������t�t�t�t�t�����	���s   �K&L% �%
M$�/*M�M$�__main__)/r"  �yfinancer�   �pandasr8   �numpyr�   r�   r�   r   r   r�   �os�warnings�	threading�pickle�typingr   r   r   r   �filterwarnings�sklearn.preprocessingr
   �sklearn.ensembler   �sklearn.metricsr   r
   �sklearn.model_selectionr   r~  r�  rA   rR   �ImportError�matplotlib.pyplot�pyplot�plt�seaborn�sns�scipyr   rU   r   r.  r  rp   rY   rW   �<module>rD     su  ��� � � � � � � � � � � � � � ���� ���� (� (� (� (� (� (� (� (� ���� 	�	�	�	� ���� � � � � 
�
�
�
� .� .� .� .� .� .� .� .� .� .� .� .� �� �� !� !� !� /� .� .� .� .� .� 2� 2� 2� 2� 2� 2� 8� 8� 8� 8� 8� 8� 8� 8� 3� 3� 3� 3� 3� 3�E�������	�E�
"�#�#�#�#��� E� E� E���	�E�
C�D�D�D�D�D�E����
  � � � � � � � � � �H��������O�	�E�
 �!�!�!�!��� H� H� H��O�	�E�
F�G�G�G�G�G�H����V"� V"� V"� V"� V"� V"� V"� V"�r(4� 4� 4�l �z���*�*�,�,�,�,�,� �s$   �*A< �<B�B�B3 �3C�C