#!/usr/bin/env python3
"""
🚀 ULTIMATE TRADING DASHBOARD LAUNCHER
=====================================
Professioneller Launcher für das Ultimate Bitcoin Trading Dashboard
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def check_dependencies():
    """🔍 Erweiterte Abhängigkeitsprüfung"""
    print("🔍 PRÜFE SYSTEM-ABHÄNGIGKEITEN")
    print("=" * 40)

    required_modules = {
        'tkinter': 'GUI-Framework',
        'numpy': 'Numerische Berechnungen',
        'requests': 'API-Kommunikation',
        'matplotlib': 'Chart-Visualisierungen'
    }

    optional_modules = {
        'pandas': 'Datenanalyse (optional)',
        'sklearn': 'Machine Learning (optional)'
    }

    missing_required = []
    missing_optional = []

    # Erforderliche Module prüfen
    for module, description in required_modules.items():
        try:
            __import__(module)
            print(f"✅ {module:<12} - {description}")
        except ImportError:
            print(f"❌ {module:<12} - {description} (FEHLT)")
            missing_required.append(module)

    print("\n📦 OPTIONALE MODULE:")
    print("-" * 25)

    # Optionale Module prüfen
    for module, description in optional_modules.items():
        try:
            __import__(module)
            print(f"✅ {module:<12} - {description}")
        except ImportError:
            print(f"⚠️ {module:<12} - {description} (nicht verfügbar)")
            missing_optional.append(module)

    print("\n" + "=" * 40)

    if missing_required:
        print(f"❌ FEHLENDE ERFORDERLICHE MODULE: {', '.join(missing_required)}")
        print("\n📦 INSTALLATION:")
        print("Führen Sie folgenden Befehl aus:")
        print(f"pip install {' '.join(missing_required)}")
        return False

    print("✅ ALLE ERFORDERLICHEN MODULE VERFÜGBAR")

    if missing_optional:
        print(f"\n💡 TIPP: Für erweiterte Features installieren Sie:")
        print(f"pip install {' '.join(missing_optional)}")

    return True

def show_feature_overview():
    """✨ Feature-Übersicht anzeigen"""
    print("\n✨ ULTIMATE TRADING DASHBOARD FEATURES")
    print("=" * 45)

    features = [
        "📊 Erweiterte Technical Analysis (10+ Indikatoren)",
        "🔮 48h-Prognose mit Machine Learning",
        "⚠️ Risk Management System",
        "🔔 Alert-System mit Benachrichtigungen",
        "📈 Interaktive Charts mit Zoom",
        "📊 Datenexport (CSV, Reports)",
        "🎨 Dark/Light Theme System",
        "💰 Portfolio-Management",
        "🗄️ SQLite-Datenbank",
        "🔧 Modulare Architektur",
        "🚀 Live Bitcoin-Daten (3 APIs)",
        "📱 Professionelle GUI"
    ]

    for feature in features:
        print(f"  {feature}")

    print("\n🎯 TRADING-INDIKATOREN:")
    print("  • RSI, MACD, Bollinger Bands")
    print("  • Stochastic, Williams %R, CCI")
    print("  • ADX, Momentum, ROC")

def start_dashboard():
    """🚀 Dashboard starten"""
    dashboard_file = Path("ultimate_modular_trading_dashboard.py")

    if not dashboard_file.exists():
        print("❌ Dashboard-Datei nicht gefunden!")
        return False

    print("\n🚀 STARTE ULTIMATE TRADING DASHBOARD")
    print("=" * 40)
    print("📊 Lade Module...")
    print("💰 Verbinde zu Bitcoin-APIs...")
    print("🎨 Erstelle GUI...")
    print("\n⏳ Bitte warten...")

    try:
        result = subprocess.run([sys.executable, str(dashboard_file)],
                              capture_output=False, text=True)

        if result.returncode == 0:
            print("\n✅ Dashboard erfolgreich beendet")
        else:
            print(f"\n⚠️ Dashboard beendet mit Code: {result.returncode}")

        return True

    except Exception as e:
        print(f"\n❌ Fehler: {e}")
        return False

def main():
    """🚀 Hauptfunktion"""
    print("🚀 ULTIMATE BITCOIN TRADING DASHBOARD LAUNCHER")
    print("=" * 55)
    print("🎯 Professional Edition - Alle Features")
    print("💎 Entwickelt für professionelle Trader")
    print("=" * 55)

    # Abhängigkeiten prüfen
    if not check_dependencies():
        print("\n💡 INSTALLATION ERFORDERLICH")
        print("Installieren Sie die fehlenden Module und starten Sie erneut.")
        input("\n⏸️ Drücken Sie Enter zum Beenden...")
        return

    # Feature-Übersicht
    show_feature_overview()

    # Dashboard starten
    print("\n" + "=" * 55)
    response = input("🚀 Dashboard starten? (j/n): ").lower().strip()

    if response in ['j', 'ja', 'y', 'yes', '']:
        success = start_dashboard()

        if success:
            print("\n✅ ULTIMATE TRADING DASHBOARD ERFOLGREICH AUSGEFÜHRT")
        else:
            print("\n❌ FEHLER BEIM STARTEN DES DASHBOARDS")
    else:
        print("\n👋 Auf Wiedersehen!")

    print("\n" + "=" * 55)
    print("💎 Ultimate Bitcoin Trading Dashboard")
    print("🎯 Professional Edition")
    print("=" * 55)

if __name__ == "__main__":
    main()