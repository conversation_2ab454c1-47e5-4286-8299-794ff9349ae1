#!/usr/bin/env python3
"""
ULTIMATE BITCOIN TRADING GUI V3.0
=================================
VOLLSTÄNDIG FUNKTIONSFÄHIGE MODERNE BENUTZEROBERFLÄCHE
- Komplett überarbeitet und optimiert
- Selbstlernendes ML-System Integration
- 60%+ Genauigkeit Anzeige
- Streamlined Design ohne unnötige Elemente
- Real-time Updates und Performance-Monitoring
- Moderne responsive GUI

ULTIMATE TRADING GUI V3.0 - PERFEKTION IN BENUTZERFREUNDLICHKEIT!
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import threading
import time
import json
import os

# Import des Ultimate Trading Systems V3.0
from ultimate_bitcoin_trading_system_v3 import UltimateBitcoinTradingSystemV3

class UltimateBitcoinGUIV3:
    """
    ULTIMATE BITCOIN TRADING GUI V3.0
    =================================
    Vollständig funktionsfähige moderne Benutzeroberfläche
    """
    
    def __init__(self):
        # SYSTEM KONFIGURATION
        self.VERSION = "Ultimate_GUI_v3.0"
        self.TITLE = "Ultimate Bitcoin Trading System V3.0"
        
        # GUI SETUP
        self.root = tk.Tk()
        self.root.title(self.TITLE)
        self.root.geometry("1600x1000")
        self.root.configure(bg='#0a0a0a')
        
        # TRADING SYSTEM
        self.trading_system = UltimateBitcoinTradingSystemV3()
        
        # STATUS VARIABLEN
        self.is_running = False
        self.auto_update = False
        self.update_interval = 60  # 1 Minute für optimale Performance
        self.last_analysis = None
        self.update_job_id = None
        
        # GUI KOMPONENTEN
        self.main_frame = None
        self.status_text = None
        self.chart_canvas = None
        self.accuracy_chart = None
        
        # PERFORMANCE TRACKING
        self.analysis_history = []
        self.accuracy_history = []
        
        print(f"ULTIMATE BITCOIN TRADING GUI V3.0 initialisiert")
        print(f"Version: {self.VERSION}")
        print(f"Integration: Ultimate Trading System V3.0")
        
        # Setup GUI
        self.setup_modern_gui()
        self.log_message("Ultimate Bitcoin Trading GUI V3.0 bereit!")
        self.log_message("Klicken Sie 'TRADING STARTEN' für selbstlernende Analyse")
    
    def setup_modern_gui(self):
        """Setup moderne GUI-Struktur"""
        
        # MODERN STYLE
        style = ttk.Style()
        style.theme_use('clam')
        
        # HEADER
        self.create_modern_header()
        
        # MAIN CONTENT
        self.create_main_content()
        
        # FOOTER
        self.create_footer()
        
        # CLEANUP HANDLER
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_modern_header(self):
        """Erstelle modernen Header"""
        header_frame = tk.Frame(self.root, bg='#1a1a1a', height=100)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)
        
        # TITLE
        title_label = tk.Label(
            header_frame,
            text="ULTIMATE BITCOIN TRADING SYSTEM V3.0",
            font=('Arial', 24, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # SUBTITLE
        subtitle_label = tk.Label(
            header_frame,
            text="Selbstlernendes ML-System • 60%+ Genauigkeit • Vollständig Optimiert",
            font=('Arial', 12),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        subtitle_label.pack(side=tk.LEFT, padx=(0, 20), pady=(45, 20))
        
        # STATUS INDICATORS
        status_frame = tk.Frame(header_frame, bg='#1a1a1a')
        status_frame.pack(side=tk.RIGHT, padx=20, pady=20)
        
        # System Status
        self.system_status_label = tk.Label(
            status_frame,
            text="SYSTEM: BEREIT",
            font=('Arial', 12, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        self.system_status_label.pack()
        
        # Accuracy Status
        self.accuracy_status_label = tk.Label(
            status_frame,
            text="GENAUIGKEIT: ---%",
            font=('Arial', 12, 'bold'),
            fg='#ffaa00',
            bg='#1a1a1a'
        )
        self.accuracy_status_label.pack()
        
        # Last Update
        self.last_update_label = tk.Label(
            status_frame,
            text="LETZTES UPDATE: ---",
            font=('Arial', 10),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        self.last_update_label.pack()
    
    def create_main_content(self):
        """Erstelle Hauptinhalt"""
        self.main_frame = tk.Frame(self.root, bg='#0a0a0a')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # LEFT PANEL - Controls
        self.create_control_panel()
        
        # CENTER PANEL - Charts
        self.create_chart_panel()
        
        # RIGHT PANEL - Analysis
        self.create_analysis_panel()
    
    def create_control_panel(self):
        """Erstelle Kontroll-Panel"""
        control_frame = tk.Frame(self.main_frame, bg='#1a1a1a', width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # TITLE
        tk.Label(
            control_frame,
            text="TRADING KONTROLLEN",
            font=('Arial', 16, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        ).pack(pady=(20, 30))
        
        # MAIN BUTTONS
        button_frame = tk.Frame(control_frame, bg='#1a1a1a')
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 30))
        
        # START BUTTON
        self.start_button = tk.Button(
            button_frame,
            text="TRADING STARTEN",
            command=self.start_trading,
            font=('Arial', 14, 'bold'),
            bg='#00aa44',
            fg='white',
            relief=tk.FLAT,
            pady=20,
            cursor='hand2'
        )
        self.start_button.pack(fill=tk.X, pady=(0, 15))
        
        # STOP BUTTON
        self.stop_button = tk.Button(
            button_frame,
            text="TRADING STOPPEN",
            command=self.stop_trading,
            font=('Arial', 14, 'bold'),
            bg='#cc3333',
            fg='white',
            relief=tk.FLAT,
            pady=20,
            cursor='hand2',
            state=tk.DISABLED
        )
        self.stop_button.pack(fill=tk.X, pady=(0, 15))
        
        # ANALYZE BUTTON
        self.analyze_button = tk.Button(
            button_frame,
            text="SOFORT ANALYSIEREN",
            command=self.run_immediate_analysis,
            font=('Arial', 12, 'bold'),
            bg='#3366cc',
            fg='white',
            relief=tk.FLAT,
            pady=15,
            cursor='hand2'
        )
        self.analyze_button.pack(fill=tk.X, pady=(0, 15))
        
        # TRAIN MODELS BUTTON
        self.train_button = tk.Button(
            button_frame,
            text="MODELLE TRAINIEREN",
            command=self.force_model_training,
            font=('Arial', 12, 'bold'),
            bg='#9933cc',
            fg='white',
            relief=tk.FLAT,
            pady=15,
            cursor='hand2'
        )
        self.train_button.pack(fill=tk.X)
        
        # SETTINGS
        self.create_settings_section(control_frame)
        
        # CURRENT DATA
        self.create_current_data_display(control_frame)
    
    def create_settings_section(self, parent):
        """Erstelle Einstellungen"""
        settings_frame = tk.LabelFrame(
            parent,
            text="EINSTELLUNGEN",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a',
            bd=2
        )
        settings_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # Auto Update
        auto_frame = tk.Frame(settings_frame, bg='#1a1a1a')
        auto_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.auto_update_var = tk.BooleanVar(value=True)
        auto_check = tk.Checkbutton(
            auto_frame,
            text="Auto-Update (60s)",
            variable=self.auto_update_var,
            command=self.toggle_auto_update,
            font=('Arial', 10),
            fg='#ffffff',
            bg='#1a1a1a',
            selectcolor='#333333'
        )
        auto_check.pack(side=tk.LEFT)
        
        # Accuracy Target
        target_frame = tk.Frame(settings_frame, bg='#1a1a1a')
        target_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        tk.Label(target_frame, text="Ziel-Genauigkeit:", 
                font=('Arial', 9), fg='#cccccc', bg='#1a1a1a').pack(side=tk.LEFT)
        
        self.accuracy_target_label = tk.Label(
            target_frame,
            text="60%",
            font=('Arial', 9, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        self.accuracy_target_label.pack(side=tk.RIGHT)
    
    def create_current_data_display(self, parent):
        """Erstelle aktuelle Daten-Anzeige"""
        data_frame = tk.LabelFrame(
            parent,
            text="AKTUELLE ANALYSE",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a',
            bd=2
        )
        data_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))
        
        # Current Price
        self.current_price_label = tk.Label(
            data_frame,
            text="Preis: $---.---",
            font=('Arial', 16, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        self.current_price_label.pack(pady=15)
        
        # Signal
        self.current_signal_label = tk.Label(
            data_frame,
            text="Signal: ---",
            font=('Arial', 14, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        )
        self.current_signal_label.pack(pady=10)
        
        # Confidence
        self.current_confidence_label = tk.Label(
            data_frame,
            text="Konfidenz: ---%",
            font=('Arial', 12),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        self.current_confidence_label.pack(pady=5)
        
        # ML Prediction
        self.ml_prediction_label = tk.Label(
            data_frame,
            text="ML-Vorhersage: ---",
            font=('Arial', 12),
            fg='#9933cc',
            bg='#1a1a1a'
        )
        self.ml_prediction_label.pack(pady=5)
        
        # Models Count
        self.models_count_label = tk.Label(
            data_frame,
            text="Modelle: 0",
            font=('Arial', 10),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        self.models_count_label.pack(pady=5)

    def create_chart_panel(self):
        """Erstelle Chart-Panel"""
        chart_frame = tk.Frame(self.main_frame, bg='#0a0a0a')
        chart_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # CHART TITLE
        tk.Label(
            chart_frame,
            text="BITCOIN PREIS & GENAUIGKEITS-VERLAUF",
            font=('Arial', 14, 'bold'),
            fg='#ffffff',
            bg='#0a0a0a'
        ).pack(pady=(0, 10))

        # MATPLOTLIB SETUP
        plt.style.use('dark_background')
        self.chart_fig, (self.price_ax, self.accuracy_ax) = plt.subplots(2, 1, figsize=(12, 10),
                                                                         gridspec_kw={'height_ratios': [2, 1]})
        self.chart_fig.patch.set_facecolor('#0a0a0a')

        # CANVAS
        self.chart_canvas = FigureCanvasTkAgg(self.chart_fig, chart_frame)
        self.chart_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Initial Charts
        self.show_waiting_chart()

    def create_analysis_panel(self):
        """Erstelle Analyse-Panel"""
        analysis_frame = tk.Frame(self.main_frame, bg='#1a1a1a', width=400)
        analysis_frame.pack(side=tk.RIGHT, fill=tk.Y)
        analysis_frame.pack_propagate(False)

        # TITLE
        tk.Label(
            analysis_frame,
            text="LIVE ANALYSE & STATUS",
            font=('Arial', 14, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        ).pack(pady=(20, 15))

        # STATUS LOG
        log_frame = tk.Frame(analysis_frame, bg='#1a1a1a')
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        tk.Label(
            log_frame,
            text="STATUS LOG:",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        ).pack(anchor=tk.W, pady=(0, 5))

        # Text Widget mit Scrollbar
        text_frame = tk.Frame(log_frame, bg='#1a1a1a')
        text_frame.pack(fill=tk.BOTH, expand=True)

        self.status_text = tk.Text(
            text_frame,
            height=25,
            font=('Courier', 9),
            bg='#333333',
            fg='#00ff88',
            insertbackground='white',
            state=tk.DISABLED,
            wrap=tk.WORD
        )

        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_footer(self):
        """Erstelle Footer"""
        footer_frame = tk.Frame(self.root, bg='#1a1a1a', height=50)
        footer_frame.pack(fill=tk.X, padx=10, pady=(10, 10))
        footer_frame.pack_propagate(False)

        # Version Info
        tk.Label(
            footer_frame,
            text=f"Ultimate Bitcoin Trading System V3.0 • Selbstlernend • Optimiert für 60%+ Genauigkeit",
            font=('Arial', 10),
            fg='#cccccc',
            bg='#1a1a1a'
        ).pack(side=tk.LEFT, padx=20, pady=15)

        # Performance Info
        self.performance_label = tk.Label(
            footer_frame,
            text="Performance: Bereit",
            font=('Arial', 10),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        self.performance_label.pack(side=tk.RIGHT, padx=20, pady=15)

    def show_waiting_chart(self):
        """Zeige Warteanzeige"""
        # Price Chart
        self.price_ax.clear()
        self.price_ax.text(0.5, 0.5, 'Klicken Sie "TRADING STARTEN"\nfür Live Bitcoin-Charts',
                          horizontalalignment='center', verticalalignment='center',
                          transform=self.price_ax.transAxes, fontsize=14, color='#00ff88',
                          weight='bold', bbox=dict(boxstyle='round,pad=1', facecolor='#1a1a1a', alpha=0.8))
        self.price_ax.set_facecolor('#0a0a0a')
        self.price_ax.set_title('Bitcoin Preis-Chart', color='white', fontsize=12, fontweight='bold')

        # Accuracy Chart
        self.accuracy_ax.clear()
        self.accuracy_ax.text(0.5, 0.5, 'Genauigkeits-Verlauf\nwird hier angezeigt',
                             horizontalalignment='center', verticalalignment='center',
                             transform=self.accuracy_ax.transAxes, fontsize=12, color='#ffaa00',
                             weight='bold', bbox=dict(boxstyle='round,pad=1', facecolor='#1a1a1a', alpha=0.8))
        self.accuracy_ax.set_facecolor('#0a0a0a')
        self.accuracy_ax.set_title('ML-Modell Genauigkeit', color='white', fontsize=12, fontweight='bold')

        # Remove ticks
        for ax in [self.price_ax, self.accuracy_ax]:
            ax.set_xticks([])
            ax.set_yticks([])
            for spine in ax.spines.values():
                spine.set_visible(False)

        self.chart_canvas.draw()

    def log_message(self, message):
        """Füge Nachricht zum Status-Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, log_entry)
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
        self.root.update()

        print(log_entry.strip())

    # BUTTON FUNKTIONEN
    def start_trading(self):
        """Starte Trading"""
        if self.is_running:
            self.log_message("Trading läuft bereits!")
            return

        self.log_message("STARTE ULTIMATE BITCOIN TRADING SYSTEM V3.0...")
        self.log_message("Selbstlernendes ML-System wird aktiviert...")

        # Update GUI
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.system_status_label.config(text="SYSTEM: LÄUFT", fg='#00ff88')

        # Starte ersten Analyse-Lauf
        self.run_analysis_thread()

        # Starte Auto-Update
        if self.auto_update_var.get():
            self.start_auto_update()

    def stop_trading(self):
        """Stoppe Trading"""
        if not self.is_running:
            self.log_message("Trading läuft nicht!")
            return

        self.log_message("STOPPE ULTIMATE BITCOIN TRADING SYSTEM V3.0...")

        # Update GUI
        self.is_running = False
        self.auto_update = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.system_status_label.config(text="SYSTEM: GESTOPPT", fg='#cc3333')

        # Stoppe Auto-Update
        if self.update_job_id:
            self.root.after_cancel(self.update_job_id)
            self.update_job_id = None

        self.log_message("Trading gestoppt - ML-Modelle gespeichert")

    def run_immediate_analysis(self):
        """Führe sofortige Analyse durch"""
        self.log_message("Führe sofortige Analyse durch...")
        self.run_analysis_thread()

    def force_model_training(self):
        """Erzwinge Model-Training"""
        self.log_message("Erzwinge ML-Model Training...")

        def training_worker():
            try:
                df = self.trading_system.get_optimized_market_data()
                if not df.empty:
                    success = self.trading_system.train_self_learning_models(df, force_retrain=True)
                    if success:
                        self.log_message("ML-Modelle erfolgreich trainiert!")
                    else:
                        self.log_message("ML-Training fehlgeschlagen")
                else:
                    self.log_message("Keine Marktdaten für Training verfügbar")
            except Exception as e:
                self.log_message(f"FEHLER beim Training: {e}")

        thread = threading.Thread(target=training_worker, daemon=True)
        thread.start()

    def toggle_auto_update(self):
        """Schalte Auto-Update um"""
        if self.auto_update_var.get() and self.is_running:
            self.start_auto_update()
        else:
            self.auto_update = False
            if self.update_job_id:
                self.root.after_cancel(self.update_job_id)
                self.update_job_id = None

        status = "EIN" if self.auto_update_var.get() else "AUS"
        self.log_message(f"Auto-Update: {status}")

    def run_analysis_thread(self):
        """Führe Analyse in separatem Thread durch"""
        def analysis_worker():
            try:
                start_time = time.time()

                # Führe Ultimate Analyse durch
                result = self.trading_system.run_ultimate_analysis()

                analysis_time = time.time() - start_time

                if 'error' in result:
                    self.log_message(f"FEHLER: {result['error']}")
                    return

                # Speichere Ergebnis
                self.last_analysis = result
                self.analysis_history.append(result)

                # Behalte nur letzte 100 Analysen
                if len(self.analysis_history) > 100:
                    self.analysis_history = self.analysis_history[-100:]

                # Update GUI im Hauptthread
                self.root.after(0, lambda: self.update_gui_with_analysis(result))

                self.log_message(f"Analyse abgeschlossen in {analysis_time:.2f}s")

            except Exception as e:
                self.log_message(f"FEHLER bei Analyse: {e}")

        # Starte Thread
        thread = threading.Thread(target=analysis_worker, daemon=True)
        thread.start()

    def update_gui_with_analysis(self, result):
        """Update GUI mit Analyse-Ergebnis"""
        try:
            # Update Header Status
            self.last_update_label.config(
                text=f"LETZTES UPDATE: {datetime.now().strftime('%H:%M:%S')}")

            # Update Current Data
            current_price = result.get('current_price', 0)
            signal = result.get('signal', 'N/A')
            confidence = result.get('confidence', 0)
            ml_prediction = result.get('ml_prediction', 0.5)
            models_count = result.get('models_available', 0)

            self.current_price_label.config(text=f"Preis: ${current_price:,.2f}")

            # Signal mit Farbe
            signal_colors = {'KAUFEN': '#00ff88', 'VERKAUFEN': '#ff3333', 'HALTEN': '#ffaa00'}
            signal_color = signal_colors.get(signal, '#ffffff')
            self.current_signal_label.config(text=f"Signal: {signal}", fg=signal_color)

            self.current_confidence_label.config(text=f"Konfidenz: {confidence:.1%}")
            self.ml_prediction_label.config(text=f"ML-Vorhersage: {ml_prediction:.3f}")
            self.models_count_label.config(text=f"Modelle: {models_count}")

            # Update Accuracy Status
            session_stats = result.get('session_stats', {})
            current_accuracy = session_stats.get('current_accuracy', 0)
            self.accuracy_status_label.config(text=f"GENAUIGKEIT: {current_accuracy:.1%}")

            # Färbe Genauigkeit basierend auf Ziel
            if current_accuracy >= 0.6:
                accuracy_color = '#00ff88'  # Grün für >= 60%
            elif current_accuracy >= 0.5:
                accuracy_color = '#ffaa00'  # Orange für >= 50%
            else:
                accuracy_color = '#ff3333'  # Rot für < 50%

            self.accuracy_status_label.config(fg=accuracy_color)

            # Update Performance
            analysis_time = result.get('analysis_time', 0)
            data_points = result.get('data_points', 0)
            self.performance_label.config(
                text=f"Performance: {analysis_time:.2f}s • {data_points} Datenpunkte")

            # Update Charts
            self.update_charts(result)

            # Log Ergebnis
            self.log_message(f"ANALYSE: {signal} (Konfidenz: {confidence:.1%}) - Preis: ${current_price:,.2f}")
            self.log_message(f"ML-Vorhersage: {ml_prediction:.3f} - Genauigkeit: {current_accuracy:.1%}")

            # Speichere Accuracy für Chart
            self.accuracy_history.append({
                'timestamp': datetime.now(),
                'accuracy': current_accuracy,
                'confidence': confidence
            })

            # Behalte nur letzte 50 Accuracy-Punkte
            if len(self.accuracy_history) > 50:
                self.accuracy_history = self.accuracy_history[-50:]

        except Exception as e:
            self.log_message(f"FEHLER bei GUI-Update: {e}")

    def update_charts(self, result):
        """Update Charts mit neuen Daten"""
        try:
            # Hole Marktdaten
            market_data = self.trading_system.market_data

            if market_data is not None and not market_data.empty:
                # PREIS-CHART
                self.price_ax.clear()

                # Letzte 7 Tage
                recent_data = market_data.tail(168)  # 7 Tage * 24 Stunden

                # Fix Timezone-Issues
                plot_dates = recent_data.index
                if hasattr(plot_dates, 'tz') and plot_dates.tz is not None:
                    plot_dates = plot_dates.tz_localize(None)

                # Preis-Linie
                self.price_ax.plot(plot_dates, recent_data['Close'],
                                  color='#00ff88', linewidth=2, label='Bitcoin Preis')

                # Moving Average
                if len(recent_data) >= 20:
                    sma_20 = recent_data['Close'].rolling(20).mean()
                    self.price_ax.plot(plot_dates, sma_20,
                                      color='#ffaa00', linewidth=1, alpha=0.8, label='SMA 20')

                # Aktueller Preis Marker
                current_price = result.get('current_price', 0)
                self.price_ax.axhline(y=current_price, color='#ffffff', linestyle='--', alpha=0.7)

                # Styling
                self.price_ax.set_title('Bitcoin Preis (7 Tage)', color='white', fontsize=12, fontweight='bold')
                self.price_ax.set_ylabel('Preis ($)', color='white')
                self.price_ax.tick_params(colors='white', labelsize=8)
                self.price_ax.legend(loc='upper left', fontsize=8)
                self.price_ax.grid(True, alpha=0.3)
                self.price_ax.set_facecolor('#0a0a0a')

                # Format x-axis
                if len(plot_dates) > 0 and not isinstance(plot_dates[0], (int, float)):
                    self.price_ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
                    plt.setp(self.price_ax.xaxis.get_majorticklabels(), rotation=45)

            # GENAUIGKEITS-CHART
            self.accuracy_ax.clear()

            if len(self.accuracy_history) > 1:
                timestamps = [entry['timestamp'] for entry in self.accuracy_history]
                accuracies = [entry['accuracy'] for entry in self.accuracy_history]
                confidences = [entry['confidence'] for entry in self.accuracy_history]

                # Genauigkeits-Linie
                self.accuracy_ax.plot(timestamps, accuracies,
                                     color='#00ff88', linewidth=2, marker='o', markersize=4, label='Genauigkeit')

                # Konfidenz-Linie
                self.accuracy_ax.plot(timestamps, confidences,
                                     color='#3366cc', linewidth=1, alpha=0.7, label='Konfidenz')

                # 60% Ziel-Linie
                self.accuracy_ax.axhline(y=0.6, color='#ffaa00', linestyle='--', alpha=0.8, label='60% Ziel')

                # Styling
                self.accuracy_ax.set_title('ML-Modell Genauigkeit & Konfidenz', color='white', fontsize=12, fontweight='bold')
                self.accuracy_ax.set_ylabel('Genauigkeit / Konfidenz', color='white')
                self.accuracy_ax.tick_params(colors='white', labelsize=8)
                self.accuracy_ax.legend(loc='upper left', fontsize=8)
                self.accuracy_ax.grid(True, alpha=0.3)
                self.accuracy_ax.set_facecolor('#0a0a0a')
                self.accuracy_ax.set_ylim(0, 1)

                # Format x-axis
                self.accuracy_ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                plt.setp(self.accuracy_ax.xaxis.get_majorticklabels(), rotation=45)

            # Tight layout und draw
            self.chart_fig.tight_layout()
            self.chart_canvas.draw()

        except Exception as e:
            self.log_message(f"FEHLER bei Chart-Update: {e}")

    def start_auto_update(self):
        """Starte Auto-Update"""
        if not self.is_running:
            return

        self.auto_update = True
        self.schedule_next_update()
        self.log_message(f"Auto-Update gestartet (Intervall: {self.update_interval}s)")

    def schedule_next_update(self):
        """Plane nächstes Update"""
        if self.auto_update and self.is_running:
            self.update_job_id = self.root.after(self.update_interval * 1000, self.auto_update_callback)

    def auto_update_callback(self):
        """Auto-Update Callback"""
        if self.auto_update and self.is_running:
            self.log_message("Auto-Update: Führe Analyse durch...")
            self.run_analysis_thread()
            self.schedule_next_update()

    def on_closing(self):
        """Handler für Fenster-Schließen"""
        self.log_message("Schließe Ultimate Bitcoin Trading GUI V3.0...")

        # Stoppe alle Prozesse
        self.is_running = False
        self.auto_update = False

        if self.update_job_id:
            self.root.after_cancel(self.update_job_id)

        # Speichere ML-Modelle
        if hasattr(self.trading_system, '_save_persistent_data'):
            self.trading_system._save_persistent_data()

        # Schließe matplotlib
        plt.close('all')

        self.log_message("GUI geschlossen - ML-Modelle gespeichert")
        self.root.quit()
        self.root.destroy()

    def run(self):
        """Starte GUI"""
        self.log_message("ULTIMATE BITCOIN TRADING GUI V3.0 bereit!")
        self.log_message("Selbstlernendes ML-System • 60%+ Genauigkeit • Vollständig Optimiert")
        self.log_message("Klicken Sie 'TRADING STARTEN' um zu beginnen")

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("GUI beendet")
        finally:
            self.on_closing()

def main():
    """Hauptfunktion"""
    print("STARTE ULTIMATE BITCOIN TRADING GUI V3.0...")

    try:
        gui = UltimateBitcoinGUIV3()
        gui.run()
    except Exception as e:
        print(f"FEHLER beim Starten der GUI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
