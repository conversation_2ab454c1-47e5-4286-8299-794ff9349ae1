#!/usr/bin/env python3
"""
🚀 WORKING TRADING DASHBOARD LAUNCHER
====================================
Launcher für die funktionierende Version
"""

import subprocess
import sys
from pathlib import Path

def check_dependencies():
    """🔍 Abhängigkeiten prüfen"""
    print("🔍 PRÜFE ABHÄNGIGKEITEN")
    print("-" * 25)

    required = ['tkinter', 'numpy', 'requests']
    missing = []

    for module in required:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} (FEHLT)")
            missing.append(module)

    if missing:
        print(f"\n❌ FEHLENDE MODULE: {', '.join(missing)}")
        print(f"📦 INSTALLATION: pip install {' '.join(missing)}")
        return False

    print("\n✅ ALLE MODULE VERFÜGBAR")
    return True

def start_dashboard():
    """🚀 Dashboard starten"""
    dashboard_file = Path("working_trading_dashboard.py")

    if not dashboard_file.exists():
        print("❌ Dashboard-Datei nicht gefunden!")
        return False

    print("\n🚀 STARTE WORKING TRADING DASHBOARD")
    print("=" * 40)
    print("📊 Lade Bitcoin-Daten...")
    print("🎨 Erstelle GUI...")
    print("\n⏳ Dashboard wird geöffnet...")

    try:
        subprocess.run([sys.executable, str(dashboard_file)])
        return True
    except Exception as e:
        print(f"\n❌ Fehler: {e}")
        return False

def main():
    """🚀 Hauptfunktion"""
    print("🚀 WORKING BITCOIN TRADING DASHBOARD LAUNCHER")
    print("=" * 50)
    print("✅ Funktionierende Version ohne Chart-Probleme")
    print("📊 Features: Live-Preis, RSI, SMA, Trading-Signale")
    print("=" * 50)

    # Abhängigkeiten prüfen
    if not check_dependencies():
        input("\n⏸️ Drücken Sie Enter zum Beenden...")
        return

    # Dashboard starten
    print("\n" + "=" * 50)
    response = input("🚀 Dashboard starten? (j/n): ").lower().strip()

    if response in ['j', 'ja', 'y', 'yes', '']:
        start_dashboard()
    else:
        print("\n👋 Auf Wiedersehen!")

if __name__ == "__main__":
    main()