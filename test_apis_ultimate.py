#!/usr/bin/env python3
"""
ULTIMATE API TESTS
==================
Teste alle APIs vor der Integration ins Trading Tool
- Binance API (Kostenlos)
- CoinGecko API (Kostenlos)
- Yahoo Finance API
- News Sentiment API
- Technische Indikatoren

AUSGIEBIGE TESTS VOR INTEGRATION!
"""

import requests
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import json

class UltimateAPITester:
    """
    ULTIMATE API TESTER
    ===================
    Teste alle APIs ausgiebig vor Integration
    """
    
    def __init__(self):
        self.test_results = {}
        print("ULTIMATE API TESTER initialisiert")
        print("Teste alle APIs ausgiebig vor Integration...")
    
    def test_binance_api(self):
        """Teste Binance API"""
        print("\n🔍 TESTE BINANCE API...")
        
        try:
            # Test 1: Aktueller Bitcoin Preis
            url = "https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                price = float(data['price'])
                print(f"✅ Binance Preis: ${price:,.2f}")
                
                # Test 2: 24h Statistiken
                url_24h = "https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT"
                response_24h = requests.get(url_24h, timeout=10)
                
                if response_24h.status_code == 200:
                    data_24h = response_24h.json()
                    change_24h = float(data_24h['priceChangePercent'])
                    volume_24h = float(data_24h['volume'])
                    
                    print(f"✅ 24h Änderung: {change_24h:+.2f}%")
                    print(f"✅ 24h Volume: {volume_24h:,.0f} BTC")
                    
                    # Test 3: Klines (Candlestick Daten)
                    url_klines = "https://api.binance.com/api/v3/klines?symbol=BTCUSDT&interval=15m&limit=100"
                    response_klines = requests.get(url_klines, timeout=10)
                    
                    if response_klines.status_code == 200:
                        klines = response_klines.json()
                        print(f"✅ Klines Daten: {len(klines)} 15-Min-Intervalle")
                        
                        self.test_results['binance'] = {
                            'status': 'SUCCESS',
                            'price': price,
                            'change_24h': change_24h,
                            'volume_24h': volume_24h,
                            'klines_count': len(klines),
                            'latest_kline': klines[-1]
                        }
                        return True
                    else:
                        print(f"❌ Binance Klines Fehler: {response_klines.status_code}")
                else:
                    print(f"❌ Binance 24h Fehler: {response_24h.status_code}")
            else:
                print(f"❌ Binance Preis Fehler: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Binance API Fehler: {e}")
            self.test_results['binance'] = {'status': 'FAILED', 'error': str(e)}
            return False
    
    def test_coingecko_api(self):
        """Teste CoinGecko API"""
        print("\n🔍 TESTE COINGECKO API...")
        
        try:
            # Test 1: Bitcoin Preis und Marktdaten
            url = "https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_change=true&include_market_cap=true&include_24hr_vol=true"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                btc_data = data['bitcoin']
                
                price = btc_data['usd']
                change_24h = btc_data['usd_24h_change']
                market_cap = btc_data['usd_market_cap']
                volume_24h = btc_data['usd_24h_vol']
                
                print(f"✅ CoinGecko Preis: ${price:,.2f}")
                print(f"✅ 24h Änderung: {change_24h:+.2f}%")
                print(f"✅ Market Cap: ${market_cap:,.0f}")
                print(f"✅ 24h Volume: ${volume_24h:,.0f}")
                
                # Test 2: Markt-Metriken
                url_market = "https://api.coingecko.com/api/v3/coins/bitcoin/market_chart?vs_currency=usd&days=1&interval=hourly"
                response_market = requests.get(url_market, timeout=10)
                
                if response_market.status_code == 200:
                    market_data = response_market.json()
                    prices = market_data['prices']
                    volumes = market_data['total_volumes']
                    
                    print(f"✅ Historische Daten: {len(prices)} Stunden")
                    
                    self.test_results['coingecko'] = {
                        'status': 'SUCCESS',
                        'price': price,
                        'change_24h': change_24h,
                        'market_cap': market_cap,
                        'volume_24h': volume_24h,
                        'historical_points': len(prices)
                    }
                    return True
                else:
                    print(f"❌ CoinGecko Market Fehler: {response_market.status_code}")
            else:
                print(f"❌ CoinGecko Preis Fehler: {response.status_code}")
                
        except Exception as e:
            print(f"❌ CoinGecko API Fehler: {e}")
            self.test_results['coingecko'] = {'status': 'FAILED', 'error': str(e)}
            return False
    
    def test_yahoo_finance_api(self):
        """Teste Yahoo Finance API"""
        print("\n🔍 TESTE YAHOO FINANCE API...")
        
        try:
            # Test 1: Bitcoin Daten von Yahoo Finance
            btc = yf.Ticker("BTC-USD")
            
            # Aktuelle Info
            info = btc.info
            current_price = info.get('regularMarketPrice', 0)
            
            print(f"✅ Yahoo Finance Preis: ${current_price:,.2f}")
            
            # Test 2: Historische Daten
            hist = btc.history(period="7d", interval="1h")
            
            if not hist.empty:
                latest_price = hist['Close'].iloc[-1]
                price_change = (latest_price - hist['Close'].iloc[0]) / hist['Close'].iloc[0] * 100
                
                print(f"✅ Historische Daten: {len(hist)} Stunden")
                print(f"✅ 7-Tage Änderung: {price_change:+.2f}%")
                print(f"✅ Aktueller Preis (Hist): ${latest_price:,.2f}")
                
                # Test 3: Technische Indikatoren berechnen
                hist['SMA_20'] = hist['Close'].rolling(20).mean()
                hist['RSI'] = self.calculate_rsi(hist['Close'])
                
                current_sma = hist['SMA_20'].iloc[-1]
                current_rsi = hist['RSI'].iloc[-1]
                
                print(f"✅ SMA 20: ${current_sma:,.2f}")
                print(f"✅ RSI: {current_rsi:.1f}")
                
                self.test_results['yahoo_finance'] = {
                    'status': 'SUCCESS',
                    'current_price': current_price,
                    'latest_price': latest_price,
                    'price_change_7d': price_change,
                    'data_points': len(hist),
                    'sma_20': current_sma,
                    'rsi': current_rsi
                }
                return True
            else:
                print("❌ Yahoo Finance: Keine historischen Daten")
                
        except Exception as e:
            print(f"❌ Yahoo Finance API Fehler: {e}")
            self.test_results['yahoo_finance'] = {'status': 'FAILED', 'error': str(e)}
            return False
    
    def calculate_rsi(self, prices, period=14):
        """Berechne RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def test_news_sentiment_api(self):
        """Teste News Sentiment (ohne API Key)"""
        print("\n🔍 TESTE NEWS SENTIMENT...")
        
        try:
            # Alternative: Verwende öffentliche News-Quellen
            # Test mit CoinGecko News (falls verfügbar)
            url = "https://api.coingecko.com/api/v3/search/trending"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                trending_coins = data.get('coins', [])
                
                print(f"✅ Trending Coins: {len(trending_coins)}")
                
                # Simuliere Sentiment-Analyse
                bitcoin_trending = any(coin['item']['name'].lower() == 'bitcoin' for coin in trending_coins)
                sentiment_score = 0.6 if bitcoin_trending else 0.4
                
                print(f"✅ Bitcoin Trending: {'Ja' if bitcoin_trending else 'Nein'}")
                print(f"✅ Sentiment Score: {sentiment_score:.2f}")
                
                self.test_results['news_sentiment'] = {
                    'status': 'SUCCESS',
                    'trending_coins': len(trending_coins),
                    'bitcoin_trending': bitcoin_trending,
                    'sentiment_score': sentiment_score
                }
                return True
            else:
                print(f"❌ News API Fehler: {response.status_code}")
                
        except Exception as e:
            print(f"❌ News Sentiment Fehler: {e}")
            self.test_results['news_sentiment'] = {'status': 'FAILED', 'error': str(e)}
            return False
    
    def test_technical_indicators(self):
        """Teste erweiterte technische Indikatoren"""
        print("\n🔍 TESTE TECHNISCHE INDIKATOREN...")
        
        try:
            # Hole Daten von Yahoo Finance
            btc = yf.Ticker("BTC-USD")
            hist = btc.history(period="30d", interval="1h")
            
            if not hist.empty:
                prices = hist['Close']
                
                # RSI
                rsi = self.calculate_rsi(prices)
                current_rsi = rsi.iloc[-1]
                
                # MACD
                ema_12 = prices.ewm(span=12).mean()
                ema_26 = prices.ewm(span=26).mean()
                macd = ema_12 - ema_26
                signal = macd.ewm(span=9).mean()
                histogram = macd - signal
                
                current_macd = macd.iloc[-1]
                current_signal = signal.iloc[-1]
                current_histogram = histogram.iloc[-1]
                
                # Bollinger Bands
                sma_20 = prices.rolling(20).mean()
                std_20 = prices.rolling(20).std()
                bb_upper = sma_20 + (std_20 * 2)
                bb_lower = sma_20 - (std_20 * 2)
                bb_position = (prices - bb_lower) / (bb_upper - bb_lower)
                
                current_bb_position = bb_position.iloc[-1]
                
                # Stochastic Oscillator
                high_14 = hist['High'].rolling(14).max()
                low_14 = hist['Low'].rolling(14).min()
                k_percent = 100 * ((prices - low_14) / (high_14 - low_14))
                d_percent = k_percent.rolling(3).mean()
                
                current_k = k_percent.iloc[-1]
                current_d = d_percent.iloc[-1]
                
                print(f"✅ RSI: {current_rsi:.1f}")
                print(f"✅ MACD: {current_macd:.2f}")
                print(f"✅ MACD Signal: {current_signal:.2f}")
                print(f"✅ MACD Histogram: {current_histogram:.2f}")
                print(f"✅ BB Position: {current_bb_position:.2f}")
                print(f"✅ Stochastic %K: {current_k:.1f}")
                print(f"✅ Stochastic %D: {current_d:.1f}")
                
                self.test_results['technical_indicators'] = {
                    'status': 'SUCCESS',
                    'rsi': current_rsi,
                    'macd': current_macd,
                    'macd_signal': current_signal,
                    'macd_histogram': current_histogram,
                    'bb_position': current_bb_position,
                    'stochastic_k': current_k,
                    'stochastic_d': current_d
                }
                return True
            else:
                print("❌ Keine Daten für technische Indikatoren")
                
        except Exception as e:
            print(f"❌ Technische Indikatoren Fehler: {e}")
            self.test_results['technical_indicators'] = {'status': 'FAILED', 'error': str(e)}
            return False
    
    def run_all_tests(self):
        """Führe alle Tests durch"""
        print("\n" + "="*80)
        print("ULTIMATE API TESTS - AUSGIEBIGE TESTS VOR INTEGRATION")
        print("="*80)
        
        tests = [
            ('Binance API', self.test_binance_api),
            ('CoinGecko API', self.test_coingecko_api),
            ('Yahoo Finance API', self.test_yahoo_finance_api),
            ('News Sentiment', self.test_news_sentiment_api),
            ('Technische Indikatoren', self.test_technical_indicators)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            start_time = time.time()
            
            try:
                success = test_func()
                end_time = time.time()
                duration = end_time - start_time
                
                results[test_name] = {
                    'success': success,
                    'duration': duration,
                    'data': self.test_results.get(test_name.lower().replace(' ', '_'), {})
                }
                
                print(f"⏱️ Dauer: {duration:.2f}s")
                
            except Exception as e:
                results[test_name] = {
                    'success': False,
                    'error': str(e),
                    'duration': 0
                }
                print(f"❌ Test fehlgeschlagen: {e}")
        
        # Zusammenfassung
        print("\n" + "="*80)
        print("TEST-ZUSAMMENFASSUNG")
        print("="*80)
        
        total_tests = len(tests)
        successful_tests = sum(1 for result in results.values() if result['success'])
        
        for test_name, result in results.items():
            status = "✅ ERFOLGREICH" if result['success'] else "❌ FEHLGESCHLAGEN"
            duration = result.get('duration', 0)
            print(f"{test_name:25}: {status} ({duration:.2f}s)")
        
        print(f"\nGESAMTERGEBNIS: {successful_tests}/{total_tests} Tests erfolgreich")
        
        if successful_tests == total_tests:
            print("🎉 ALLE TESTS ERFOLGREICH - BEREIT FÜR INTEGRATION!")
            return True
        else:
            print("⚠️ EINIGE TESTS FEHLGESCHLAGEN - ÜBERPRÜFUNG ERFORDERLICH")
            return False

def main():
    """Hauptfunktion"""
    tester = UltimateAPITester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🚀 ALLE APIs GETESTET UND BEREIT FÜR INTEGRATION!")
    else:
        print("\n⚠️ TESTS TEILWEISE FEHLGESCHLAGEN - ÜBERPRÜFUNG ERFORDERLICH")

if __name__ == "__main__":
    main()
