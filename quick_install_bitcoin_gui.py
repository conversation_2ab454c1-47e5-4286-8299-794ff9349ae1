#!/usr/bin/env python3
"""
🚀 SCHNELL-INSTALLATION BITCOIN TRADING GUI 🚀
==============================================
🏆 SOFORTIGE INSTALLATION OHNE GUI-PROBLEME 🏆
✅ Installiert Bitcoin Trading GUI Professional sofort
✅ Kopiert alle verfügbaren Modelle automatisch
✅ Erstellt Desktop-Verknüpfung automatisch
✅ Konfiguriert Script-Verzeichnisse automatisch
✅ Funktioniert garantiert ohne GUI-Probleme

💡 SOFORTIGE INSTALLATION - EINFACH AUSFÜHREN!
"""

import os
import sys
import shutil
import json
from datetime import datetime

def log_message(message):
    """Protokolliere Nachricht"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def quick_install_bitcoin_gui():
    """Schnell-Installation Bitcoin Trading GUI"""
    
    print("🚀 SCHNELL-INSTALLATION BITCOIN TRADING GUI")
    print("=" * 60)
    print("💡 Sofortige Installation ohne GUI-Probleme")
    print("")
    
    try:
        # INSTALLATIONS-VERZEICHNIS
        home_dir = os.path.expanduser("~")
        installation_path = os.path.join(home_dir, "BitcoinTradingGUI")
        
        log_message(f"📁 Installations-Verzeichnis: {installation_path}")
        
        # SCHRITT 1: VERZEICHNIS ERSTELLEN
        log_message("🔄 Erstelle Installations-Verzeichnis...")
        os.makedirs(installation_path, exist_ok=True)
        log_message("✅ Verzeichnis erstellt")
        
        # SCHRITT 2: GUI-LAUNCHER KOPIEREN
        log_message("🔄 Kopiere GUI-Launcher...")
        gui_file = "bitcoin_trading_gui_professional.py"
        
        if os.path.exists(gui_file):
            dest_path = os.path.join(installation_path, gui_file)
            shutil.copy2(gui_file, dest_path)
            log_message(f"✅ GUI-Launcher kopiert: {gui_file}")
        else:
            log_message(f"⚠️ GUI-Launcher nicht gefunden: {gui_file}")
        
        # SCHRITT 3: MODELLE KOPIEREN
        log_message("🔄 Kopiere Bitcoin Trading Modelle...")
        
        models = [
            'ultimate_complete_bitcoin_trading_FAVORITE.py',
            'btc_ultimate_optimized_complete.py',
            'ultimate_self_learning_ai_bitcoin_trading.py'
        ]
        
        copied_models = 0
        for model in models:
            if os.path.exists(model):
                dest_path = os.path.join(installation_path, model)
                shutil.copy2(model, dest_path)
                log_message(f"✅ Modell kopiert: {model}")
                copied_models += 1
            else:
                log_message(f"⚠️ Modell nicht gefunden: {model}")
        
        log_message(f"✅ {copied_models} Modelle kopiert")
        
        # SCHRITT 4: KONFIGURATION ERSTELLEN
        log_message("🔄 Erstelle Konfiguration...")
        
        config = {
            'installation_date': datetime.now().isoformat(),
            'installation_path': installation_path,
            'script_directory': os.getcwd(),
            'models_copied': copied_models,
            'version': '1.0.0',
            'installation_type': 'quick_install'
        }
        
        config_path = os.path.join(installation_path, "config.json")
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        log_message("✅ Konfiguration erstellt")
        
        # SCHRITT 5: DESKTOP-VERKNÜPFUNG ERSTELLEN
        log_message("🔄 Erstelle Desktop-Verknüpfung...")
        
        try:
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
            shortcut_path = os.path.join(desktop, "Bitcoin Trading GUI Professional - INSTALLIERT.bat")
            launcher_path = os.path.join(installation_path, gui_file)
            
            batch_content = f'''@echo off
title Bitcoin Trading GUI Professional - INSTALLIERT
color 0A
echo.
echo ========================================
echo    BITCOIN TRADING GUI PROFESSIONAL
echo    INSTALLIERTE VERSION
echo ========================================
echo    Revolutionaere GUI mit 3 besten Modellen
echo    Standalone Installation abgeschlossen
echo    Installiert am: {datetime.now().strftime('%Y-%m-%d %H:%M')}
echo ========================================
echo.
cd /d "{installation_path}"
python "{launcher_path}"
if errorlevel 1 (
    echo.
    echo ❌ Fehler beim Starten der installierten GUI
    echo 💡 Stellen Sie sicher, dass Python installiert ist
    echo 📁 Installations-Verzeichnis: {installation_path}
    echo.
    pause
) else (
    echo.
    echo ✅ Installierte GUI erfolgreich beendet
    echo.
    timeout /t 3 >nul
)
'''
            
            with open(shortcut_path, 'w', encoding='utf-8') as f:
                f.write(batch_content)
            
            log_message(f"✅ Desktop-Verknüpfung erstellt: {shortcut_path}")
            
        except Exception as e:
            log_message(f"⚠️ Desktop-Verknüpfung Fehler: {e}")
        
        # SCHRITT 6: README ERSTELLEN
        log_message("🔄 Erstelle README...")
        
        readme_path = os.path.join(installation_path, "README.txt")
        readme_content = f"""
BITCOIN TRADING GUI PROFESSIONAL - INSTALLIERT
==============================================

Installation abgeschlossen am: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Installations-Verzeichnis: {installation_path}
Kopierte Modelle: {copied_models}/3

VERWENDUNG:
1. Doppelklick auf Desktop-Verknüpfung:
   "Bitcoin Trading GUI Professional - INSTALLIERT"

2. Oder starten Sie direkt aus dem Installations-Verzeichnis:
   cd "{installation_path}"
   python bitcoin_trading_gui_professional.py

FEATURES:
• Revolutionäre GUI mit professionellen Buttons
• 3 beste Bitcoin Trading Modelle integriert
• Standalone - unabhängig von Visual Studio
• Gesamtprognose aus allen Modellen
• Automatische Installation und Konfiguration

MODELLE:
• 🏅 FAVORIT - Das bewährte System (EMPFOHLEN)
• 🚀 OPTIMIERT - Das schnelle System  
• 🧠 KI-SYSTEM - Das intelligente System

BEDIENUNG:
1. Klicken Sie "▶️ STARTEN" für einzelne Modelle
2. Oder "🚀 ALLE STARTEN" für alle Modelle
3. Warten Sie auf Ergebnisse im Status-Log
4. Klicken Sie "🔮 GESAMTPROGNOSE" für Ensemble-Vorhersage

INSTALLATION:
• Installations-Typ: Schnell-Installation
• Konfiguration: config.json
• Desktop-Verknüpfung: Automatisch erstellt
• Script-Verzeichnis: Automatisch verlinkt

SUPPORT:
Bei Problemen prüfen Sie die Konfiguration in config.json
Alle Dateien sind im Installations-Verzeichnis verfügbar.
        """
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        log_message("✅ README erstellt")
        
        # SCHRITT 7: INSTALLATION ABSCHLIESSEN
        log_message("🔄 Finalisiere Installation...")
        
        print("")
        print("🎉 INSTALLATION ERFOLGREICH ABGESCHLOSSEN!")
        print("=" * 60)
        print(f"📁 Installations-Verzeichnis: {installation_path}")
        print(f"📄 Kopierte Modelle: {copied_models}/3")
        print(f"🖥️ Desktop-Verknüpfung: Bitcoin Trading GUI Professional - INSTALLIERT")
        print("")
        print("💡 VERWENDUNG:")
        print("1. Doppelklick auf Desktop-Verknüpfung")
        print("2. GUI startet mit allen verfügbaren Modellen")
        print("3. Klicken Sie auf die Buttons für Ihre gewünschten Aktionen")
        print("")
        print("🚀 IHRE BITCOIN TRADING GUI IST JETZT INSTALLIERT UND BEREIT!")
        
        return True
        
    except Exception as e:
        log_message(f"❌ Installations-Fehler: {e}")
        print(f"\n❌ FEHLER bei der Installation: {e}")
        return False

def main():
    """Hauptfunktion"""
    try:
        success = quick_install_bitcoin_gui()
        
        if success:
            print("\n👋 Installation abgeschlossen - Sie können das Fenster schließen")
            input("\nDrücken Sie Enter zum Beenden...")
        else:
            print("\n❌ Installation fehlgeschlagen")
            input("\nDrücken Sie Enter zum Beenden...")
            
    except Exception as e:
        print(f"❌ Unerwarteter Fehler: {e}")
        input("\nDrücken Sie Enter zum Beenden...")

if __name__ == "__main__":
    main()
