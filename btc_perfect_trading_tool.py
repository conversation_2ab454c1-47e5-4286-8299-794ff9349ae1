#!/usr/bin/env python3
"""
🚀 PERFECT BITCOIN TRADING TOOL 🚀
==================================
PERFEKTE KOMBINATION ALLER BESTEN TECHNIKEN
Klare KAUF/VERKAUF Signale - Optimiert für Geschwindigkeit & Genauigkeit
"""

import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import accuracy_score, classification_report
import yfinance as yf
import multiprocessing
import os

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

# PERFECT TRADING KONFIGURATION
MAX_CORES = multiprocessing.cpu_count()
UPDATE_INTERVAL = 30  # Alle 30 Sekunden
LOOKBACK_DAYS = 7     # 7 Tage optimal für Geschwindigkeit
PREDICTION_HORIZONS = [1, 6, 24]  # 1h, 6h, 24h Signale

print("🚀 PERFECT BITCOIN TRADING TOOL")
print("=" * 35)
print(f"💻 CPU: {MAX_CORES} Kerne")
print(f"🎯 FOKUS: Perfekte KAUF/VERKAUF Signale")
print(f"⏱️ Update: Alle {UPDATE_INTERVAL}s")
print(f"📊 Horizonte: {PREDICTION_HORIZONS}h")
print(f"🕐 Start: {datetime.now().strftime('%H:%M:%S')}")

# Verzeichnis erstellen
os.makedirs('./perfect_trading', exist_ok=True)

class PerfectTradingTool:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.last_signals = {}
        self.signal_history = []
        self.accuracy_tracker = {}
        
    def get_bitcoin_data(self):
        """Optimierte Bitcoin-Datensammlung"""
        try:
            btc = yf.Ticker("BTC-USD")
            df = btc.history(period=f"{LOOKBACK_DAYS}d", interval="1h")
            
            if len(df) > 50:
                df.columns = [col.lower() for col in df.columns]
                return df.dropna(), True
            else:
                raise Exception("Zu wenig Daten")
                
        except Exception as e:
            # Optimierte Fallback-Daten
            end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
            start_time = end_time - timedelta(days=LOOKBACK_DAYS)
            dates = pd.date_range(start=start_time, end=end_time, freq='H')
            
            n_points = len(dates)
            np.random.seed(int(time.time()) % 1000)
            
            # Realistische Preismodellierung
            base_price = 67000
            trend = np.cumsum(np.random.normal(0, 150, n_points))
            volatility = np.random.normal(0, 800, n_points)
            daily_cycle = 300 * np.sin(2 * np.pi * np.arange(n_points) / 24)
            
            prices = base_price + trend + volatility + daily_cycle
            prices = np.maximum(prices, 30000)
            
            df = pd.DataFrame({
                'close': prices,
                'high': prices * np.random.uniform(1.001, 1.02, n_points),
                'low': prices * np.random.uniform(0.98, 0.999, n_points),
                'open': prices * np.random.uniform(0.999, 1.001, n_points),
                'volume': np.random.lognormal(15, 0.2, n_points)
            }, index=dates)
            
            return df, False
    
    def create_perfect_features(self, df):
        """Perfekte Trading-Features - nur die besten aus Champion-Scripts"""
        df = df.copy()
        
        # === CHAMPION FEATURES (aus btc_perfect_optimized.py) ===
        
        # Returns (bewährt)
        for period in [1, 3, 6, 12, 24]:
            df[f'returns_{period}h'] = df['close'].pct_change(periods=period)
            df[f'momentum_{period}h'] = df['close'] / df['close'].shift(period) - 1
        
        # Moving Averages (Champion-Parameter)
        for window in [6, 12, 24, 48]:
            df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
            df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
            df[f'price_above_sma_{window}'] = (df['close'] > df[f'sma_{window}']).astype(float)
        
        # Golden/Death Cross (bewährt)
        df['golden_cross_6_24'] = (df['sma_6'] > df['sma_24']).astype(float)
        df['golden_cross_12_48'] = (df['sma_12'] > df['sma_48']).astype(float)
        
        # Volatilität (Champion-optimiert)
        for window in [6, 12, 24]:
            df[f'volatility_{window}'] = df['close'].rolling(window=window).std()
            df[f'vol_ratio_{window}'] = df[f'volatility_{window}'] / df['close']
        
        # RSI (bewährt)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-10)
        df['rsi_14'] = 100 - (100 / (1 + rs))
        df['rsi_oversold'] = (df['rsi_14'] < 30).astype(float)
        df['rsi_overbought'] = (df['rsi_14'] > 70).astype(float)
        
        # MACD (Champion-Parameter)
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_bullish'] = (df['macd'] > df['macd_signal']).astype(float)
        
        # Bollinger Bands (bewährt)
        bb_middle = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = bb_middle + 2 * bb_std
        df['bb_lower'] = bb_middle - 2 * bb_std
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        df['bb_squeeze'] = ((df['bb_upper'] - df['bb_lower']) / bb_middle < 0.1).astype(float)
        
        # Volume Features (optimiert)
        df['volume_sma'] = df['volume'].rolling(window=24).mean()
        df['volume_spike'] = (df['volume'] > df['volume_sma'] * 1.5).astype(float)
        
        # Zeit-Features (cyclical encoding)
        df['hour'] = df.index.hour
        df['day_of_week'] = df.index.dayofweek
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        
        # Trading Sessions
        df['is_us_trading'] = ((df['hour'] >= 9) & (df['hour'] <= 16)).astype(float)
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(float)
        
        # Trend Strength
        df['trend_strength'] = abs(df['returns_24h'])
        df['strong_trend'] = (df['trend_strength'] > df['trend_strength'].rolling(window=48).quantile(0.8)).astype(float)
        
        # Lag Features (wichtig für Zeitreihen)
        for lag in [1, 3, 6]:
            df[f'close_lag_{lag}'] = df['close'].shift(lag)
            df[f'returns_lag_{lag}'] = df['returns_1h'].shift(lag)
        
        # Perfekte Bereinigung
        df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        df = df.replace([np.inf, -np.inf], 0)
        
        return df
    
    def create_trading_labels(self, df, horizon_hours):
        """Perfekte Trading-Labels"""
        future_price = df['close'].shift(-horizon_hours)
        current_price = df['close']
        future_return = (future_price / current_price - 1).fillna(0)
        
        # Adaptive Schwellenwerte (aus Champion-Analyse)
        if horizon_hours == 1:
            threshold = 0.003  # 0.3% für 1h
        elif horizon_hours == 6:
            threshold = 0.008  # 0.8% für 6h
        else:
            threshold = 0.015  # 1.5% für 24h
        
        # Labels: 1 = KAUF (Gewinn erwartet), 0 = VERKAUF
        labels = (future_return > threshold).astype(int)
        
        return labels
    
    def train_perfect_models(self, df):
        """Perfekte Modell-Training für alle Horizonte"""
        
        print("   🤖 Trainiere perfekte Modelle...")
        
        # Features erstellen
        df_features = self.create_perfect_features(df)
        
        # Feature-Auswahl (nur die besten)
        exclude_cols = ['close', 'high', 'low', 'open', 'volume']
        feature_cols = [col for col in df_features.columns if col not in exclude_cols]
        
        results = {}
        
        for horizon in PREDICTION_HORIZONS:
            print(f"     📈 Training für {horizon}h Horizont...")
            
            # Labels für diesen Horizont
            labels = self.create_trading_labels(df_features, horizon)
            
            X = df_features[feature_cols].values
            y = labels.values
            
            # Bereinigung
            valid_mask = ~(np.isnan(X).any(axis=1) | np.isnan(y) | np.isinf(X).any(axis=1))
            X = X[valid_mask]
            y = y[valid_mask]
            
            if len(X) < 50:
                continue
            
            # Skalierung
            scaler = RobustScaler()
            X_scaled = scaler.fit_transform(X)
            self.scalers[f'{horizon}h'] = scaler
            
            # Train/Test Split
            split_idx = int(len(X_scaled) * 0.8)
            X_train, X_test = X_scaled[:split_idx], X_scaled[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # Perfekte Modelle (aus Champion-Scripts)
            models = {
                f'RandomForest_{horizon}h': RandomForestClassifier(
                    n_estimators=100,  # Optimal aus Champion
                    max_depth=15,      # Champion-Parameter
                    min_samples_split=5,
                    max_features='sqrt',
                    n_jobs=MAX_CORES,
                    random_state=42
                ),
                f'ExtraTrees_{horizon}h': ExtraTreesClassifier(
                    n_estimators=80,   # Schnell + genau
                    max_depth=12,
                    min_samples_split=3,
                    max_features='sqrt',
                    n_jobs=MAX_CORES,
                    random_state=42
                )
            }
            
            horizon_results = {}
            
            for name, model in models.items():
                try:
                    # Training
                    model.fit(X_train, y_train)
                    
                    # Evaluierung
                    y_pred = model.predict(X_test)
                    accuracy = accuracy_score(y_test, y_pred)
                    
                    horizon_results[name] = {
                        'model': model,
                        'accuracy': accuracy,
                        'feature_cols': feature_cols
                    }
                    
                    print(f"       ✅ {name}: {accuracy:.3f}")
                    
                except Exception as e:
                    print(f"       ❌ {name}: Fehler - {e}")
            
            if horizon_results:
                results[f'{horizon}h'] = horizon_results
        
        self.models = results
        return len(results) > 0

    def predict_perfect_signals(self, df):
        """Perfekte Trading-Signal Vorhersage"""

        if not self.models:
            return None

        # Features erstellen
        df_features = self.create_perfect_features(df)

        predictions = {}

        for horizon_key, horizon_models in self.models.items():
            horizon = int(horizon_key.replace('h', ''))

            # Features für diesen Horizont
            feature_cols = list(horizon_models.values())[0]['feature_cols']
            X_latest = df_features[feature_cols].iloc[-1:].values

            # Bereinigung
            if np.isnan(X_latest).any() or np.isinf(X_latest).any():
                X_latest = np.nan_to_num(X_latest, 0)

            # Skalierung
            scaler = self.scalers.get(horizon_key)
            if scaler is None:
                continue

            X_scaled = scaler.transform(X_latest)

            # Ensemble-Vorhersage
            ensemble_predictions = []
            ensemble_weights = []

            for model_name, model_data in horizon_models.items():
                try:
                    pred_proba = model_data['model'].predict_proba(X_scaled)[0]
                    buy_probability = pred_proba[1]

                    ensemble_predictions.append(buy_probability)
                    ensemble_weights.append(model_data['accuracy'])

                except Exception as e:
                    continue

            if not ensemble_predictions:
                continue

            # Gewichtetes Ensemble
            weights = np.array(ensemble_weights)
            weights = weights / weights.sum()
            ensemble_prob = np.average(ensemble_predictions, weights=weights)

            # Perfekte Trading-Signale
            if ensemble_prob > 0.7:
                signal = "STARKER KAUF 🔥🔥🔥"
                action = "SOFORT KAUFEN!"
                confidence = ensemble_prob
            elif ensemble_prob > 0.6:
                signal = "KAUF 🔥🔥"
                action = "KAUFEN"
                confidence = ensemble_prob
            elif ensemble_prob < 0.3:
                signal = "STARKER VERKAUF 🔻🔻🔻"
                action = "SOFORT VERKAUFEN!"
                confidence = 1 - ensemble_prob
            elif ensemble_prob < 0.4:
                signal = "VERKAUF 🔻🔻"
                action = "VERKAUFEN"
                confidence = 1 - ensemble_prob
            else:
                signal = "HALTEN ⚖️"
                action = "POSITION HALTEN"
                confidence = 0.5

            predictions[f'{horizon}h'] = {
                'signal': signal,
                'action': action,
                'probability': ensemble_prob,
                'confidence': confidence,
                'models_used': len(ensemble_predictions)
            }

        # Gesamtsignal (gewichtet nach Horizont-Wichtigkeit)
        if predictions:
            horizon_weights = {1: 0.5, 6: 0.3, 24: 0.2}  # Kurze Horizonte wichtiger

            weighted_prob = 0
            total_weight = 0

            for horizon_key, pred in predictions.items():
                horizon = int(horizon_key.replace('h', ''))
                weight = horizon_weights.get(horizon, 0.1)
                weighted_prob += pred['probability'] * weight
                total_weight += weight

            if total_weight > 0:
                overall_prob = weighted_prob / total_weight

                if overall_prob > 0.65:
                    overall_signal = "STARKER KAUF 🔥🔥🔥"
                    overall_action = "🚀 SOFORT KAUFEN!"
                elif overall_prob > 0.55:
                    overall_signal = "KAUF 🔥🔥"
                    overall_action = "🔥 KAUFEN"
                elif overall_prob < 0.35:
                    overall_signal = "STARKER VERKAUF 🔻🔻🔻"
                    overall_action = "💥 SOFORT VERKAUFEN!"
                elif overall_prob < 0.45:
                    overall_signal = "VERKAUF 🔻🔻"
                    overall_action = "🔻 VERKAUFEN"
                else:
                    overall_signal = "HALTEN ⚖️"
                    overall_action = "⚖️ POSITION HALTEN"

                predictions['GESAMT'] = {
                    'signal': overall_signal,
                    'action': overall_action,
                    'probability': overall_prob,
                    'confidence': max(overall_prob, 1-overall_prob)
                }

        current_price = df['close'].iloc[-1]
        current_time = df.index[-1]

        return {
            'time': current_time,
            'price': current_price,
            'predictions': predictions
        }

    def display_perfect_dashboard(self, prediction_result):
        """Perfektes Trading-Dashboard"""

        print("\n" + "="*80)
        print("🚀 PERFECT BITCOIN TRADING TOOL - LIVE DASHBOARD 🚀")
        print("="*80)

        if prediction_result and prediction_result['predictions']:
            predictions = prediction_result['predictions']

            print(f"\n📊 LIVE TRADING STATUS:")
            print(f"🕐 Zeit: {prediction_result['time'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"💰 Bitcoin Preis: ${prediction_result['price']:,.2f}")

            # Gesamtsignal (wichtigste Info)
            if 'GESAMT' in predictions:
                gesamt = predictions['GESAMT']
                print(f"\n🎯 HAUPTSIGNAL: {gesamt['signal']}")
                print(f"💡 EMPFEHLUNG: {gesamt['action']}")
                print(f"📈 Wahrscheinlichkeit: {gesamt['probability']:.1%}")
                print(f"🎪 Konfidenz: {gesamt['confidence']:.1%}")

            # Detaillierte Horizonte
            print(f"\n🔮 DETAILLIERTE SIGNALE:")
            print(f"{'Horizont':<8} | {'Signal':<20} | {'Aktion':<18} | {'Wahrsch.':<10} | {'Konfidenz':<10}")
            print("-" * 75)

            for horizon_key, pred in predictions.items():
                if horizon_key != 'GESAMT':
                    print(f"{horizon_key:<8} | {pred['signal']:<20} | {pred['action']:<18} | "
                          f"{pred['probability']:.1%}{'':>3} | {pred['confidence']:.1%}{'':>3}")

            # Trading-Empfehlung mit Begründung
            if 'GESAMT' in predictions:
                gesamt = predictions['GESAMT']

                print(f"\n💼 TRADING-EMPFEHLUNG:")
                if "STARKER KAUF" in gesamt['signal']:
                    print(f"   🚀 AKTION: SOFORT KAUFEN!")
                    print(f"   📝 Grund: {gesamt['probability']:.1%} Chance auf Gewinn")
                    print(f"   ⚡ Dringlichkeit: HOCH")
                elif "KAUF" in gesamt['signal']:
                    print(f"   🔥 AKTION: KAUFEN")
                    print(f"   📝 Grund: {gesamt['probability']:.1%} Chance auf Gewinn")
                    print(f"   ⚡ Dringlichkeit: MITTEL")
                elif "STARKER VERKAUF" in gesamt['signal']:
                    print(f"   💥 AKTION: SOFORT VERKAUFEN!")
                    print(f"   📝 Grund: {1-gesamt['probability']:.1%} Chance auf Verlust")
                    print(f"   ⚡ Dringlichkeit: HOCH")
                elif "VERKAUF" in gesamt['signal']:
                    print(f"   🔻 AKTION: VERKAUFEN")
                    print(f"   📝 Grund: {1-gesamt['probability']:.1%} Chance auf Verlust")
                    print(f"   ⚡ Dringlichkeit: MITTEL")
                else:
                    print(f"   ⚖️ AKTION: POSITION HALTEN")
                    print(f"   📝 Grund: Unklare Marktrichtung")
                    print(f"   ⚡ Dringlichkeit: NIEDRIG")
        else:
            print("\n❌ Keine Signale verfügbar")

        # System-Status
        print(f"\n⚡ SYSTEM-STATUS:")
        print(f"   🔄 Nächstes Update: {UPDATE_INTERVAL}s")
        print(f"   📊 Horizonte: {PREDICTION_HORIZONS}h")
        print(f"   💻 CPU: {MAX_CORES} Kerne")
        print(f"   🎯 Modelle: Perfekt optimiert")
        print("="*80)

    def create_perfect_visualization(self, prediction_result, df, iteration):
        """Perfekte Trading-Visualisierung"""

        if not prediction_result or not prediction_result['predictions']:
            return

        print("\n📊 Erstelle perfekte Visualisierung...")

        predictions = prediction_result['predictions']
        current_price = prediction_result['price']
        current_time = prediction_result['time']

        # Letzte 48 Stunden für Chart
        recent_df = df.tail(48)
        times = recent_df.index
        prices = recent_df['close']

        plt.figure(figsize=(20, 12))
        plt.suptitle('🚀 PERFECT BITCOIN TRADING TOOL - LIVE DASHBOARD 🚀',
                     fontsize=20, color='white', weight='bold', y=0.98)

        # === HAUPTCHART: Preis + Signale ===
        plt.subplot(2, 3, 1)

        # Preis-Linie
        plt.plot(times, prices, color='white', linewidth=3, label='Bitcoin Preis', alpha=0.9)

        # Moving Averages
        if len(recent_df) > 24:
            sma_6 = recent_df['close'].rolling(window=6).mean()
            sma_24 = recent_df['close'].rolling(window=24).mean()
            plt.plot(times, sma_6, color='#00ff88', linewidth=2, alpha=0.7, label='SMA 6h')
            plt.plot(times, sma_24, color='#ff6b35', linewidth=2, alpha=0.7, label='SMA 24h')

        # Aktueller Signal-Punkt
        if 'GESAMT' in predictions:
            gesamt = predictions['GESAMT']
            if "STARKER KAUF" in gesamt['signal']:
                color, marker, size = '#00ff00', '^', 300
            elif "KAUF" in gesamt['signal']:
                color, marker, size = '#00ff88', '^', 200
            elif "STARKER VERKAUF" in gesamt['signal']:
                color, marker, size = '#ff0000', 'v', 300
            elif "VERKAUF" in gesamt['signal']:
                color, marker, size = '#ff4757', 'v', 200
            else:
                color, marker, size = '#ffa502', 'o', 150

            plt.scatter([current_time], [current_price], color=color, s=size, marker=marker,
                       label=f'Signal: {gesamt["signal"]}', zorder=10, edgecolors='white', linewidth=2)

        plt.title('PERFECT Live Bitcoin Preis + Trading Signal', fontsize=16, color='white', weight='bold')
        plt.xlabel('Zeit', color='white', fontsize=12)
        plt.ylabel('Preis (USD)', color='white', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)

        # === SIGNAL-STÄRKE DASHBOARD ===
        plt.subplot(2, 3, 2)

        horizonte = []
        wahrscheinlichkeiten = []
        konfidenzen = []
        colors = []

        for horizon_key, pred in predictions.items():
            if horizon_key != 'GESAMT':
                horizonte.append(horizon_key)
                wahrscheinlichkeiten.append(pred['probability'])
                konfidenzen.append(pred['confidence'])

                if "STARKER KAUF" in pred['signal']:
                    colors.append('#00ff00')
                elif "KAUF" in pred['signal']:
                    colors.append('#00ff88')
                elif "STARKER VERKAUF" in pred['signal']:
                    colors.append('#ff0000')
                elif "VERKAUF" in pred['signal']:
                    colors.append('#ff4757')
                else:
                    colors.append('#ffa502')

        x_pos = np.arange(len(horizonte))
        bars = plt.bar(x_pos, wahrscheinlichkeiten, color=colors, alpha=0.8, edgecolor='white', linewidth=1)

        # Werte auf Balken
        for bar, prob in zip(bars, wahrscheinlichkeiten):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                    f'{prob:.1%}', ha='center', va='bottom', color='white', fontweight='bold', fontsize=11)

        plt.axhline(y=0.5, color='white', linestyle='--', alpha=0.7, label='50% Linie')
        plt.title('PERFECT Signal-Stärke pro Horizont', fontsize=16, color='white', weight='bold')
        plt.xlabel('Horizont', color='white', fontsize=12)
        plt.ylabel('Kauf-Wahrscheinlichkeit', color='white', fontsize=12)
        plt.xticks(x_pos, horizonte, color='white')
        plt.ylim(0, 1)
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)

        # === KONFIDENZ-METER ===
        plt.subplot(2, 3, 3)

        if 'GESAMT' in predictions:
            gesamt = predictions['GESAMT']
            confidence = gesamt['confidence']

            # Kreisdiagramm für Konfidenz
            sizes = [confidence, 1-confidence]
            colors_pie = ['#00ff88' if confidence > 0.7 else '#ffa502' if confidence > 0.5 else '#ff4757', '#333333']
            labels = [f'Konfidenz\n{confidence:.1%}', f'Unsicherheit\n{1-confidence:.1%}']

            wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors_pie, autopct='',
                                              startangle=90, textprops={'color': 'white', 'fontsize': 12})

            plt.title('PERFECT Konfidenz-Meter', fontsize=16, color='white', weight='bold')

        # === TRADING-EMPFEHLUNG ===
        plt.subplot(2, 3, 4)
        plt.axis('off')

        if 'GESAMT' in predictions:
            gesamt = predictions['GESAMT']

            empfehlung_text = f"""PERFECT TRADING EMPFEHLUNG:

🎯 SIGNAL: {gesamt['signal']}
💡 AKTION: {gesamt['action']}
📈 Wahrscheinlichkeit: {gesamt['probability']:.1%}
🎪 Konfidenz: {gesamt['confidence']:.1%}

💰 Aktueller Preis: ${current_price:,.2f}
🕐 Zeit: {current_time.strftime('%H:%M:%S')}

🔄 Iteration: {iteration}
⚡ Update: Alle 30s"""

            # Hintergrundfarbe je nach Signal
            if "STARKER KAUF" in gesamt['signal']:
                bg_color = '#004d00'
            elif "KAUF" in gesamt['signal']:
                bg_color = '#003300'
            elif "STARKER VERKAUF" in gesamt['signal']:
                bg_color = '#4d0000'
            elif "VERKAUF" in gesamt['signal']:
                bg_color = '#330000'
            else:
                bg_color = '#333300'

            plt.text(0.05, 0.95, empfehlung_text, transform=plt.gca().transAxes,
                    fontsize=13, color='white', verticalalignment='top', fontweight='bold',
                    bbox=dict(boxstyle='round', facecolor=bg_color, alpha=0.9, edgecolor='white'))

        # === MODELL-PERFORMANCE ===
        plt.subplot(2, 3, 5)

        if self.models:
            model_names = []
            accuracies = []

            for horizon_key, horizon_models in self.models.items():
                for model_name, model_data in horizon_models.items():
                    short_name = f"{model_name.split('_')[0]}\n{horizon_key}"
                    model_names.append(short_name)
                    accuracies.append(model_data['accuracy'])

            bars = plt.bar(model_names, accuracies, color=['#00ff88', '#ff6b35'] * 3, alpha=0.8,
                          edgecolor='white', linewidth=1)

            # Werte auf Balken
            for bar, acc in zip(bars, accuracies):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{acc:.1%}', ha='center', va='bottom', color='white', fontweight='bold', fontsize=10)

            plt.title('PERFECT Modell-Performance', fontsize=16, color='white', weight='bold')
            plt.xlabel('Modell', color='white', fontsize=12)
            plt.ylabel('Genauigkeit', color='white', fontsize=12)
            plt.ylim(0, 1.1)
            plt.xticks(rotation=45, color='white', fontsize=10)
            plt.grid(True, alpha=0.3)

        # === PREIS-HISTORIE (Mini-Chart) ===
        plt.subplot(2, 3, 6)

        # Letzte 24 Stunden
        mini_df = df.tail(24)
        mini_times = mini_df.index
        mini_prices = mini_df['close']

        plt.plot(mini_times, mini_prices, color='#00ff88', linewidth=3, alpha=0.9)
        plt.fill_between(mini_times, mini_prices, alpha=0.3, color='#00ff88')

        # Aktueller Punkt
        plt.scatter([current_time], [current_price], color='white', s=100, zorder=10,
                   edgecolors='#00ff88', linewidth=3)

        plt.title('PERFECT 24h Preis-Trend', fontsize=16, color='white', weight='bold')
        plt.xlabel('Zeit', color='white', fontsize=12)
        plt.ylabel('Preis (USD)', color='white', fontsize=12)
        plt.xticks(rotation=45, color='white', fontsize=10)
        plt.grid(True, alpha=0.3)

        plt.tight_layout()

        # Speichern
        timestamp = datetime.now().strftime('%H%M%S')
        filename = f'perfect_trading/perfect_trading_dashboard_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='black', edgecolor='white')
        plt.show()

        print(f"✅ PERFECT Visualisierung gespeichert: {filename}")

def run_perfect_trading_tool():
    """Hauptfunktion - Perfektes Trading-Tool"""

    tool = PerfectTradingTool()
    iteration = 0

    print(f"\n🚀 STARTE PERFECT TRADING TOOL...")
    print(f"🔄 Kontinuierliche perfekte Signale alle {UPDATE_INTERVAL}s")

    while True:
        try:
            iteration += 1
            start_time = time.time()

            print(f"\n{'='*60}")
            print(f"🔄 PERFECT ITERATION {iteration} - {datetime.now().strftime('%H:%M:%S')}")
            print(f"{'='*60}")

            # 1. Bitcoin-Daten sammeln
            print("📊 Sammle Bitcoin-Daten...")
            df, is_real = tool.get_bitcoin_data()

            # 2. Perfekte Modelle trainieren
            print("🤖 Trainiere perfekte Modelle...")
            training_success = tool.train_perfect_models(df)

            if not training_success:
                print("❌ Training fehlgeschlagen - warte...")
                time.sleep(UPDATE_INTERVAL)
                continue

            # 3. Perfekte Signale vorhersagen
            print("🔮 Erstelle perfekte Trading-Signale...")
            prediction_result = tool.predict_perfect_signals(df)

            # 4. Perfektes Dashboard anzeigen
            tool.display_perfect_dashboard(prediction_result)

            # 5. Perfekte Visualisierung (alle 3 Iterationen)
            if iteration % 3 == 0 and prediction_result:
                tool.create_perfect_visualization(prediction_result, df, iteration)

            # 6. Timing
            elapsed_time = time.time() - start_time
            print(f"\n⚡ Perfect Iteration {iteration} in {elapsed_time:.1f}s")

            # 7. Warten
            sleep_time = max(0, UPDATE_INTERVAL - elapsed_time)
            if sleep_time > 0:
                print(f"💤 Warte {sleep_time:.1f}s bis nächste perfekte Iteration...")
                time.sleep(sleep_time)

        except KeyboardInterrupt:
            print(f"\n🛑 PERFECT TRADING TOOL gestoppt")
            break
        except Exception as e:
            print(f"❌ Perfect Fehler: {e}")
            import traceback
            traceback.print_exc()
            time.sleep(UPDATE_INTERVAL)

if __name__ == "__main__":
    run_perfect_trading_tool()
