#!/usr/bin/env python3
"""
🚀 ULTIMATE BITCOIN PREDICTION MASTER SCRIPT 🚀
===============================================
Kombiniert die besten Funktionen aus allen verfügbaren Scripts:
- Hybrid Ensemble Modelle (LSTM, GRU, Transformer)
- Multiple Datenquellen (Binance, CoinGecko, Alpha Vantage)
- Erweiterte technische Indikatoren
- Bayesian Optimization
- Monte Carlo Simulationen
- Ultimate Visualisierung
"""

import os
import sys
import warnings
import time
import logging
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler, RobustScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import TimeSeriesSplit
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import (LSTM, GRU, Dense, Dropout, BatchNormalization, 
                                   Input, Concatenate, Attention, MultiHeadAttention,
                                   LayerNormalization, Bidirectional)
from tensorflow.keras.optimizers import Adam, RMSprop
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
import requests
import yfinance as yf
from pycoingecko import CoinGeckoAPI
import joblib
import json
from scipy import stats
from scipy.optimize import minimize
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

# Konfiguration
warnings.filterwarnings('ignore')
plt.style.use('dark_background')
tf.random.set_seed(42)
np.random.seed(42)

# Logging Setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ULTIMATE KONFIGURATION
ULTIMATE_CONFIG = {
    'data_sources': {
        'yahoo_finance': {'weight': 0.4, 'symbol': 'BTC-USD'},
        'binance': {'weight': 0.3, 'symbol': 'BTCUSDT'},
        'coingecko': {'weight': 0.3, 'coin_id': 'bitcoin'}
    },
    'model_config': {
        'sequence_length': 60,
        'prediction_horizons': [1, 6, 12, 24, 48, 72],
        'ensemble_models': ['lstm_bidirectional', 'gru_attention', 'transformer', 'hybrid_cnn_lstm'],
        'optimization_method': 'bayesian',
        'cross_validation_folds': 5
    },
    'training_config': {
        'epochs': 100,
        'batch_size': 32,
        'validation_split': 0.2,
        'early_stopping_patience': 15,
        'learning_rate_reduction_patience': 10
    },
    'feature_engineering': {
        'technical_indicators': True,
        'fourier_features': True,
        'lag_features': True,
        'rolling_statistics': True,
        'volatility_features': True
    },
    'visualization': {
        'save_plots': True,
        'interactive_plots': True,
        'plot_format': 'png',
        'dpi': 300
    },
    'output_dirs': {
        'models': 'ultimate_models',
        'plots': 'ultimate_plots',
        'data': 'ultimate_data',
        'results': 'ultimate_results'
    }
}

class UltimateDataFetcher:
    """Ultimate Datensammler mit mehreren Quellen"""
    
    def __init__(self):
        self.cg = CoinGeckoAPI()
        self.setup_directories()
    
    def setup_directories(self):
        """Erstelle notwendige Verzeichnisse"""
        for dir_name in ULTIMATE_CONFIG['output_dirs'].values():
            os.makedirs(dir_name, exist_ok=True)
    
    def fetch_yahoo_data(self, period='1y', interval='1h'):
        """Yahoo Finance Daten abrufen"""
        try:
            logger.info("📊 Lade Yahoo Finance Daten...")
            symbol = ULTIMATE_CONFIG['data_sources']['yahoo_finance']['symbol']
            ticker = yf.Ticker(symbol)
            df = ticker.history(period=period, interval=interval)
            df.columns = [col.lower() for col in df.columns]
            df['source'] = 'yahoo'
            logger.info(f"✅ Yahoo Finance: {len(df)} Datenpunkte geladen")
            return df
        except Exception as e:
            logger.error(f"❌ Yahoo Finance Fehler: {e}")
            return pd.DataFrame()
    
    def fetch_binance_data(self, limit=1000):
        """Binance Daten abrufen"""
        try:
            logger.info("📊 Lade Binance Daten...")
            symbol = ULTIMATE_CONFIG['data_sources']['binance']['symbol']
            url = f"https://api.binance.com/api/v3/klines"
            params = {
                'symbol': symbol,
                'interval': '1h',
                'limit': limit
            }
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            df = pd.DataFrame(data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            df = df[['open', 'high', 'low', 'close', 'volume']].astype(float)
            df['source'] = 'binance'
            logger.info(f"✅ Binance: {len(df)} Datenpunkte geladen")
            return df
        except Exception as e:
            logger.error(f"❌ Binance Fehler: {e}")
            return pd.DataFrame()
    
    def fetch_coingecko_data(self, days=30):
        """CoinGecko Daten abrufen"""
        try:
            logger.info("📊 Lade CoinGecko Daten...")
            coin_id = ULTIMATE_CONFIG['data_sources']['coingecko']['coin_id']
            data = self.cg.get_coin_market_chart_by_id(
                id=coin_id, vs_currency='usd', days=days, interval='hourly'
            )
            
            df = pd.DataFrame(data['prices'], columns=['timestamp', 'close'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Volume und andere Daten hinzufügen
            volume_df = pd.DataFrame(data['total_volumes'], columns=['timestamp', 'volume'])
            volume_df['timestamp'] = pd.to_datetime(volume_df['timestamp'], unit='ms')
            volume_df.set_index('timestamp', inplace=True)
            
            df = df.join(volume_df, how='left')
            df['source'] = 'coingecko'
            logger.info(f"✅ CoinGecko: {len(df)} Datenpunkte geladen")
            return df
        except Exception as e:
            logger.error(f"❌ CoinGecko Fehler: {e}")
            return pd.DataFrame()
    
    def combine_data_sources(self):
        """Kombiniere alle Datenquellen intelligent"""
        logger.info("🔄 Kombiniere Datenquellen...")
        
        # Alle Datenquellen laden
        yahoo_df = self.fetch_yahoo_data()
        binance_df = self.fetch_binance_data()
        coingecko_df = self.fetch_coingecko_data()
        
        dataframes = []
        weights = []
        
        if not yahoo_df.empty:
            dataframes.append(yahoo_df)
            weights.append(ULTIMATE_CONFIG['data_sources']['yahoo_finance']['weight'])
        
        if not binance_df.empty:
            dataframes.append(binance_df)
            weights.append(ULTIMATE_CONFIG['data_sources']['binance']['weight'])
        
        if not coingecko_df.empty:
            dataframes.append(coingecko_df)
            weights.append(ULTIMATE_CONFIG['data_sources']['coingecko']['weight'])
        
        if not dataframes:
            logger.error("❌ Keine Daten verfügbar!")
            return pd.DataFrame()
        
        # Gewichtete Kombination
        combined_df = self._weighted_combine(dataframes, weights)
        logger.info(f"✅ Kombinierte Daten: {len(combined_df)} Datenpunkte")
        
        return combined_df
    
    def _weighted_combine(self, dataframes, weights):
        """Gewichtete Kombination der Datenquellen"""
        # Gemeinsamen Zeitbereich finden
        common_index = dataframes[0].index
        for df in dataframes[1:]:
            common_index = common_index.intersection(df.index)
        
        if len(common_index) == 0:
            logger.warning("⚠️ Keine gemeinsamen Zeitpunkte gefunden, verwende ersten Datensatz")
            return dataframes[0]
        
        # Gewichtete Durchschnitte berechnen
        combined_data = {}
        
        for column in ['close', 'volume']:
            if column in dataframes[0].columns:
                weighted_values = np.zeros(len(common_index))
                total_weight = 0
                
                for df, weight in zip(dataframes, weights):
                    if column in df.columns:
                        df_reindexed = df.reindex(common_index)
                        valid_mask = ~df_reindexed[column].isna()
                        weighted_values[valid_mask] += df_reindexed[column][valid_mask] * weight
                        total_weight += weight
                
                combined_data[column] = weighted_values / total_weight if total_weight > 0 else weighted_values
        
        combined_df = pd.DataFrame(combined_data, index=common_index)
        
        # Zusätzliche Spalten aus dem ersten DataFrame
        for column in ['open', 'high', 'low']:
            if column in dataframes[0].columns:
                combined_df[column] = dataframes[0].reindex(common_index)[column]
        
        return combined_df.dropna()

class UltimateTechnicalIndicators:
    """Ultimate technische Indikatoren"""
    
    @staticmethod
    def add_all_indicators(df):
        """Alle technischen Indikatoren hinzufügen"""
        df = df.copy()
        
        # Trend-Indikatoren
        df = UltimateTechnicalIndicators.add_moving_averages(df)
        df = UltimateTechnicalIndicators.add_macd(df)
        df = UltimateTechnicalIndicators.add_adx(df)
        
        # Momentum-Indikatoren
        df = UltimateTechnicalIndicators.add_rsi(df)
        df = UltimateTechnicalIndicators.add_stochastic(df)
        df = UltimateTechnicalIndicators.add_williams_r(df)
        
        # Volatilitäts-Indikatoren
        df = UltimateTechnicalIndicators.add_bollinger_bands(df)
        df = UltimateTechnicalIndicators.add_atr(df)
        
        # Volumen-Indikatoren
        if 'volume' in df.columns:
            df = UltimateTechnicalIndicators.add_volume_indicators(df)
        
        # Erweiterte Features
        df = UltimateTechnicalIndicators.add_fourier_features(df)
        df = UltimateTechnicalIndicators.add_lag_features(df)
        df = UltimateTechnicalIndicators.add_rolling_statistics(df)
        
        return df.dropna()

    @staticmethod
    def add_moving_averages(df):
        """Moving Averages hinzufügen"""
        df['sma_7'] = df['close'].rolling(window=7).mean()
        df['sma_21'] = df['close'].rolling(window=21).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        df['sma_200'] = df['close'].rolling(window=200).mean()

        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()
        df['ema_50'] = df['close'].ewm(span=50).mean()

        return df

    @staticmethod
    def add_macd(df):
        """MACD Indikator"""
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        return df

    @staticmethod
    def add_rsi(df, period=14):
        """RSI Indikator"""
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=period).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        return df

    @staticmethod
    def add_bollinger_bands(df, period=20, std_dev=2):
        """Bollinger Bands"""
        df['bb_middle'] = df['close'].rolling(window=period).mean()
        std = df['close'].rolling(window=period).std()
        df['bb_upper'] = df['bb_middle'] + (std * std_dev)
        df['bb_lower'] = df['bb_middle'] - (std * std_dev)
        df['bb_width'] = df['bb_upper'] - df['bb_lower']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        return df

    @staticmethod
    def add_atr(df, period=14):
        """Average True Range"""
        if 'high' in df.columns and 'low' in df.columns:
            high_low = df['high'] - df['low']
            high_close = np.abs(df['high'] - df['close'].shift())
            low_close = np.abs(df['low'] - df['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            df['atr'] = true_range.rolling(window=period).mean()
        return df

    @staticmethod
    def add_stochastic(df, k_period=14, d_period=3):
        """Stochastic Oscillator"""
        if 'high' in df.columns and 'low' in df.columns:
            lowest_low = df['low'].rolling(window=k_period).min()
            highest_high = df['high'].rolling(window=k_period).max()
            df['stoch_k'] = 100 * (df['close'] - lowest_low) / (highest_high - lowest_low)
            df['stoch_d'] = df['stoch_k'].rolling(window=d_period).mean()
        return df

    @staticmethod
    def add_williams_r(df, period=14):
        """Williams %R"""
        if 'high' in df.columns and 'low' in df.columns:
            highest_high = df['high'].rolling(window=period).max()
            lowest_low = df['low'].rolling(window=period).min()
            df['williams_r'] = -100 * (highest_high - df['close']) / (highest_high - lowest_low)
        return df

    @staticmethod
    def add_adx(df, period=14):
        """Average Directional Index"""
        if 'high' in df.columns and 'low' in df.columns:
            plus_dm = df['high'].diff()
            minus_dm = df['low'].diff()
            plus_dm[plus_dm < 0] = 0
            minus_dm[minus_dm > 0] = 0
            minus_dm = minus_dm.abs()

            tr = UltimateTechnicalIndicators._true_range(df)
            plus_di = 100 * (plus_dm.rolling(window=period).mean() / tr.rolling(window=period).mean())
            minus_di = 100 * (minus_dm.rolling(window=period).mean() / tr.rolling(window=period).mean())

            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df['adx'] = dx.rolling(window=period).mean()
        return df

    @staticmethod
    def add_volume_indicators(df):
        """Volumen-Indikatoren"""
        # On-Balance Volume
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()

        # Volume-Weighted Average Price
        df['vwap'] = (df['close'] * df['volume']).cumsum() / df['volume'].cumsum()

        # Volume Rate of Change
        df['volume_roc'] = df['volume'].pct_change(periods=10)

        return df

    @staticmethod
    def add_fourier_features(df, n_components=5):
        """Fourier-Features für Zyklen"""
        close_fft = np.fft.fft(df['close'].fillna(method='ffill'))
        # frequencies = np.fft.fftfreq(len(close_fft))  # Nicht verwendet

        for i in range(1, n_components + 1):
            df[f'fourier_real_{i}'] = np.real(close_fft)[i]
            df[f'fourier_imag_{i}'] = np.imag(close_fft)[i]

        return df

    @staticmethod
    def add_lag_features(df, lags=[1, 2, 3, 6, 12, 24]):
        """Lag-Features"""
        for lag in lags:
            df[f'close_lag_{lag}'] = df['close'].shift(lag)
            df[f'volume_lag_{lag}'] = df['volume'].shift(lag) if 'volume' in df.columns else 0
        return df

    @staticmethod
    def add_rolling_statistics(df, windows=[6, 12, 24]):
        """Rolling Statistics"""
        for window in windows:
            df[f'close_mean_{window}'] = df['close'].rolling(window=window).mean()
            df[f'close_std_{window}'] = df['close'].rolling(window=window).std()
            df[f'close_min_{window}'] = df['close'].rolling(window=window).min()
            df[f'close_max_{window}'] = df['close'].rolling(window=window).max()
            df[f'close_skew_{window}'] = df['close'].rolling(window=window).skew()
        return df

    @staticmethod
    def _true_range(df):
        """True Range Berechnung"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        return np.maximum(high_low, np.maximum(high_close, low_close))

class UltimateModelBuilder:
    """Ultimate Modell-Architekturen"""

    def __init__(self):
        self.models = {}
        self.model_history = {}

    def build_lstm_bidirectional(self, input_shape):
        """Bidirectional LSTM Modell"""
        model = Sequential([
            Bidirectional(LSTM(128, return_sequences=True, dropout=0.2), input_shape=input_shape),
            BatchNormalization(),
            Bidirectional(LSTM(64, return_sequences=True, dropout=0.2)),
            BatchNormalization(),
            LSTM(32, return_sequences=False, dropout=0.2),
            Dense(64, activation='relu'),
            Dropout(0.3),
            Dense(32, activation='relu'),
            Dropout(0.2),
            Dense(1)
        ])

        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        return model

    def build_gru_attention(self, input_shape):
        """GRU mit Attention Mechanismus"""
        inputs = Input(shape=input_shape)

        # GRU Layers
        gru1 = GRU(128, return_sequences=True, dropout=0.2)(inputs)
        gru1_norm = BatchNormalization()(gru1)

        gru2 = GRU(64, return_sequences=True, dropout=0.2)(gru1_norm)
        gru2_norm = BatchNormalization()(gru2)

        # Attention Layer
        attention = MultiHeadAttention(num_heads=8, key_dim=64)(gru2_norm, gru2_norm)
        attention_norm = LayerNormalization()(attention)

        # Final layers
        flatten = tf.keras.layers.GlobalAveragePooling1D()(attention_norm)
        dense1 = Dense(64, activation='relu')(flatten)
        dropout1 = Dropout(0.3)(dense1)
        dense2 = Dense(32, activation='relu')(dropout1)
        dropout2 = Dropout(0.2)(dense2)
        output = Dense(1)(dropout2)

        model = Model(inputs=inputs, outputs=output)
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        return model

    def build_transformer_model(self, input_shape):
        """Transformer Modell"""
        inputs = Input(shape=input_shape)

        # Positional Encoding
        x = Dense(input_shape[-1])(inputs)

        # Multi-Head Attention Blocks
        for _ in range(3):
            # Attention
            attention = MultiHeadAttention(num_heads=8, key_dim=64)(x, x)
            attention = Dropout(0.1)(attention)
            x = LayerNormalization()(x + attention)

            # Feed Forward
            ff = Dense(256, activation='relu')(x)
            ff = Dense(input_shape[-1])(ff)
            ff = Dropout(0.1)(ff)
            x = LayerNormalization()(x + ff)

        # Global pooling and output
        x = tf.keras.layers.GlobalAveragePooling1D()(x)
        x = Dense(128, activation='relu')(x)
        x = Dropout(0.3)(x)
        x = Dense(64, activation='relu')(x)
        x = Dropout(0.2)(x)
        output = Dense(1)(x)

        model = Model(inputs=inputs, outputs=output)
        model.compile(
            optimizer=Adam(learning_rate=0.0005),
            loss='mse',
            metrics=['mae']
        )
        return model

    def build_hybrid_cnn_lstm(self, input_shape):
        """Hybrid CNN-LSTM Modell"""
        inputs = Input(shape=input_shape)

        # CNN Branch
        cnn = tf.keras.layers.Conv1D(filters=64, kernel_size=3, activation='relu')(inputs)
        cnn = tf.keras.layers.Conv1D(filters=64, kernel_size=3, activation='relu')(cnn)
        cnn = tf.keras.layers.MaxPooling1D(pool_size=2)(cnn)
        cnn = Dropout(0.2)(cnn)

        # LSTM Branch
        lstm = LSTM(128, return_sequences=True, dropout=0.2)(inputs)
        lstm = LSTM(64, return_sequences=True, dropout=0.2)(lstm)

        # Combine branches
        combined = Concatenate()([cnn, lstm])
        combined = BatchNormalization()(combined)

        # Final LSTM
        final_lstm = LSTM(32, return_sequences=False, dropout=0.2)(combined)

        # Dense layers
        dense1 = Dense(64, activation='relu')(final_lstm)
        dropout1 = Dropout(0.3)(dense1)
        dense2 = Dense(32, activation='relu')(dropout1)
        dropout2 = Dropout(0.2)(dense2)
        output = Dense(1)(dropout2)

        model = Model(inputs=inputs, outputs=output)
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        return model

    def build_ensemble_model(self, input_shape):
        """Ensemble aller Modelle"""
        # Einzelmodelle erstellen
        lstm_model = self.build_lstm_bidirectional(input_shape)
        gru_model = self.build_gru_attention(input_shape)
        transformer_model = self.build_transformer_model(input_shape)
        hybrid_model = self.build_hybrid_cnn_lstm(input_shape)

        # Input
        inputs = Input(shape=input_shape)

        # Vorhersagen der Einzelmodelle
        lstm_pred = lstm_model(inputs)
        gru_pred = gru_model(inputs)
        transformer_pred = transformer_model(inputs)
        hybrid_pred = hybrid_model(inputs)

        # Ensemble Layer
        ensemble_input = Concatenate()([lstm_pred, gru_pred, transformer_pred, hybrid_pred])
        ensemble_dense = Dense(16, activation='relu')(ensemble_input)
        ensemble_dropout = Dropout(0.2)(ensemble_dense)
        ensemble_output = Dense(1)(ensemble_dropout)

        ensemble_model = Model(inputs=inputs, outputs=ensemble_output)
        ensemble_model.compile(
            optimizer=Adam(learning_rate=0.0005),
            loss='mse',
            metrics=['mae']
        )

        return ensemble_model

class UltimateTrainer:
    """Ultimate Training und Optimierung"""

    def __init__(self):
        self.model_builder = UltimateModelBuilder()
        self.scalers = {}
        self.best_models = {}
        self.training_history = {}

    def prepare_data(self, df, sequence_length=60, target_column='close'):
        """Daten für Training vorbereiten"""
        logger.info("🔄 Bereite Trainingsdaten vor...")

        # Features und Target trennen
        feature_columns = [col for col in df.columns if col != target_column]
        features = df[feature_columns].values
        target = df[target_column].values

        # Skalierung
        feature_scaler = RobustScaler()
        target_scaler = MinMaxScaler()

        features_scaled = feature_scaler.fit_transform(features)
        target_scaled = target_scaler.fit_transform(target.reshape(-1, 1)).flatten()

        self.scalers['feature'] = feature_scaler
        self.scalers['target'] = target_scaler

        # Sequenzen erstellen
        X, y = [], []
        for i in range(sequence_length, len(features_scaled)):
            X.append(features_scaled[i-sequence_length:i])
            y.append(target_scaled[i])

        X, y = np.array(X), np.array(y)

        # Train/Validation/Test Split
        train_size = int(len(X) * 0.7)
        val_size = int(len(X) * 0.15)

        X_train = X[:train_size]
        y_train = y[:train_size]
        X_val = X[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        X_test = X[train_size+val_size:]
        y_test = y[train_size+val_size:]

        logger.info(f"✅ Daten vorbereitet: Train={len(X_train)}, Val={len(X_val)}, Test={len(X_test)}")

        return (X_train, y_train), (X_val, y_val), (X_test, y_test)

    def train_model(self, model_type, X_train, y_train, X_val, y_val):
        """Einzelnes Modell trainieren"""
        logger.info(f"🚀 Trainiere {model_type} Modell...")

        input_shape = (X_train.shape[1], X_train.shape[2])

        # Modell erstellen
        if model_type == 'lstm_bidirectional':
            model = self.model_builder.build_lstm_bidirectional(input_shape)
        elif model_type == 'gru_attention':
            model = self.model_builder.build_gru_attention(input_shape)
        elif model_type == 'transformer':
            model = self.model_builder.build_transformer_model(input_shape)
        elif model_type == 'hybrid_cnn_lstm':
            model = self.model_builder.build_hybrid_cnn_lstm(input_shape)
        elif model_type == 'ensemble':
            model = self.model_builder.build_ensemble_model(input_shape)
        else:
            raise ValueError(f"Unbekannter Modelltyp: {model_type}")

        # Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=ULTIMATE_CONFIG['training_config']['early_stopping_patience'],
                restore_best_weights=True
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=ULTIMATE_CONFIG['training_config']['learning_rate_reduction_patience'],
                min_lr=1e-7
            )
        ]

        # Training
        start_time = time.time()
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=ULTIMATE_CONFIG['training_config']['epochs'],
            batch_size=ULTIMATE_CONFIG['training_config']['batch_size'],
            callbacks=callbacks,
            verbose=0
        )
        training_time = time.time() - start_time

        # Modell speichern
        model_path = os.path.join(ULTIMATE_CONFIG['output_dirs']['models'], f'{model_type}_model.h5')
        model.save(model_path)

        self.best_models[model_type] = model
        self.training_history[model_type] = {
            'history': history.history,
            'training_time': training_time,
            'model_path': model_path
        }

        logger.info(f"✅ {model_type} Training abgeschlossen in {training_time:.1f}s")

        return model, history

    def train_all_models(self, train_data, val_data):
        """Alle Modelle trainieren"""
        X_train, y_train = train_data
        X_val, y_val = val_data

        models = {}

        for model_type in ULTIMATE_CONFIG['model_config']['ensemble_models']:
            try:
                model, history = self.train_model(model_type, X_train, y_train, X_val, y_val)
                models[model_type] = model
            except Exception as e:
                logger.error(f"❌ Fehler beim Training von {model_type}: {e}")
                continue

        return models

    def evaluate_models(self, models, test_data):
        """Modelle evaluieren"""
        X_test, y_test = test_data
        results = {}

        for model_name, model in models.items():
            logger.info(f"📊 Evaluiere {model_name}...")

            # Vorhersagen
            y_pred = model.predict(X_test, verbose=0)

            # Metriken berechnen
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_test, y_pred)

            # Zurück transformieren für bessere Interpretation
            y_test_orig = self.scalers['target'].inverse_transform(y_test.reshape(-1, 1)).flatten()
            y_pred_orig = self.scalers['target'].inverse_transform(y_pred.reshape(-1, 1)).flatten()

            mse_orig = mean_squared_error(y_test_orig, y_pred_orig)
            mae_orig = mean_absolute_error(y_test_orig, y_pred_orig)
            rmse_orig = np.sqrt(mse_orig)

            results[model_name] = {
                'mse': mse,
                'mae': mae,
                'rmse': rmse,
                'r2': r2,
                'mse_orig': mse_orig,
                'mae_orig': mae_orig,
                'rmse_orig': rmse_orig,
                'y_test': y_test_orig,
                'y_pred': y_pred_orig
            }

            logger.info(f"✅ {model_name}: RMSE=${rmse_orig:.2f}, MAE=${mae_orig:.2f}, R²={r2:.4f}")

        return results

class UltimatePredictor:
    """Ultimate Vorhersage-System"""

    def __init__(self, models, scalers):
        self.models = models
        self.scalers = scalers

    def predict_future(self, last_sequence, horizons=[1, 6, 12, 24, 48, 72], n_simulations=1000):
        """Monte Carlo Zukunftsprognose"""
        logger.info("🔮 Erstelle Zukunftsprognosen...")

        predictions = {}

        for horizon in horizons:
            logger.info(f"📈 Prognose für {horizon}h...")

            # Ensemble Vorhersagen
            ensemble_predictions = []

            for model_name, model in self.models.items():
                model_predictions = []

                # Monte Carlo Simulationen
                for _ in range(n_simulations):
                    # Noise hinzufügen für Unsicherheit
                    noisy_sequence = last_sequence + np.random.normal(0, 0.01, last_sequence.shape)

                    # Iterative Vorhersage
                    current_sequence = noisy_sequence.copy()

                    for step in range(horizon):
                        pred = model.predict(current_sequence.reshape(1, *current_sequence.shape), verbose=0)[0, 0]

                        # Sequence aktualisieren
                        new_row = current_sequence[-1].copy()
                        new_row[0] = pred  # Annahme: erste Spalte ist der Preis

                        current_sequence = np.vstack([current_sequence[1:], new_row])

                    # Finale Vorhersage
                    final_pred = model.predict(current_sequence.reshape(1, *current_sequence.shape), verbose=0)[0, 0]
                    model_predictions.append(final_pred)

                ensemble_predictions.append(model_predictions)

            # Ensemble-Statistiken
            ensemble_predictions = np.array(ensemble_predictions)
            mean_predictions = np.mean(ensemble_predictions, axis=0)

            # Zurück transformieren
            mean_pred_orig = self.scalers['target'].inverse_transform(mean_predictions.reshape(-1, 1)).flatten()

            predictions[horizon] = {
                'mean': np.mean(mean_pred_orig),
                'std': np.std(mean_pred_orig),
                'median': np.median(mean_pred_orig),
                'q25': np.percentile(mean_pred_orig, 25),
                'q75': np.percentile(mean_pred_orig, 75),
                'q5': np.percentile(mean_pred_orig, 5),
                'q95': np.percentile(mean_pred_orig, 95),
                'all_predictions': mean_pred_orig
            }

        return predictions

class UltimateVisualizer:
    """Ultimate Visualisierung"""

    def __init__(self):
        self.setup_style()

    def setup_style(self):
        """Visualisierungsstil einrichten"""
        plt.style.use('dark_background')
        sns.set_palette("husl")

    def plot_ultimate_dashboard(self, df, results, predictions, save_path=None):
        """Ultimate Dashboard erstellen"""
        logger.info("📊 Erstelle Ultimate Dashboard...")

        # Plotly Dashboard
        fig = make_subplots(
            rows=4, cols=3,
            subplot_titles=[
                'Bitcoin Preisverlauf', 'Modell-Performance', 'Zukunftsprognosen',
                'Technische Indikatoren', 'Volatilität', 'Volumen-Analyse',
                'Korrelationsmatrix', 'Feature Importance', 'Residuen-Analyse',
                'Monte Carlo Simulation', 'Risiko-Analyse', 'Trading Signale'
            ],
            specs=[
                [{"secondary_y": True}, {"type": "bar"}, {"secondary_y": True}],
                [{"secondary_y": True}, {"secondary_y": True}, {"secondary_y": True}],
                [{"type": "heatmap"}, {"type": "bar"}, {"type": "scatter"}],
                [{"secondary_y": True}, {"type": "bar"}, {"secondary_y": True}]
            ]
        )

        # 1. Bitcoin Preisverlauf
        fig.add_trace(
            go.Scatter(
                x=df.index[-500:],
                y=df['close'].iloc[-500:],
                mode='lines',
                name='BTC Preis',
                line=dict(color='orange', width=2)
            ),
            row=1, col=1
        )

        # 2. Modell-Performance
        model_names = list(results.keys())
        r2_scores = [results[name]['r2'] for name in model_names]

        fig.add_trace(
            go.Bar(
                x=model_names,
                y=r2_scores,
                name='R² Score',
                marker_color='lightblue'
            ),
            row=1, col=2
        )

        # 3. Zukunftsprognosen
        horizons = list(predictions.keys())
        mean_predictions = [predictions[h]['mean'] for h in horizons]
        upper_bounds = [predictions[h]['q95'] for h in horizons]
        lower_bounds = [predictions[h]['q5'] for h in horizons]

        fig.add_trace(
            go.Scatter(
                x=horizons,
                y=mean_predictions,
                mode='lines+markers',
                name='Prognose',
                line=dict(color='green', width=3)
            ),
            row=1, col=3
        )

        fig.add_trace(
            go.Scatter(
                x=horizons + horizons[::-1],
                y=upper_bounds + lower_bounds[::-1],
                fill='toself',
                fillcolor='rgba(0,255,0,0.2)',
                line=dict(color='rgba(255,255,255,0)'),
                name='Konfidenzintervall'
            ),
            row=1, col=3
        )

        # 4. Technische Indikatoren
        if 'rsi' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index[-200:],
                    y=df['rsi'].iloc[-200:],
                    mode='lines',
                    name='RSI',
                    line=dict(color='purple')
                ),
                row=2, col=1
            )

        # 5. Volatilität
        if 'atr' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index[-200:],
                    y=df['atr'].iloc[-200:],
                    mode='lines',
                    name='ATR',
                    line=dict(color='red')
                ),
                row=2, col=2
            )

        # Layout anpassen
        fig.update_layout(
            title="🚀 Ultimate Bitcoin Prediction Dashboard",
            height=1200,
            showlegend=True,
            template="plotly_dark"
        )

        # Speichern und anzeigen
        if save_path:
            fig.write_html(save_path)

        fig.show()

        return fig

    def plot_model_comparison(self, results, save_path=None):
        """Modell-Vergleich visualisieren"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('🔥 Ultimate Model Comparison', fontsize=16, color='white')

        model_names = list(results.keys())

        # R² Scores
        r2_scores = [results[name]['r2'] for name in model_names]
        axes[0, 0].bar(model_names, r2_scores, color='lightblue', alpha=0.8)
        axes[0, 0].set_title('R² Scores', color='white')
        axes[0, 0].set_ylabel('R² Score', color='white')
        axes[0, 0].tick_params(colors='white')

        # RMSE
        rmse_scores = [results[name]['rmse_orig'] for name in model_names]
        axes[0, 1].bar(model_names, rmse_scores, color='lightcoral', alpha=0.8)
        axes[0, 1].set_title('RMSE (USD)', color='white')
        axes[0, 1].set_ylabel('RMSE', color='white')
        axes[0, 1].tick_params(colors='white')

        # Vorhersage vs. Tatsächlich (bestes Modell)
        best_model = max(results.keys(), key=lambda x: results[x]['r2'])
        axes[1, 0].scatter(results[best_model]['y_test'][-100:],
                          results[best_model]['y_pred'][-100:],
                          alpha=0.6, color='orange')
        axes[1, 0].plot([results[best_model]['y_test'].min(), results[best_model]['y_test'].max()],
                       [results[best_model]['y_test'].min(), results[best_model]['y_test'].max()],
                       'r--', lw=2)
        axes[1, 0].set_title(f'Prediction vs Actual ({best_model})', color='white')
        axes[1, 0].set_xlabel('Actual', color='white')
        axes[1, 0].set_ylabel('Predicted', color='white')
        axes[1, 0].tick_params(colors='white')

        # Residuen
        residuals = results[best_model]['y_test'] - results[best_model]['y_pred']
        axes[1, 1].hist(residuals, bins=30, alpha=0.7, color='lightgreen')
        axes[1, 1].set_title('Residuals Distribution', color='white')
        axes[1, 1].set_xlabel('Residuals', color='white')
        axes[1, 1].set_ylabel('Frequency', color='white')
        axes[1, 1].tick_params(colors='white')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, facecolor='black', dpi=300)

        plt.show()

        return fig

class UltimateBitcoinPredictor:
    """🚀 ULTIMATE BITCOIN PREDICTION SYSTEM 🚀"""

    def __init__(self):
        self.data_fetcher = UltimateDataFetcher()
        self.trainer = UltimateTrainer()
        self.visualizer = UltimateVisualizer()
        self.models = {}
        self.results = {}
        self.predictions = {}

        logger.info("🚀 Ultimate Bitcoin Predictor initialisiert!")

    def run_complete_analysis(self):
        """Komplette Analyse durchführen"""
        start_time = time.time()

        try:
            # 1. Daten laden und vorbereiten
            logger.info("\n" + "="*60)
            logger.info("PHASE 1: DATENSAMMLUNG UND VORBEREITUNG")
            logger.info("="*60)

            # Daten von allen Quellen laden
            df = self.data_fetcher.combine_data_sources()

            if df.empty:
                logger.error("❌ Keine Daten verfügbar!")
                return None

            # Fallback auf lokale CSV-Datei
            if len(df) < 1000:
                logger.warning("⚠️ Zu wenig API-Daten, verwende lokale CSV...")
                try:
                    df = pd.read_csv('crypto_data.csv', index_col=0, parse_dates=True)
                    logger.info(f"✅ Lokale Daten geladen: {len(df)} Datenpunkte")
                except:
                    logger.error("❌ Auch lokale Daten nicht verfügbar!")
                    return None

            # 2. Feature Engineering
            logger.info("\n" + "="*60)
            logger.info("PHASE 2: FEATURE ENGINEERING")
            logger.info("="*60)

            df_features = UltimateTechnicalIndicators.add_all_indicators(df)
            logger.info(f"✅ Features erstellt: {df_features.shape[1]} Spalten")

            # 3. Daten für Training vorbereiten
            logger.info("\n" + "="*60)
            logger.info("PHASE 3: DATENAUFBEREITUNG")
            logger.info("="*60)

            train_data, val_data, test_data = self.trainer.prepare_data(
                df_features,
                sequence_length=ULTIMATE_CONFIG['model_config']['sequence_length']
            )

            # 4. Modelle trainieren
            logger.info("\n" + "="*60)
            logger.info("PHASE 4: MODELL-TRAINING")
            logger.info("="*60)

            self.models = self.trainer.train_all_models(train_data, val_data)

            if not self.models:
                logger.error("❌ Keine Modelle erfolgreich trainiert!")
                return None

            # 5. Modelle evaluieren
            logger.info("\n" + "="*60)
            logger.info("PHASE 5: MODELL-EVALUATION")
            logger.info("="*60)

            self.results = self.trainer.evaluate_models(self.models, test_data)

            # 6. Zukunftsprognosen
            logger.info("\n" + "="*60)
            logger.info("PHASE 6: ZUKUNFTSPROGNOSEN")
            logger.info("="*60)

            # Letztes Sequence für Vorhersagen
            X_test, y_test = test_data
            last_sequence = X_test[-1]

            predictor = UltimatePredictor(self.models, self.trainer.scalers)
            self.predictions = predictor.predict_future(
                last_sequence,
                horizons=ULTIMATE_CONFIG['model_config']['prediction_horizons']
            )

            # 7. Visualisierung
            logger.info("\n" + "="*60)
            logger.info("PHASE 7: ULTIMATE VISUALISIERUNG")
            logger.info("="*60)

            # Dashboard erstellen
            dashboard_path = os.path.join(ULTIMATE_CONFIG['output_dirs']['plots'], 'ultimate_dashboard.html')
            self.visualizer.plot_ultimate_dashboard(
                df_features, self.results, self.predictions, dashboard_path
            )

            # Modell-Vergleich
            comparison_path = os.path.join(ULTIMATE_CONFIG['output_dirs']['plots'], 'model_comparison.png')
            self.visualizer.plot_model_comparison(self.results, comparison_path)

            # 8. Ergebnisse zusammenfassen
            total_time = time.time() - start_time
            self.print_ultimate_summary(total_time)

            # 9. Ergebnisse speichern
            self.save_results()

            logger.info(f"\n🎉 ULTIMATE ANALYSE ABGESCHLOSSEN in {total_time:.1f}s! 🎉")

            return {
                'models': self.models,
                'results': self.results,
                'predictions': self.predictions,
                'data': df_features
            }

        except Exception as e:
            logger.error(f"❌ Fehler in der Ultimate Analyse: {e}")
            import traceback
            traceback.print_exc()
            return None

    def print_ultimate_summary(self, total_time):
        """Ultimate Zusammenfassung ausgeben"""
        print("\n" + "="*80)
        print("🚀 ULTIMATE BITCOIN PREDICTION RESULTS 🚀")
        print("="*80)

        # Beste Modelle
        best_model = max(self.results.keys(), key=lambda x: self.results[x]['r2'])
        print(f"\n🏆 BESTES MODELL: {best_model}")
        print(f"   R² Score: {self.results[best_model]['r2']:.4f}")
        print(f"   RMSE: ${self.results[best_model]['rmse_orig']:.2f}")
        print(f"   MAE: ${self.results[best_model]['mae_orig']:.2f}")

        # Alle Modell-Performances
        print(f"\n📊 ALLE MODELL-PERFORMANCES:")
        for model_name, result in self.results.items():
            print(f"   {model_name:20} | R²: {result['r2']:.4f} | RMSE: ${result['rmse_orig']:8.2f} | MAE: ${result['mae_orig']:8.2f}")

        # Zukunftsprognosen
        print(f"\n🔮 ZUKUNFTSPROGNOSEN:")
        current_price = list(self.results.values())[0]['y_test'][-1]
        print(f"   Aktueller Preis: ${current_price:.2f}")

        for horizon, pred in self.predictions.items():
            change_pct = ((pred['mean'] / current_price) - 1) * 100
            direction = "📈" if change_pct > 0 else "📉"
            print(f"   {horizon:2}h Prognose: ${pred['mean']:8.2f} ({change_pct:+6.2f}%) {direction}")
            print(f"       Konfidenz: ${pred['q5']:8.2f} - ${pred['q95']:8.2f}")

        # Risiko-Analyse
        print(f"\n⚠️ RISIKO-ANALYSE:")
        volatility_24h = self.predictions[24]['std']
        volatility_72h = self.predictions[72]['std']
        print(f"   24h Volatilität: ${volatility_24h:.2f}")
        print(f"   72h Volatilität: ${volatility_72h:.2f}")

        # Performance
        print(f"\n⚡ PERFORMANCE:")
        print(f"   Gesamtzeit: {total_time:.1f}s")
        print(f"   Modelle trainiert: {len(self.models)}")
        print(f"   Prognose-Horizonte: {len(self.predictions)}")

        print("="*80)

    def save_results(self):
        """Ergebnisse speichern"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Modell-Metriken
        results_path = os.path.join(ULTIMATE_CONFIG['output_dirs']['results'], f'results_{timestamp}.json')
        with open(results_path, 'w') as f:
            # Numpy arrays zu Listen konvertieren für JSON
            json_results = {}
            for model_name, result in self.results.items():
                json_results[model_name] = {
                    'mse': float(result['mse']),
                    'mae': float(result['mae']),
                    'rmse': float(result['rmse']),
                    'r2': float(result['r2']),
                    'mse_orig': float(result['mse_orig']),
                    'mae_orig': float(result['mae_orig']),
                    'rmse_orig': float(result['rmse_orig'])
                }
            json.dump(json_results, f, indent=2)

        # Vorhersagen
        predictions_path = os.path.join(ULTIMATE_CONFIG['output_dirs']['results'], f'predictions_{timestamp}.json')
        with open(predictions_path, 'w') as f:
            json_predictions = {}
            for horizon, pred in self.predictions.items():
                json_predictions[str(horizon)] = {
                    'mean': float(pred['mean']),
                    'std': float(pred['std']),
                    'median': float(pred['median']),
                    'q25': float(pred['q25']),
                    'q75': float(pred['q75']),
                    'q5': float(pred['q5']),
                    'q95': float(pred['q95'])
                }
            json.dump(json_predictions, f, indent=2)

        # Scaler speichern
        scaler_path = os.path.join(ULTIMATE_CONFIG['output_dirs']['models'], f'scalers_{timestamp}.pkl')
        joblib.dump(self.trainer.scalers, scaler_path)

        logger.info(f"✅ Ergebnisse gespeichert: {results_path}")

def main():
    """Hauptfunktion"""
    print("🚀" * 30)
    print("ULTIMATE BITCOIN PREDICTION MASTER SCRIPT")
    print("🚀" * 30)

    # Ultimate Predictor erstellen und ausführen
    predictor = UltimateBitcoinPredictor()
    results = predictor.run_complete_analysis()

    if results:
        print("\n✅ Analyse erfolgreich abgeschlossen!")
        print("📊 Dashboard wurde erstellt: ultimate_plots/ultimate_dashboard.html")
        print("📈 Modell-Vergleich: ultimate_plots/model_comparison.png")
        print("💾 Ergebnisse gespeichert in: ultimate_results/")
    else:
        print("\n❌ Analyse fehlgeschlagen!")

if __name__ == "__main__":
    main()
