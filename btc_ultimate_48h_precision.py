#!/usr/bin/env python3
"""
🚀 ULTIMATE 48H BITCOIN PREDICTION - PRECISION VERSION 🚀
=========================================================
Speziell optimiert für präzise 48-Stunden Vorhersagen mit detaillierter Visualisierung
"""

import os
import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge

# TensorFlow für Deep Learning
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, GR<PERSON>, <PERSON>se, Dropout, BatchNormalization, Bidirectional
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
tf.random.set_seed(42)
np.random.seed(42)

print("🚀 ULTIMATE 48H BITCOIN PREDICTION - PRECISION VERSION")
print("=" * 60)

# PRECISION KONFIGURATION für 48h Vorhersagen
PRECISION_CONFIG = {
    'sequence_length': 72,  # 3 Tage für bessere 48h Vorhersagen
    'prediction_horizon': 48,  # Fokus auf 48h
    'detailed_intervals': [1, 3, 6, 12, 18, 24, 30, 36, 42, 48],  # Detaillierte Zeitpunkte
    'ensemble_models': ['bidirectional_lstm', 'gru_advanced', 'gradient_boosting', 'ridge_optimized'],
    'monte_carlo_simulations': 500,
    'confidence_levels': [0.05, 0.25, 0.75, 0.95],  # 5%, 25%, 75%, 95% Quantile
    'feature_importance_analysis': True,
    'hourly_breakdown': True
}

def load_and_prepare_data():
    """Daten laden und für 48h Vorhersagen optimieren"""
    try:
        df = pd.read_csv('crypto_data.csv', index_col=0, parse_dates=True)
        print(f"✅ Daten geladen: {len(df)} Datenpunkte")
    except:
        print("🔄 Generiere optimierte Beispieldaten...")
        dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='H')
        np.random.seed(42)
        
        # Realistischere Bitcoin-Simulation
        base_price = 45000
        trend = np.linspace(0, 25000, len(dates))
        
        # Mehrere Volatilitätskomponenten
        daily_volatility = 1500 * np.random.normal(0, 1, len(dates))
        weekly_cycle = 3000 * np.sin(2 * np.pi * np.arange(len(dates)) / (24 * 7))
        monthly_cycle = 5000 * np.sin(2 * np.pi * np.arange(len(dates)) / (24 * 30))
        
        # Markt-Events (zufällige Spikes)
        events = np.random.choice([0, 1], len(dates), p=[0.99, 0.01])
        event_impact = events * np.random.normal(0, 8000, len(dates))
        
        prices = base_price + trend + daily_volatility + weekly_cycle + monthly_cycle + event_impact
        prices = np.maximum(prices, 15000)  # Mindestpreis
        
        # Realistische OHLCV Daten
        high_factor = np.random.uniform(1.001, 1.08, len(dates))
        low_factor = np.random.uniform(0.92, 0.999, len(dates))
        open_factor = np.random.uniform(0.98, 1.02, len(dates))
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * high_factor,
            'low': prices * low_factor,
            'open': prices * open_factor,
            'volume': np.random.lognormal(15, 0.5, len(dates))  # Log-normal für realistische Volumen
        }, index=dates)
        
        print(f"✅ Optimierte Beispieldaten generiert: {len(df)} Datenpunkte")
    
    return df

def create_precision_features(df):
    """Erweiterte Features speziell für 48h Vorhersagen"""
    print("🔧 Erstelle Precision Features für 48h Vorhersagen...")
    
    df = df.copy()
    
    # === TREND FEATURES ===
    # Multiple Moving Averages
    for window in [6, 12, 24, 48, 72, 168]:  # 6h bis 1 Woche
        df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
        df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
        df[f'price_vs_sma_{window}'] = df['close'] / df[f'sma_{window}'] - 1
    
    # === MOMENTUM FEATURES ===
    # RSI mit verschiedenen Perioden
    for period in [6, 14, 24, 48]:
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=period).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
        rs = gain / loss
        df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
    
    # MACD Familie
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    ema_9 = df['close'].ewm(span=9).mean()
    
    df['macd'] = ema_12 - ema_26
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    df['macd_histogram'] = df['macd'] - df['macd_signal']
    df['macd_slope'] = df['macd'].diff()
    
    # === VOLATILITY FEATURES ===
    # Bollinger Bands mit verschiedenen Perioden
    for window in [12, 24, 48]:
        bb_middle = df['close'].rolling(window=window).mean()
        bb_std = df['close'].rolling(window=window).std()
        df[f'bb_upper_{window}'] = bb_middle + 2 * bb_std
        df[f'bb_lower_{window}'] = bb_middle - 2 * bb_std
        df[f'bb_width_{window}'] = df[f'bb_upper_{window}'] - df[f'bb_lower_{window}']
        df[f'bb_position_{window}'] = (df['close'] - df[f'bb_lower_{window}']) / df[f'bb_width_{window}']
    
    # Volatilität Features
    for window in [6, 12, 24, 48, 72]:
        df[f'volatility_{window}'] = df['close'].rolling(window=window).std()
        df[f'volatility_ratio_{window}'] = df[f'volatility_{window}'] / df['close']
    
    # === PRICE ACTION FEATURES ===
    # High-Low Features
    if 'high' in df.columns and 'low' in df.columns:
        df['hl_ratio'] = df['high'] / df['low']
        df['price_range'] = df['high'] - df['low']
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        # True Range und ATR
        df['tr'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                np.abs(df['high'] - df['close'].shift()),
                np.abs(df['low'] - df['close'].shift())
            )
        )
        for window in [14, 24, 48]:
            df[f'atr_{window}'] = df['tr'].rolling(window=window).mean()
    
    # === VOLUME FEATURES ===
    if 'volume' in df.columns:
        for window in [12, 24, 48]:
            df[f'volume_sma_{window}'] = df['volume'].rolling(window=window).mean()
            df[f'volume_ratio_{window}'] = df['volume'] / df[f'volume_sma_{window}']
        
        # Volume-Price Trend
        df['vpt'] = (df['volume'] * df['close'].pct_change()).cumsum()
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()
    
    # === MOMENTUM & CHANGE FEATURES ===
    # Price Changes für verschiedene Zeiträume
    for period in [1, 3, 6, 12, 24, 48]:
        df[f'price_change_{period}'] = df['close'].pct_change(periods=period)
        df[f'price_momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
    
    # === LAG FEATURES ===
    # Wichtige Lag Features für 48h Vorhersagen
    for lag in [1, 2, 3, 6, 12, 24, 48]:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
        df[f'volume_lag_{lag}'] = df['volume'].shift(lag) if 'volume' in df.columns else 0
        df[f'volatility_lag_{lag}'] = df['volatility_24'].shift(lag)
    
    # === ROLLING STATISTICS ===
    for window in [12, 24, 48, 72]:
        df[f'close_mean_{window}'] = df['close'].rolling(window=window).mean()
        df[f'close_std_{window}'] = df['close'].rolling(window=window).std()
        df[f'close_min_{window}'] = df['close'].rolling(window=window).min()
        df[f'close_max_{window}'] = df['close'].rolling(window=window).max()
        df[f'close_skew_{window}'] = df['close'].rolling(window=window).skew()
        df[f'close_kurt_{window}'] = df['close'].rolling(window=window).kurt()
    
    # === TIME-BASED FEATURES ===
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['month'] = df.index.month
    df['quarter'] = df.index.quarter
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    
    # Cyclical encoding für bessere Zeitreihen-Performance
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
    df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
    
    # === MARKET REGIME FEATURES ===
    # Trend Strength
    df['trend_strength'] = np.abs(df['close'].rolling(window=24).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0]))
    
    # Market Regime (Bull/Bear/Sideways)
    short_ma = df['close'].rolling(window=12).mean()
    long_ma = df['close'].rolling(window=48).mean()
    df['market_regime'] = np.where(short_ma > long_ma * 1.02, 1,  # Bull
                                  np.where(short_ma < long_ma * 0.98, -1, 0))  # Bear, Sideways
    
    print(f"✅ Precision Features erstellt: {df.shape[1]} Spalten")
    return df.dropna()

def prepare_precision_data(df, sequence_length=72):
    """Daten speziell für 48h Vorhersagen vorbereiten"""
    print(f"🔄 Bereite Precision Daten vor (Sequenz: {sequence_length})...")
    
    # Features auswählen (alle außer close)
    feature_cols = [col for col in df.columns if col != 'close']
    features = df[feature_cols].values
    target = df['close'].values
    
    # Robuste Skalierung für bessere Performance
    feature_scaler = RobustScaler()
    target_scaler = MinMaxScaler()
    
    features_scaled = feature_scaler.fit_transform(features)
    target_scaled = target_scaler.fit_transform(target.reshape(-1, 1)).flatten()
    
    # Sequenzen erstellen
    X, y = [], []
    for i in range(sequence_length, len(features_scaled)):
        X.append(features_scaled[i-sequence_length:i])
        y.append(target_scaled[i])
    
    X, y = np.array(X), np.array(y)
    
    # Optimaler Split für 48h Vorhersagen
    train_size = int(len(X) * 0.75)
    val_size = int(len(X) * 0.15)
    
    X_train = X[:train_size]
    y_train = y[:train_size]
    X_val = X[train_size:train_size+val_size]
    y_val = y[train_size:train_size+val_size]
    X_test = X[train_size+val_size:]
    y_test = y[train_size+val_size:]
    
    print(f"✅ Precision Daten vorbereitet:")
    print(f"   Train: {X_train.shape}")
    print(f"   Validation: {X_val.shape}")
    print(f"   Test: {X_test.shape}")
    print(f"   Features: {X_train.shape[2]}")
    
    return (X_train, y_train), (X_val, y_val), (X_test, y_test), (feature_scaler, target_scaler)

def build_precision_models(input_shape):
    """Speziell optimierte Modelle für 48h Vorhersagen"""
    models = {}

    # 1. Bidirectional LSTM - Optimiert für 48h
    print("🧠 Baue Bidirectional LSTM für 48h...")
    model1 = Sequential([
        Bidirectional(LSTM(128, return_sequences=True, dropout=0.2, recurrent_dropout=0.1),
                     input_shape=input_shape),
        BatchNormalization(),
        Bidirectional(LSTM(64, return_sequences=True, dropout=0.2, recurrent_dropout=0.1)),
        BatchNormalization(),
        LSTM(32, return_sequences=False, dropout=0.2),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(32, activation='relu'),
        Dropout(0.2),
        Dense(16, activation='relu'),
        Dense(1)
    ])
    model1.compile(optimizer=Adam(learning_rate=0.001, clipnorm=1.0),
                   loss='mse', metrics=['mae', 'mape'])
    models['Bidirectional_LSTM'] = model1

    # 2. Advanced GRU - Speziell für 48h Horizont
    print("🔥 Baue Advanced GRU für 48h...")
    model2 = Sequential([
        GRU(128, return_sequences=True, dropout=0.2, recurrent_dropout=0.1, input_shape=input_shape),
        BatchNormalization(),
        GRU(64, return_sequences=True, dropout=0.2, recurrent_dropout=0.1),
        BatchNormalization(),
        GRU(32, return_sequences=False, dropout=0.2),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(32, activation='relu'),
        Dropout(0.2),
        Dense(16, activation='relu'),
        Dense(1)
    ])
    model2.compile(optimizer=Adam(learning_rate=0.0008, clipnorm=1.0),
                   loss='mse', metrics=['mae', 'mape'])
    models['Advanced_GRU'] = model2

    return models

def build_ml_models():
    """Machine Learning Modelle für Ensemble"""
    print("🤖 Baue ML Modelle für Ensemble...")

    ml_models = {
        'Gradient_Boosting': GradientBoostingRegressor(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=6,
            subsample=0.8,
            random_state=42
        ),
        'Ridge_Optimized': Ridge(
            alpha=1.0,
            solver='auto'
        ),
        'Random_Forest': RandomForestRegressor(
            n_estimators=150,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
    }

    return ml_models

def train_precision_models(dl_models, ml_models, train_data, val_data, test_data):
    """Alle Modelle für 48h Vorhersagen trainieren"""
    print("\n🚀 Starte Precision Model Training für 48h...")

    X_train, y_train = train_data
    X_val, y_val = val_data
    X_test, y_test = test_data

    results = {}

    # === DEEP LEARNING MODELLE ===
    callbacks = [
        EarlyStopping(monitor='val_loss', patience=20, restore_best_weights=True, verbose=1),
        ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=10, min_lr=1e-7, verbose=1)
    ]

    for name, model in dl_models.items():
        print(f"\n🔥 Trainiere {name}...")

        start_time = time.time()
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=100,
            batch_size=32,
            callbacks=callbacks,
            verbose=1
        )
        training_time = time.time() - start_time

        # Vorhersagen
        y_pred = model.predict(X_test, verbose=0)

        # Metriken
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        results[name] = {
            'model': model,
            'type': 'deep_learning',
            'history': history,
            'training_time': training_time,
            'mse': mse,
            'mae': mae,
            'rmse': np.sqrt(mse),
            'r2': r2,
            'y_pred': y_pred.flatten(),
            'y_test': y_test
        }

        print(f"✅ {name}: R²={r2:.4f}, RMSE={np.sqrt(mse):.4f}, Zeit={training_time:.1f}s")

    # === MACHINE LEARNING MODELLE ===
    X_train_flat = X_train.reshape(X_train.shape[0], -1)
    X_test_flat = X_test.reshape(X_test.shape[0], -1)

    for name, model in ml_models.items():
        print(f"\n🤖 Trainiere {name}...")

        start_time = time.time()
        model.fit(X_train_flat, y_train)
        training_time = time.time() - start_time

        # Vorhersagen
        y_pred = model.predict(X_test_flat)

        # Metriken
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        results[name] = {
            'model': model,
            'type': 'machine_learning',
            'training_time': training_time,
            'mse': mse,
            'mae': mae,
            'rmse': np.sqrt(mse),
            'r2': r2,
            'y_pred': y_pred,
            'y_test': y_test
        }

        print(f"✅ {name}: R²={r2:.4f}, RMSE={np.sqrt(mse):.4f}, Zeit={training_time:.1f}s")

    return results

def create_ensemble_prediction(results, X_test):
    """Erstelle Ensemble-Vorhersage basierend auf R² Gewichtung"""
    print("🎯 Erstelle Ensemble-Vorhersage...")

    # Gewichte basierend auf R² Score
    weights = {}
    total_r2 = 0

    for name, result in results.items():
        r2 = max(0.1, result['r2'])  # Minimum Gewicht
        weights[name] = r2
        total_r2 += r2

    # Normalisiere Gewichte
    for name in weights:
        weights[name] = weights[name] / total_r2

    # Gewichtete Vorhersage
    ensemble_pred = np.zeros_like(results[list(results.keys())[0]]['y_test'])

    for name, result in results.items():
        ensemble_pred += weights[name] * result['y_pred']

    # Ensemble Metriken
    y_test = results[list(results.keys())[0]]['y_test']
    ensemble_mse = mean_squared_error(y_test, ensemble_pred)
    ensemble_mae = mean_absolute_error(y_test, ensemble_pred)
    ensemble_r2 = r2_score(y_test, ensemble_pred)

    print(f"✅ Ensemble: R²={ensemble_r2:.4f}, RMSE={np.sqrt(ensemble_mse):.4f}")
    print(f"📊 Modell-Gewichte:")
    for name, weight in weights.items():
        print(f"   {name}: {weight:.3f}")

    return {
        'ensemble_pred': ensemble_pred,
        'ensemble_r2': ensemble_r2,
        'ensemble_rmse': np.sqrt(ensemble_mse),
        'ensemble_mae': ensemble_mae,
        'weights': weights
    }

def predict_48h_detailed(best_models, last_sequence, target_scaler, feature_scaler):
    """Detaillierte 48h Vorhersage mit Monte Carlo"""
    print("🔮 Erstelle detaillierte 48h Vorhersage...")

    detailed_predictions = {}
    intervals = PRECISION_CONFIG['detailed_intervals']
    n_simulations = PRECISION_CONFIG['monte_carlo_simulations']

    for interval in intervals:
        print(f"📈 Prognose für {interval}h...")

        model_predictions = []

        # Für jedes Modell
        for model_name, model_data in best_models.items():
            model = model_data['model']
            model_type = model_data['type']

            # Monte Carlo Simulationen
            interval_predictions = []

            for sim in range(n_simulations):
                # Noise für Unsicherheit
                noisy_sequence = last_sequence + np.random.normal(0, 0.005, last_sequence.shape)
                current_sequence = noisy_sequence.copy()

                # Iterative Vorhersage bis zum Zielintervall
                for step in range(interval):
                    if model_type == 'deep_learning':
                        pred_scaled = model.predict(current_sequence.reshape(1, *current_sequence.shape), verbose=0)[0, 0]
                    else:  # machine_learning
                        pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]

                    # Sequence für nächsten Schritt aktualisieren
                    new_row = current_sequence[-1].copy()
                    new_row[0] = pred_scaled  # Erste Spalte ist der Preis
                    current_sequence = np.vstack([current_sequence[1:], new_row])

                # Finale Vorhersage für dieses Intervall
                if model_type == 'deep_learning':
                    final_pred_scaled = model.predict(current_sequence.reshape(1, *current_sequence.shape), verbose=0)[0, 0]
                else:
                    final_pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]

                interval_predictions.append(final_pred_scaled)

            # Zurück transformieren
            interval_predictions = np.array(interval_predictions)
            interval_predictions_orig = target_scaler.inverse_transform(interval_predictions.reshape(-1, 1)).flatten()
            model_predictions.extend(interval_predictions_orig)

        # Statistiken für dieses Intervall
        all_predictions = np.array(model_predictions)

        detailed_predictions[interval] = {
            'mean': np.mean(all_predictions),
            'median': np.median(all_predictions),
            'std': np.std(all_predictions),
            'min': np.min(all_predictions),
            'max': np.max(all_predictions),
            'q05': np.percentile(all_predictions, 5),
            'q25': np.percentile(all_predictions, 25),
            'q75': np.percentile(all_predictions, 75),
            'q95': np.percentile(all_predictions, 95),
            'all_predictions': all_predictions
        }

    return detailed_predictions

def create_ultimate_48h_visualization(df, results, ensemble_result, detailed_predictions, target_scaler):
    """Ultimate detaillierte 48h Visualisierung"""
    print("📊 Erstelle Ultimate 48h Visualisierung...")

    # Große Figure für detaillierte Analyse
    fig = plt.figure(figsize=(28, 20))
    fig.patch.set_facecolor('#0a0a0a')
    fig.suptitle('🚀 ULTIMATE 48H BITCOIN PREDICTION - PRECISION ANALYSIS',
                 fontsize=24, color='white', fontweight='bold', y=0.98)

    # === 1. HAUPTCHART: Historische Daten + Vorhersagen ===
    ax1 = plt.subplot2grid((5, 6), (0, 0), colspan=4, rowspan=2)

    # Letzte 7 Tage für Kontext
    recent_data = df.tail(168)  # 7 Tage
    ax1.plot(recent_data.index, recent_data['close'],
             color='#00D4FF', linewidth=3, label='Historischer Preis', alpha=0.9)

    # Aktuelle Zeit markieren
    current_time = recent_data.index[-1]
    ax1.axvline(x=current_time, color='#FF6B6B', linestyle='-', linewidth=2,
                label='Jetzt', alpha=0.8)

    # 48h Vorhersage-Timeline
    future_times = pd.date_range(start=current_time, periods=49, freq='H')[1:]

    # Vorhersage-Linie mit Konfidenzintervall
    intervals = list(detailed_predictions.keys())
    mean_predictions = [detailed_predictions[h]['mean'] for h in intervals]
    q05_predictions = [detailed_predictions[h]['q05'] for h in intervals]
    q95_predictions = [detailed_predictions[h]['q95'] for h in intervals]

    # Interpolation für glatte Linie
    future_hours = np.arange(1, 49)
    interp_mean = np.interp(future_hours, intervals, mean_predictions)
    interp_q05 = np.interp(future_hours, intervals, q05_predictions)
    interp_q95 = np.interp(future_hours, intervals, q95_predictions)

    ax1.plot(future_times, interp_mean, color='#FFD700', linewidth=4,
             label='48h Vorhersage', alpha=0.9)
    ax1.fill_between(future_times, interp_q05, interp_q95,
                     color='#FFD700', alpha=0.2, label='90% Konfidenzintervall')

    # Wichtige Zeitpunkte markieren
    for hour in [6, 12, 24, 36, 48]:
        if hour <= len(future_times):
            ax1.axvline(x=future_times[hour-1], color='#FF9500', linestyle='--',
                       alpha=0.5, linewidth=1)
            ax1.text(future_times[hour-1], ax1.get_ylim()[1]*0.95, f'{hour}h',
                    rotation=90, color='#FF9500', fontsize=10)

    ax1.set_title('48-Stunden Bitcoin Preisprognose', fontsize=18, color='white', fontweight='bold')
    ax1.set_ylabel('Preis (USD)', color='white', fontsize=14)
    ax1.legend(loc='upper left', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(colors='white', labelsize=12)

    # X-Achse formatieren
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax1.xaxis.set_major_locator(mdates.HourLocator(interval=6))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

    # === 2. MODELL PERFORMANCE RANKING ===
    ax2 = plt.subplot2grid((5, 6), (0, 4), colspan=2)

    # Sortiere Modelle nach R²
    sorted_models = sorted(results.items(), key=lambda x: x[1]['r2'], reverse=True)
    model_names = [name for name, _ in sorted_models]
    r2_scores = [result['r2'] for _, result in sorted_models]

    # Farben basierend auf Performance
    colors = ['#00FF00' if r2 > 0.8 else '#FFD700' if r2 > 0.6 else '#FF6B6B' for r2 in r2_scores]

    bars = ax2.barh(range(len(model_names)), r2_scores, color=colors, alpha=0.8)
    ax2.set_yticks(range(len(model_names)))
    ax2.set_yticklabels(model_names, fontsize=10)
    ax2.set_xlabel('R² Score', color='white', fontsize=12)
    ax2.set_title('Modell Performance Ranking', color='white', fontweight='bold', fontsize=14)
    ax2.tick_params(colors='white', labelsize=10)
    ax2.grid(True, alpha=0.3, axis='x')

    # R² Werte anzeigen
    for i, (bar, score) in enumerate(zip(bars, r2_scores)):
        ax2.text(score + 0.01, bar.get_y() + bar.get_height()/2,
                f'{score:.3f}', va='center', color='white', fontsize=9)

    # === 3. ENSEMBLE GEWICHTE ===
    ax3 = plt.subplot2grid((5, 6), (1, 4), colspan=2)

    weights = ensemble_result['weights']
    weight_names = list(weights.keys())
    weight_values = list(weights.values())

    wedges, texts, autotexts = ax3.pie(weight_values, labels=weight_names, autopct='%1.1f%%',
                                       colors=plt.cm.Set3(np.linspace(0, 1, len(weight_names))))
    ax3.set_title('Ensemble Modell-Gewichte', color='white', fontweight='bold', fontsize=14)

    # Text-Farben anpassen
    for text in texts + autotexts:
        text.set_color('white')
        text.set_fontsize(9)

    # === 4. DETAILLIERTE 48H PROGNOSE TABELLE ===
    ax4 = plt.subplot2grid((5, 6), (2, 0), colspan=3)
    ax4.axis('off')

    current_price = df['close'].iloc[-1]

    # Tabellen-Daten vorbereiten
    table_data = []
    headers = ['Stunden', 'Prognose (USD)', 'Änderung (%)', 'Min-Max (USD)', 'Konfidenz (90%)']

    for hour in [1, 3, 6, 12, 18, 24, 30, 36, 42, 48]:
        if hour in detailed_predictions:
            pred = detailed_predictions[hour]
            change_pct = ((pred['mean'] / current_price) - 1) * 100

            table_data.append([
                f"{hour}h",
                f"${pred['mean']:,.0f}",
                f"{change_pct:+.1f}%",
                f"${pred['min']:,.0f} - ${pred['max']:,.0f}",
                f"${pred['q05']:,.0f} - ${pred['q95']:,.0f}"
            ])

    # Tabelle erstellen
    table = ax4.table(cellText=table_data, colLabels=headers,
                     cellLoc='center', loc='center',
                     colWidths=[0.15, 0.25, 0.15, 0.25, 0.25])

    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)

    # Tabellen-Styling
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#333333')
        table[(0, i)].set_text_props(weight='bold', color='white')

    for i in range(1, len(table_data) + 1):
        for j in range(len(headers)):
            table[(i, j)].set_facecolor('#1a1a1a')
            table[(i, j)].set_text_props(color='white')

            # Farbkodierung für Änderung
            if j == 2:  # Änderung % Spalte
                change_val = float(table_data[i-1][j].replace('%', '').replace('+', ''))
                if change_val > 0:
                    table[(i, j)].set_facecolor('#2d5a2d')  # Grün für positiv
                else:
                    table[(i, j)].set_facecolor('#5a2d2d')  # Rot für negativ

    ax4.set_title('Detaillierte 48h Prognose-Tabelle', color='white', fontweight='bold', fontsize=14)

    # === 5. UNSICHERHEITS-ANALYSE ===
    ax5 = plt.subplot2grid((5, 6), (2, 3), colspan=3)

    hours = list(detailed_predictions.keys())
    uncertainties = [detailed_predictions[h]['std'] for h in hours]

    ax5.plot(hours, uncertainties, 'o-', color='#FF6B6B', linewidth=3, markersize=8)
    ax5.fill_between(hours, uncertainties, alpha=0.3, color='#FF6B6B')

    ax5.set_title('Vorhersage-Unsicherheit über Zeit', color='white', fontweight='bold', fontsize=14)
    ax5.set_xlabel('Stunden', color='white', fontsize=12)
    ax5.set_ylabel('Standardabweichung (USD)', color='white', fontsize=12)
    ax5.grid(True, alpha=0.3)
    ax5.tick_params(colors='white', labelsize=10)

    # === 6. PREIS-VERTEILUNG FÜR 24H UND 48H ===
    ax6 = plt.subplot2grid((5, 6), (3, 0), colspan=2)

    if 24 in detailed_predictions and 48 in detailed_predictions:
        pred_24h = detailed_predictions[24]['all_predictions']
        pred_48h = detailed_predictions[48]['all_predictions']

        ax6.hist(pred_24h, bins=30, alpha=0.7, color='#00D4FF', label='24h Prognose', density=True)
        ax6.hist(pred_48h, bins=30, alpha=0.7, color='#FFD700', label='48h Prognose', density=True)

        ax6.set_title('Prognose-Verteilungen', color='white', fontweight='bold', fontsize=14)
        ax6.set_xlabel('Preis (USD)', color='white', fontsize=12)
        ax6.set_ylabel('Dichte', color='white', fontsize=12)
        ax6.legend(fontsize=10)
        ax6.grid(True, alpha=0.3)
        ax6.tick_params(colors='white', labelsize=10)

    # === 7. TREND-ANALYSE ===
    ax7 = plt.subplot2grid((5, 6), (3, 2), colspan=2)

    # Trend-Stärke über die 48h
    trend_changes = []
    for i, hour in enumerate(hours[1:], 1):
        prev_hour = hours[i-1]
        change = ((detailed_predictions[hour]['mean'] / detailed_predictions[prev_hour]['mean']) - 1) * 100
        trend_changes.append(change)

    colors = ['#00FF00' if change > 0 else '#FF6B6B' for change in trend_changes]
    ax7.bar(range(len(trend_changes)), trend_changes, color=colors, alpha=0.8)

    ax7.set_title('Stündliche Trend-Änderungen', color='white', fontweight='bold', fontsize=14)
    ax7.set_xlabel('Intervall', color='white', fontsize=12)
    ax7.set_ylabel('Änderung (%)', color='white', fontsize=12)
    ax7.axhline(y=0, color='white', linestyle='-', alpha=0.5)
    ax7.grid(True, alpha=0.3)
    ax7.tick_params(colors='white', labelsize=10)

    # === 8. ZUSAMMENFASSUNG ===
    ax8 = plt.subplot2grid((5, 6), (3, 4), colspan=2, rowspan=2)
    ax8.axis('off')

    # Beste Modell-Info
    best_model_name = max(results.keys(), key=lambda x: results[x]['r2'])
    best_r2 = results[best_model_name]['r2']

    # 48h Prognose-Info
    pred_48h_mean = detailed_predictions[48]['mean']
    change_48h = ((pred_48h_mean / current_price) - 1) * 100
    confidence_range = detailed_predictions[48]['q95'] - detailed_predictions[48]['q05']

    # Risiko-Bewertung
    risk_level = "NIEDRIG" if confidence_range < 2000 else "MITTEL" if confidence_range < 5000 else "HOCH"
    risk_color = "#00FF00" if risk_level == "NIEDRIG" else "#FFD700" if risk_level == "MITTEL" else "#FF6B6B"

    summary_text = f"""
🚀 ULTIMATE 48H PROGNOSE ZUSAMMENFASSUNG

📊 MODELL-PERFORMANCE:
   Bestes Modell: {best_model_name}
   R² Score: {best_r2:.4f} ({best_r2*100:.1f}%)
   Ensemble R²: {ensemble_result['ensemble_r2']:.4f}

💰 AKTUELLE SITUATION:
   Aktueller Preis: ${current_price:,.0f}

🔮 48H PROGNOSE:
   Erwarteter Preis: ${pred_48h_mean:,.0f}
   Änderung: {change_48h:+.1f}%

📈 TREND: {"BULLISH 📈" if change_48h > 0 else "BEARISH 📉"}

⚠️ RISIKO-ANALYSE:
   Unsicherheitsbereich: ${confidence_range:,.0f}
   Risiko-Level: {risk_level}

🎯 KONFIDENZ-INTERVALL (90%):
   Minimum: ${detailed_predictions[48]['q05']:,.0f}
   Maximum: ${detailed_predictions[48]['q95']:,.0f}

⚡ EMPFEHLUNG:
   {"KAUFEN" if change_48h > 2 else "HALTEN" if change_48h > -2 else "VERKAUFEN"}
   (Basierend auf 48h Prognose)
    """

    ax8.text(0.05, 0.95, summary_text, transform=ax8.transAxes,
             fontsize=11, color='white', verticalalignment='top',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='#1a1a1a', alpha=0.9))

    # Risiko-Level farbig hervorheben
    ax8.text(0.7, 0.35, risk_level, transform=ax8.transAxes,
             fontsize=16, color=risk_color, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.3', facecolor=risk_color, alpha=0.2))

    # === 9. TECHNISCHE INDIKATOREN ===
    ax9 = plt.subplot2grid((5, 6), (4, 0), colspan=6)

    # Letzte 48h der technischen Indikatoren
    recent_48h = df.tail(48)

    # RSI
    ax9_rsi = ax9.twinx()
    if 'rsi_14' in df.columns:
        ax9_rsi.plot(recent_48h.index, recent_48h['rsi_14'],
                     color='#9500FF', linewidth=2, alpha=0.8, label='RSI (14)')
        ax9_rsi.axhline(y=70, color='#FF0000', linestyle='--', alpha=0.5)
        ax9_rsi.axhline(y=30, color='#00FF00', linestyle='--', alpha=0.5)
        ax9_rsi.set_ylabel('RSI', color='white', fontsize=12)
        ax9_rsi.set_ylim(0, 100)
        ax9_rsi.tick_params(colors='white', labelsize=10)

    # MACD
    if 'macd' in df.columns and 'macd_signal' in df.columns:
        ax9.plot(recent_48h.index, recent_48h['macd'],
                 color='#00FFFF', linewidth=2, label='MACD')
        ax9.plot(recent_48h.index, recent_48h['macd_signal'],
                 color='#FF9500', linewidth=2, label='Signal')
        ax9.fill_between(recent_48h.index,
                         recent_48h['macd'], recent_48h['macd_signal'],
                         alpha=0.3, color='green')

    ax9.set_title('Technische Indikatoren (Letzte 48h)', color='white', fontweight='bold', fontsize=14)
    ax9.set_xlabel('Zeit', color='white', fontsize=12)
    ax9.set_ylabel('MACD', color='white', fontsize=12)
    ax9.legend(loc='upper left', fontsize=10)
    ax9_rsi.legend(loc='upper right', fontsize=10)
    ax9.grid(True, alpha=0.3)
    ax9.tick_params(colors='white', labelsize=10)

    # X-Achse formatieren
    ax9.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax9.xaxis.set_major_locator(mdates.HourLocator(interval=6))
    plt.setp(ax9.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()
    plt.subplots_adjust(wspace=0.3, hspace=0.4)

    # Speichern
    os.makedirs('ultimate_plots', exist_ok=True)
    plt.savefig('ultimate_plots/ultimate_48h_precision_analysis.png',
                facecolor='#0a0a0a', dpi=300, bbox_inches='tight')

    print("✅ Ultimate 48h Visualisierung gespeichert: ultimate_plots/ultimate_48h_precision_analysis.png")
    plt.show()

    return fig

def main():
    """Hauptfunktion für Ultimate 48h Precision Prediction"""
    print("\n🚀" * 25)
    print("ULTIMATE 48H BITCOIN PREDICTION - PRECISION VERSION")
    print("🚀" * 25)

    start_time = time.time()

    try:
        # === PHASE 1: DATENSAMMLUNG ===
        print("\n" + "="*70)
        print("PHASE 1: DATENSAMMLUNG UND VORBEREITUNG")
        print("="*70)

        df = load_and_prepare_data()

        # === PHASE 2: PRECISION FEATURE ENGINEERING ===
        print("\n" + "="*70)
        print("PHASE 2: PRECISION FEATURE ENGINEERING")
        print("="*70)

        df_features = create_precision_features(df)
        print(f"📊 Finale Datenform: {df_features.shape}")

        # === PHASE 3: DATENAUFBEREITUNG ===
        print("\n" + "="*70)
        print("PHASE 3: PRECISION DATENAUFBEREITUNG")
        print("="*70)

        train_data, val_data, test_data, scalers = prepare_precision_data(
            df_features,
            sequence_length=PRECISION_CONFIG['sequence_length']
        )
        feature_scaler, target_scaler = scalers
        input_shape = (train_data[0].shape[1], train_data[0].shape[2])

        # === PHASE 4: MODELL-ERSTELLUNG ===
        print("\n" + "="*70)
        print("PHASE 4: PRECISION MODELL-ERSTELLUNG")
        print("="*70)

        dl_models = build_precision_models(input_shape)
        ml_models = build_ml_models()

        print(f"✅ Deep Learning Modelle: {len(dl_models)}")
        print(f"✅ Machine Learning Modelle: {len(ml_models)}")

        # === PHASE 5: MODELL-TRAINING ===
        print("\n" + "="*70)
        print("PHASE 5: PRECISION MODELL-TRAINING")
        print("="*70)

        results = train_precision_models(dl_models, ml_models, train_data, val_data, test_data)

        if not results:
            print("❌ Keine Modelle erfolgreich trainiert!")
            return None

        # === PHASE 6: ENSEMBLE-ERSTELLUNG ===
        print("\n" + "="*70)
        print("PHASE 6: ENSEMBLE-ERSTELLUNG")
        print("="*70)

        ensemble_result = create_ensemble_prediction(results, test_data[0])

        # === PHASE 7: 48H VORHERSAGE ===
        print("\n" + "="*70)
        print("PHASE 7: DETAILLIERTE 48H VORHERSAGE")
        print("="*70)

        # Beste Modelle für Vorhersage auswählen (Top 3)
        sorted_results = sorted(results.items(), key=lambda x: x[1]['r2'], reverse=True)
        best_models = dict(sorted_results[:3])

        X_test, y_test = test_data
        last_sequence = X_test[-1]

        detailed_predictions = predict_48h_detailed(
            best_models, last_sequence, target_scaler, feature_scaler
        )

        # === PHASE 8: ULTIMATE VISUALISIERUNG ===
        print("\n" + "="*70)
        print("PHASE 8: ULTIMATE 48H VISUALISIERUNG")
        print("="*70)

        fig = create_ultimate_48h_visualization(
            df_features, results, ensemble_result, detailed_predictions, target_scaler
        )

        # === PHASE 9: FINALE ZUSAMMENFASSUNG ===
        total_time = time.time() - start_time
        print_ultimate_48h_summary(results, ensemble_result, detailed_predictions,
                                  df_features, total_time)

        # === PHASE 10: ERGEBNISSE SPEICHERN ===
        save_48h_results(results, ensemble_result, detailed_predictions)

        print(f"\n🎉 ULTIMATE 48H PRECISION ANALYSE ABGESCHLOSSEN in {total_time:.1f}s! 🎉")

        return {
            'results': results,
            'ensemble_result': ensemble_result,
            'detailed_predictions': detailed_predictions,
            'data': df_features,
            'total_time': total_time
        }

    except Exception as e:
        print(f"❌ Fehler in der Ultimate 48h Analyse: {e}")
        import traceback
        traceback.print_exc()
        return None

def print_ultimate_48h_summary(results, ensemble_result, detailed_predictions, df, total_time):
    """Ultimate 48h Zusammenfassung ausgeben"""
    print("\n" + "="*80)
    print("🚀 ULTIMATE 48H BITCOIN PREDICTION RESULTS 🚀")
    print("="*80)

    # Beste Modelle
    best_model = max(results.keys(), key=lambda x: results[x]['r2'])
    print(f"\n🏆 BESTES MODELL: {best_model}")
    print(f"   R² Score: {results[best_model]['r2']:.4f} ({results[best_model]['r2']*100:.1f}%)")
    print(f"   RMSE: {results[best_model]['rmse']:.4f}")
    print(f"   MAE: {results[best_model]['mae']:.4f}")
    print(f"   Training Zeit: {results[best_model]['training_time']:.1f}s")

    # Ensemble Performance
    print(f"\n🎯 ENSEMBLE PERFORMANCE:")
    print(f"   R² Score: {ensemble_result['ensemble_r2']:.4f} ({ensemble_result['ensemble_r2']*100:.1f}%)")
    print(f"   RMSE: {ensemble_result['ensemble_rmse']:.4f}")
    print(f"   MAE: {ensemble_result['ensemble_mae']:.4f}")

    # Alle Modell-Performances
    print(f"\n📊 ALLE MODELL-PERFORMANCES:")
    print(f"{'Modell':<20} | {'Typ':<15} | {'R²':<8} | {'RMSE':<8} | {'Zeit':<8}")
    print("-" * 75)
    for model_name, result in results.items():
        model_type = result['type'].replace('_', ' ').title()
        print(f"{model_name:<20} | {model_type:<15} | {result['r2']:<8.4f} | {result['rmse']:<8.4f} | {result['training_time']:<8.1f}s")

    # 48h Detaillierte Vorhersagen
    current_price = df['close'].iloc[-1]
    print(f"\n🔮 DETAILLIERTE 48H VORHERSAGEN:")
    print(f"   Aktueller Preis: ${current_price:,.2f}")
    print(f"\n{'Zeit':<6} | {'Prognose':<12} | {'Änderung':<10} | {'Konfidenz (90%)':<20}")
    print("-" * 55)

    for hour in [1, 6, 12, 24, 36, 48]:
        if hour in detailed_predictions:
            pred = detailed_predictions[hour]
            change_pct = ((pred['mean'] / current_price) - 1) * 100
            direction = "📈" if change_pct > 0 else "📉"

            print(f"{hour:>4}h | ${pred['mean']:>10,.0f} | {change_pct:>+7.1f}% {direction} | ${pred['q05']:>6,.0f} - ${pred['q95']:>6,.0f}")

    # Risiko-Analyse
    print(f"\n⚠️ RISIKO-ANALYSE:")
    uncertainty_24h = detailed_predictions[24]['std']
    uncertainty_48h = detailed_predictions[48]['std']
    confidence_range_48h = detailed_predictions[48]['q95'] - detailed_predictions[48]['q05']

    risk_level = "NIEDRIG 🟢" if confidence_range_48h < 2000 else "MITTEL 🟡" if confidence_range_48h < 5000 else "HOCH 🔴"

    print(f"   24h Unsicherheit: ${uncertainty_24h:,.0f}")
    print(f"   48h Unsicherheit: ${uncertainty_48h:,.0f}")
    print(f"   Konfidenzbereich: ${confidence_range_48h:,.0f}")
    print(f"   Risiko-Level: {risk_level}")

    # Trading-Empfehlung
    change_48h = ((detailed_predictions[48]['mean'] / current_price) - 1) * 100

    if change_48h > 3 and risk_level.startswith("NIEDRIG"):
        recommendation = "STARKER KAUF 🚀"
    elif change_48h > 1:
        recommendation = "KAUF 📈"
    elif change_48h > -1:
        recommendation = "HALTEN ⚖️"
    elif change_48h > -3:
        recommendation = "VERKAUF 📉"
    else:
        recommendation = "STARKER VERKAUF 🔻"

    print(f"\n💡 TRADING-EMPFEHLUNG: {recommendation}")
    print(f"   (Basierend auf 48h Prognose: {change_48h:+.1f}%)")

    # Performance-Statistiken
    print(f"\n⚡ PERFORMANCE-STATISTIKEN:")
    print(f"   Gesamtzeit: {total_time:.1f}s")
    print(f"   Datenpunkte: {len(df):,}")
    print(f"   Features: {df.shape[1]}")
    print(f"   Modelle trainiert: {len(results)}")
    print(f"   Monte Carlo Sims: {PRECISION_CONFIG['monte_carlo_simulations']}")

    print("="*80)

def save_48h_results(results, ensemble_result, detailed_predictions):
    """48h Ergebnisse speichern"""
    from datetime import datetime
    import json

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    os.makedirs('ultimate_results', exist_ok=True)

    # Detaillierte Vorhersagen als JSON
    predictions_path = f'ultimate_results/48h_predictions_{timestamp}.json'
    json_predictions = {}
    for hour, pred in detailed_predictions.items():
        json_predictions[str(hour)] = {
            'mean': float(pred['mean']),
            'median': float(pred['median']),
            'std': float(pred['std']),
            'min': float(pred['min']),
            'max': float(pred['max']),
            'q05': float(pred['q05']),
            'q25': float(pred['q25']),
            'q75': float(pred['q75']),
            'q95': float(pred['q95'])
        }

    with open(predictions_path, 'w') as f:
        json.dump(json_predictions, f, indent=2)

    print(f"\n💾 48H ERGEBNISSE GESPEICHERT:")
    print(f"   Vorhersagen: {predictions_path}")
    print(f"   Visualisierung: ultimate_plots/ultimate_48h_precision_analysis.png")

if __name__ == "__main__":
    main()
