{"prediction_horizons": [1, 6, 24], "max_cores": 16, "base_thresholds": {"1": 0.003, "6": 0.008, "24": 0.015}, "model_params": {"RandomForest": {"n_estimators": 150, "max_depth": 18, "min_samples_split": 3, "max_features": "sqrt", "random_state": 42}, "ExtraTrees": {"n_estimators": 120, "max_depth": 15, "min_samples_split": 2, "max_features": "sqrt", "random_state": 42}, "XGBoost": {"n_estimators": 100, "max_depth": 8, "learning_rate": 0.1, "random_state": 42}, "LogisticRegression": {"max_iter": 1000, "C": 1.0, "random_state": 42}}, "horizon_weights": {"1": 0.5, "6": 0.3, "24": 0.2}, "signal_thresholds": {"STARKER_KAUF": 0.7, "KAUF": 0.6, "VERKAUF": 0.4, "STARKER_VERKAUF": 0.3}}