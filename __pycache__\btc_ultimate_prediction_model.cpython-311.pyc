�

    ��Yh|�  �                   �   � d dl Zd dlZd dlmZ d dlmZ	 d dl
Z
d dlZd dl
mZ d dlmZmZmZ d dlmZmZmZ d dlmZ d dlmZmZ d dlmZmZmZm Z  d dl!m!Z!m"Z" d dl#Z#d dl$Z$d dl%Z%d dl&Z'd dl(Z( e(j)        d	�  �         ej*        j+        �,                    d �  �         ej*        j+        �-                    d �  �          e. e$j/        �   �         �  �        e$j0        d
<    e. e$j/        �   �         �  �        e$j0        d<    e. e$j/        �   �         �  �        e$j0        d<   d
ddddddddddddd�
Z1 G d� d�  �        Z2 G d� d�  �        Z3 G d� d�  �        Z4 G d � d!�  �        Z5 G d"� d#�  �        Z6 G d$� d%�  �        Z7d&� Z8e9d'k    r e8�   �          dS dS )(�    N)�
Sequential)�LSTM�Dense�Dropout)�
EarlyStopping�ModelCheckpoint�ReduceLROnPlateau)�Adam)�MinMaxScaler�RobustScaler)�mean_squared_error�mean_absolute_error�r2_score�mean_absolute_percentage_error)�datetime�	timedelta�ignore�OMP_NUM_THREADS�TF_NUM_INTEROP_THREADS�TF_NUM_INTRAOP_THREADSzBTC-USD�90d�1hg333333�?�   i   �2   �   �speed_optimized�saved_modelsF�d   )
�symbol�data_period�
data_interval�train_split�	look_back�future_steps�
batch_size�epochs�patience�
model_type�save_dir�use_multiple_sources�monte_carlo_simulationsc                   �:   � e Zd Zed� �   �         Zed� �   �         ZdS )�AdvancedDataSourcec                  �D  � 	 t          d�  �         t          j        t          d         t          d         t          d         d��  �        } | j        rt          d�  �         dS t          | j        t          j        �  �        rd	� | j        D �   �         | _        d
ddd
ddd�}| �	                    |��  �        } d| j        v r| �
                    dd��  �        } t          dt          | �  �        � d��  �         | S # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)z%Optimierte Yahoo Finance Datenabfrageu    📊 Lade Yahoo Finance Daten...r   r    r!   F)�period�interval�progressu&   ❌ Keine Yahoo Finance Daten erhaltenNc                 �   � g | ]
}|d          ��S )r   � )�.0�cols     �'e:\Dev\btc_ultimate_prediction_model.py�
<listcomp>z:AdvancedDataSource.fetch_yahoo_finance.<locals>.<listcomp>=   s   � �?�?�?�3��A��?�?�?�    �open�high�low�close�	adj_close�volume)�Open�High�Low�Closez	Adj Close�Volume��columns�   ��axisu   ✅ Yahoo Finance: � Datenpunkteu   ❌ Yahoo Finance Fehler: )
�print�yf�download�CONFIG�empty�
isinstancerE   �pd�
MultiIndex�rename�drop�len�	Exception)�data�column_mapping�es      r6   �fetch_yahoo_financez&AdvancedDataSource.fetch_yahoo_finance.   sX  � �"	��4�5�5�5��;�v�h�/��}�8M�&,�_�&=��O� O� O�D� �z� 
��>�?�?�?��t� �$�,��
�6�6� 
@�?�?�$�,�?�?�?��� ��� �(�"�
� �N� �;�;�~�;�6�6�D� �d�l�*�*��y�y��1�y�5�5���?��D�	�	�?�?�?�@�@�@��K��� 	� 	� 	��2�q�2�2�3�3�3��4�4�4�4�4�����	���s   �AC8 �"BC8 �8
D�D�Dc                  ��  � 	 t          d�  �         d} dt          t          d         �                    dd�  �        �  �        dd�}t	          d	�  �        D ]e}	 t          j        | |d
��  �        }|�                    �   �          |�                    �   �         } n"#  |dk    r� t          j
        d�  �         Y �cxY w|rd
|vrdS t          j        |d
         ddg��  �        }t          j
        |d         d��  �        |d<   |�                    dd��  �         t          j        |d         ddg��  �        }t          j
        |d         d��  �        |d<   |�                    dd��  �         |�                    |�  �        }|d         �                    d�  �        |d<   |d         t"          j        �                    ddt)          |�  �        �  �        z  |d<   |d         t"          j        �                    ddt)          |�  �        �  �        z  |d<   |�                    d��  �         t          d t)          |�  �        � d!��  �         |S # t,          $ r}t          d"|� ��  �         Y d}~dS d}~ww xY w)#zCoinGecko API mit Retry-Logiku   📊 Lade CoinGecko Daten...z;https://api.coingecko.com/api/v3/coins/bitcoin/market_chart�usdr    �d� �hourly)�vs_currency�daysr0   �   �   )�params�timeout�   �pricesN�	timestampr<   rD   �ms)�unitT��inplace�
total_volumesr>   rF   r9   gj�t��?g)\���(�?r:   g�G�z��?g+�����?r;   u   ✅ CoinGecko: rI   u   ❌ CoinGecko Fehler: )rJ   �intrM   �replace�range�requests�get�raise_for_status�json�time�sleeprP   �	DataFrame�to_datetime�	set_index�join�shift�np�random�uniformrT   �dropnarU   )�urlrc   �attempt�responserV   �df�volumesrX   s           r6   �fetch_coingeckoz"AdvancedDataSource.fetch_coingeckoU   s�  � �,	��0�1�1�1�O�C�$��F�=�1�9�9�#�r�B�B�C�C�$�� �F� !��8�8� 	
"� 	
"��"�'�|�C���K�K�K�H��-�-�/�/�/�#�=�=�?�?�D��E��"��!�|�|���J�q�M�M�M�M�M����� 
�8�4�/�/��t� ��d�8�n�{�G�6L�M�M�M�B� �n�R��_�4�H�H�H�B�{�O��L�L��d�L�3�3�3� �l�4��#8�;�PX�BY�Z�Z�Z�G�#%�>�'�+�2F�T�#R�#R�#R�G�K� ����k�4��8�8�8�����!�!�B� �G��*�*�1�-�-�B�v�J��G��r�y�'8�'8���c�"�g�g�'N�'N�N�B�v�J��7��b�i�&7�&7��e�S��W�W�&M�&M�M�B�u�I��I�I�d�I�#�#�#��9�C��G�G�9�9�9�:�:�:��I��� 	� 	� 	��.�1�.�.�/�/�/��4�4�4�4�4�����	���s7   �AI	 �?B�I	 �B7�5I	 �FI	 �	
I0�I+�+I0N)�__name__�
__module__�__qualname__�staticmethodrY   r�   r3   r8   r6   r-   r-   -   sI   � � � � � ��$� $� �\�$�L �.� .� �\�.� .� .r8   r-   c                   �,  � e Zd Zed� �   �         Zed� �   �         Zed� �   �         Zed� �   �         Zed� �   �         Zed� �   �         Z	ed� �   �         Z
ed� �   �         Zed	� �   �         Zed
� �   �         Z
ed� �   �         Zed� �   �         Zed
� �   �         ZdS )�AdvancedTechnicalIndicatorsc                 �  � | �                     �   �         } t          �                    | �  �        } t          �                    | �  �        } t          �                    | �  �        } t          �                    | �  �        } t          �                    | �  �        } t          �                    | �  �        } t          �                    | �  �        } t          �	                    | �  �        } t          �
                    | �  �        } t          �                    | �  �        } t          �                    | �  �        } | �
                    �   �         S )z!Umfassende technische Indikatoren)�copyr�   �add_moving_averages�add_macd�add_adx�add_rsi�add_stochastic�add_williams_r�add_bollinger_bands�add_atr�add_keltner_channels�add_volume_indicators�add_price_patternsr~   )r�   s    r6   �add_comprehensive_indicatorsz8AdvancedTechnicalIndicators.add_comprehensive_indicators�   s�   � � �W�W�Y�Y�� )�
<�
<�R�
@�
@��
(�
1�
1�"�
5�
5��
(�
0�
0��
4�
4�� )�
0�
0��
4�
4��
(�
7�
7��
;�
;��
(�
7�
7��
;�
;�� )�
<�
<�R�
@�
@��
(�
0�
0��
4�
4��
(�
=�
=�b�
A�
A�� )�
>�
>�r�
B�
B�� )�
;�
;�B�
?�
?���y�y�{�{�r8   c                 �  � dD ]6}| d         �                     |��  �        �                    �   �         | d|� �<   �7dD ]6}| d         �                    |��  �        �                    �   �         | d|� �<   �7| d         | d         z  �                    d	�  �        �                    �   �         | d         �                    d	�  �        �                    �   �         z  | d
<   | S )zErweiterte Moving Averages)�	   �   �   �   r   r<   ��span�ema_)�   r   r   ��   ��window�sma_r>   r�   �vwma_20)�ewm�mean�rolling�sum�r�   r/   s     r6   r�   z/AdvancedTechnicalIndicators.add_moving_averages�   s�   � � *� 	F� 	F�F�"$�W�+�/�/�v�/�">�">�"C�"C�"E�"E�B��f����� )� 	L� 	L�F�"$�W�+�"5�"5�V�"5�"D�"D�"I�"I�"K�"K�B��f����� �G��r�(�|�3�<�<�R�@�@�D�D�F�F��H��I]�I]�^`�Ia�Ia�Ie�Ie�Ig�Ig�g��9�
��	r8   c                 �X  � | d         �                     d��  �        �                    �   �         }| d         �                     d��  �        �                    �   �         }||z
  | d<   | d         �                     d��  �        �                    �   �         | d<   | d         | d         z
  | d<   | S )	zMACD mit Histogramr<   r�   r�   r�   �macdr�   �macd_signal�macd_histogram)r�   r�   )r�   �ema_12�ema_26s      r6   r�   z$AdvancedTechnicalIndicators.add_macd�   s�   � � �G����b��)�)�.�.�0�0���G����b��)�)�.�.�0�0���f�_��6�
��v�J�N�N��N�2�2�7�7�9�9��=��!�&�z�B�}�,=�=�����	r8   c                 �z  � dD ]�}| d         �                     �   �         }|�                    |dk    d�  �        }|�                    |dk     d�  �         }|�                    |��  �        �                    �   �         }|�                    |��  �        �                    �   �         }||z  }ddd|z   z  z
  | d|� �<   ��| S )zRSI mit verschiedenen Perioden)�   r�   r<   r   r�   r   rF   �rsi_)�diff�wherer�   r�   )r�   r/   �delta�gain�loss�avg_gain�avg_loss�rss           r6   r�   z#AdvancedTechnicalIndicators.add_rsi�   s�   � � � 	9� 	9�F��w�K�$�$�&�&�E��;�;�u�q�y�!�,�,�D��K�K���	�1�-�-�-�D��|�|�6�|�2�2�7�7�9�9�H��|�|�6�|�2�2�7�7�9�9�H��H�$�B�"%���B���"8�B��f������	r8   c                 �  � dD ]�}| d         �                     |��  �        �                    �   �         }| d         �                     |��  �        �                    �   �         }||dz  z   | d|� �<   ||dz  z
  | d|� �<   | d|� �         | d|� �         z
  |z  | d|� �<   | d         | d|� �         z
  | d|� �         | d|� �         z
  z  | d|� �<   ��| S )	z*Bollinger Bands mit verschiedenen Perioden)r�   r   r<   r�   re   �	bb_upper_�	bb_lower_�	bb_width_�bb_position_)r�   r�   �std)r�   r/   �smar�   s       r6   r�   z/AdvancedTechnicalIndicators.add_bollinger_bands�   sI  � � � 	K� 	K�F��W�+�%�%�V�%�4�4�9�9�;�;�C��W�+�%�%�V�%�4�4�8�8�:�:�C�'*�c�A�g��B�#�6�#�#�$�'*�c�A�g��B�#�6�#�#�$�(*�+?�v�+?�+?�(@�2�FZ�RX�FZ�FZ�C[�([�_b�'b�B�#�6�#�#�$�+-�g�;��<P��<P�<P�9Q�+Q�VX�Ym�ek�Ym�Ym�Vn�qs�  uI�  AG�  uI�  uI�  rJ�  WJ�  +K�B�&�f�&�&�'�'��	r8   c                 ��  � | d         | d         z
  }| d         | d         �                     �   �         z
  �                    �   �         }| d         | d         �                     �   �         z
  �                    �   �         }t          j        |||gd��  �        }|�                    d��  �        }|�                    d�  �        �                    �   �         | d<   | d         | d         z  dz  | d	<   | S )
zAverage True Ranger:   r;   r<   rF   rG   r�   �atrr   �atr_percent�rz   �absrP   �concat�maxr�   r�   �r�   �high_low�
high_close�	low_close�ranges�
true_ranges         r6   r�   z#AdvancedTechnicalIndicators.add_atr�   s�   � � �f�:��5�	�)����j�2�g�;�#4�#4�#6�#6�6�;�;�=�=�
���Y��G��!2�!2�!4�!4�4�9�9�;�;�	���H�j�)�<�1�E�E�E���Z�Z�Q�Z�'�'�
��&�&�r�*�*�/�/�1�1��5�	��u�I��7��3�c�9��=���	r8   c                 �H  � | d         �                     d�  �        �                    �   �         }| d         �                     d�  �        �                    �   �         }d| d         |z
  ||z
  z  z  | d<   | d         �                     d�  �        �                    �   �         | d<   | S )	zStochastic Oscillatorr;   r�   r:   r   r<   �stoch_kra   �stoch_d)r�   �minr�   r�   )r�   �low_14�high_14s      r6   r�   z*AdvancedTechnicalIndicators.add_stochastic�   s�   � � �E��"�"�2�&�&�*�*�,�,���V�*�$�$�R�(�(�,�,�.�.����7��f� 4��6�9I�J�K��9�
��9�
�-�-�a�0�0�5�5�7�7��9�
��	r8   c                 ��   � | d         �                     d�  �        �                    �   �         }| d         �                     d�  �        �                    �   �         }d|| d         z
  ||z
  z  z  | d<   | S )zWilliams %Rr:   r�   r;   i����r<   �
williams_r)r�   r�   r�   )r�   r�   r�   s      r6   r�   z*AdvancedTechnicalIndicators.add_williams_r�   sq   � � �V�*�$�$�R�(�(�,�,�.�.���E��"�"�2�&�&�*�*�,�,���G�b��k�$9�g��>N�#O�P��<���	r8   c                 �p  � | d         �                     �   �         }| d         �                     �   �         }|�                    ||k    |dk    z  d�  �        }| �                    ||k    |dk    z  d�  �        }t          �                    | �  �        }d|�                    d�  �        �                    �   �         |z  z  }d|�                    d�  �        �                    �   �         |z  z  }d||z
  �                    �   �         z  ||z   z  }|�                    d�  �        �                    �   �         | d<   | S )zAverage Directional Indexr:   r;   r   r   r�   �adx)r�   r�   r�   �
calculate_atrr�   r�   r�   )	r�   �	high_diff�low_diff�plus_dm�minus_dmr�   �plus_di�minus_di�dxs	            r6   r�   z#AdvancedTechnicalIndicators.add_adx�   s  � � �v�J�O�O�%�%�	��e�9�>�>�#�#���/�/�9�x�#7�I��M�"J�A�N�N���I�$�$�h��&:�x�!�|�%L�a�P�P��)�7�7��;�;�������,�,�1�1�3�3�c�9�:���(�*�*�2�.�.�3�3�5�5��;�<��
�G�h�&�+�+�-�-�
-��8�1C�
D���J�J�r�N�N�'�'�)�)��5�	��	r8   c                 ��   � | d         �                     d��  �        �                    �   �         }t          �                    | �  �        }|d|z  z   | d<   |d|z  z
  | d<   | S )zKeltner Channelsr<   r�   r�   re   �
keltner_upper�
keltner_lower)r�   r�   r�   r�   )r�   �ema_20r�   s      r6   r�   z0AdvancedTechnicalIndicators.add_keltner_channels  se   � � �G����b��)�)�.�.�0�0��)�7�7��;�;��$��C��0��?��$��C��0��?���	r8   c                 ��  � dg}t          dt          | �  �        �  �        D ]�}| d         j        |         | d         j        |dz
           k    r0|�                    |d         | d         j        |         z   �  �         �]| d         j        |         | d         j        |dz
           k     r0|�                    |d         | d         j        |         z
  �  �         ��|�                    |d         �  �         ��|| d<   | d         �                    d��  �        d	z  | d
<   | d         | d         z
  | d         | d         z
  z
  | d         | d         z
  z  }|| d         z  �                    �   �         | d
<   | S )zVolumen-Indikatorenr   rF   r<   �����r>   �obvr�   ��periodsr   �
volume_rocr;   r:   �ad_line)ro   rT   �iloc�append�
pct_change�cumsum)r�   r�   �i�clvs       r6   r�   z1AdvancedTechnicalIndicators.add_volume_indicators  sr  � � �c���q�#�b�'�'�"�"� 	$� 	$�A��'�{���"�R��[�%5�a��c�%:�:�:��
�
�3�r�7�R��\�%6�q�%9�9�:�:�:�:��G��!�!�$�r�'�{�'7��!��'<�<�<��
�
�3�r�7�R��\�%6�q�%9�9�:�:�:�:��
�
�3�r�7�#�#�#�#���5�	� �h�<�2�2�2�2�>�>��D��<�� �7��b��i�'�B�v�J��G��,D�E�"�V�*�WY�Z_�W`�J`�a���r�(�|�+�3�3�5�5��9�
��	r8   c                 �  � dD ]'}| d         �                     |��  �        dz  | d|� �<   �(| d         | d         �                    d�  �        z
  | d<   | d         �                     �   �         �                    d�	�  �        �                    �   �         dz  | d
<   | d         | d         z  | d
<   | d         | d         z  | d<   | S )zPreis-Pattern Indikatoren)r�   r   r<   r�   r   �
price_roc_�
   �momentumr�   r�   �
volatilityr:   r;   �high_low_ratior9   �close_open_ratio)r�   rz   r�   r�   r�   s     r6   r�   z.AdvancedTechnicalIndicators.add_price_patterns#  s�   � � � 	U� 	U�F�(*�7��(>�(>�v�(>�(N�(N�QT�(T�B�$�F�$�$�%�%� �G��r�'�{�'8�'8��'<�'<�<��:�� �g�;�1�1�3�3�;�;�2�;�F�F�J�J�L�L�s�R��<��  "�&�z�B�u�I�5����!#�G��r�&�z�!9�����	r8   c                 �  � | d         | d         z
  }| d         | d         �                     �   �         z
  �                    �   �         }| d         | d         �                     �   �         z
  �                    �   �         }t          j        |||gd��  �        }|�                    d��  �        }|�                    d�  �        �                    �   �         S )zATR Berechnung Hilfsfunktionr:   r;   r<   rF   rG   r�   r�   r�   s         r6   r�   z)AdvancedTechnicalIndicators.calculate_atr6  s�   � � �f�:��5�	�)����j�2�g�;�#4�#4�#6�#6�6�;�;�=�=�
���Y��G��!2�!2�!4�!4�4�9�9�;�;�	���H�j�)�<�1�E�E�E���Z�Z�Q�Z�'�'�
��!�!�"�%�%�*�*�,�,�,r8   N)r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r3   r8   r6   r�   r�   �   sq  � � � � � ��� � �\��6 �
� 
� �\�
� �� � �\�� �
� 
� �\�
� �	� 	� �\�	� �	� 	� �\�	� �� � �\�� �� � �\�� �
� 
� �\�
� �� � �\�� �� � �\��, �� � �\��$ �-� -� �\�-� -� -r8   r�   c                   �,   � e Zd Zd� Zd� Zd� Zd� Zd� ZdS )�AdvancedDataProcessorc                 �0   � d | _         d | _        d | _        d S �N)�feature_scaler�
target_scaler�feature_columns��selfs    r6   �__init__zAdvancedDataProcessor.__init__A  s    � �"���!���#����r8   c                 �`  �� t          d�  �         t          �                    �   �         }t          �                    �   �         }|�p|�n|j        �                    |j        �  �        }|j        |         }|j        |         }t          j        |��  �        �dD ]}d||         z  d||         z  z   �|<   �n>|�|�n9|�|�n4t          d�  �         | �	                    �   �         ���t          d�  �        �t          d	�  �         t          �                    ��  �        �g d
�}�fd�|D �   �         | _
        t          dt          | j
        �  �        � d
��  �         �| j
                 j        �                    d�  �        }�d         j        �                    dd�  �        �                    d�  �        }	t%          �   �         | _        t)          d��  �        | _        | j        �                    |�  �        }
| j        �                    |	�  �        }t/          t          |
�  �        t0          d         z  �  �        }| �                    |
d|�         |d|�         �  �        \  }
}| �                    |
|d�         ||d�         �  �        \  }}t          d|
j        � d|j        � ��  �         |
|||�|fS )z.Erweiterte Datenvorbereitung mit lokalen Datenu   🔄 Bereite Daten vor...N)�index)r9   r:   r;   r<   r>   �ffffff�?�333333�?u7   ⚠️  APIs nicht verfügbar, verwende lokale Daten...u   ❌ Keine Daten verfügbar!u'   📈 Berechne technische Indikatoren...)r9   r:   r;   r<   r>   �ema_9�ema_21�sma_20r�   r�   �rsi_14�bb_width_20r�   r�   c                 �&   �� g | ]
}|�j         v �|��S r3   rD   )r4   r5   r�   s     �r6   r7   z6AdvancedDataProcessor.prepare_data.<locals>.<listcomp>r  s%   �� �T�T�T��#���BS�BS��BS�BS�BSr8   u   📊 Verwende z	 Features�float32r<   r�   rF   )r   rF   )�
feature_ranger"   u   ✅ Daten vorbereitet: Train z, Test )rJ   r-   rY   r�   r  �intersection�locrP   rv   �load_local_data�
ValueErrorr�   r�   r  rT   �values�astype�reshaper   r   r   r  �
fit_transformrm   rM   �create_sequences�shape)r  �df_yahoo�df_coingecko�common_index�
df_yahoo_sync�df_coingecko_syncr5   r  �feature_data�target_data�feature_scaled�
target_scaled�
train_size�X_train�y_train�X_test�y_testr�   s                    @r6   �prepare_dataz"AdvancedDataProcessor.prepare_dataF  s�  �� �
�)�*�*�*� &�9�9�;�;��)�9�9�;�;�� ��L�$<�#�>�6�6�|�7I�J�J�L�$�L��6�M� ,� 0�� >�� ��L�1�1�1�B�A� 
R� 
R���
�c� 2�2�S�;L�S�;Q�5Q�Q��3���
R�
�
!��B�B�
�
%��B�B� 
�K�L�L�L��%�%�'�'�B��z� �!>�?�?�?� 	�7�8�8�8�
(�
E�
E�b�
I�
I��
� 
� 
��  U�T�T�T��T�T�T���
�C�s�4�#7�8�8�C�C�C�D�D�D� �$�.�/�6�=�=�i�H�H����k�(�0�0��Q�7�7�>�>�y�I�I�� +�n�n���)��?�?�?����,�:�:�<�H�H���*�8�8��E�E�
� ��^�,�,�v�m�/D�D�E�E�
�  �0�0��;�J�;�'��{�
�{�)C�
� 
���� �.�.��:�;�;�'��z�{�{�)C�
� 
���� 	�R�g�m�R�R�F�L�R�R�S�S�S������Z�?�?r8   c                 �\  � g g }}t          t          |�  �        t          d         z
  �  �        D ]V}|�                    |||t          d         z   �         �  �         |�                    ||t          d         z            �  �         �Wt	          j        |�  �        t	          j        |�  �        fS )u   Sequenzen für LSTM erstellenr#   )ro   rT   rM   r�   r{   �array)r  �features�targets�X�yr�   s         r6   r  z&AdvancedDataProcessor.create_sequences�  s�   � ��2�1���s�8�}�}�v�k�':�:�;�;� 	7� 	7�A�
�H�H�X�a��F�;�$7� 7�7�8�9�9�9�
�H�H�W�Q���!4�4�5�6�6�6�6��x��{�{�B�H�Q�K�K�'�'r8   c                 ��  � 	 t          d�  �         t          j        d�  �        }t          j        |d         �  �        |d<   |�                    dd��  �         t          dt          |�  �        � d��  �         t          d|j        d	         � d
|j        d         � ��  �         t          d|d
         j        d         d���  �         |S # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)z"Lade lokale CSV-Daten als Fallbacku-   📊 Lade lokale Daten aus crypto_data.csv...zcrypto_data.csvrt   Trj   u   ✅ Lokale Daten geladen: rI   z
   Zeitraum: r   z bis r�   z   Aktueller Preis: $r<   �.2fu%   ❌ Fehler beim Laden lokaler Daten: N)	rJ   rP   �read_csvrw   rx   rT   r  r�   rU   )r  r�   rX   s      r6   r  z%AdvancedDataProcessor.load_local_data�  s  � �	��A�B�B�B���.�/�/�B� ���6�
�3�3�B�v�J��L�L���L�.�.�.��D�s�2�w�w�D�D�D�E�E�E��B�"�(�1�+�B�B�B�H�R�L�B�B�C�C�C��D�"�W�+�*:�2�*>�D�D�D�E�E�E��I��� 	� 	� 	��=�!�=�=�>�>�>��4�4�4�4�4�����	���s   �CC
 �

C1�C,�,C1c                 �6   � | j         �                    |�  �        S )u%   Target-Skalierung rückgängig machen)r  �inverse_transform)r  rV   s     r6   �inverse_transform_targetz.AdvancedDataProcessor.inverse_transform_target�  s   � ��!�3�3�D�9�9�9r8   N)r�   r�   r�   r  r*  r  r  r6  r3   r8   r6   r�   r�   @  sf   � � � � � �$� $� $�
H@� H@� H@�T(� (� (�� � �&:� :� :� :� :r8   r�   c                   �    � e Zd Zd� Zd� Zd� ZdS )�AdvancedModelBuilderc                 �   � d | _         d S r�   )�modelr  s    r6   r  zAdvancedModelBuilder.__init__�  s
   � ���
�
�
r8   c                 �8  � t          t          ddd|��  �        t          ddd��  �        t          dd�	�  �        t          d
�  �        t          dd�	�  �        t          dd
�	�  �        g�  �        }t	          d��  �        }|�                    |ddg��  �         |S )zSpeed-optimiertes Modell�@   T皙�����?)�return_sequences�dropout�input_shape�    F)r>  r?  �relu)�
activationr	  �   rF   �linearg����Mb`?)�
learning_rate�mse�mae)�	optimizerr�   �metrics)r   r   r   r   r
   �compile)r  r@  r:  rI  s       r6   �build_hybrid_advanced_modelz0AdvancedModelBuilder.build_hybrid_advanced_model�  s�   � ����d�C�[�Q�Q�Q� 
��e�S�9�9�9� 
�"��(�(�(��C�L�L��"��(�(�(��!��)�)�)�
� � �� �u�-�-�-�	�
�
�
����G� 	� 	
� 	
� 	
� �r8   c           
      �  � t          j        t          d         d��  �         t          j        �   �         �                    d�  �        }t           j        �                    t          d         d|� d��  �        }t          dt          d         dd	d
��  �        t          |dddd	�
�  �        t          ddddd	d��  �        g}||fS )zErweiterte Callbacksr)   T)�exist_ok�
%Y%m%d_%H%M%S�
btc_ultimate_z.h5�val_lossr'   rF   g-C��6?)�monitorr'   �restore_best_weights�verbose�	min_deltaF)rR  �save_best_only�save_weights_onlyrT  r  r   g�h㈵��>ra   )rR  �factorr'   �min_lrrT  �cooldown)�os�makedirsrM   r   �now�strftime�pathry   r   r   r	   )r  rg   �
model_path�	callbackss       r6   �get_advanced_callbacksz+AdvancedModelBuilder.get_advanced_callbacks�  s�   � �
��F�:�&��6�6�6�6��L�N�N�+�+�O�<�<�	��W�\�\�&��"4�6T�i�6T�6T�6T�U�U�
� 
�"��
�+�%)�� �
� 
� 
� 
��"�#�"'��
� 
� 
� 
�"������

� 
� 
�
�	�2 �*�$�$r8   N)r�   r�   r�   r  rL  rb  r3   r8   r6   r8  r8  �  sA   � � � � � �� � �� � �6 %�  %�  %�  %�  %r8   r8  c                   �,   � e Zd Zd� Zd� Zd� Zd� Zd� ZdS )�AdvancedPredictorc                 �"   � || _         || _        d S r�   )r:  �	processor)r  r:  rf  s      r6   r  zAdvancedPredictor.__init__�  s   � ���
�"����r8   c           	      �$  � t          d�  �         t          d         }t          d         }g }t          |�  �        D ]�}|dz  dk    rt          d|dz   � d|� ��  �         |�                    �   �         }g }t          |�  �        D ]�}| j        �                     |j        dg|j        �R � d�	�  �        d
         }	|�                    |	�  �         | �	                    ||	|�  �        }
t          j        |dd�         |
�                    dd�  �        g�  �        }��|�                    |�  �         ��t          j        |�  �        }t          j
        |d�
�  �        }t          j        |d�
�  �        }t          j        |dd�
�  �        }
t          j        |dd�
�  �        }t          j        |dd�
�  �        }t          j        |dd�
�  �        }| j        �                    |�                    dd�  �        �  �        �                    �   �         }| j        �                    |
�                    dd�  �        �  �        �                    �   �         }| j        �                    |�                    dd�  �        �  �        �                    �   �         }| j        �                    |�                    dd�  �        �  �        �                    �   �         }| j        �                    |�                    dd�  �        �  �        �                    �   �         }||||||d�S )z<Erweiterte Zukunftsprognose mit Unsicherheitsquantifizierungu"   🔮 Erstelle Zukunftsprognosen...r+   r$   r   r   z   Simulation rF   �/�rT  )r   r   Nr�   rG   g     `X@g      @�Z   r�   )r�   �upper_95�lower_95�upper_80�lower_80r�   )rJ   rM   ro   r�   r:  �predictr  r  r�   �generate_next_featuresr{   �vstackr,  r�   r�   �
percentilerf  r6  �flatten)r  �
last_sequence�
n_simulationsr$   �all_predictions�sim�current_seq�sim_predictions�step�pred�new_features�	mean_pred�std_pred�confidence_95_upper�confidence_95_lower�confidence_80_upper�confidence_80_lower�mean_pred_rescaled�upper_95_rescaled�lower_95_rescaled�upper_80_rescaled�lower_80_rescaleds                         r6   �predict_future_with_uncertaintyz1AdvancedPredictor.predict_future_with_uncertainty�  s  � �
�2�3�3�3��8�9�
��n�-���� ��'�'� 	4� 	4�C��S�y�A�~�~��>�s�1�u�>�>�}�>�>�?�?�?�'�,�,�.�.�K� �O��l�+�+� 

X� 

X���z�)�)�*=�+�*=�a�*T�+�BS�*T�*T�*T�^_�)�`�`�ae�f���&�&�t�,�,�,�  $�:�:�;��d�S�S�� !�i��Q�R�R��,�:N�:N�q�RT�:U�:U�(V�W�W����"�"�?�3�3�3�3� �(�?�3�3���G�O�!�4�4�4�	��6�/��2�2�2�� !�m�O�T��J�J�J�� �m�O�S�q�I�I�I�� �m�O�R�a�H�H�H�� �m�O�R�a�H�H�H�� "�^�D�D�Y�EV�EV�WY�[\�E]�E]�^�^�f�f�h�h�� �N�C�C�DW�D_�D_�`b�de�Df�Df�g�g�o�o�q�q�� �N�C�C�DW�D_�D_�`b�de�Df�Df�g�g�o�o�q�q�� �N�C�C�DW�D_�D_�`b�de�Df�Df�g�g�o�o�q�q�� �N�C�C�DW�D_�D_�`b�de�Df�Df�g�g�o�o�q�q�� '�)�)�)�)��

� 
� 	
r8   c           	      �  � |d         �                     �   �         }dd|dz  z   z  }t          j        �                    d|�  �        }|d|dz  z   z  }t	          ||�  �        dt          t          j        �                    d|dz  �  �        �  �        z   z  }t
          ||�  �        dt          t          j        �                    d|dz  �  �        �  �        z
  z  }	t          j        �                    dd�  �        }
|d	         d|
z   z  }|�                     �   �         }||d<   ||d<   |	|d
<   ||d<   ||d	<   t          dt          |�  �        �  �        D ]6}
t          j        �                    d|dz  �  �        }||
         d|z   z  ||
<   �7|S )
u4   Generiere nächste Features basierend auf Vorhersager�   g{�G�z�?rF   g����MbP?r   �      �?r	  皙�����?�   re   ra   �   )	r�   r{   r|   �normalr�   r�   r�   ro   rT   )r  rx  �predicted_closerz  �
last_featuresr�   �noise�
open_price�
high_price�	low_price�
volume_changer>   r|  r�   �changes                  r6   rp  z(AdvancedPredictor.generate_next_features/  s�  � �#�B��,�,�.�.�
� �Q����-�.�
� �	� � ��J�/�/��$��E�C�K��8�
���_�5�5��S���AQ�AQ�RS�U_�be�Ue�Af�Af�=g�=g�9g�h�
��
�O�4�4��C��	�@P�@P�QR�T^�ad�Td�@e�@e�<f�<f�8f�g�	� �	�(�(��C�0�0�
��q�!�Q��%6�7�� %�)�)�+�+��$��Q��$��Q��#��Q��)��Q�� ��Q�� �q�#�l�+�+�,�,� 	=� 	=�A��Y�%�%�a��c�)9�:�:�F�*�1�o��V��<�L��O�O��r8   c                 �  � t          d�  �         | j        �                    |d��  �        }| j        �                    |d��  �        }| j        �                    |�  �        }| j        �                    |�  �        }| j        �                    |�  �        }	| j        �                    |�  �        }
t          j        t          ||	�  �        �  �        t          j        t          ||
�  �        �  �        t          ||	�  �        t          ||
�  �        t          ||	�  �        t          ||
�  �        t          ||	�  �        t          ||
�  �        d�}| �                    ||	�  �        }| �                    ||
�  �        }
||d<   |
|d<   ||||	|
fS )zUmfassende Modellevaluationu   📊 Evaluiere Modell...r   ri  )�
train_rmse�	test_rmse�	train_mae�test_mae�train_r2�test_r2�
train_mape�	test_mape�train_direction_accuracy�test_direction_accuracy)rJ   r:  ro  rf  r6  r{   �sqrtr
   r   r   r   �calculate_direction_accuracy)r  r&  r'  r(  r)  �
train_pred�	test_pred�y_train_orig�y_test_orig�train_pred_orig�test_pred_origrJ  �train_direction_acc�test_direction_accs                 r6   �evaluate_model_comprehensivez.AdvancedPredictor.evaluate_model_comprehensiveP  s~  � �
�(�)�)�)� �Z�'�'���'�;�;�
��J�&�&�v�q�&�9�9�	� �~�>�>�w�G�G���n�=�=�f�E�E���.�A�A�*�M�M����@�@��K�K�� �'�"4�\�?�"S�"S�T�T���!3�K��!P�!P�Q�Q�,�\�?�K�K�+�K��H�H� ���?�?���^�<�<�8���W�W�7��^�T�T�	
� 	
�� #�?�?��o�^�^��!�>�>�{�N�[�[��.A��*�+�-?��)�*���k�?�N�R�Rr8   c                 ��   � t          j        |�                    �   �         �  �        dk    }t          j        |�                    �   �         �  �        dk    }t          j        ||k    �  �        dz  S )zBerechne Richtungsgenauigkeitr   r   )r{   r�   rs  r�   )r  �y_true�y_pred�true_direction�pred_directions        r6   r�  z.AdvancedPredictor.calculate_direction_accuracys  sX   � ������!1�!1�2�2�Q�6�������!1�!1�2�2�Q�6���w�~��7�8�8�3�>�>r8   N)r�   r�   r�   r  r�  rp  r�  r�  r3   r8   r6   rd  rd  �  se   � � � � � �#� #� #�8
� 8
� 8
�t� � �B!S� !S� !S�F?� ?� ?� ?� ?r8   rd  c                   �:   � e Zd Zd� Zd	d�Zd� Zd� Zd� Zd� Zd� Z	dS )
�AdvancedVisualizerc                 �   � t           j        �                    d�  �         dt           j        d<   dt           j        d<   d S )N�default)�   r�   zfigure.figsizer�   z	font.size)�plt�style�use�rcParamsr  s    r6   r  zAdvancedVisualizer.__init__z  s5   � ��	�
�
�i� � � �)1���%�&�$&���[�!�!�!r8   Nc                 �>  � t          j        d��  �         t          j        ddd�  �        }| �                    |||||�  �         t          j        ddd�  �        }| �                    ||�  �         t          j        ddd�  �        }| �                    ||�  �         t          j        ddd�  �        }	|r| �                    |	|�  �         t          j        ddd�  �        }
| �                    |
|�  �         t          j        �   �          t          j	        �   �          d	S )
z!Umfassende Ergebnisvisualisierung)r�   rD  )�figsizera   re   )rF   re   r�  r�  �   N)
r�  �figure�subplot�plot_price_predictions�plot_training_metrics�plot_residuals�plot_future_detail�plot_metrics_overview�tight_layout�show)r  r�   r%  rJ  �predictions�future_pred�ax1�ax2�ax3�ax4�ax5s              r6   �plot_comprehensive_resultsz-AdvancedVisualizer.plot_comprehensive_results  s  � ��
�8�$�$�$�$� �k�!�Q��'�'���#�#�C��Z��k�R�R�R� �k�!�Q��"�"���"�"�3��0�0�0� �k�!�Q��"�"�����C��-�-�-� �k�!�Q��"�"��� 	6��#�#�C��5�5�5� �k�!�Q��"�"���"�"�3��0�0�0��������
�
�
�
�
r8   c                 �  � |j         }|d         j        }|d|�         }||d�         }	|d|�         }
|d         |d         }}|�                    ||
dddd�	�  �         |�                    |	t          |�  �         d�         |�                    �   �         d
dd�
�  �         |�                    |	t          |�  �         d�         |�                    �   �         dddd��  �         |r�t          j        |d         t          j        d��  �        z   t          |d         �  �        d��  �        }
|�                    |
|d         ddd�
�  �         |�                    |
|d         |d         ddd��  �         |�                    |
|d         |d         dd d!��  �         |�	                    d"d#d$�%�  �         |�
                    d&�  �         |�                    d'�  �         |�                    �   �          |�
                    d(d �)�  �         |j        �                    t!          j        d*�  �        �  �         |j        �                    t!          j        d+�,�  �        �  �         t)          j        |j        �                    �   �         d-�.�  �         dS )/zHauptpreis-Chartr<   N�test_actualr�  �Training�bluer  rF   )�label�color�alpha�	linewidthz
Test (Actual)�greenre   )r�  r�  r�  zTest (Predicted)�red�--)r�  r�  �	linestyler�  r�   )�hoursr�   �1H)�startr�   �freqzFuture Prediction�purplerl  rk  r=  z95% Confidence)r�  r�  r�  rn  rm  r	  z80% Confidencez)Bitcoin Price Prediction - Ultimate Modelr�   �bold)�fontsize�
fontweight�Date�Price (USD)T�r�  z%Y-%m-%d�   )r0   �-   )�rotation)r  r  �plotrT   rs  rP   �
date_range�	Timedelta�fill_between�	set_title�
set_xlabel�
set_ylabel�legend�grid�xaxis�set_major_formatter�mdates�
DateFormatter�set_major_locator�
DayLocatorr�  �setp�get_majorticklabels)r  �axr�   r%  r�  r�  �datesrf   �train_dates�
test_dates�train_pricesr�  r�  �future_datess                 r6   r�  z)AdvancedVisualizer.plot_price_predictions�  s�  � � ����G��#���K�Z�K�(���:�;�;�'�
��k�z�k�*��&1�-�&@�+�k�BZ�^�� 	����\��6�QT�`a��b�b�b�
���
�C��,�,�,�-�-�.��0C�0C�0E�0E�%�W�� 	� 	C� 	C� 	C�
���
�C��/�/�/�0�0�1�>�3I�3I�3K�3K�(���QR� 	� 	T� 	T� 	T� � 	N��=��B�i�"�,�Q�"7�"7�"7�7��K��/�0�0��� � �L� 
�G�G�L�+�f�"5�,�H�� 
� 
K� 
K� 
K��O�O�L�+�j�*A�;�z�CZ�!)��<L� 
� 
N� 
N� 
N��O�O�L�+�j�*A�;�z�CZ�!)��<L� 
� 
N� 
N� 
N� 	���@�2�Z`��a�a�a�
�
�
�f����
�
�
�m�$�$�$�
�	�	����
����C�� � � � 	��$�$�V�%9�*�%E�%E�F�F�F�
��"�"�6�#4�a�#@�#@�#@�A�A�A�����-�-�/�/�"�=�=�=�=�=�=r8   c           	      �P  � d|v r�|d         }t          dt          |j        d         �  �        dz   �  �        }|�                    ||j        d         ddd��  �         |�                    ||j        d         d	d
d��  �         |�                    dd�
�  �         |�                    d�  �         |�                    d�  �         |�                    �   �          |�                    dd��  �         dS |�	                    ddddd|j
        d��  �         |�                    d�  �         dS )�Trainingsmetriken�historyrF   r�   zb-z
Training Lossre   )r�  r�  rQ  zr-zValidation LosszTraining Progressr�  �r�  �Epoch�LossTr	  r�  r�  zTraining History
Not Available�centerr�   )�ha�va�	transformr�  N)ro   rT   r  r�  r�  r�  r�  r�  r�  �text�	transAxes)r  r�  rJ  r  r&   s        r6   r�  z(AdvancedVisualizer.plot_training_metrics�  s0  � ������i�(�G��1�c�'�/�&�"9�:�:�Q�>�?�?�F��G�G�F�G�O�F�3�T��\]�G�^�^�^��G�G�F�G�O�J�7��EV�bc�G�d�d�d��L�L�,��L�@�@�@��M�M�'�"�"�"��M�M�&�!�!�!��I�I�K�K�K��G�G�D��G�$�$�$�$�$��G�G�C��?��8�r�|�b� 
� 
R� 
R� 
R��L�L�,�-�-�-�-�-r8   c                 �  � |d         �                     �   �         }|d         �                     �   �         }||z
  }|�                    ||dd��  �         |�                    ddd�	�  �         |�                    d d
��  �         |�                    d�  �         |�                    d
�  �         |�                    dd��  �         dS )zResiduals Plotr�  r�  g333333�?r�   )r�  �sr   r�  r�  )r0  r�  r�  r�  r  zPredicted Values�	ResidualsTr	  r�  N)rs  �scatter�axhliner�  r�  r�  r�  )r  r�  r�  r)  r�  �	residualss         r6   r�  z!AdvancedVisualizer.plot_residuals�  s�   � ��]�+�3�3�5�5���[�)�1�1�3�3���V�O�	�
�
�
�6�9�C�2�
�6�6�6�
�
�
�Q�e�t�
�4�4�4�
���%�&��9�9�9�
�
�
�(�)�)�)�
�
�
�k�"�"�"�
����C�� � � � � r8   c                 �  � t          dt          |d         �  �        dz   �  �        }|�                    ||d         ddd��  �         |�                    ||d         |d         d	dd
��  �         |�                    ||d         |d
         ddd��  �         |�                    dd��  �         |�                    d�  �         |�                    d�  �         |�                    �   �          |�                    dd��  �         dS )zDetaillierte ZukunftsprognoserF   r�   r�  re   �
Prediction)r�  r�  rl  rk  r=  z95% CI)r�  r�  r�  rn  rm  r	  z80% CIz72-Hour Future Predictionr�  r  zHours Aheadr�  Tr�  N)	ro   rT   r�  r�  r�  r�  r�  r�  r�  )r  r�  r�  r�  s       r6   r�  z%AdvancedVisualizer.plot_future_detail�  s  � ��a��[��0�1�1�A�5�6�6��
����{�6�*�H����V�V�V�
����{�:�6��J�8O� ��� 	� 	B� 	B� 	B�
����{�:�6��J�8O� ��� 	� 	B� 	B� 	B� 	���0�V��D�D�D�
�
�
�m�$�$�$�
�
�
�m�$�$�$�
�	�	����
����C�� � � � � r8   c                 ��  � |�                     d�  �         d|�                    dd�  �        d�d|�                    dd�  �        d�d|�                    d	d�  �        d
�d|�                    dd�  �        d�d
|�                    dd�  �        d�d|�                    dd�  �        d�d|�                    dd�  �        d
�d�}|�                    dd||j        dddt	          ddd��  �        ��  �         dS ) u   Metriken Übersicht�offz8
        MODEL PERFORMANCE METRICS

        Test RMSE: $r�  r   r2  z
        Test MAE: $r�  u   
        Test R²: r�  �.4fz
        Test MAPE: r�  z%

        Direction Accuracy: r�  �.1fz%

        Train RMSE: $r�  u   
        Train R²: r�  z	
        r�  g�������?�   �top�	monospace�round�	lightgrayg�������?)�boxstyle�	facecolorr�  )r	  r�  �verticalalignment�
fontfamily�bboxN)rH   rq   r
  r  �dict)r  r�  rJ  �metrics_texts       r6   r�  z(AdvancedVisualizer.plot_metrics_overview�  s[  � �
�������� �[�[��a�0�0�� � � �K�K�
�A�.�.�	� � �
 �;�;�y�!�,�,�� � � �K�K��Q�/�/�
� � � %�[�[�)B�A�F�F�� � � �k�k�,��2�2�� � � �K�K�
�A�.�.�� � � �� 	����S�,�"�,��"'�K��7�k��M�M�M� 	� 	O� 	O� 	O� 	O� 	Or8   r�   )
r�   r�   r�   r  r�  r�  r�  r�  r�  r�  r3   r8   r6   r�  r�  y  s�   � � � � � �'� '� '�
� � � �8+>� +>� +>�Z.� .� .�&!� !� !�!� !� !� O� O� O� O� Or8   r�  c                  ��  � t          d�  �         t          d�  �         t          j        �   �         } 	 t          �   �         }|�                    �   �         \  }}}}}}t          d�  �         t	          �   �         }|�                    |j        d         |j        d         f�  �        }	|�                    �   �         \  }
}t          d�  �         |	�                    �   �          t          d�  �         |	�	                    ||||ft          d         t          d	         |
dd
��  �        }t          |	|�  �        }
|
�                    ||||�  �        \  }}}}}||d<   t          d
�  �         t          d|d         d���  �         t          d|d         d���  �         t          d|d         d���  �         t          d|d         d�d��  �         t          d|d         d�d��  �         t          d�  �         |d         }|
�
                    |�  �        }t          d�  �         t          j        �   �         �                    d�  �        }t#          j        |j        t          d          � d!|� d"��  �         t#          j        |j        t          d          � d#|� d"��  �         t          d$�  �         t+          �   �         }||||d%�}|�                    |||||�  �         t          d&�  �         |d'         j        d         }d(D ]|}|t1          |d)         �  �        k    ra|d)         |dz
           }||z  dz
  d*z  }|d+         |dz
           }|d,         |dz
           }t          d-|� d.|d�d/|d0�d1|d�d2|d�d3��  �         �}t          j        �   �         | z
  }t          d4|d�d5��  �         t          d6|� ��  �         d9S # t2          $ r5}t          d7|� ��  �         d8d9l} | �                    �   �          Y d9}~d9S d9}~ww xY w):z4Hauptfunktion - Ultimatives Bitcoin Prediction Modelu&   🚀 ULTIMATE BITCOIN PREDICTION MODELz2==================================================u-   🏗️  Erstelle fortschrittliches Modell...rF   re   u   📋 Modell-Architektur:u   🎯 Starte Training...r&   r%   T)�validation_datar&   r%   ra  rT  �shuffler  u   
📊 MODELL-PERFORMANCE:zTest RMSE: $r�  r2  zTest MAE: $r�  u
   Test R²: r�  r  zTest MAPE: r�  �%zDirection Accuracy: r�  r  u%   
🔮 Erstelle 72-Stunden Prognose...r�   u#   💾 Speichere Modell und Scaler...rO  r)   z/feature_scaler_z.pklz/target_scaler_u!   📈 Erstelle Visualisierungen...)r�  r�  �train_actualr�  u   
🎯 72-STUNDEN PROGNOSE:r<   )r   �0   �H   r�   r   rl  rk  zIn zh: $z (z+.2fz
%) [95% CI: $z - $�]u   
✅ Fertig! Gesamtzeit: z	 Sekundenu   📁 Modell gespeichert: u   ❌ Fehler: r   N)rJ   rt   r�   r*  r8  rL  r  rb  �summary�fitrM   rd  r�  r�  r   r]  r^  �joblib�dumpr   r  r�  r�  r�   rT   rU   �	traceback�	print_exc)!�
start_timerf  r&  r'  r(  r)  r�   r%  �builderr:  ra  r`  r  �	predictorrJ  r�  r�  r�  r�  rt  �future_predictionsrg   �
visualizer�predictions_dict�
current_pricer�  �
pred_price�
change_pctrl  rk  �
total_timerX   r0  s!                                    r6   �mainr<    s�  � �	�
2�3�3�3�	�(�O�O�O�����J�W�)�+�+�	�;D�;Q�;Q�;S�;S�8���&�&�"�j� 	�=�>�>�>�&�(�(���3�3�W�]�1�5E�w�}�UV�GW�4X�Y�Y�� '� >� >� @� @��	�:�
�)�*�*�*�
�
�
���� 	�'�(�(�(��)�)��W�#�V�,��(�#��l�+���� � 
� 
�� &�e�Y�7�7�	�NW�Nt�Nt��W�f�f�O
� O
�K���{�O�^�
 %��	��
�*�+�+�+�
�7�W�[�1�7�7�7�8�8�8�
�5�G�J�/�5�5�5�6�6�6�
�3�7�9�-�3�3�3�4�4�4�
�7�G�K�0�7�7�7�7�8�8�8�
�N�W�-F�%G�N�N�N�N�O�O�O� 	�6�7�7�7��r�
�
�&�F�F�}�U�U�� 	�3�4�4�4��L�N�N�+�+�O�<�<�	���I�,��
�1C�.d�.d�U^�.d�.d�.d�e�e�e���I�+��z�0B�-b�-b�S\�-b�-b�-b�c�c�c� 	�1�2�2�2�'�)�)�
� '�'�(�)�	
� 
�� 	�-�-�b�*�g�GW�Yk�l�l�l� 	�+�,�,�,��7��(��,�
�!� 	F� 	F�E���.�v�6�7�7�7�7�/��7��a��@�
�)�M�9�Q�>�#�E�
�-�j�9�%��'�B��-�j�9�%��'�B��� E�E� E� E�z�M� E� E��M� E� E�#+�D�E� E�5=�D�E� E� E� F� F� F�� �Y�[�[�:�-�
�
�D�:�D�D�D�D�E�E�E�
�6�*�6�6�7�7�7�7�7��� � � �
� �Q� � �!�!�!������������������������s   �NN8 �8
O7�*O2�2O7�__main__):�numpyr{   �pandasrP   �matplotlib.pyplot�pyplotr�  �matplotlib.datesr�  r�  rp   �
tensorflow�tf�tensorflow.keras.modelsr   �tensorflow.keras.layersr   r   r   �tensorflow.keras.callbacksr   r   r	   �tensorflow.keras.optimizersr
   �sklearn.preprocessingr   r   �sklearn.metricsr
   r   r   r   r   r   rt   r[  r.  �yfinancerK   �warnings�filterwarnings�config�	threading� set_intra_op_parallelism_threads� set_inter_op_parallelism_threads�str�	cpu_count�environrM   r-   r�   r�   r8  rd  r�  r<  r�   r3   r8   r6   �<module>rU     sp  �� � � � � � � � � � � � � � � !� !� !� !� !� !� ���� � � � � .� .� .� .� .� .� 8� 8� 8� 8� 8� 8� 8� 8� 8� 8� X� X� X� X� X� X� X� X� X� X� ,� ,� ,� ,� ,� ,� <� <� <� <� <� <� <� <� m� m� m� m� m� m� m� m� m� m� m� m� (� (� (� (� (� (� (� (� ���� 	�	�	�	� 
�
�
�
� � � � � ���� �� �� !� !� !� �	� � 4� 4�Q� 7� 7� 7� �	� � 4� 4�Q� 7� 7� 7� #��L�B�L�N�N� 3� 3��
�� �'*�s�<�2�<�>�>�':�':��
�#� $�'*�s�<�2�<�>�>�':�':��
�#� $� ���������#��!�"�
� 
�� W� W� W� W� W� W� W� W�rx-� x-� x-� x-� x-� x-� x-� x-�tm:� m:� m:� m:� m:� m:� m:� m:�^?%� ?%� ?%� ?%� ?%� ?%� ?%� ?%�BG?� G?� G?� G?� G?� G?� G?� G?�RTO� TO� TO� TO� TO� TO� TO� TO�l^� ^� ^�@ �z����D�F�F�F�F�F� �r8   