#!/usr/bin/env python3
"""
BITCOIN LAUNCHER ENHANCED VISUALIZATION - OPTIMIERT
==================================================
OPTIMIERTER LAUNCHER MIT ERWEITERTEN VISUALISIERUNGEN
- Verbesserte Effizienz und Performance
- Hochpräzise Preis- und Trend-Anzeige
- Detaillierte Zeitstempel und Preisformatierung
- Erweiterte technische Indikatoren
- Multi-Timeframe-Analyse
- Optimierte Algorithmen

ENHANCED VISUALIZATION - MAXIMALE PRÄZISION!
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import threading
import time
import os
import sys
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
import numpy as np
import pandas as pd
import math
import random

# Matplotlib Konfiguration für hochwertige Visualisierungen
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['figure.dpi'] = 100
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 10
plt.rcParams['axes.titlesize'] = 12
plt.rcParams['axes.labelsize'] = 10
plt.rcParams['xtick.labelsize'] = 8
plt.rcParams['ytick.labelsize'] = 8

class BitcoinLauncherEnhancedVisualization:
    """
    BITCOIN LAUNCHER ENHANCED VISUALIZATION
    ======================================
    Optimierter Launcher mit erweiterten Visualisierungen
    für maximale Präzision und detaillierte Darstellung.
    """
    
    def __init__(self):
        # GUI SETUP
        self.root = tk.Tk()
        self.root.title("Bitcoin Trading Launcher - Enhanced Visualization")
        self.root.geometry("1800x1000")
        self.root.configure(bg='#1a1a1a')
        
        # STYLE KONFIGURATION
        self.setup_enhanced_styles()
        
        # DIE 3 BESTEN MODELLE
        self.models = {
            'favorit': {
                'name': 'FAVORIT - Das Bewaehrte System',
                'file': 'ultimate_complete_bitcoin_trading_FAVORITE_NO_EMOJI.py',
                'description': 'Das bewaehrte System mit kontinuierlichem Lernen',
                'color': '#00ff88',
                'status': 'Bereit',
                'process': None,
                'recommended': True
            },
            'optimized': {
                'name': 'OPTIMIERT - Das Schnelle System',
                'file': 'btc_ultimate_optimized_complete_NO_EMOJI.py',
                'description': 'Das optimierte System fuer schnelle Analysen',
                'color': '#ff6600',
                'status': 'Bereit',
                'process': None,
                'recommended': False
            },
            'simple': {
                'name': 'SIMPLE - Das Zuverlaessige System',
                'file': 'bitcoin_trading_simple_fixed_NO_EMOJI.py',
                'description': 'Das zuverlaessige System ohne Abhaengigkeiten',
                'color': '#3366cc',
                'status': 'Bereit',
                'process': None,
                'recommended': False
            }
        }
        
        # LAUNCHER ZUSTAND
        self.running_processes = {}
        self.script_directory = os.getcwd()
        self.current_data = None
        self.last_update = datetime.now()
        self.update_interval = 5  # Sekunden
        self.precision_mode = True  # Hochpräzisions-Modus
        
        # ERWEITERTE DATEN-SPEICHER
        self.price_history = []
        self.signal_history = []
        self.prediction_history = []
        self.technical_indicators = {}
        
        # GUI KOMPONENTEN ERSTELLEN
        self.create_enhanced_visualization_gui()
        
        # DEMO-DATEN GENERIEREN
        self.generate_enhanced_demo_data()
        
        # AUTO-UPDATE TIMER
        self.setup_auto_update_timer()
        
        print("Bitcoin Trading Launcher mit Enhanced Visualization initialisiert")
        print("Optimierte Algorithmen und detaillierte Visualisierungen")
    
    def setup_enhanced_styles(self):
        """Setup erweiterte GUI-Styles"""
        plt.style.use('dark_background')
        
        # TTK Styles
        style = ttk.Style()
        style.theme_use('clam')
        
        # Custom Styles
        style.configure('Title.TLabel', 
                       background='#1a1a1a', 
                       foreground='#00ff88',
                       font=('Arial', 16, 'bold'))
        
        style.configure('Precision.TCheckbutton',
                       background='#1a1a1a',
                       foreground='#00ff88')
        
        style.map('Precision.TCheckbutton',
                 background=[('active', '#1a1a1a')],
                 foreground=[('active', '#00ff88')])
    
    def create_enhanced_visualization_gui(self):
        """Erstelle erweiterte GUI mit verbesserten Visualisierungen"""
        
        # HAUPTCONTAINER
        main_container = tk.Frame(self.root, bg='#1a1a1a')
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # TITEL
        title_frame = tk.Frame(main_container, bg='#1a1a1a')
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = tk.Label(
            title_frame,
            text="BITCOIN TRADING LAUNCHER - ENHANCED VISUALIZATION",
            font=('Arial', 18, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="Optimierte Algorithmen * Hochpraezise Visualisierungen * Detaillierte Preis- und Trend-Anzeige",
            font=('Arial', 11),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        subtitle_label.pack()
        
        # HAUPTBEREICH - 2 SPALTEN
        content_frame = tk.Frame(main_container, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # LINKE SPALTE - KONTROLLEN
        left_frame = tk.Frame(content_frame, bg='#1a1a1a', width=500)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)
        
        # RECHTE SPALTE - VISUALISIERUNGEN
        right_frame = tk.Frame(content_frame, bg='#1a1a1a')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # ERSTELLE LINKE SPALTE
        self.create_enhanced_control_panel(left_frame)
        
        # ERSTELLE RECHTE SPALTE
        self.create_enhanced_visualization_panel(right_frame)
    
    def create_enhanced_control_panel(self, parent):
        """Erstelle erweitertes Kontroll-Panel"""
        
        # MODELLE BEREICH
        models_frame = tk.LabelFrame(
            parent,
            text="Bitcoin Trading Modelle",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d',
            bd=2,
            relief=tk.RAISED
        )
        models_frame.pack(fill=tk.X, pady=(0, 10))
        
        # MODELL BUTTONS
        for i, (key, model) in enumerate(self.models.items()):
            self.create_enhanced_model_button(models_frame, key, model, i)
        
        # HAUPT-KONTROLLEN
        control_frame = tk.LabelFrame(
            parent,
            text="Erweiterte Kontrollen",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d',
            bd=2,
            relief=tk.RAISED
        )
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # KONTROLL-BUTTONS
        button_frame = tk.Frame(control_frame, bg='#2d2d2d')
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # ALLE STARTEN
        start_all_btn = tk.Button(
            button_frame,
            text="ALLE STARTEN",
            command=self.start_all_models,
            font=('Arial', 11, 'bold'),
            bg='#00aa44',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        start_all_btn.pack(fill=tk.X, pady=(0, 5))
        
        # ALLE STOPPEN
        stop_all_btn = tk.Button(
            button_frame,
            text="ALLE STOPPEN",
            command=self.stop_all_models,
            font=('Arial', 11, 'bold'),
            bg='#cc3333',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        stop_all_btn.pack(fill=tk.X, pady=(0, 5))
        
        # CHARTS AKTUALISIEREN
        refresh_btn = tk.Button(
            button_frame,
            text="CHARTS AKTUALISIEREN",
            command=self.refresh_enhanced_visualizations,
            font=('Arial', 11, 'bold'),
            bg='#ff6600',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        refresh_btn.pack(fill=tk.X, pady=(0, 5))
        
        # PRÄZISIONS-MODUS
        precision_frame = tk.Frame(button_frame, bg='#2d2d2d')
        precision_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.precision_var = tk.BooleanVar(value=self.precision_mode)
        precision_check = ttk.Checkbutton(
            precision_frame,
            text="HOCHPRAEZISIONS-MODUS",
            variable=self.precision_var,
            command=self.toggle_precision_mode,
            style='Precision.TCheckbutton'
        )
        precision_check.pack(side=tk.LEFT)
        
        # ZEITSTEMPEL
        self.timestamp_label = tk.Label(
            button_frame,
            text=f"Letzte Aktualisierung: {self.last_update.strftime('%H:%M:%S.%f')[:-3]}",
            font=('Arial', 8),
            fg='#cccccc',
            bg='#2d2d2d'
        )
        self.timestamp_label.pack(fill=tk.X, pady=(5, 0))
        
        # STATUS LOG
        log_frame = tk.LabelFrame(
            parent,
            text="Live Status & Logs",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d',
            bd=2,
            relief=tk.RAISED
        )
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.status_log = scrolledtext.ScrolledText(
            log_frame,
            height=12,
            font=('Consolas', 8),
            bg='#1a1a1a',
            fg='#00ff88',
            insertbackground='#00ff88',
            wrap=tk.WORD
        )
        self.status_log.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_enhanced_model_button(self, parent, key, model, index):
        """Erstelle erweiterten Modell-Button"""
        
        # MODELL CONTAINER
        model_frame = tk.Frame(parent, bg='#2d2d2d', relief=tk.RAISED, bd=1)
        model_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # MODELL INFO
        info_frame = tk.Frame(model_frame, bg='#2d2d2d')
        info_frame.pack(fill=tk.X, padx=10, pady=8)
        
        # NAME UND STATUS
        header_frame = tk.Frame(info_frame, bg='#2d2d2d')
        header_frame.pack(fill=tk.X)
        
        name_label = tk.Label(
            header_frame,
            text=model['name'],
            font=('Arial', 10, 'bold'),
            fg=model['color'],
            bg='#2d2d2d'
        )
        name_label.pack(side=tk.LEFT)
        
        # EMPFOHLEN BADGE
        if model.get('recommended'):
            rec_label = tk.Label(
                header_frame,
                text="*** EMPFOHLEN ***",
                font=('Arial', 7, 'bold'),
                fg='#ffd700',
                bg='#2d2d2d'
            )
            rec_label.pack(side=tk.RIGHT)
        
        # BESCHREIBUNG
        desc_label = tk.Label(
            info_frame,
            text=model['description'],
            font=('Arial', 8),
            fg='#cccccc',
            bg='#2d2d2d',
            justify=tk.LEFT
        )
        desc_label.pack(anchor=tk.W, pady=(3, 0))
        
        # STATUS
        status_label = tk.Label(
            info_frame,
            text=f"Status: {model['status']}",
            font=('Arial', 8),
            fg='#cccccc',
            bg='#2d2d2d'
        )
        status_label.pack(anchor=tk.W, pady=(3, 0))
        model['status_label'] = status_label
        
        # BUTTON
        start_button = tk.Button(
            info_frame,
            text=">> STARTEN",
            command=lambda k=key: self.start_enhanced_model(k),
            font=('Arial', 9, 'bold'),
            bg=model['color'],
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5,
            cursor='hand2'
        )
        start_button.pack(anchor=tk.W, pady=(5, 0))
        model['start_button'] = start_button
    
    def create_enhanced_visualization_panel(self, parent):
        """Erstelle erweitertes Visualisierungs-Panel"""
        
        # VISUALISIERUNG TITEL
        viz_title = tk.Label(
            parent,
            text="ENHANCED BITCOIN TRADING DASHBOARD",
            font=('Arial', 14, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        viz_title.pack(pady=(0, 10))
        
        # NOTEBOOK FUER TABS
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # TAB 1: PREIS-CHART (ERWEITERT)
        self.create_enhanced_price_chart_tab()
        
        # TAB 2: MULTI-TIMEFRAME-ANALYSE
        self.create_multi_timeframe_tab()
        
        # TAB 3: TECHNISCHE INDIKATOREN
        self.create_technical_indicators_tab()
        
        # TAB 4: PROGNOSE-DASHBOARD
        self.create_prediction_dashboard_tab()
    
    def create_enhanced_price_chart_tab(self):
        """Erstelle erweiterten Preis-Chart Tab"""
        
        # FRAME FUER PREIS-CHART
        price_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(price_frame, text="Enhanced Bitcoin Preis-Chart")
        
        # MATPLOTLIB FIGURE
        self.price_fig, self.price_ax = plt.subplots(figsize=(12, 8), facecolor='#1a1a1a')
        self.price_ax.set_facecolor('#1a1a1a')
        
        # CANVAS
        self.price_canvas = FigureCanvasTkAgg(self.price_fig, price_frame)
        self.price_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # INITIAL CHART
        self.update_enhanced_price_chart()
    
    def create_multi_timeframe_tab(self):
        """Erstelle Multi-Timeframe-Analyse Tab"""
        
        # FRAME FUER MULTI-TIMEFRAME
        mtf_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(mtf_frame, text="Multi-Timeframe-Analyse")
        
        # MATPLOTLIB FIGURE
        self.mtf_fig, self.mtf_axes = plt.subplots(2, 2, figsize=(12, 8), facecolor='#1a1a1a')
        self.mtf_fig.suptitle('Multi-Timeframe Bitcoin-Analyse', color='white', fontsize=14)
        
        for ax in self.mtf_axes.flat:
            ax.set_facecolor('#1a1a1a')
        
        # CANVAS
        self.mtf_canvas = FigureCanvasTkAgg(self.mtf_fig, mtf_frame)
        self.mtf_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # INITIAL CHARTS
        self.update_multi_timeframe_charts()
    
    def create_technical_indicators_tab(self):
        """Erstelle Technische Indikatoren Tab"""
        
        # FRAME FUER TECHNISCHE INDIKATOREN
        tech_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(tech_frame, text="Technische Indikatoren")
        
        # MATPLOTLIB FIGURE
        self.tech_fig, self.tech_axes = plt.subplots(3, 1, figsize=(12, 8), 
                                                   facecolor='#1a1a1a', 
                                                   gridspec_kw={'height_ratios': [3, 1, 1]})
        self.tech_fig.suptitle('Erweiterte Technische Indikatoren', color='white', fontsize=14)
        
        for ax in self.tech_axes:
            ax.set_facecolor('#1a1a1a')
        
        # CANVAS
        self.tech_canvas = FigureCanvasTkAgg(self.tech_fig, tech_frame)
        self.tech_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # INITIAL CHARTS
        self.update_technical_indicators_charts()
    
    def create_prediction_dashboard_tab(self):
        """Erstelle Prognose-Dashboard Tab"""
        
        # FRAME FUER PROGNOSE-DASHBOARD
        pred_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(pred_frame, text="Prognose-Dashboard")
        
        # OBERER BEREICH - PROGNOSE-CHART
        upper_frame = tk.Frame(pred_frame, bg='#1a1a1a')
        upper_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # MATPLOTLIB FIGURE
        self.pred_fig, self.pred_ax = plt.subplots(figsize=(12, 6), facecolor='#1a1a1a')
        self.pred_ax.set_facecolor('#1a1a1a')
        self.pred_fig.suptitle('Bitcoin-Preisprognose (48h)', color='white', fontsize=14)
        
        # CANVAS
        self.pred_canvas = FigureCanvasTkAgg(self.pred_fig, upper_frame)
        self.pred_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # UNTERER BEREICH - PROGNOSE-DETAILS
        lower_frame = tk.Frame(pred_frame, bg='#1a1a1a', height=200)
        lower_frame.pack(fill=tk.X, padx=10, pady=10)
        lower_frame.pack_propagate(False)
        
        # PROGNOSE-DETAILS GRID
        self.create_prediction_details_grid(lower_frame)
        
        # INITIAL CHARTS
        self.update_prediction_dashboard()

    def generate_enhanced_demo_data(self):
        """Generiere erweiterte Demo-Daten mit höherer Präzision"""

        # Bitcoin-Preis Demo-Daten (60 Tage, stündlich)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=60)
        dates = pd.date_range(start=start_date, end=end_date, freq='1H')

        base_price = 106234.56  # Präziser Startpreis

        # Realistische Preisbewegung mit Volatilität
        price_changes = np.random.normal(0, 0.015, len(dates))
        trend_component = np.sin(np.arange(len(dates)) / 100) * 0.002

        prices = [base_price]
        volumes = []

        for i, (change, trend) in enumerate(zip(price_changes[1:], trend_component[1:])):
            new_price = prices[-1] * (1 + change + trend)
            new_price = max(95000, min(120000, new_price))
            prices.append(new_price)

            # Realistische Volume mit Korrelation zu Preisbewegung
            volume_base = 1.2e9  # 1.2 Milliarden USD
            volume_factor = 1 + abs(change) * 5  # Höhere Volume bei größeren Bewegungen
            volume = volume_base * random.uniform(0.5, 2.0) * volume_factor
            volumes.append(volume)

        # Letztes Volume
        volumes.append(volume_base * random.uniform(0.5, 2.0))

        # Technische Indikatoren berechnen
        prices_series = pd.Series(prices)

        # RSI (14 Perioden)
        delta = prices_series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # MACD
        ema_12 = prices_series.ewm(span=12).mean()
        ema_26 = prices_series.ewm(span=26).mean()
        macd = ema_12 - ema_26
        macd_signal = macd.ewm(span=9).mean()
        macd_histogram = macd - macd_signal

        # Bollinger Bands
        bb_period = 20
        bb_middle = prices_series.rolling(bb_period).mean()
        bb_std = prices_series.rolling(bb_period).std()
        bb_upper = bb_middle + (bb_std * 2)
        bb_lower = bb_middle - (bb_std * 2)

        # Stochastic Oscillator
        high_14 = prices_series.rolling(14).max()
        low_14 = prices_series.rolling(14).min()
        stoch_k = 100 * (prices_series - low_14) / (high_14 - low_14)
        stoch_d = stoch_k.rolling(3).mean()

        self.current_data = {
            'dates': dates,
            'prices': prices,
            'volume': volumes,
            'rsi': rsi.fillna(50).values,
            'macd': macd.fillna(0).values,
            'macd_signal': macd_signal.fillna(0).values,
            'macd_histogram': macd_histogram.fillna(0).values,
            'bb_upper': bb_upper.fillna(base_price).values,
            'bb_middle': bb_middle.fillna(base_price).values,
            'bb_lower': bb_lower.fillna(base_price).values,
            'stoch_k': stoch_k.fillna(50).values,
            'stoch_d': stoch_d.fillna(50).values,
            'ema_12': ema_12.fillna(base_price).values,
            'ema_26': ema_26.fillna(base_price).values
        }

        # Prognose-Daten generieren
        self.generate_prediction_data()

    def generate_prediction_data(self):
        """Generiere Prognose-Daten für 48h"""

        if not self.current_data:
            return

        current_price = self.current_data['prices'][-1]
        current_date = self.current_data['dates'][-1]

        # 48h Prognose (stündlich)
        future_dates = pd.date_range(start=current_date + timedelta(hours=1),
                                   periods=48, freq='1H')

        # Prognose-Algorithmus (vereinfacht aber realistisch)
        trend_strength = np.mean(np.diff(self.current_data['prices'][-24:]))  # 24h Trend
        volatility = np.std(np.diff(self.current_data['prices'][-168:])) / current_price  # 7d Volatilität

        predicted_prices = [current_price]
        confidence_levels = []

        for i in range(48):
            # Trend-basierte Prognose mit abnehmender Konfidenz
            trend_factor = trend_strength * (0.95 ** i)  # Trend schwächt ab
            noise_factor = random.gauss(0, volatility * (1 + i * 0.02))  # Unsicherheit steigt

            predicted_price = predicted_prices[-1] * (1 + trend_factor + noise_factor)
            predicted_price = max(current_price * 0.8, min(current_price * 1.2, predicted_price))
            predicted_prices.append(predicted_price)

            # Konfidenz sinkt mit Zeit
            confidence = max(0.3, 0.95 - (i * 0.015))
            confidence_levels.append(confidence)

        predicted_prices = predicted_prices[1:]  # Ersten Wert entfernen

        self.prediction_data = {
            'dates': future_dates,
            'prices': predicted_prices,
            'confidence': confidence_levels,
            'trend_strength': trend_strength,
            'volatility': volatility
        }

    def update_enhanced_price_chart(self):
        """Aktualisiere erweiterten Preis-Chart"""

        if not self.current_data:
            return

        self.price_ax.clear()

        # Hauptpreis-Linie mit höherer Auflösung
        dates = self.current_data['dates']
        prices = self.current_data['prices']

        # Preis-Linie
        self.price_ax.plot(dates, prices, color='#00ff88', linewidth=2.5,
                          label=f'Bitcoin Preis (${prices[-1]:,.2f})', alpha=0.9)

        # Bollinger Bands
        self.price_ax.fill_between(dates,
                                  self.current_data['bb_upper'],
                                  self.current_data['bb_lower'],
                                  alpha=0.1, color='#3366cc', label='Bollinger Bands')

        self.price_ax.plot(dates, self.current_data['bb_upper'],
                          color='#3366cc', linewidth=1, alpha=0.7, linestyle='--')
        self.price_ax.plot(dates, self.current_data['bb_lower'],
                          color='#3366cc', linewidth=1, alpha=0.7, linestyle='--')
        self.price_ax.plot(dates, self.current_data['bb_middle'],
                          color='#3366cc', linewidth=1, alpha=0.5, label='BB Mitte (SMA 20)')

        # EMAs
        self.price_ax.plot(dates, self.current_data['ema_12'],
                          color='#ff6600', linewidth=1.5, alpha=0.8, label='EMA 12')
        self.price_ax.plot(dates, self.current_data['ema_26'],
                          color='#ff0066', linewidth=1.5, alpha=0.8, label='EMA 26')

        # Styling
        self.price_ax.set_title('Enhanced Bitcoin Preis-Chart (60 Tage)',
                               color='white', fontsize=14, fontweight='bold')
        self.price_ax.set_xlabel('Datum & Zeit', color='white')
        self.price_ax.set_ylabel('Preis (USD)', color='white')
        self.price_ax.tick_params(colors='white')
        self.price_ax.legend(loc='upper left', fancybox=True, shadow=True)
        self.price_ax.grid(True, alpha=0.3, linestyle=':')

        # Präzise Y-Achsen-Formatierung
        if self.precision_mode:
            self.price_ax.yaxis.set_major_formatter(
                plt.FuncFormatter(lambda x, p: f'${x:,.2f}'))
        else:
            self.price_ax.yaxis.set_major_formatter(
                plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

        # X-Achsen-Formatierung
        self.price_ax.xaxis.set_major_formatter(mdates.DateFormatter('%d.%m %H:%M'))
        self.price_ax.xaxis.set_major_locator(mdates.HourLocator(interval=24))
        plt.setp(self.price_ax.xaxis.get_majorticklabels(), rotation=45)

        # Aktuelle Preis-Annotation
        current_price = prices[-1]
        current_date = dates[-1]
        self.price_ax.annotate(f'${current_price:,.2f}',
                              xy=(current_date, current_price),
                              xytext=(10, 10), textcoords='offset points',
                              bbox=dict(boxstyle='round,pad=0.3', facecolor='#00ff88', alpha=0.8),
                              arrowprops=dict(arrowstyle='->', color='#00ff88'),
                              color='black', fontweight='bold')

        # Tight layout
        self.price_fig.tight_layout()
        self.price_canvas.draw()

    def update_multi_timeframe_charts(self):
        """Aktualisiere Multi-Timeframe-Charts"""

        if not self.current_data:
            return

        dates = self.current_data['dates']
        prices = self.current_data['prices']

        # 1H Chart (letzte 24h)
        ax1 = self.mtf_axes[0, 0]
        ax1.clear()
        recent_24h = slice(-24, None)
        ax1.plot(dates[recent_24h], prices[recent_24h], color='#00ff88', linewidth=2)
        ax1.set_title('1H Chart (24h)', color='white', fontsize=10)
        ax1.tick_params(colors='white', labelsize=8)
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))

        # 4H Chart (letzte 7 Tage)
        ax2 = self.mtf_axes[0, 1]
        ax2.clear()
        recent_7d = slice(-168, None, 4)  # Jede 4. Stunde
        ax2.plot(dates[recent_7d], prices[recent_7d], color='#ff6600', linewidth=2)
        ax2.set_title('4H Chart (7 Tage)', color='white', fontsize=10)
        ax2.tick_params(colors='white', labelsize=8)
        ax2.grid(True, alpha=0.3)
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%d.%m'))

        # 1D Chart (letzte 30 Tage)
        ax3 = self.mtf_axes[1, 0]
        ax3.clear()
        recent_30d = slice(-720, None, 24)  # Jede 24. Stunde
        ax3.plot(dates[recent_30d], prices[recent_30d], color='#3366cc', linewidth=2)
        ax3.set_title('1D Chart (30 Tage)', color='white', fontsize=10)
        ax3.tick_params(colors='white', labelsize=8)
        ax3.grid(True, alpha=0.3)
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%d.%m'))

        # Volume Chart
        ax4 = self.mtf_axes[1, 1]
        ax4.clear()
        volume_24h = self.current_data['volume'][-24:]
        ax4.bar(dates[-24:], volume_24h, color='#ff6600', alpha=0.7, width=0.03)
        ax4.set_title('Volume (24h)', color='white', fontsize=10)
        ax4.tick_params(colors='white', labelsize=8)
        ax4.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1e9:.1f}B'))
        ax4.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))

        # Tight layout
        self.mtf_fig.tight_layout()
        self.mtf_canvas.draw()

    def update_technical_indicators_charts(self):
        """Aktualisiere Technische Indikatoren Charts"""

        if not self.current_data:
            return

        dates = self.current_data['dates']

        # Haupt-Chart: Preis mit MACD
        ax1 = self.tech_axes[0]
        ax1.clear()
        ax1.plot(dates, self.current_data['prices'], color='#00ff88', linewidth=2, label='Bitcoin Preis')
        ax1.set_title('Bitcoin Preis mit Technischen Indikatoren', color='white', fontsize=12)
        ax1.tick_params(colors='white')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

        # RSI Chart
        ax2 = self.tech_axes[1]
        ax2.clear()
        ax2.plot(dates, self.current_data['rsi'], color='#3366cc', linewidth=2, label='RSI')
        ax2.axhline(y=70, color='red', linestyle='--', alpha=0.7, label='Überkauft (70)')
        ax2.axhline(y=30, color='green', linestyle='--', alpha=0.7, label='Überverkauft (30)')
        ax2.fill_between(dates, 30, 70, alpha=0.1, color='gray')
        ax2.set_title('RSI (14)', color='white', fontsize=10)
        ax2.set_ylim(0, 100)
        ax2.tick_params(colors='white')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # MACD Chart
        ax3 = self.tech_axes[2]
        ax3.clear()
        ax3.plot(dates, self.current_data['macd'], color='#ff6600', linewidth=2, label='MACD')
        ax3.plot(dates, self.current_data['macd_signal'], color='#ff0066', linewidth=2, label='Signal')
        ax3.bar(dates, self.current_data['macd_histogram'], color='#cccccc', alpha=0.6,
               width=0.02, label='Histogram')
        ax3.axhline(y=0, color='white', linestyle='-', alpha=0.5)
        ax3.set_title('MACD', color='white', fontsize=10)
        ax3.tick_params(colors='white')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # X-Achsen-Formatierung
        for ax in self.tech_axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%d.%m'))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # Tight layout
        self.tech_fig.tight_layout()
        self.tech_canvas.draw()

    def update_prediction_dashboard(self):
        """Aktualisiere Prognose-Dashboard"""

        if not hasattr(self, 'prediction_data') or not self.prediction_data:
            return

        self.pred_ax.clear()

        # Historische Daten (letzte 24h)
        hist_dates = self.current_data['dates'][-24:]
        hist_prices = self.current_data['prices'][-24:]

        # Prognose-Daten
        pred_dates = self.prediction_data['dates']
        pred_prices = self.prediction_data['prices']
        confidence = self.prediction_data['confidence']

        # Kombinierte Zeitachse
        all_dates = list(hist_dates) + list(pred_dates)
        all_prices = list(hist_prices) + list(pred_prices)

        # Historische Linie
        self.pred_ax.plot(hist_dates, hist_prices, color='#00ff88', linewidth=3,
                         label='Historisch (24h)', alpha=0.9)

        # Prognose-Linie
        self.pred_ax.plot(pred_dates, pred_prices, color='#ff6600', linewidth=2.5,
                         label='Prognose (48h)', linestyle='--', alpha=0.8)

        # Konfidenz-Bereich
        upper_bound = [p * (1 + (1-c) * 0.1) for p, c in zip(pred_prices, confidence)]
        lower_bound = [p * (1 - (1-c) * 0.1) for p, c in zip(pred_prices, confidence)]

        self.pred_ax.fill_between(pred_dates, lower_bound, upper_bound,
                                 alpha=0.2, color='#ff6600', label='Konfidenz-Bereich')

        # Verbindungslinie zwischen historisch und Prognose
        connection_dates = [hist_dates[-1], pred_dates[0]]
        connection_prices = [hist_prices[-1], pred_prices[0]]
        self.pred_ax.plot(connection_dates, connection_prices, color='#cccccc',
                         linewidth=1, alpha=0.5)

        # Styling
        self.pred_ax.set_title('Bitcoin-Preisprognose (48h)', color='white', fontsize=14)
        self.pred_ax.set_xlabel('Datum & Zeit', color='white')
        self.pred_ax.set_ylabel('Preis (USD)', color='white')
        self.pred_ax.tick_params(colors='white')
        self.pred_ax.legend(loc='upper left')
        self.pred_ax.grid(True, alpha=0.3, linestyle=':')

        # Präzise Formatierung
        if self.precision_mode:
            self.pred_ax.yaxis.set_major_formatter(
                plt.FuncFormatter(lambda x, p: f'${x:,.2f}'))
        else:
            self.pred_ax.yaxis.set_major_formatter(
                plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

        # X-Achsen-Formatierung
        self.pred_ax.xaxis.set_major_formatter(mdates.DateFormatter('%d.%m %H:%M'))
        self.pred_ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
        plt.setp(self.pred_ax.xaxis.get_majorticklabels(), rotation=45)

        # Wichtige Prognose-Punkte markieren
        key_points = [0, 11, 23, 47]  # 1h, 12h, 24h, 48h
        for i in key_points:
            if i < len(pred_dates):
                self.pred_ax.scatter(pred_dates[i], pred_prices[i],
                                   color='#ffd700', s=50, zorder=5)
                self.pred_ax.annotate(f'{i+1}h\n${pred_prices[i]:,.0f}',
                                    xy=(pred_dates[i], pred_prices[i]),
                                    xytext=(0, 20), textcoords='offset points',
                                    ha='center', va='bottom', color='white',
                                    bbox=dict(boxstyle='round,pad=0.3',
                                            facecolor='#ffd700', alpha=0.8))

        # Tight layout
        self.pred_fig.tight_layout()
        self.pred_canvas.draw()

    def create_prediction_details_grid(self, parent):
        """Erstelle Prognose-Details Grid"""

        # Grid für Prognose-Details
        details_frame = tk.Frame(parent, bg='#1a1a1a')
        details_frame.pack(fill=tk.BOTH, expand=True)

        # Grid-Konfiguration
        for i in range(4):
            details_frame.grid_columnconfigure(i, weight=1)
        for i in range(2):
            details_frame.grid_rowconfigure(i, weight=1)

        # Prognose-Karten
        self.create_prediction_card(details_frame, "1h Prognose", "$106,456.78", "+0.21%", 0, 0)
        self.create_prediction_card(details_frame, "12h Prognose", "$107,234.56", "+0.94%", 0, 1)
        self.create_prediction_card(details_frame, "24h Prognose", "$108,123.45", "*****%", 0, 2)
        self.create_prediction_card(details_frame, "48h Prognose", "$109,876.54", "*****%", 0, 3)

        self.create_prediction_card(details_frame, "Trend-Stärke", "Bullish", "↗", 1, 0)
        self.create_prediction_card(details_frame, "Volatilität", "Mittel", "1.8%", 1, 1)
        self.create_prediction_card(details_frame, "Konfidenz", "Hoch", "87.5%", 1, 2)
        self.create_prediction_card(details_frame, "Risk Score", "Niedrig", "2.3", 1, 3)

    def create_prediction_card(self, parent, title, value, subtitle, row, col):
        """Erstelle einzelne Prognose-Karte"""

        card = tk.Frame(parent, bg='#2d2d2d', relief=tk.RAISED, bd=2)
        card.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")

        title_label = tk.Label(
            card,
            text=title,
            font=('Arial', 9, 'bold'),
            fg='#cccccc',
            bg='#2d2d2d'
        )
        title_label.pack(pady=(10, 2))

        value_label = tk.Label(
            card,
            text=value,
            font=('Arial', 12, 'bold'),
            fg='#00ff88',
            bg='#2d2d2d'
        )
        value_label.pack(pady=(0, 2))

        subtitle_label = tk.Label(
            card,
            text=subtitle,
            font=('Arial', 8),
            fg='#ff6600',
            bg='#2d2d2d'
        )
        subtitle_label.pack(pady=(0, 10))

    def toggle_precision_mode(self):
        """Schalte Präzisions-Modus um"""
        self.precision_mode = self.precision_var.get()
        self.log_message(f"Hochpräzisions-Modus: {'EIN' if self.precision_mode else 'AUS'}")
        self.refresh_enhanced_visualizations()

    def setup_auto_update_timer(self):
        """Setup automatischer Update-Timer"""
        def auto_update():
            if hasattr(self, 'root') and self.root.winfo_exists():
                self.refresh_enhanced_visualizations()
                # Nächstes Update planen
                self.root.after(self.update_interval * 1000, auto_update)

        # Ersten Update nach 5 Sekunden starten
        self.root.after(5000, auto_update)

    def refresh_enhanced_visualizations(self):
        """Aktualisiere alle erweiterten Visualisierungen"""
        self.log_message("Aktualisiere erweiterte Visualisierungen...")

        # Update Zeitstempel
        self.last_update = datetime.now()
        if hasattr(self, 'timestamp_label'):
            self.timestamp_label.config(
                text=f"Letzte Aktualisierung: {self.last_update.strftime('%H:%M:%S.%f')[:-3]}")

        # Generiere neue Demo-Daten
        self.generate_enhanced_demo_data()

        # Update alle Charts
        self.update_enhanced_price_chart()
        self.update_multi_timeframe_charts()
        self.update_technical_indicators_charts()
        self.update_prediction_dashboard()

        self.log_message("Erweiterte Visualisierungen aktualisiert")

    def log_message(self, message):
        """Füge Nachricht zum Status-Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]  # Millisekunden
        log_entry = f"[{timestamp}] {message}\n"

        self.status_log.insert(tk.END, log_entry)
        self.status_log.see(tk.END)
        self.root.update()

        print(log_entry.strip())

    def start_enhanced_model(self, model_key):
        """Starte Modell mit erweiterten Features"""
        if model_key not in self.models:
            self.log_message(f"FEHLER: Ungültiges Modell: {model_key}")
            return

        model = self.models[model_key]

        # Prüfe ob bereits läuft
        if model_key in self.running_processes:
            self.log_message(f"WARNUNG: {model['name']} läuft bereits")
            return

        # Prüfe Datei
        if not os.path.exists(model['file']):
            self.log_message(f"FEHLER: Datei nicht gefunden: {model['file']}")
            return

        try:
            self.log_message(f"STARTE: {model['name']} (Enhanced Mode)")

            # Update GUI
            model['status'] = 'Läuft'
            model['status_label'].config(text=f"Status: {model['status']}")

            # Starte Prozess
            process = subprocess.Popen(
                [sys.executable, model['file']],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.script_directory,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )

            self.running_processes[model_key] = process
            model['process'] = process

            # Starte Enhanced Monitoring
            monitor_thread = threading.Thread(
                target=self.monitor_enhanced_model,
                args=(model_key, process),
                daemon=True
            )
            monitor_thread.start()

            self.log_message(f"ERFOLGREICH: {model['name']} gestartet (PID: {process.pid})")

            # Update Visualisierungen
            self.refresh_enhanced_visualizations()

        except Exception as e:
            self.log_message(f"FEHLER beim Starten von {model['name']}: {e}")
            model['status'] = 'Fehler'
            model['status_label'].config(text=f"Status: {model['status']}")

    def monitor_enhanced_model(self, model_key, process):
        """Überwache Modell mit erweiterten Features"""
        model = self.models[model_key]

        try:
            stdout, stderr = process.communicate()

            # Cleanup
            if model_key in self.running_processes:
                del self.running_processes[model_key]

            # Update GUI
            self.root.after(0, lambda: self.enhanced_model_finished(model_key, process.returncode, stdout))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"FEHLER: Enhanced Monitor-Fehler: {e}"))

    def enhanced_model_finished(self, model_key, return_code, stdout):
        """Modell beendet mit erweiterten Features"""
        model = self.models[model_key]

        # Update GUI
        model['process'] = None
        model['status'] = 'Beendet' if return_code == 0 else 'Fehler'
        model['status_label'].config(text=f"Status: {model['status']}")

        if return_code == 0:
            self.log_message(f"ERFOLGREICH: {model['name']} erfolgreich beendet (Enhanced)")

            # Extrahiere und zeige wichtige Ergebnisse
            if stdout:
                self.extract_and_display_results(stdout, model['name'])
        else:
            self.log_message(f"WARNUNG: {model['name']} mit Fehler beendet")

        # Update Visualisierungen
        self.refresh_enhanced_visualizations()

    def extract_and_display_results(self, stdout, model_name):
        """Extrahiere und zeige wichtige Ergebnisse"""
        lines = stdout.split('\n')

        # Suche nach wichtigen Informationen
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in
                  ['signal:', 'preis:', 'vorhersage:', 'konfidenz:', 'kaufen', 'verkaufen', 'halten']):
                if line:
                    self.log_message(f"[{model_name}] {line}")

                    # Speichere Signal-Historie
                    if 'signal:' in line.lower():
                        self.signal_history.append({
                            'timestamp': datetime.now(),
                            'model': model_name,
                            'signal': line
                        })

                        # Behalte nur letzte 10 Signale
                        if len(self.signal_history) > 10:
                            self.signal_history.pop(0)

    def start_all_models(self):
        """Starte alle Modelle mit erweiterten Features"""
        self.log_message("STARTE ALLE BITCOIN TRADING MODELLE (Enhanced Mode)...")

        for model_key in self.models.keys():
            if model_key not in self.running_processes:
                self.start_enhanced_model(model_key)
                time.sleep(1.5)  # Etwas längere Pause für Enhanced Mode

        self.log_message("Alle verfügbaren Modelle gestartet (Enhanced)")

    def stop_all_models(self):
        """Stoppe alle Modelle"""
        if not self.running_processes:
            self.log_message("INFO: Keine Modelle laufen")
            return

        self.log_message("STOPPE ALLE MODELLE...")

        for model_key in list(self.running_processes.keys()):
            process = self.running_processes[model_key]
            model = self.models[model_key]

            try:
                process.terminate()
                process.wait(timeout=5)

                del self.running_processes[model_key]
                model['process'] = None
                model['status'] = 'Gestoppt'
                model['status_label'].config(text=f"Status: {model['status']}")

                self.log_message(f"GESTOPPT: {model['name']}")

            except Exception as e:
                self.log_message(f"FEHLER beim Stoppen: {e}")

        self.log_message("Alle Modelle gestoppt")

    def run(self):
        """Starte Enhanced GUI"""
        self.log_message("Bitcoin Trading Launcher Enhanced Visualization bereit!")
        self.log_message("Optimierte Algorithmen und detaillierte Visualisierungen")
        self.root.mainloop()

def main():
    """Hauptfunktion"""
    try:
        app = BitcoinLauncherEnhancedVisualization()
        app.run()
    except Exception as e:
        print(f"FEHLER: Enhanced GUI-Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
