#!/usr/bin/env python3
"""
🧠 ADAPTIVE MEMORY LEARNING - BITCOIN TRADING TOOL 🧠
====================================================
REVOLUTIONÄRER ANSATZ:
- Kein Festplatten-Spam (Memory-only)
- Kontinuierliches Lernen bei jeder Ausführung
- Modell wird automatisch präziser
- Leicht, schnell, effizient
"""

import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score
import yfinance as yf
import pickle
import os
from typing import Dict, List, Tuple, Optional
from collections import deque
import threading
import json

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

class AdaptiveMemoryLearning:
    """
    🧠 ADAPTIVE MEMORY LEARNING SYSTEM
    ==================================
    - Memory-basiertes kontinuierliches Lernen
    - Kein Festplatten-Spam
    - Automatische Modell-Verbesserung
    """
    
    def __init__(self):
        # MEMORY-BASIERTE KONFIGURATION
        self.MEMORY_SIZE = 1000  # Letzte 1000 Datenpunkte im Memory
        self.MIN_TRAINING_SIZE = 30   # REDUZIERT: Minimum für Training
        self.LEARNING_RATE = 0.1  # Adaptive Lernrate
        
        # MEMORY STORAGE (kein Festplatten-Zugriff)
        self.price_memory = deque(maxlen=self.MEMORY_SIZE)
        self.feature_memory = deque(maxlen=self.MEMORY_SIZE)
        self.prediction_memory = deque(maxlen=self.MEMORY_SIZE)
        self.accuracy_history = deque(maxlen=100)

        # ADAPTIVE MODELLE (im RAM)
        self.models = {}
        self.scalers = {}
        self.model_weights = {'1h': 0.5, '6h': 0.3, '24h': 0.2}

        # BOOTSTRAP-MODUS für ersten Start
        self.bootstrap_mode = True
        
        # PERFORMANCE TRACKING
        self.session_count = 0
        self.total_accuracy = 0.0
        self.best_accuracy = 0.0
        
        # HORIZONTE
        self.horizons = [1, 6, 24]
        
        print("🧠 ADAPTIVE MEMORY LEARNING initialisiert")
        print(f"💾 Memory-Größe: {self.MEMORY_SIZE} Datenpunkte")
        print(f"🎯 Kein Festplatten-Zugriff (außer Visualisierung)")
    
    def get_bitcoin_data_efficient(self) -> pd.DataFrame:
        """Effiziente Bitcoin-Datensammlung - garantiert genug Daten"""

        # BOOTSTRAP-MODUS: Immer Fallback verwenden für garantierte Datenmenge
        if self.bootstrap_mode:
            print("🚀 Bootstrap-Modus: Verwende garantierte Fallback-Daten...")
            return self._generate_efficient_fallback()

        # NORMALER MODUS: Versuche echte Daten
        try:
            print("📊 Sammle neue Bitcoin-Daten...")
            btc = yf.Ticker("BTC-USD")

            # Nur 2 Tage für Updates (schneller)
            df = btc.history(period="2d", interval="1h")

            if len(df) > 10:
                df.columns = [col.lower() for col in df.columns]
                df = df.dropna()

                # Memory-Optimierung
                df = df.astype('float32')

                print(f"✅ Echte Bitcoin-Daten: {len(df)} Stunden")
                return df
            else:
                print(f"⚠️ API-Daten zu wenig: {len(df)} Stunden")
                raise ValueError("Zu wenig API-Daten")

        except Exception as e:
            print(f"⚠️ API-Problem: {e}")
            print("🔄 Verwende Fallback-Daten...")
            return self._generate_efficient_fallback()
    
    def _generate_efficient_fallback(self) -> pd.DataFrame:
        """Effiziente Fallback-Daten (minimal)"""
        print("🔄 Generiere effiziente Fallback-Daten...")
        
        end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        start_time = end_time - timedelta(days=7)  # 7 Tage für mehr Daten
        dates = pd.date_range(start=start_time, end=end_time, freq='H')
        
        n_points = len(dates)
        np.random.seed(int(time.time()) % 1000)
        
        # Einfache, realistische Preismodellierung
        base_price = 105000
        trend = np.cumsum(np.random.normal(0, 100, n_points))
        noise = np.random.normal(0, 500, n_points)
        
        prices = base_price + trend + noise
        prices = np.maximum(prices, 50000)
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * np.random.uniform(1.001, 1.015, n_points),
            'low': prices * np.random.uniform(0.985, 0.999, n_points),
            'open': prices * np.random.uniform(0.998, 1.002, n_points),
            'volume': np.random.lognormal(15, 0.2, n_points)
        }, index=dates)
        
        df = df.astype('float32')
        print(f"✅ Fallback-Daten: {len(df)} Stunden (7 Tage)")
        return df
    
    def create_efficient_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Effiziente Feature-Erstellung (nur essenzielle Features)"""
        print("🔧 Erstelle effiziente Features...")
        
        # NUR ESSENZIELLE FEATURES (schnell & effektiv)
        
        # 1. Returns (wichtigste Features)
        for period in [1, 3, 6, 12, 24]:
            df[f'returns_{period}h'] = df['close'].pct_change(periods=period)
        
        # 2. Moving Averages (nur wichtigste)
        for window in [6, 12, 24]:
            df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
            df[f'price_above_sma_{window}'] = (df['close'] > df[f'sma_{window}']).astype(float)
        
        # 3. Volatilität (essentiell)
        df['volatility_6h'] = df['close'].rolling(window=6).std()
        df['volatility_24h'] = df['close'].rolling(window=24).std()
        
        # 4. RSI (vereinfacht)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-10)
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 5. Zeit-Features (minimal)
        df['hour'] = df.index.hour
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        
        # 6. Lag Features (nur wichtigste)
        df['close_lag_1'] = df['close'].shift(1)
        df['returns_lag_1'] = df['returns_1h'].shift(1)
        
        # Bereinigung
        df = df.fillna(method='ffill').fillna(0)
        df = df.replace([np.inf, -np.inf], 0)
        
        print(f"✅ Effiziente Features: {df.shape[1]} Spalten")
        return df
    
    def update_memory(self, df: pd.DataFrame):
        """Aktualisiert Memory mit neuen Daten"""
        print("🧠 Aktualisiere Memory...")
        
        # Features erstellen
        df_features = self.create_efficient_features(df)
        
        # Neue Daten zu Memory hinzufügen
        # Beim ersten Start: Alle verfügbaren Daten verwenden (BOOTSTRAP)
        if self.bootstrap_mode:
            data_range = df_features.index  # ALLE Daten beim ersten Start
            print(f"🚀 Bootstrap-Modus: Verwende alle {len(data_range)} Datenpunkte")
        else:
            data_range = df_features.index[-10:]  # Nur neue Daten bei Updates

        for idx in data_range:
            if idx in df_features.index:
                row = df_features.loc[idx]
                
                # Preis-Memory
                self.price_memory.append({
                    'timestamp': idx,
                    'price': row['close'],
                    'volume': row.get('volume', 0)
                })
                
                # Feature-Memory (nur numerische Features)
                feature_cols = [col for col in df_features.columns 
                               if col not in ['close', 'high', 'low', 'open', 'volume']]
                
                features = {}
                for col in feature_cols:
                    if not np.isnan(row[col]) and not np.isinf(row[col]):
                        features[col] = float(row[col])
                
                if features:  # Nur wenn Features vorhanden
                    self.feature_memory.append({
                        'timestamp': idx,
                        'features': features
                    })
        
        print(f"💾 Memory aktualisiert: {len(self.price_memory)} Preise, {len(self.feature_memory)} Features")

        # Bootstrap-Modus nach erstem Durchlauf deaktivieren
        if self.bootstrap_mode and len(self.feature_memory) >= self.MIN_TRAINING_SIZE:
            self.bootstrap_mode = False
            print("🚀 Bootstrap-Modus abgeschlossen - Memory-Learning aktiviert!")
    
    def adaptive_training(self):
        """Adaptives Training mit Memory-Daten"""
        if len(self.feature_memory) < self.MIN_TRAINING_SIZE:
            print(f"⚠️ Zu wenig Memory-Daten für Training: {len(self.feature_memory)}")
            return False
        
        print("🤖 Starte adaptives Memory-Training...")
        
        # Memory-Daten zu DataFrame konvertieren
        memory_data = []
        for item in list(self.feature_memory):
            row = {'timestamp': item['timestamp']}
            row.update(item['features'])
            memory_data.append(row)
        
        if not memory_data:
            return False
        
        df_memory = pd.DataFrame(memory_data)
        df_memory.set_index('timestamp', inplace=True)
        df_memory = df_memory.sort_index()
        
        # Preise hinzufügen
        price_dict = {item['timestamp']: item['price'] for item in self.price_memory}
        df_memory['price'] = df_memory.index.map(price_dict)
        df_memory = df_memory.dropna(subset=['price'])
        
        if len(df_memory) < self.MIN_TRAINING_SIZE:
            return False
        
        # Training für jeden Horizont
        for horizon in self.horizons:
            print(f"  📈 Training {horizon}h Horizont...")
            
            # Labels erstellen
            future_prices = df_memory['price'].shift(-horizon)
            current_prices = df_memory['price']
            returns = (future_prices / current_prices - 1).fillna(0)
            
            # Adaptive Schwellenwerte
            threshold = 0.01 * horizon  # 1% pro Stunde
            labels = (returns > threshold).astype(int)
            
            # Features
            feature_cols = [col for col in df_memory.columns if col != 'price']
            X = df_memory[feature_cols].values
            y = labels.values
            
            # Bereinigung
            valid_mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
            X = X[valid_mask]
            y = y[valid_mask]
            
            if len(X) < 50:
                continue
            
            # Skalierung
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # Modell trainieren
            model = RandomForestClassifier(
                n_estimators=50,  # Weniger für Geschwindigkeit
                max_depth=10,
                min_samples_split=5,
                random_state=42
            )
            
            # Training mit den neuesten 80% der Daten
            split_idx = int(len(X_scaled) * 0.8)
            X_train = X_scaled[split_idx:]  # Neueste Daten für Training
            y_train = y[split_idx:]
            X_test = X_scaled[:split_idx]   # Ältere Daten für Test
            y_test = y[:split_idx]
            
            if len(X_train) > 10 and len(X_test) > 10:
                model.fit(X_train, y_train)
                
                # Evaluierung
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                # Memory-basierte Speicherung
                self.models[f'{horizon}h'] = model
                self.scalers[f'{horizon}h'] = scaler
                
                # Accuracy tracking
                self.accuracy_history.append(accuracy)
                
                print(f"    ✅ {horizon}h: {accuracy:.3f} Genauigkeit")
        
        self.session_count += 1
        return True

    def predict_adaptive_signals(self, df: pd.DataFrame) -> Optional[Dict]:
        """Adaptive Signalvorhersage mit Memory-Modellen"""
        if not self.models:
            print("❌ Keine trainierten Modelle im Memory")
            return None

        print("🔮 Erstelle adaptive Memory-Signale...")

        # Features für aktuellen Zeitpunkt
        df_features = self.create_efficient_features(df)
        latest_row = df_features.iloc[-1]

        # Feature-Vektor erstellen
        feature_cols = [col for col in df_features.columns
                       if col not in ['close', 'high', 'low', 'open', 'volume']]

        X_latest = []
        for col in feature_cols:
            if col in latest_row and not np.isnan(latest_row[col]):
                X_latest.append(float(latest_row[col]))
            else:
                X_latest.append(0.0)

        X_latest = np.array(X_latest).reshape(1, -1)

        predictions = {}

        for horizon_key, model in self.models.items():
            horizon = int(horizon_key.replace('h', ''))
            scaler = self.scalers.get(horizon_key)

            if scaler is None:
                continue

            try:
                # Skalierung
                X_scaled = scaler.transform(X_latest)

                # Vorhersage
                pred_proba = model.predict_proba(X_scaled)[0]
                buy_probability = pred_proba[1] if len(pred_proba) > 1 else 0.5

                # Adaptive Konfidenz basierend auf Memory-Performance
                recent_accuracy = np.mean(list(self.accuracy_history)[-10:]) if self.accuracy_history else 0.5
                confidence = max(buy_probability, 1-buy_probability) * recent_accuracy

                # Signal bestimmen
                if buy_probability > 0.7:
                    signal = "STARKER KAUF 🔥🔥🔥"
                    action = "SOFORT KAUFEN!"
                elif buy_probability > 0.6:
                    signal = "KAUF 🔥🔥"
                    action = "KAUFEN"
                elif buy_probability < 0.3:
                    signal = "STARKER VERKAUF 🔻🔻🔻"
                    action = "SOFORT VERKAUFEN!"
                elif buy_probability < 0.4:
                    signal = "VERKAUF 🔻🔻"
                    action = "VERKAUFEN"
                else:
                    signal = "HALTEN ⚖️"
                    action = "POSITION HALTEN"

                predictions[horizon_key] = {
                    'signal': signal,
                    'action': action,
                    'probability': buy_probability,
                    'confidence': confidence,
                    'memory_accuracy': recent_accuracy
                }

            except Exception as e:
                print(f"    ❌ Fehler bei {horizon_key}: {e}")
                continue

        # Gesamtsignal mit adaptiven Gewichten
        if predictions:
            weighted_prob = 0
            total_weight = 0

            for horizon_key, pred in predictions.items():
                horizon = int(horizon_key.replace('h', ''))
                base_weight = self.model_weights.get(f'{horizon}h', 0.1)

                # Gewichtung basierend auf Memory-Performance
                performance_weight = pred['memory_accuracy']
                final_weight = base_weight * performance_weight

                weighted_prob += pred['probability'] * final_weight
                total_weight += final_weight

            if total_weight > 0:
                overall_prob = weighted_prob / total_weight
                overall_confidence = np.mean([pred['confidence'] for pred in predictions.values()])

                # Gesamtsignal
                if overall_prob > 0.7:
                    overall_signal = "STARKER KAUF 🔥🔥🔥"
                    overall_action = "🚀 SOFORT KAUFEN!"
                elif overall_prob > 0.6:
                    overall_signal = "KAUF 🔥🔥"
                    overall_action = "🔥 KAUFEN"
                elif overall_prob < 0.3:
                    overall_signal = "STARKER VERKAUF 🔻🔻🔻"
                    overall_action = "💥 SOFORT VERKAUFEN!"
                elif overall_prob < 0.4:
                    overall_signal = "VERKAUF 🔻🔻"
                    overall_action = "🔻 VERKAUFEN"
                else:
                    overall_signal = "HALTEN ⚖️"
                    overall_action = "⚖️ POSITION HALTEN"

                predictions['GESAMT'] = {
                    'signal': overall_signal,
                    'action': overall_action,
                    'probability': overall_prob,
                    'confidence': overall_confidence
                }

        current_price = df['close'].iloc[-1]
        current_time = df.index[-1]

        return {
            'time': current_time,
            'price': current_price,
            'predictions': predictions,
            'memory_stats': {
                'session_count': self.session_count,
                'memory_size': len(self.feature_memory),
                'avg_accuracy': np.mean(list(self.accuracy_history)) if self.accuracy_history else 0.0,
                'best_accuracy': max(self.accuracy_history) if self.accuracy_history else 0.0
            }
        }

    def display_adaptive_dashboard(self, prediction_result: Dict):
        """Adaptives Memory-Dashboard"""

        print("\n" + "="*80)
        print("🧠 ADAPTIVE MEMORY LEARNING - LIVE DASHBOARD 🧠")
        print("="*80)

        if prediction_result and prediction_result['predictions']:
            predictions = prediction_result['predictions']
            memory_stats = prediction_result['memory_stats']

            print(f"\n📊 LIVE TRADING STATUS:")
            print(f"🕐 Zeit: {prediction_result['time'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"💰 Bitcoin Preis: ${prediction_result['price']:,.2f}")

            # Memory-Statistiken
            print(f"\n🧠 MEMORY-LEARNING STATUS:")
            print(f"🔄 Session: #{memory_stats['session_count']}")
            print(f"💾 Memory-Größe: {memory_stats['memory_size']} Datenpunkte")
            print(f"📈 Durchschnittliche Genauigkeit: {memory_stats['avg_accuracy']:.1%}")
            print(f"🏆 Beste Genauigkeit: {memory_stats['best_accuracy']:.1%}")

            # Gesamtsignal
            if 'GESAMT' in predictions:
                gesamt = predictions['GESAMT']
                print(f"\n🎯 ADAPTIVE HAUPTSIGNAL: {gesamt['signal']}")
                print(f"💡 EMPFEHLUNG: {gesamt['action']}")
                print(f"📈 Wahrscheinlichkeit: {gesamt['probability']:.1%}")
                print(f"🎪 Memory-Konfidenz: {gesamt['confidence']:.1%}")

            # Detaillierte Horizonte
            print(f"\n🔮 ADAPTIVE MULTI-HORIZONT SIGNALE:")
            print(f"{'Horizont':<8} | {'Signal':<22} | {'Wahrsch.':<10} | {'Memory-Acc.':<12}")
            print("-" * 65)

            for horizon_key, pred in predictions.items():
                if horizon_key != 'GESAMT':
                    print(f"{horizon_key:<8} | {pred['signal']:<22} | "
                          f"{pred['probability']:.1%}{'':>3} | {pred['memory_accuracy']:.1%}{'':>6}")

            # Adaptive Empfehlung
            if 'GESAMT' in predictions:
                gesamt = predictions['GESAMT']

                print(f"\n💼 ADAPTIVE TRADING-EMPFEHLUNG:")
                if "STARKER KAUF" in gesamt['signal']:
                    print(f"   🚀 AKTION: SOFORT KAUFEN!")
                    print(f"   📝 Memory-Grund: {gesamt['probability']:.1%} Chance (Gelernt aus {memory_stats['memory_size']} Datenpunkten)")
                    print(f"   ⚡ Dringlichkeit: SEHR HOCH")
                elif "KAUF" in gesamt['signal']:
                    print(f"   🔥 AKTION: KAUFEN")
                    print(f"   📝 Memory-Grund: {gesamt['probability']:.1%} Chance (Adaptive Lernrate)")
                    print(f"   ⚡ Dringlichkeit: HOCH")
                elif "STARKER VERKAUF" in gesamt['signal']:
                    print(f"   💥 AKTION: SOFORT VERKAUFEN!")
                    print(f"   📝 Memory-Grund: {1-gesamt['probability']:.1%} Verlustrisiko (Memory-basiert)")
                    print(f"   ⚡ Dringlichkeit: SEHR HOCH")
                elif "VERKAUF" in gesamt['signal']:
                    print(f"   🔻 AKTION: VERKAUFEN")
                    print(f"   📝 Memory-Grund: {1-gesamt['probability']:.1%} Verlustrisiko")
                    print(f"   ⚡ Dringlichkeit: HOCH")
                else:
                    print(f"   ⚖️ AKTION: POSITION HALTEN")
                    print(f"   📝 Memory-Grund: Unklare Signale (Mehr Daten sammeln)")
                    print(f"   ⚡ Dringlichkeit: NIEDRIG")
        else:
            print("\n❌ Keine Signale verfügbar")

        print("="*80)

    def create_memory_visualization(self, prediction_result: Dict, df: pd.DataFrame):
        """Memory-basierte Visualisierung (nur bei Bedarf speichern)"""

        if not prediction_result or not prediction_result['predictions']:
            return

        print("\n📊 Erstelle Memory-Visualisierung...")

        predictions = prediction_result['predictions']
        current_price = prediction_result['price']
        current_time = prediction_result['time']
        memory_stats = prediction_result['memory_stats']

        # Kompakte 2x2 Visualisierung
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('🧠 ADAPTIVE MEMORY LEARNING - BITCOIN DASHBOARD 🧠',
                     fontsize=20, color='white', weight='bold', y=0.98)

        # 1. Bitcoin Preis + Memory-Signal
        recent_df = df.tail(24)  # Letzte 24 Stunden
        times = recent_df.index
        prices = recent_df['close']

        ax1.plot(times, prices, color='white', linewidth=3, label=f'Bitcoin: ${current_price:,.0f}', alpha=0.9)

        # Memory-Signal Punkt
        if 'GESAMT' in predictions:
            gesamt = predictions['GESAMT']
            if "STARKER KAUF" in gesamt['signal']:
                color, marker, size = '#00ff00', '^', 400
            elif "KAUF" in gesamt['signal']:
                color, marker, size = '#00ff88', '^', 300
            elif "STARKER VERKAUF" in gesamt['signal']:
                color, marker, size = '#ff0000', 'v', 400
            elif "VERKAUF" in gesamt['signal']:
                color, marker, size = '#ff4757', 'v', 300
            else:
                color, marker, size = '#ffa502', 'o', 250

            ax1.scatter([current_time], [current_price], color=color, s=size, marker=marker,
                       zorder=10, edgecolors='white', linewidth=3)

            # Memory-Info
            ax1.annotate(f'Memory-Signal\n{gesamt["probability"]:.0%}',
                        xy=(current_time, current_price),
                        xytext=(10, 20), textcoords='offset points',
                        fontsize=12, fontweight='bold', color='white',
                        bbox=dict(boxstyle='round,pad=0.5', facecolor=color, alpha=0.8))

        ax1.set_title('📈 BITCOIN + MEMORY-SIGNAL', fontsize=16, color='white', weight='bold')
        ax1.set_xlabel('Zeit', color='white')
        ax1.set_ylabel('Preis (USD)', color='white')
        ax1.legend(fontsize=12)
        ax1.grid(True, alpha=0.3)

        # 2. Memory-Learning Progress
        if self.accuracy_history:
            sessions = list(range(1, len(self.accuracy_history) + 1))
            accuracies = list(self.accuracy_history)

            ax2.plot(sessions, accuracies, color='#00ff88', linewidth=3, marker='o', markersize=6)
            ax2.fill_between(sessions, accuracies, alpha=0.3, color='#00ff88')

            # Trend-Linie
            if len(accuracies) > 1:
                z = np.polyfit(sessions, accuracies, 1)
                p = np.poly1d(z)
                ax2.plot(sessions, p(sessions), "--", color='white', alpha=0.8, linewidth=2)

            ax2.set_title(f'🧠 MEMORY-LEARNING FORTSCHRITT (Session #{memory_stats["session_count"]})',
                         fontsize=16, color='white', weight='bold')
            ax2.set_xlabel('Learning Sessions', color='white')
            ax2.set_ylabel('Genauigkeit', color='white')
            ax2.grid(True, alpha=0.3)

            # Beste Genauigkeit markieren
            best_idx = np.argmax(accuracies)
            ax2.scatter([sessions[best_idx]], [accuracies[best_idx]],
                       color='gold', s=200, marker='*', zorder=10, edgecolors='white', linewidth=2)

        # 3. Multi-Horizont Memory-Signale
        horizonte = []
        wahrscheinlichkeiten = []
        memory_accuracies = []
        colors = []

        for horizon_key, pred in predictions.items():
            if horizon_key != 'GESAMT':
                horizonte.append(horizon_key)
                wahrscheinlichkeiten.append(pred['probability'])
                memory_accuracies.append(pred['memory_accuracy'])

                if "STARKER KAUF" in pred['signal']:
                    colors.append('#00ff00')
                elif "KAUF" in pred['signal']:
                    colors.append('#00ff88')
                elif "STARKER VERKAUF" in pred['signal']:
                    colors.append('#ff0000')
                elif "VERKAUF" in pred['signal']:
                    colors.append('#ff4757')
                else:
                    colors.append('#ffa502')

        if horizonte:
            bars = ax3.bar(horizonte, wahrscheinlichkeiten, color=colors, alpha=0.8,
                          edgecolor='white', linewidth=2)

            # Memory-Accuracy als Text
            for i, (bar, prob, mem_acc) in enumerate(zip(bars, wahrscheinlichkeiten, memory_accuracies)):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                        f'{prob:.0%}\n(Mem: {mem_acc:.0%})', ha='center', va='bottom',
                        color='white', fontweight='bold', fontsize=12)

            ax3.axhline(y=0.5, color='white', linestyle='--', alpha=0.7, linewidth=2)
            ax3.set_title('🔮 MEMORY-HORIZONT SIGNALE', fontsize=16, color='white', weight='bold')
            ax3.set_xlabel('Horizont', color='white')
            ax3.set_ylabel('Kauf-Wahrscheinlichkeit', color='white')
            ax3.set_ylim(0, 1.1)
            ax3.grid(True, alpha=0.3)

        # 4. Memory-Statistiken
        ax4.axis('off')

        if 'GESAMT' in predictions:
            gesamt = predictions['GESAMT']

            stats_text = f"""🧠 ADAPTIVE MEMORY STATS:

💾 Memory-Größe: {memory_stats['memory_size']} Datenpunkte
🔄 Learning Session: #{memory_stats['session_count']}
📈 Ø Genauigkeit: {memory_stats['avg_accuracy']:.1%}
🏆 Beste Genauigkeit: {memory_stats['best_accuracy']:.1%}

🎯 AKTUELLES SIGNAL:
{gesamt['action']}

📊 Wahrscheinlichkeit: {gesamt['probability']:.1%}
🎪 Memory-Konfidenz: {gesamt['confidence']:.1%}

💰 Bitcoin: ${current_price:,.2f}
🕐 Zeit: {current_time.strftime('%H:%M:%S')}

🚀 MEMORY-LEARNING:
Modell wird bei jeder Ausführung
automatisch präziser!"""

            # Hintergrundfarbe je nach Signal
            if "STARKER KAUF" in gesamt['signal']:
                bg_color = '#004d00'
                text_color = '#00ff00'
            elif "KAUF" in gesamt['signal']:
                bg_color = '#003300'
                text_color = '#00ff88'
            elif "STARKER VERKAUF" in gesamt['signal']:
                bg_color = '#4d0000'
                text_color = '#ff4444'
            elif "VERKAUF" in gesamt['signal']:
                bg_color = '#330000'
                text_color = '#ff6666'
            else:
                bg_color = '#333300'
                text_color = '#ffaa00'

            ax4.text(0.5, 0.5, stats_text, transform=ax4.transAxes,
                    fontsize=14, color=text_color, ha='center', va='center', fontweight='bold',
                    bbox=dict(boxstyle='round,pad=1', facecolor=bg_color, alpha=0.9, edgecolor='white', linewidth=2))

        plt.tight_layout()

        # AUTOMATISCHE VISUALISIERUNG (kein Festplatten-Spam)
        print("\n📊 Zeige Memory-Learning Visualisierung...")
        plt.show()

        # Optional: Speichern nur bei expliziter Anfrage
        print("\n💾 Visualisierung wurde angezeigt (nicht gespeichert - Schutz vor Festplatten-Spam)")
        print("💡 Zum Speichern: Manuell über plt.savefig() im Code aktivieren")

def run_adaptive_memory_learning():
    """Hauptfunktion - Adaptive Memory Learning"""

    aml = AdaptiveMemoryLearning()

    print(f"\n🧠 STARTE ADAPTIVE MEMORY LEARNING...")
    print(f"🎯 Kein Festplatten-Spam - Nur Memory-basiert!")

    try:
        start_time = time.time()

        print(f"\n{'='*60}")
        print(f"🔄 MEMORY-LEARNING ANALYSE - {datetime.now().strftime('%H:%M:%S')}")
        print(f"{'='*60}")

        # 1. Neue Bitcoin-Daten sammeln
        df = aml.get_bitcoin_data_efficient()

        # 2. Memory aktualisieren
        aml.update_memory(df)

        # 3. Adaptives Training (nur im Memory)
        training_success = aml.adaptive_training()

        if not training_success:
            print("⚠️ Training übersprungen - sammle mehr Daten...")

        # 4. Adaptive Signale vorhersagen
        prediction_result = aml.predict_adaptive_signals(df)

        if not prediction_result:
            print("❌ Vorhersage fehlgeschlagen")
            return None

        # 5. Adaptives Dashboard anzeigen
        aml.display_adaptive_dashboard(prediction_result)

        # 6. Memory-Visualisierung (optional)
        aml.create_memory_visualization(prediction_result, df)

        # 7. Timing
        elapsed_time = time.time() - start_time
        print(f"\n⚡ MEMORY-LEARNING abgeschlossen in {elapsed_time:.1f}s")

        return {
            'prediction_result': prediction_result,
            'df': df,
            'elapsed_time': elapsed_time,
            'memory_stats': prediction_result['memory_stats']
        }

    except Exception as e:
        print(f"❌ MEMORY-LEARNING Fehler: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = run_adaptive_memory_learning()

    if result:
        memory_stats = result['memory_stats']
        print(f"\n🎉 ADAPTIVE MEMORY LEARNING erfolgreich!")
        print(f"⚡ Laufzeit: {result['elapsed_time']:.1f}s")
        print(f"🧠 Memory-Session: #{memory_stats['session_count']}")
        print(f"📈 Durchschnittliche Genauigkeit: {memory_stats['avg_accuracy']:.1%}")
        print(f"🏆 Beste Genauigkeit: {memory_stats['best_accuracy']:.1%}")
        print(f"💾 Kein Festplatten-Spam - Alles im Memory!")
        print(f"🚀 Modell wird bei jeder Ausführung präziser!")
    else:
        print(f"\n❌ ADAPTIVE MEMORY LEARNING fehlgeschlagen")
