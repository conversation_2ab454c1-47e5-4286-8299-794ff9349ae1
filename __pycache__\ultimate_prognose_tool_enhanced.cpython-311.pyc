�

    6meh��  �                   �N  � d Z ddlZddlZddlZddlZddlZ ej        d�  �         ddlZddl	Z
ddlmZmZ ddl
Z
ddlZddlZddlmZmZ dddddd�Z	 ddlZddlmZ dd	lmZmZmZmZ d
ed<    edej        �  �         ej        j        �                    d�  �         ej        j        �                     d�  �         	 ej        �!                    d
�  �        Z"e"r=e"D ]"Z#ej        j$        �%                    e#d
�  �         �# ed e&e"�  �        � ��  �         n ed�  �         n # e'$ rZ( ede(� ��  �         Y dZ([(ndZ([(ww xY wn # e)$ rZ( ede(� ��  �         Y dZ([(ndZ([(ww xY w	 ddl*m+Z+m,Z, ddl-m.Z. ddl/m0Z0m1Z1m2Z2 ddl3m4Z4m5Z5 ddl6m7Z7m8Z8m9Z9 ddl:m;Z;m<Z< d
ed<    ed�  �         n # e)$ rZ( ede(� ��  �         Y dZ([(ndZ([(ww xY w	 ddl=Z>d
ed<    ed�  �         n# e)$ r  ed�  �         Y nw xY w	 ddl?m@Z@ ddlAmBZB d
ed <    ed!�  �         n# e)$ r  ed"�  �         Y nw xY wddlCZDddlEmFZG dd#lHmIZI dd$lJmKZK ddlLZM	 ddlNmOZP ddlQmRZS dd%lTmUZU ddlVmWZX d
ed&<    ed'�  �         n# e)$ r  ed(�  �         Y nw xY wddlYZZdd)lYm[Z[m\Z\m]Z]  ej^        ej_        d* ej`        d+�  �         eja        �   �         g�,�  �          G d-� d.�  �        Zbecd/k    rL	  eb�   �         Zd ed0�  �         dS # e'$ r,Z( ed1e(� ��  �          eje        d2e(� ��  �         Y dZ([(dS dZ([(ww xY wdS )3a  
ULTIMATE PROGNOSE-TOOL ENHANCED
===============================

Verbessertes Bitcoin-Prognose-Tool mit:
- 3D-Visualisierung aller Trading-Faktoren
- Erweiterte Fehlerbehandlung
- Optimierte Performance
- Trading-Signal-Generierung

Erstellt: 2025-07-02
Version: 2.0 ENHANCED
�    N�ignore)�datetime�	timedelta)�defaultdict�dequeF)�
tensorflow�sklearn�xgboost�plotly�scipy)�keras)�layers�models�
optimizers�	callbacksTr   z TensorFlow verfuegbar - Version:�GPUzGPU verfuegbar: zCPU-Modus aktiviertz"GPU-Konfiguration fehlgeschlagen: zTensorFlow nicht verfuegbar: )�RandomForestRegressor�GradientBoostingRegressor)�MLPRegressor)�RobustScaler�MinMaxScaler�StandardScaler)�cross_val_score�TimeSeriesSplit)�mean_squared_error�r2_score�mean_absolute_error)�SelectKBest�f_regressionr	   zScikit-Learn verfuegbarzScikit-Learn nicht verfuegbar: r
   zXGBoost verfuegbarzXGBoost nicht verfuegbar)�stats)�
find_peaksr   zSciPy verfuegbarzSciPy nicht verfuegbar)�FigureCanvasTkAgg)�Axes3D)�
make_subplotsr   z/Plotly verfuegbar - 3D-Visualisierung aktiviertz1Plotly nicht verfuegbar - Matplotlib als Fallback)�ttk�
messagebox�
filedialogz)%(asctime)s - %(levelname)s - %(message)szultimate_prognose_enhanced.log)�level�format�handlersc                   �   � e Zd ZdZdZd� Zd� Zd� Zd� Zd� Z	d� Z
d	� Zd
� Zd� Z
d� Zd
� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zdd�Zd� Zd� Zd� ZdS )�UltimatePrognoseToolEnhancedz�
    ULTIMATE PROGNOSE-TOOL ENHANCED
    
    Verbessertes Bitcoin-Prognose-System mit 3D-Visualisierung
    und erweiterten Trading-Funktionen
    z2.0 ENHANCEDc                 �   � t          d| j        � ��  �         t          d�  �         ddddddg d	�d
ddd
�
| _        ddt          d��  �        t          d��  �        t          d��  �        t          d��  �        t          d��  �        d�| _        dddddddd�| _        t
          j        �   �         | _        t
          j        �   �         | _	        i | _
        i | _        i | _        d| _
        i | _        g | _        d| _        t#          j        �   �         | _        d| _        | �                    �   �          | �                    �   �          | �                    �   �          t          d�  �         t1          j        d�  �         dS )z1Initialisierung mit verbesserter Fehlerbehandlungz"
ULTIMATE PROGNOSE-TOOL ENHANCED vz<============================================================�<   �    �   �����MbP?g�������?�d   )�   �   �   �   �0   �{�G�z�?g�������?gffffff�?)
�sequence_length�
batch_size�epochs�
learning_rate�validation_split�max_features�prediction_horizons�trading_threshold�risk_tolerance�confidence_thresholdr   ��  )�maxlen��  )�total_predictions�successful_predictions�accuracy_history�trading_signals�profit_loss�confidence_scores�execution_times�        )�total_trades�winning_trades�
losing_trades�total_profit�max_drawdown�sharpe_ratio�win_rateFNzTool erfolgreich initialisiert!z*UltimatePrognoseToolEnhanced initialisiert)�print�VERSION�configr   �performance_metrics�trading_metrics�pd�	DataFrame�market_data�processed_featuresr   �model_performance�scalers�data_quality_score�current_signals�signal_history�
is_running�	threading�Lock�	data_lock�root�_setup_directories�_initialize_models�_load_historical_performance�logging�info��selfs    �)E:\Dev\ultimate_prognose_tool_enhanced.py�__init__z%UltimatePrognoseToolEnhanced.__init__�   s�  � �
�B�D�L�B�B�C�C�C�
�h����  "���"� #��#5�#5�#5�!%�"�$'�
� 
��� "#�&'� %�T� 2� 2� 2�$�C�0�0�0� ��,�,�,�!&�c�!2�!2�!2�$�C�0�0�0�$
� $
�� � ������� 
�  
��� �<�>�>���"$�,�.�.������!#������"%���  "��� ���  ���"��)�)��� ��	� 	
���!�!�!����!�!�!��)�)�+�+�+�
�/�0�0�0���A�B�B�B�B�B�    c                 �D   � g d�}|D ]}t          j        |d��  �         �dS )z'Erstelle erweiterte Verzeichnisstruktur)r   �data�logs�exportsrI   �performance_reports�3d_visualizationsT)�exist_okN)�os�makedirs)rn   �directories�	directorys      ro   rh   z/UltimatePrognoseToolEnhanced._setup_directories�   sF   � �
� 
� 
�� %� 	2� 	2�I��K�	�D�1�1�1�1�1�	2� 	2rq   c                 �  � 	 d}t           j        �                    |�  �        r|t          |d�  �        5 }t	          j        |�  �        }| j        �                    |�                    di �  �        �  �         t          d�  �         ddd�  �         dS # 1 swxY w Y   dS dS # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)z"Lade historische Performance-Datenz data/historical_performance.json�rrY   zHistorische Performance geladenNz,Konnte historische Performance nicht laden: )
ry   �path�exists�open�json�loadrY   �update�getrU   �	Exceptionrk   �warning)rn   �	perf_file�f�historical_data�es        ro   rj   z9UltimatePrognoseToolEnhanced._load_historical_performance�   s3  � �	P�:�I��w�~�~�i�(�(� 
=��)�S�)�)� =�Q�&*�i��l�l�O��(�/�/��0C�0C�DU�WY�0Z�0Z�[�[�[��;�<�<�<�=� =� =� =� =� =� =� =� =� =� =� =���� =� =� =� =� =� =�
=� 
=��
 � 	P� 	P� 	P��O�N�1�N�N�O�O�O�O�O�O�O�O�O�����	P���s;   �1B! �AB�B! �B�B! �B�B! �!
C
�+C�C
c           
      �  � t          d�  �         t          d         rX| j        �                    t	          ddddddd	�
�  �        t          ddd
dd��  �        t
          ddddddd��  �        d��  �         t          d         r&t          j        ddd
ddd
d
d
dd	��
  �
        | j        d<   t          d         r
d| j        d<   t          �   �         t          �   �         t          �   �         d�| _        t          dt          | j        �  �        � ��  �         t          j        d t!          | j        �                    �   �         �  �        � ��  �         d!S )"z=Initialisiere erweiterte Modelle mit verbesserter Architekturz4Initialisiere erweiterte Machine Learning Modelle...r	   ��   �   �   �   �sqrt�*   �����)�n_estimators�	max_depth�min_samples_split�min_samples_leafr>   �random_state�n_jobs�   �   皙�����?皙�����?)r�   r�   r<   �	subsampler�   )r�   r2   �2   �relu�adamr1   �adaptiverE   )�hidden_layer_sizes�
activation�solver�alphar<   �max_iterr�   )�random_forest_optimized�gradient_boost_optimized�neural_network_optimizedr
   )
r�   r�   r<   r�   �colsample_bytree�gamma�	reg_alpha�
reg_lambdar�   r�   �xgboost_optimizedr   �tensorflow_placeholder�deep_learning_ensemble)�robust�minmax�standardz"Erweiterte Modelle initialisiert: zModelle initialisiert: N)rU   �LIBRARIES_STATUSr   r�   r   r   r   �xgb�XGBRegressorr   r   r   r_   �lenrk   rl   �list�keysrm   s    ro   ri   z/UltimatePrognoseToolEnhanced._initialize_models�   s�  � �
�D�E�E�E� �I�&� 	��K���+@�!$� �&'�%&�!'�!#��,� ,� ,� -F�!$��"%�!�!#�-� -� -� -9�'5�%�!��",� �!#�-� -� -�# �  � 
� 
� 
�: �I�&� 	�/2�/?� ��!��!$������0� 0� 0�D�K�+�,� �L�)� 	M�4L�D�K�0�1� #�n�n�"�n�n�&�(�(�
� 
��� 	�E�3�t�{�3C�3C�E�E�F�F�F���I�t�D�K�4D�4D�4F�4F�/G�/G�I�I�J�J�J�J�Jrq   c                 ��  � t          d�  �         t          j        �   �         }	 | j        5  | �                    �   �         }| �                    �   �         }| �                    ||�  �        }| �                    |�  �        | _        || _        t          j        �   �         |z
  }| j	        d         �
                    |�  �         t          d|d�d��  �         t          d| j        d���  �         t          j        dt          |�  �        � d	��  �         |cd
d
d
�  �         S # 1 swxY w Y   d
S # t          $ r5}t          j        d|� ��  �         | �                    �   �         cY d
}~S d
}~ww xY w)z>Sammle erweiterte Marktdaten mit verbesserter FehlerbehandlungzSammle erweiterte Marktdaten...rL   z#Erweiterte Marktdaten gesammelt in �.2f�su   Datenqualität: z.1%zMarktdaten gesammelt: z ZeilenNz$Fehler beim Sammeln der Marktdaten: )rU   �timerf   �_fetch_bitcoin_data_enhanced�_fetch_additional_market_data�_combine_enhanced_data�_assess_enhanced_data_qualityr`   r\   rX   �appendrk   rl   r�   r�   �error� _generate_enhanced_fallback_data)rn   �
start_time�btc_data�additional_data�
combined_data�execution_timer�   s          ro   �collect_enhanced_market_dataz9UltimatePrognoseToolEnhanced.collect_enhanced_market_data'  s�  � �
�/�0�0�0��Y�[�[�
�	;��� 
%� 
%��<�<�>�>�� #'�"D�"D�"F�"F�� !%� ;� ;�H�o� V� V�
� +/�*L�*L�]�*[�*[��'� $1�� �!%����z�!9���(�):�;�B�B�>�R�R�R��Q�N�Q�Q�Q�Q�R�R�R��F��)@�F�F�F�G�G�G���Q�c�-�6H�6H�Q�Q�Q�R�R�R�$�/
%� 
%� 
%� 
%� 
%� 
%� 
%� 
%� 
%� 
%� 
%� 
%���� 
%� 
%� 
%� 
%� 
%� 
%��2 � 	;� 	;� 	;��M�D��D�D�E�E�E��8�8�:�:�:�:�:�:�:�:�����	;���sA   �D- �C(D �D- � D$�$D- �'D$�(D- �-
E,�7*E'�!E,�'E,c                 �4  � 	 t          j        d�  �        }|�                    dd��  �        }t          |�  �        dk    r�d� |j        D �   �         |_        |�                    �   �         }|d         �                    �   �         |d<   |d	         �                    �   �         |d
<   |d         |d         z  |d
<   |d         |d         z  |d<   |�                    d�  �        S t          d�  �        �# t          $ r}t          j        d|� ��  �         � d}~ww xY w)zHole erweiterte Bitcoin-DatenzBTC-USD�30d�1h��period�intervalr2   c                 �6   � g | ]}|�                     �   �         ��S � )�lower)�.0�cols     ro   �
<listcomp>zMUltimatePrognoseToolEnhanced._fetch_bitcoin_data_enhanced.<locals>.<listcomp>S  s    � �@�@�@�c�c�i�i�k�k�@�@�@rq   �close�price_change�volume�
volume_change�high�low�high_low_ratior�   �open_close_ratio�float32zZu wenig Bitcoin-DatenzFehler bei Bitcoin-Daten: N)�yf�Ticker�historyr�   �columns�dropna�
pct_change�astype�
ValueErrorr�   rk   r�   )rn   �btc�dfr�   s       ro   r�   z9UltimatePrognoseToolEnhanced._fetch_bitcoin_data_enhancedJ  s"  � �	��)�I�&�&�C� ���E�D��9�9�B��2�w�w��}�}�@�@�R�Z�@�@�@��
��Y�Y�[�[�� &(��[�%;�%;�%=�%=��>�"�&(��l�&=�&=�&?�&?��?�#�')�&�z�B�u�I�'=��#�$�)+�F��b��k�)A��%�&��y�y��+�+�+� �!9�:�:�:��� 	� 	� 	��M�:�q�:�:�;�;�;������	���s   �CC0 �!C0 �0
D�:D�Dc                 ��  � i }	 g d�}|D ]�}	 t          j        |�  �        }|�                    dd��  �        }t          |�  �        dk    r/|d         �                    �   �         �                    �   �         ||<   �q# t          $ r$}t          j        d|� d|� ��  �         Y d	}~��d	}~ww xY w| �	                    �   �         |d
<   n.# t          $ r!}t          j        d|� ��  �         Y d	}~nd	}~ww xY w|S )u5   Hole zusätzliche Marktdaten für bessere Vorhersagen)zETH-USDzBNB-USDzADA-USD�7dr�   r�   r�   �ClosezKonnte z nicht laden: N�market_sentimentu%   Fehler bei zusätzlichen Marktdaten: )
r�   r�   r�   r�   r�   r�   r�   rk   r�   �_calculate_market_sentiment)rn   r�   �crypto_symbols�symbol�tickerrs   r�   s          ro   r�   z:UltimatePrognoseToolEnhanced._fetch_additional_market_datad  sK  � ���	I�>�>�>�N�(� 
I� 
I��I��Y�v�.�.�F�!�>�>���>�E�E�D��4�y�y�2�~�~�26�w�-�2J�2J�2L�2L�2S�2S�2U�2U���/��� � I� I� I��O�$G�f�$G�$G�A�$G�$G�H�H�H�H�H�H�H�H�����I���� 37�2R�2R�2T�2T�O�.�/�/��� 	I� 	I� 	I��O�G�A�G�G�H�H�H�H�H�H�H�H�����	I���� �sA   �C �A-A;�:C �;
B)�B$�C �$B)�)C �
C/�C*�*C/c                 �"  � 	 | j         j        sa| j         d         �                    �   �         �                    d�  �        �                    �   �         }dd|z   z  }|�                    d�  �        S n#  Y nxY wt
          j        dgdz  d��  �        S )z&Berechne vereinfachten Markt-Sentimentr�   r6   r3   g      �?�   r�   )�name)r\   �emptyr�   �rolling�std�fillnarZ   �Series)rn   �
volatility�	sentiments      ro   r�   z8UltimatePrognoseToolEnhanced._calculate_market_sentiment}  s�   � �	��#�)� 
-�!�-�g�6�A�A�C�C�K�K�B�O�O�S�S�U�U�
���Z��0�	� �'�'��,�,�,�
-��	��D���� �y�#����+=�>�>�>�>s   �A,A0 �0A4c                 �H  � 	 |�                     �   �         }|�                    �   �         D ]�\  }}t          |t          j        �  �        r�t          |�  �        dk    rn|dk    rct          |�  �        t          |�  �        k    r|j        t          |�  �         d�         }n|�                    |j        d��  �        }|||� d�<   ��|||<   ��|�	                    d��  �        �	                    d�  �        S # t          $ r#}t          j        d|� ��  �         |cY d}~S d}~ww xY w)z2Kombiniere alle Daten zu einem erweiterten Datasetr   r�   N�ffill��method�_correlationz#Fehler beim Kombinieren der Daten: )
�copy�items�
isinstancerZ   r�   r�   �iloc�reindex�indexr�   r�   rk   r�   )rn   r�   r�   �combined_dfr�   rs   �aligned_datar�   s           ro   r�   z3UltimatePrognoseToolEnhanced._combine_enhanced_data�  sA  � �	�"�-�-�/�/�K� !0� 5� 5� 7� 7� 
3� 
3�����d�B�I�.�.� 3�3�t�9�9�q�=�=��!3�3�3��t�9�9��K�(8�(8�8�8�+/�9�c�+�6F�6F�5F�5G�5G�+H�L�L�+/�<�<��8I�RY�<�+Z�+Z�L�?K��v�$;�$;�$;�<�<�.2��F�+���%�%�W�%�5�5�<�<�Q�?�?�?��� 	� 	� 	��M�C��C�C�D�D�D��O�O�O�O�O�O�����	���s   �C1C4 �4
D!�>D�D!�D!c                 �:  � |j         rdS g }d|�                    �   �         �                    �   �         �                    �   �         t          |�  �        t          |j        �  �        z  z  z
  }|�                    |�  �         |j         s�|j        d         }t          |d�  �        r|j        �|�	                    d�  �        n|}n(|�
                    �   �         �                    d��  �        }t          j
        �   �         |z
  }t          dd|�                    �   �         dz  z
  �  �        }|�                    |�  �         |d	         �                    �   �         dk    rXt#          d
|d	         �                    �   �         |d	         �                    �   �         z  �  �        }|�                    |�  �         t          |�  �        dk    r�|d	         �                    �   �         �                    �   �         }	dt#          d
t+          |	�  �        dk    �                    �   �         t          |	�  �        z  �  �        z
  }
|�                    |
�  �         t#          d
t          |j        �  �        dz  �  �        }|�                    |�  �         t-          j        |�  �        S )
u#   Erweiterte DatenqualitätsbewertungrM   r3   r�   �tz_localizeN)�tzinfor   i   r�   �      �?g333333�?�   )r�   �isnull�sumr�   r�   r�   r  �hasattr�tzr  �
to_pydatetime�replacer   �now�max�
total_secondsr�   �min�meanr�   r�   �abs�np)rn   r�   �quality_factors�completeness�	last_time�last_time_naive�	time_diff�recency�variability�
price_changes�consistency�richnesss               ro   r�   z:UltimatePrognoseToolEnhanced._assess_enhanced_data_quality�  sF  � �
�8� 	��3��� �B�I�I�K�K�O�O�-�-�1�1�3�3�s�2�w�w��R�Z���7P�Q�R�����|�,�,�,� �x� 
	,�����I��y�-�0�0� 
Q�AJ��AY�)�"7�"7��"=�"=�"=�_h���"+�"9�"9�";�";�"C�"C�4�"C�"P�"P�� �����8�I��!�Q�)�"9�"9�";�";�d�"B�C�D�D�G��"�"�7�+�+�+� �g�;�?�?���q� � ��c�2�g�;�?�?�#4�#4�r�'�{�7G�7G�7I�7I�#I�J�J�K��"�"�;�/�/�/� �r�7�7�Q�;�;��w�K�2�2�4�4�;�;�=�=�M��c�#��M�(:�(:�T�(A�'F�'F�'H�'H�3�}�K]�K]�']�^�^�^�K��"�"�;�/�/�/� �s�C��
�O�O�b�0�1�1�����x�(�(�(��w��'�'�'rq   c                 �  � t          d�  �         d}t          j        �   �         }|t          |��  �        z
  }t	          j        ||d��  �        }t          j        �                    d�  �         d}t          j        �	                    dd	|�  �        }|g}|D ]3}|d
         d|z   z  }	|�
                    t          |	d�  �        �  �         �4|dd
�         }t	          j        |d
t          |�  �        �         ��  �        }
||
d<   |
d         �                    d�  �        �                    |
d         j        d         �  �        |
d<   |
ddg         �                    d��  �        t          j        �                    ddt          |
�  �        �  �        z  |
d<   |
ddg         �                    d��  �        t          j        �                    ddt          |
�  �        �  �        z  |
d<   t          j        �                    ddt          |
�  �        �  �        |
d<   |
d         �                    �   �         |
d<   |
d         �                    �   �         |
d<   |
d         |
d         z  |
d<   |
d         |
d         z  |
d<   |
�                    d�  �        | _        d| _        t/          j        d �  �         |
�                    d�  �        S )!z3Generiere erweiterte Fallback-Daten bei API-Fehlernz&Generiere erweiterte Fallback-Daten...r�   )�hours�H)�start�end�freqr�   iȯ  r   r8   r�   r3   rC   N)r  r�   r�   )�axisr
  gR���Q�?r�   g\���(\�?r�   i@B i@KL r�   r�   r�   r�   r�   r�   r�   z#Erweiterte Fallback-Daten generiert)rU   r   r  r   rZ   �
date_ranger  �random�seed�normalr�   r  r[   r�   �shiftr�   r  �uniformr  r�   r�   r\   r`   rk   rl   )rn   r'  �end_timer�   �
time_index�
base_price�returns�prices�ret�	new_pricer�   s              ro   r�   z=UltimatePrognoseToolEnhanced._generate_enhanced_fallback_data�  s�  � �
�6�7�7�7����<�>�>���	�� 6� 6� 6�6�
��]����L�L�L�
�
�	���r�����
��)�"�"�1�d�E�2�2������ 	0� 	0�C��r�
�a�#�g�.�I��M�M�#�i��.�.�/�/�/�/�������
�\�
�<�C��K�K�<� 8�
9�
9�
9����7����[�&�&�q�)�)�0�0��G��1A�!�1D�E�E��6�
����)�*�.�.�A�.�6�6���9J�9J�3�PT�VY�Z\�V]�V]�9^�9^�^��6�
����(�)�-�-�1�-�5�5��	�8I�8I�$�PS�UX�Y[�U\�U\�8]�8]�]��5�	��y�(�(��'�3�r�7�7�C�C��8��  ��[�3�3�5�5��>�� ��l�5�5�7�7��?��!�&�z�B�u�I�5����!#�F��b��k�!9�����9�9�Y�/�/���"%�����:�;�;�;��y�y��#�#�#rq   c                 �  � t          d�  �         t          j        �   �         }|�                    �   �         }| �                    |�  �        }| �                    |�  �        }| �                    |�  �        }| �                    |�  �        }| �                    |�  �        }|�                    d��  �        �                    d��  �        �                    d�  �        }t          |j
        �  �        | j        d         k    r| �                    |�  �        }t          j        �   �         |z
  }t          dt          |j
        �  �        � d|d	�d
��  �         t          j        dt          |j
        �  �        � d��  �         || _        |S )
zCErweiterte Feature Engineering mit Trading-spezifischen Indikatorenu4   Erstelle erweiterte Features für Trading-Analyse...r�   r�   �bfillr   r>   zErweiterte Features erstellt: z
 Features in r�   r�   z#Feature Engineering abgeschlossen: z	 Features)rU   r�   r  �_add_enhanced_price_features�"_add_advanced_technical_indicators�_add_trading_specific_features�_add_market_structure_features�_add_volatility_featuresr�   r�   r�   rW   �_select_best_featuresrk   rl   r]   )rn   r�   r�   �features_dfr�   s        ro   �engineer_enhanced_featuresz7UltimatePrognoseToolEnhanced.engineer_enhanced_features�  sj  � �
�D�E�E�E��Y�[�[�
��g�g�i�i�� �7�7��D�D���=�=�k�J�J���9�9�+�F�F���9�9�+�F�F���3�3�K�@�@�� "�(�(��(�8�8�?�?�w�?�O�O�V�V�WX�Y�Y�� �{�"�#�#�d�k�.�&A�A�A��4�4�[�A�A�K�����z�1��
�k�s�;�3F�/G�/G�k�k�Vd�k�k�k�k�l�l�l���^�3�{�?R�;S�;S�^�^�^�_�_�_�"-����rq   c                 ��  � dD ]a}|d         �                     |�  �        |d|� d�<   t          j        |d         |d         �                    |�  �        z  �  �        |d|� d�<   �bdD ]c}|d         |d         �                    |�  �        z  dz
  |d|� d�<   |d|� d�         |d|� d�         �                    d�  �        z
  |d	|� d�<   �dd
D ]�}|d         |d         �                    |�  �        �                    �   �         z
  |d         �                    |�  �        �                    �   �         |d         �                    |�  �        �                    �   �         z
  z  |d|� d�<   ��|S )z"Erweiterte preis-basierte Features)r3   �   r4   r5   r6   r7   r�   r�   �return_�h�log_return_)r4   r5   r6   r3   �	momentum_�
acceleration_�r6   r7   r�   �price_position_)r�   r  �logr1  r�   r  r  )rn   r�   r�   �windows       ro   r<  z9UltimatePrognoseToolEnhanced._add_enhanced_price_features  s�  � � 1� 	Z� 	Z�F�&(��k�&<�&<�V�&D�&D�B�"��"�"�"�#�*,�&��G��r�'�{�?P�?P�QW�?X�?X�1X�*Y�*Y�B�&�V�&�&�&�'�'� "� 	k� 	k�F�(*�7��b��k�6G�6G��6O�6O�(O�RS�(S�B�$�6�$�$�$�%�,.�/D�6�/D�/D�/D�,E��K`�W]�K`�K`�K`�Ha�Hg�Hg�hi�Hj�Hj�,j�B�(�v�(�(�(�)�)� $� 	W� 	W�F�/1�'�{�R��[�=P�=P�QW�=X�=X�=\�=\�=^�=^�/^��7��#�#�F�+�+�/�/�1�1�B�w�K�4G�4G��4O�4O�4S�4S�4U�4U�U�/W�B�*��*�*�*�+�+� �	rq   c                 �f  � dD ]�}|d         �                     |�  �        �                    �   �         |d|� �<   |d         �                    |��  �        �                    �   �         |d|� �<   |d         �                     |�  �        �                    d� d��  �        |d	|� �<   |d         |d|� �         z  |d
|� d�<   |d         |d|� �         z  |d|� d�<   ��d
D ]�\  }}}|d         �                    |��  �        �                    �   �         }|d         �                    |��  �        �                    �   �         }||z
  }|�                    |��  �        �                    �   �         }	||d|� d|� �<   |	|d|� d|� �<   ||	z
  |d|� d|� �<   ��dD ]�}
|d         �                    �   �         }|�                    |dk    d�  �        �                     |
��  �        �                    �   �         }|�                    |dk     d�  �         �                     |
��  �        �                    �   �         }
||
z  }ddd|z   z  z
  |d|
� �<   ��dD ]�\  }}|d         �                     |�  �        �                    �   �         }|d         �                     |�  �        �                    �   �         }|||z  z   |d|� d|� �<   |||z  z
  |d|� d|� �<   |d         |d|� d|� �         z
  |d|� d|� �         |d|� d|� �         z
  z  |d|� d|� �<   |d|� d|� �         |d|� d|� �         z
  |z  |d|� d|� �<   ��|S )z!Erweiterte technische Indikatoren)r�   �
   r  r�   r2   r�   r�   �sma_)�span�ema_c           	      �j   � t          j        | t          dt          | �  �        dz   �  �        ��  �        S )Nr3   )�weights)r  �average�ranger�   )�xs    ro   �<lambda>zQUltimatePrognoseToolEnhanced._add_advanced_technical_indicators.<locals>.<lambda>+  s)   � �"�*�Q��a��Q�����0B�0B�C�C�C� rq   T)�raw�wma_�
price_sma_�_ratio�
price_ema_))r5   �   �	   )r�   �#   r�   )�   �'   r`  �macd_�_�macd_signal_�macd_histogram_��   �   r0   r   )rN  r2   r3   �rsi_))r  r�   )r  r3   )r�   r�   �	bb_upper_�	bb_lower_�bb_position_�	bb_width_)r�   r  �ewm�apply�diff�wherer�   )rn   r�   rN  �fast�slow�signal�ema_fast�ema_slow�macd�macd_signalr�   �delta�gain�loss�rs�std_dev�smar�   s                     ro   r=  z?UltimatePrognoseToolEnhanced._add_advanced_technical_indicators$  s'  � � 0� 	P� 	P�F�"$�W�+�"5�"5�f�"=�"=�"B�"B�"D�"D�B��f����"$�W�+�/�/�v�/�">�">�"C�"C�"E�"E�B��f����"$�W�+�"5�"5�f�"=�"=�"C�"C�C�C�� #D� #O� #O�B��f���� /1��k�B��f���<O�.O�B�*�F�*�*�*�+�.0��k�B��f���<O�.O�B�*�F�*�*�*�+�+� #I� 	E� 	E��D�$���'�{���D��1�1�6�6�8�8�H��'�{���D��1�1�6�6�8�8�H��h�&�D��(�(��(�/�/�4�4�6�6�K�(,�B�$�t�$�$�d�$�$�%�/:�B�+�d�+�+�T�+�+�,�26��2D�B�.��.�.��.�.�/�/� #� 	9� 	9�F��w�K�$�$�&�&�E��K�K���	�1�-�-�6�6�f�6�E�E�J�J�L�L�D��[�[����A�.�.�.�7�7�v�7�F�F�K�K�M�M�D����B�"%���B���"8�B��f�����  ;� 	[� 	[�O�F�G��W�+�%�%�f�-�-�2�2�4�4�C��W�+�%�%�f�-�-�1�1�3�3�C�14��#�
�1F�B�-�6�-�-�G�-�-�.�14��#�
�1F�B�-�6�-�-�G�-�-�.�57��[�2�Fd�RX�Fd�Fd�[b�Fd�Fd�Ce�5e��1�v�1�1��1�1�2�R�8V�F�8V�8V�W�8V�8V�5W�W�5Y�B�0�f�0�0�w�0�0�1�24�5S��5S�5S�'�5S�5S�2T�13�4R��4R�4R��4R�4R�1S�3T�WZ�2[�B�-�6�-�-�G�-�-�.�.� �	rq   c                 �   � |d         |d         �                     �   �         z  �                    �   �         |d<   |d         t          j        |d         �                    �   �         �  �        z  �                    �   �         |d<   dD ]�}|d         |d         z  �                    |�  �        �                    �   �         |d         �                    |�  �        �                    �   �         z  |d|� d�<   |d         |d|� d�         z  |d|� d	�<   ��|d
         |d         z
  }t          j        |d
         |d         �                    �   �         z
  �  �        }t          j        |d         |d         �                    �   �         z
  �  �        }t          j	        |t          j	        ||�  �        �  �        }dD ]I}|�                    |�  �        �
                    �   �         |d
|� �<   |d
|� �         |d         z  |d|� �<   �J|S )zTrading-spezifische Featuresr�   r�   �vpt�obvrK  �vwap_rG  �price_vwap_�h_ratior�   r�   rh  �atr_�
atr_ratio_)r�   �cumsumr  �signrr  r�   r  r  r1  �maximumr  )rn   r�   rN  �high_low�
high_close�	low_close�
true_ranger�   s           ro   r>  z;UltimatePrognoseToolEnhanced._add_trading_specific_featuresQ  s  � � ��\�B�w�K�$:�$:�$<�$<�<�D�D�F�F��5�	� ��\�B�G�B�w�K�,<�,<�,>�,>�$?�$?�?�G�G�I�I��5�	� $� 	T� 	T�F�%'��[�2�h�<�%?�$H�$H��$P�$P�$T�$T�$V�$V�Y[�\d�Ye�Ym�Ym�nt�Yu�Yu�Yy�Yy�Y{�Y{�${�B� �v� � � �!�02�7��b�AR��AR�AR�AR�>S�0S�B�,�V�,�,�,�-�-� �f�:��5�	�)���V�B�v�J��G��):�):�)<�)<�<�=�=�
��F�2�e�9�r�'�{�'8�'8�':�':�:�;�;�	��Z��"�*�Z��*K�*K�L�L�
�"� 	J� 	J�F�",�"4�"4�V�"<�"<�"A�"A�"C�"C�B��f����(*�?�&�?�?�(;�b��k�(I�B�$�F�$�$�%�%��	rq   c                 �  � dD ]�}|d         �                     |�  �        �                    �   �         |d|� d�<   |d         �                     |�  �        �                    �   �         |d|� d�<   |d         |d|� d�         z
  |d         z  |d|� d�<   |d|� d�         |d         z
  |d         z  |d	|� d�<   ��|d         |d         z   |d         z   d
z  |d<   d|d         z  |d         z
  |d
<   d|d         z  |d         z
  |d<   |d         |d         |d         z
  z   |d<   |d         |d         |d         z
  z
  |d<   |S )zMarktstruktur-FeaturesrK  r�   �support_rG  r�   �resistance_r�   �support_distance_�resistance_distance_rE  �pivotr�   �r1�s1�r2�s2)r�   r  r  )rn   r�   rN  s      ro   r?  z;UltimatePrognoseToolEnhanced._add_market_structure_featuresj  s�  � � $� 	m� 	m�F�')�%�y�'8�'8��'@�'@�'D�'D�'F�'F�B�#�&�#�#�#�$�*,�V�*�*<�*<�V�*D�*D�*H�*H�*J�*J�B�&�V�&�&�&�'�13�G��r�BV�V�BV�BV�BV�?W�1W�[]�^e�[f�0f�B�,�6�,�,�,�-�46�7N�V�7N�7N�7N�4O�RT�U\�R]�4]�ac�dk�al�3l�B�/�f�/�/�/�0�0� �&�z�B�u�I�-��7��;�q�@��7���r�'�{�?�R��Y�.��4���r�'�{�?�R��Z�/��4���g�;�"�V�*�r�%�y�"8�9��4���g�;�"�V�*�r�%�y�"8�9��4���	rq   c                 �@  � dD ]�}|d         �                     �   �         }|�                    |�  �        �                    �   �         |d|� d�<   t          j        |dz  �                    |�  �        �                    �   �         �  �        |d|� d�<   |d|� d�         |d|� d�         �                    |dz  �  �        �                    �   �         z  |d|� d�<   ��|d         �                     �   �         }|�                    d�	�  �        �                    �   �         |d
<   |S )u   Volatilitäts-Features)r4   r5   r6   r7   r�   �volatility_rG  r�   �realized_volatility_�volatility_ratio_r�   )r�   �garch_volatility)r�   r�   r�   r  r�   r  r  rp  )rn   r�   rN  r6  s       ro   r@  z5UltimatePrognoseToolEnhanced._add_volatility_features|  sM  � � &� 	C� 	C�F���k�,�,�.�.�G�*1�/�/�&�*A�*A�*E�*E�*G�*G�B�&�V�&�&�&�'�35�7�G�Q�J�;O�;O�PV�;W�;W�;[�;[�;]�;]�3^�3^�B�/�f�/�/�/�0�02�3J��3J�3J�3J�0K�b�Qh�_e�Qh�Qh�Qh�Ni�Nq�Nq�rx�yz�rz�N{�N{�  OA�  OA�  OC�  OC�  1C�B�,�6�,�,�,�-�-� �W�+�(�(�*�*��!(���3��!7�!7�!;�!;�!=�!=�����	rq   c                 �  � 	 |�                     t          j        g��  �        j        }t	          |�  �        | j        d         k    r||         S ||         �                    �   �         �                    d��  �        }|||�                    d�  �        k             j	        }t	          |�  �        | j        d         k    r|d| j        d         �         }n|}t          j        dt	          |�  �        � dt	          |�  �        � d	��  �         ||         S # t          $ rb}t          j
        d
|� ��  �         |�                     t          j        g��  �        j        }||d| j        d         �                  cY d}~S d}~ww xY w)zErweiterte Feature-Selektion)�includer>   F)�	ascendingr�   NzFeature-Selektion: z von u    Features ausgewähltzFehler bei Feature-Selektion: )�
select_dtypesr  �numberr�   r�   rW   �var�sort_values�quantiler  rk   rl   r�   r�   )rn   r�   �numeric_cols�	variances�high_variance_features�selected_featuresr�   s          ro   rA  z2UltimatePrognoseToolEnhanced._select_best_features�  s�  � �	B��+�+�R�Y�K�+�@�@�H�L��<� � �D�K��$?�?�?��,�'�'� �<�(�,�,�.�.�:�:�U�:�K�K�I� &/�y�9�;M�;M�c�;R�;R�/R�%S�%Y�"� �)�*�*�T�[��-H�H�H�$:�;W�D�K��<W�;W�$X�!�!�$:�!��L�t�s�3D�/E�/E�t�t�C�P\�L]�L]�t�t�t�u�u�u��'�(�(��� 	B� 	B� 	B��M�>�1�>�>�?�?�?��+�+�R�Y�K�+�@�@�H�L��l�#?�D�K��$?�#?�@�A�A�A�A�A�A�A�����		B���s&   �AD �CD �
E?�AE:�4E?�:E?Nc                 �  � t          d�  �         t          d         s| �                    ||�  �        S t          dddddiddigddiddigg��  �        }| �                    ||dd�	�  �         | �                    ||dd�	�  �         | �                    ||dd�	�  �         | �                    ||dd�	�  �         |�                    d
ddt          d
��  �        t          dddd��  �        ��  �         t          j        �   �         �                    d�  �        }d|� d�}|�
                    |�  �         t          d|� ��  �         t          j        d|� ��  �         |S )z<Erstelle umfassende 3D-Visualisierung aller Trading-Faktorenz%Erstelle 3D-Trading-Visualisierung...r   r�   )u   3D Preis-Volumen-VolatilitätzTrading-Signale 3DzTechnische Indikatoren 3DzRisiko-Rendite-Analyse 3D�type�	scatter3d)�rows�cols�subplot_titles�specsr3   ��rowr�   z/ULTIMATE BITCOIN TRADING ANALYSE - 3D DASHBOARDi�  TrP  )�sizer   �P   )�lr~   �t�b)�title�height�
showlegend�font�marginz
%Y%m%d_%H%M%Sz&3d_visualizations/trading_analysis_3d_z.htmlz'3D-Trading-Visualisierung gespeichert: z3D-Visualisierung erstellt: )rU   r�   �_create_matplotlib_3d_tradingr$   �_add_price_volume_volatility_3d�_add_trading_signals_3d�_add_technical_indicators_3d�_add_risk_return_analysis_3d�
update_layout�dictr   r  �strftime�
write_htmlrk   rl   )rn   r�   �predictions�fig�	timestamp�output_files         ro   �create_3d_trading_visualizationz<UltimatePrognoseToolEnhanced.create_3d_trading_visualization�  s�  � �
�5�6�6�6���)� 	G��5�5�b�+�F�F�F� ���� �+�&���(=�>��+�&���(=�>��
� 
� 
�� 	
�,�,�S�"�!��,�C�C�C� 	
�$�$�S�"�!��$�;�;�;� 	
�)�)�#�r�q�a�)�@�@�@� 	
�)�)�#�r�q�a�)�@�@�@� 	���C����2�����!�q�B�!�,�,�,� 	� 	
� 	
� 	
� �L�N�N�+�+�O�<�<�	�O�y�O�O�O�����{�#�#�#�
�E��E�E�F�F�F���A�K�A�A�B�B�B��
rq   c                 �  � t          |�  �        dk     rdS t          t          t          |�  �        �  �        �  �        }|d         j        }|d         j        }d|j        v r#|d         �                    d�  �        j        }d}	nF|d         �                    �   �         �                    �   �         �                    d�  �        j        }d}	t          j	        |d         j        |d         �
                    �   �         z  d	z  d
d�  �        }
|�                    t          j
        |||dt          |
||	d
t          dd��  �        ��  �        t          d
d��  �        dd��  �        ||��  �         |�                    ddd||��  �         dS )u"   3D Preis-Volumen-Volatilität Plotr�   Nr�   r�   �volatility_24hr   �Viridis�Plasmar  rE  r�   z
markers+linesr�   u   Volatilitätr�   )r�  rX  )r�  �color�
colorscale�opacity�colorbarzrgba(100,100,100,0.5))�widthr�  u   Preis-Volumen-VolatilitätzBZeit: %{x}<br>Preis: $%{y:.2f}<br>Volumen: %{z:.0f}<extra></extra>)rX  �y�z�mode�marker�liner�   �
hovertemplater�  �Zeit�Preis (USD)�Volumen��xaxis_title�yaxis_title�zaxis_titler�  r�   )r�   r�   rW  �valuesr�   r�   r�   r  r  �clipr  �	add_trace�go�	Scatter3dr�  �
update_scenes)rn   r�  r�   r�  r�   rX  r�  r�  �colorsr�  �sizess              ro   r�  z<UltimatePrognoseToolEnhanced._add_price_volume_volatility_3d�  s�  � ��r�7�7�R�<�<��F� 
��s�2�w�w��� � ���w�K����x�L��� �r�z�)�)��(�)�0�0��3�3�:�F�"�J�J���[�+�+�-�-�1�1�3�3�:�:�1�=�=�D�F�!�J� ���8��+�b��l�.>�.>�.@�.@�@�2�E�q�"�M�M���
�
��L��q�A�$��� �)��!��#�>�>�>�� � � ��)@�A�A�A�1�b�

� 

� 

� �� 	� 	
� 	
� 	
�$ 	����%�!���	 	� 	
� 	
� 	
� 	
� 	
rq   c           
      �  � t          |�  �        dk     rdS | �                    |�  �        }t          t          t          |�  �        �  �        �  �        }|d         j        }d|j        v r!|d         �                    d�  �        j        }n$t          j        t          |�  �        �  �        dz  }g }	|D ]O}
|
dk    r|	�	                    d�  �         �|
dk    r|	�	                    d�  �         �:|	�	                    d	�  �         �P|�
                    t          j        |||d
t          d|	d�
�  �        dd��  �        ||��  �         |�
                    t          j        ||dgt          |�  �        z  dt          ddd��  �        dd��  �        ||��  �         |�
                    t          j        ||dgt          |�  �        z  dt          ddd��  �        dd��  �        ||��  �         |�                    ddd||��  �         dS ) z!3D Trading-Signale Visualisierungr�   Nr�   �rsi_14�BUY�green�SELL�red�blue�markersr�   r�   )r�  r�  r�  zTrading-Signalez>Zeit: %{x}<br>Preis: $%{y:.2f}<br>RSI: %{z:.1f}<extra></extra>)rX  r�  r�  r�  r�  r�   r�  r�  �F   �linesr�   �dash)r�  r�  r�  u   Überkauft (70)F)rX  r�  r�  r�  r�  r�   r�  r0   u   Überverkauft (30)r�  r�  �RSIr�  )r�   �_generate_trading_signalsr�   rW  r�  r�   r�   r  �onesr�   r�  r�  r�  r�  r�  )rn   r�  r�   r�  r�   �signalsrX  r�  r�  �
signal_colorsrv  s              ro   r�  z4UltimatePrognoseToolEnhanced._add_trading_signals_3d  sj  � ��r�7�7�R�<�<��F� �0�0��4�4����s�2�w�w��� � ���w�K��� �r�z�!�!��8��#�#�B�'�'�.�A�A����B��� � �2�%�A� �
�� 	-� 	-�F������$�$�W�-�-�-�-��6�!�!��$�$�U�+�+�+�+��$�$�V�,�,�,�,��
�
��L��q�A����'��� � �
 '�^�

� 

� 

� �� 	� 
	
� 
	
� 
	
�  	�
�
��L��q�R�D��Q���K����Q�V�<�<�<�&� �
� 
� 
� �� 	� 		
� 		
� 		
� 	�
�
��L��q�R�D��Q���K����q�v�>�>�>�)� �
� 
� 
� �� 	� 		
� 		
� 		
� 	����%����	 	� 	
� 	
� 	
� 	
� 	
rq   c                 �8  � g }t          t          |�  �        �  �        D ]�}d}d|j        v r$|d         j        |         }|dk     rd}n|dk    rd}d|j        v r~d|j        v ru|d         j        |         }|d         j        |         }|d	k    rI|d         j        |d
z
           }|d         j        |d
z
           }	||k    r	||	k    rd}n||k     r||	k    rd}d|j        v r$|d         j        |         }
|
dk     rd}n|
d
k    rd}|�                    |�  �         ��|S )z?Generiere Trading-Signale basierend auf technischen Indikatoren�HOLDr�  r0   r�  r�  r�  �
macd_12_26�macd_signal_12_26r   r3   �bb_position_20_2r�   g�������?)rW  r�   r�   r  r�   )rn   r�   r�  �irv  �rsiry  rz  �	prev_macd�prev_signal�bb_poss              ro   r�  z6UltimatePrognoseToolEnhanced._generate_trading_signalsT  sn  � ����s�2�w�w��� "	#� "	#�A��F� �2�:�%�%���l�'��*����8�8�"�F�F��2�X�X�#�F� �r�z�)�)�.A�R�Z�.O�.O��,�'�,�Q�/�� �!4�5�:�1�=���q�5�5� "�<� 0� 5�a��c� :�I�"$�%8�"9�">�q��s�"C�K� �k�)�)�i�;�.F�.F�!&�����+�+�	�[�0H�0H�!'�� "�R�Z�/�/��.�/�4�Q�7���C�<�<�"�F�F��c�\�\�#�F��N�N�6�"�"�"�"��rq   )N)�__name__�
__module__�__qualname__�__doc__rV   rp   rh   rj   ri   r�   r�   r�   r�   r�   r�   r�   rC  r<  r=  r>  r?  r@  rA  r�  r�  r�  r�  r�   rq   ro   r,   r,   �   s�  � � � � � �� � �G�BC� BC� BC�H2� 2� 2�
P� 
P� 
P�=K� =K� =K�~!;� !;� !;�F� � �4� � �2?� ?� ?�� � �4'(� '(� '(�R%$� %$� %$�N� � �8� � �&+� +� +�Z� � �2� � �$
� 
� 
�B� B� B�<3� 3� 3� 3�j,
� ,
� ,
�\F
� F
� F
�P(� (� (� (� (rq   r,   �__main__u'   Tool bereit für erweiterte Funktionen!z Fehler bei der Initialisierung: zInitialisierungsfehler: )fr  ry   �sysr�   rd   �warnings�filterwarnings�numpyr  �pandasrZ   r   r   r�   �picklerk   �collectionsr   r   r�   r   �tfr
   �tensorflow.kerasr   r   r   r   rU   �__version__rW   � set_intra_op_parallelism_threads� set_inter_op_parallelism_threads�list_physical_devices�gpus�gpu�experimental�set_memory_growthr�   r�   r�   �ImportError�sklearn.ensembler   r   �sklearn.neural_networkr   �sklearn.preprocessingr   r   r   �sklearn.model_selectionr   r   �sklearn.metricsr   r   r   �sklearn.feature_selectionr   r   r
   r�   r   r    �scipy.signalr!   �yfinancer�   �matplotlib.pyplot�pyplot�plt�!matplotlib.backends.backend_tkaggr"   �mpl_toolkits.mplot3dr#   �seaborn�sns�plotly.graph_objects�
graph_objectsr�  �plotly.express�express�px�plotly.subplotsr$   �plotly.offline�offline�pyo�tkinter�tkr%   r&   r'   �basicConfig�INFO�FileHandler�
StreamHandlerr,   r  �toolr�   r�   rq   ro   �<module>r:     s�  ��� � 
�	�	�	� 
�
�
�
� ���� � � � � ���� �� �� !� !� !� � � � � � � � � (� (� (� (� (� (� (� (� ���� 
�
�
�
� ���� *� *� *� *� *� *� *� *� ����
�� � �/����� � � � � � �F�F�F�F�F�F�F�F�F�F�F�F�%)��\�"�	�E�
,�b�n�=�=�=� �I��8�8��;�;�;��I��8�8��;�;�;�	8��y�.�.�u�5�5��� 	)�� 
D� 
D���	�&�8�8��d�C�C�C�C��E�0�S�S��Y�Y�0�0�1�1�1�1��E�'�(�(�(���� 8� 8� 8�
��6�1�6�6�7�7�7�7�7�7�7�7�����8������ � /� /� /�	�E�
-�!�
-�
-�.�.�.�.�.�.�.�.�����/����
1�Q�Q�Q�Q�Q�Q�Q�Q�3�3�3�3�3�3�P�P�P�P�P�P�P�P�P�P�H�H�H�H�H�H�H�H�Q�Q�Q�Q�Q�Q�Q�Q�Q�Q�C�C�C�C�C�C�C�C�"&��Y��	�E�
#�$�$�$�$��� 1� 1� 1�	�E�
/�A�
/�
/�0�0�0�0�0�0�0�0�����1����&�����"&��Y��	�E�
������� &� &� &�	�E�
$�%�%�%�%�%�&����$�������'�'�'�'�'�'� $��W��	�E�
������� $� $� $�	�E�
"�#�#�#�#�#�$���� � � � �  � � � � � � ?� ?� ?� ?� ?� ?� '� '� '� '� '� '� � � � �?�%�%�%�%�%�%�������-�-�-�-�-�-� � � � � � �!%��X��	�E�
;�<�<�<�<��� ?� ?� ?�	�E�
=�>�>�>�>�>�?���� � � � � /� /� /� /� /� /� /� /� /� /� �� �
�,�6����<�=�=�������� � � �w� w� w� w� w� w� w� w�r �z���6�+�+�-�-��
��7�8�8�8�8�8��� 6� 6� 6�
��4��4�4�5�5�5���
�4��4�4�5�5�5�5�5�5�5�5�5�����6����	 �s�   �A*E �?A$D$ �#E �$E�)D<�7E �<E�E �E"�
E�E"�&AF) �)G�.G�G�
G �G2�1G2�6H �H&�%H&�(I- �-J �?J �K1 �1L"�6!L�L"