#!/usr/bin/env python3
"""
Test-Skript für das Ultimate Bitcoin Prediction Model
Führt eine schnelle Validierung des Modells durch
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')

# Konfiguration für schnellen Test
TEST_CONFIG = {
    'symbol': 'BTC-USD',
    'data_period': '30d',      # Weniger Daten für schnelleren Test
    'data_interval': '1h',
    'train_split': 0.8,
    'look_back': 24,           # Kürzere Sequenzen
    'future_steps': 24,        # 24h Prognose statt 72h
    'batch_size': 32,
    'epochs': 10,              # <PERSON>iger Epochen für Test
    'patience': 5,
    'model_type': 'hybrid_advanced',
    'save_dir': 'test_models',
    'use_multiple_sources': False,  # Nur Yahoo Finance für Test
    'monte_carlo_simulations': 50   # Weniger Simulationen
}

def quick_test():
    """Schneller Test des Modells"""
    print("🧪 QUICK TEST - Ultimate Bitcoin Model")
    print("=" * 40)
    
    try:
        # Importiere das Hauptmodul
        from btc_ultimate_prediction_model import (
            AdvancedDataProcessor, AdvancedModelBuilder, 
            AdvancedPredictor, AdvancedVisualizer, CONFIG
        )
        
        # Überschreibe CONFIG für Test
        CONFIG.update(TEST_CONFIG)
        
        print("✅ Module erfolgreich importiert")
        
        # 1. Daten testen
        print("\n📊 Teste Datenvorbereitung...")
        processor = AdvancedDataProcessor()
        
        # Vereinfachte Datenvorbereitung nur mit Yahoo Finance
        from btc_ultimate_prediction_model import AdvancedDataSource
        df = AdvancedDataSource.fetch_yahoo_finance()
        
        if df is None or df.empty:
            print("❌ Keine Daten verfügbar")
            return False
        
        print(f"✅ Daten geladen: {len(df)} Datenpunkte")
        print(f"   Zeitraum: {df.index[0]} bis {df.index[-1]}")
        print(f"   Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
        
        # 2. Technische Indikatoren testen
        print("\n📈 Teste technische Indikatoren...")
        from btc_ultimate_prediction_model import AdvancedTechnicalIndicators
        df_with_indicators = AdvancedTechnicalIndicators.add_comprehensive_indicators(df)
        
        print(f"✅ Indikatoren berechnet: {len(df_with_indicators.columns)} Features")
        
        # 3. Modell-Architektur testen
        print("\n🏗️  Teste Modell-Architektur...")
        builder = AdvancedModelBuilder()
        
        # Dummy Input Shape für Test
        test_model = builder.build_hybrid_advanced_model((24, 30))  # 24 timesteps, 30 features
        print("✅ Modell erfolgreich erstellt")
        print(f"   Parameter: {test_model.count_params():,}")
        
        # 4. Einfache Vorhersage testen
        print("\n🔮 Teste Vorhersage-Pipeline...")
        import numpy as np
        
        # Dummy-Daten für Test
        dummy_sequence = np.random.random((24, 30))
        dummy_prediction = test_model.predict(dummy_sequence.reshape(1, 24, 30), verbose=0)
        
        print(f"✅ Vorhersage funktioniert: {dummy_prediction[0][0]:.6f}")
        
        print("\n🎉 ALLE TESTS BESTANDEN!")
        print("\nDas Modell ist bereit für den vollständigen Lauf.")
        print("Führe 'python btc_ultimate_prediction_model.py' aus für die komplette Analyse.")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import-Fehler: {e}")
        print("Stelle sicher, dass alle Abhängigkeiten installiert sind:")
        print("pip install tensorflow pandas numpy matplotlib scikit-learn yfinance requests")
        return False
        
    except Exception as e:
        print(f"❌ Unerwarteter Fehler: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """Prüfe erforderliche Abhängigkeiten"""
    print("🔍 Prüfe Abhängigkeiten...")
    
    required_packages = [
        'tensorflow', 'pandas', 'numpy', 'matplotlib', 
        'sklearn', 'yfinance', 'requests', 'joblib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - FEHLT")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Fehlende Pakete: {', '.join(missing_packages)}")
        print("Installiere mit: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ Alle Abhängigkeiten verfügbar")
    return True

def main():
    """Hauptfunktion für Tests"""
    print("🚀 ULTIMATE BITCOIN MODEL - TEST SUITE")
    print("=" * 50)
    
    # 1. Abhängigkeiten prüfen
    if not check_dependencies():
        print("\n❌ Test abgebrochen - Abhängigkeiten fehlen")
        return
    
    # 2. Schnelltest
    print("\n" + "=" * 50)
    success = quick_test()
    
    if success:
        print("\n" + "=" * 50)
        print("🎯 NÄCHSTE SCHRITTE:")
        print("1. Führe das vollständige Modell aus:")
        print("   python btc_ultimate_prediction_model.py")
        print("\n2. Oder passe die Konfiguration an und teste erneut")
        print("\n3. Das Modell wird automatisch gespeichert und kann wiederverwendet werden")
    else:
        print("\n❌ Tests fehlgeschlagen - Bitte Fehler beheben")

if __name__ == "__main__":
    main()
