#!/usr/bin/env python3
"""
🚀 OPTIMIZED REALISTIC 48H BITCOIN PREDICTION 🚀
=================================================
Optimiert für realistische, nicht-geradlinige Prognosen mit detaillierter Lesbarkeit
"""

import os
import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge
import yfinance as yf
from multiprocessing import Pool, cpu_count
from concurrent.futures import ThreadPoolExecutor

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

# OPTIMIZED KONFIGURATION
MAX_CORES = cpu_count()
PARALLEL_JOBS = -1
MONTE_CARLO_SIMS = 2000  # Erhöht für bessere Genauigkeit

print("🚀 OPTIMIZED REALISTIC 48H BITCOIN PREDICTION")
print("=" * 48)
print(f"💻 CPU Kerne: {MAX_CORES}")
print(f"🔮 Monte Carlo: {MONTE_CARLO_SIMS} Simulationen")
print(f"🎯 Fokus: Realistische, nicht-geradlinige Prognosen")
print(f"🕐 Start: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def get_bitcoin_data_optimized():
    """Optimierte Bitcoin-Datensammlung"""
    print("📊 Lade Bitcoin-Daten für optimierte Prognose...")
    
    try:
        btc = yf.Ticker("BTC-USD")
        df = btc.history(period="3mo", interval="1h")
        
        if len(df) > 100:
            df.columns = [col.lower() for col in df.columns]
            print(f"✅ Echte Bitcoin-Daten: {len(df)} Stunden")
            print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
            return df, True
        else:
            raise Exception("Zu wenig Daten")
            
    except Exception as e:
        print(f"⚠️ API-Fehler, generiere realistische Daten...")
        
        # Realistische Datengeneration mit mehr Volatilität
        end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        start_time = end_time - timedelta(days=90)
        dates = pd.date_range(start=start_time, end=end_time, freq='H')
        
        n_points = len(dates)
        np.random.seed(42)
        
        # Realistischere Bitcoin-Preisbewegungen
        base_price = 67000
        
        # Trend mit mehr Variation
        trend_changes = np.random.choice([-1, 0, 1], n_points//24, p=[0.3, 0.4, 0.3])
        trend = np.repeat(trend_changes, 24)[:n_points] * np.random.uniform(500, 2000, n_points)
        trend = np.cumsum(trend)
        
        # Volatilitäts-Clustering (realistische Bitcoin-Eigenschaft)
        volatility_regime = np.random.choice([0.5, 1.0, 2.0], n_points//12, p=[0.6, 0.3, 0.1])
        volatility_regime = np.repeat(volatility_regime, 12)[:n_points]
        
        # Intraday-Volatilität
        daily_vol = np.random.normal(0, 1500, n_points) * volatility_regime
        
        # Marktzyklen
        weekly_cycle = 1000 * np.sin(2 * np.pi * np.arange(n_points) / (24 * 7))
        monthly_cycle = 2000 * np.sin(2 * np.pi * np.arange(n_points) / (24 * 30))
        
        # Sprünge (News Events)
        jumps = np.random.choice([0, 1], n_points, p=[0.99, 0.01])
        jump_sizes = jumps * np.random.normal(0, 5000, n_points)
        
        prices = base_price + trend + daily_vol + weekly_cycle + monthly_cycle + jump_sizes
        prices = np.maximum(prices, 25000)
        
        # Realistische OHLCV mit mehr Spread
        high_mult = np.random.uniform(1.002, 1.08, n_points)
        low_mult = np.random.uniform(0.92, 0.998, n_points)
        open_mult = np.random.uniform(0.97, 1.03, n_points)
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * high_mult,
            'low': prices * low_mult,
            'open': prices * open_mult,
            'volume': np.random.lognormal(15, 0.4, n_points)
        }, index=dates)
        
        print(f"✅ Realistische Daten: {len(df)} Stunden")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
        return df, False

def create_enhanced_features(df):
    """Erweiterte Features für realistische Prognosen"""
    print("🔧 Erstelle erweiterte Features für realistische Prognosen...")
    
    df = df.copy()
    
    # === VOLATILITY FEATURES (erweitert) ===
    print("   📊 Erweiterte Volatility Features...")
    
    # Verschiedene Volatilitäts-Zeiträume
    for window in [6, 12, 24, 48, 72, 168]:
        df[f'volatility_{window}'] = df['close'].rolling(window=window).std()
        df[f'volatility_ratio_{window}'] = df[f'volatility_{window}'] / df['close']
        
        # Volatilitäts-Perzentile (für Regime-Erkennung)
        df[f'vol_percentile_{window}'] = df[f'volatility_{window}'].rolling(window=window*2).rank(pct=True)
    
    # GARCH-ähnliche Volatilitäts-Features
    returns = df['close'].pct_change()
    df['returns'] = returns
    df['returns_squared'] = returns ** 2
    
    # Exponential weighted volatility
    for span in [12, 24, 48]:
        df[f'ewm_vol_{span}'] = returns.ewm(span=span).std()
        df[f'ewm_vol_ratio_{span}'] = df[f'ewm_vol_{span}'] / df[f'ewm_vol_{span}'].rolling(window=span*2).mean()
    
    # === MOMENTUM FEATURES (erweitert) ===
    print("   ⚡ Erweiterte Momentum Features...")
    
    # Multi-timeframe RSI
    for period in [6, 14, 24, 48, 72]:
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=period).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
        rs = gain / loss
        df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # RSI Divergenz
        df[f'rsi_divergence_{period}'] = df[f'rsi_{period}'].diff()
    
    # MACD Familie (erweitert)
    for fast, slow in [(12, 26), (6, 12), (24, 48)]:
        ema_fast = df['close'].ewm(span=fast).mean()
        ema_slow = df['close'].ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal = macd.ewm(span=9).mean()
        
        df[f'macd_{fast}_{slow}'] = macd
        df[f'macd_signal_{fast}_{slow}'] = signal
        df[f'macd_histogram_{fast}_{slow}'] = macd - signal
        df[f'macd_slope_{fast}_{slow}'] = macd.diff()
    
    # === TREND FEATURES (erweitert) ===
    print("   📈 Erweiterte Trend Features...")
    
    # Multi-timeframe Moving Averages
    for window in [6, 12, 24, 48, 72, 168, 336]:
        df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
        df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
        
        # Trend Strength
        df[f'trend_strength_{window}'] = (df['close'] - df[f'sma_{window}']) / df[f'sma_{window}']
        
        # Moving Average Convergence/Divergence
        if window > 12:
            short_ma = df[f'sma_{window//2}'] if f'sma_{window//2}' in df.columns else df['close'].rolling(window=window//2).mean()
            df[f'ma_convergence_{window}'] = (short_ma - df[f'sma_{window}']) / df[f'sma_{window}']
    
    # === PRICE ACTION FEATURES (erweitert) ===
    print("   💰 Erweiterte Price Action Features...")
    
    # Multi-timeframe Returns
    for period in [1, 2, 3, 6, 12, 24, 48, 72]:
        df[f'returns_{period}'] = df['close'].pct_change(periods=period)
        df[f'log_returns_{period}'] = np.log(df['close'] / df['close'].shift(period))
        
        # Return Volatility
        df[f'return_vol_{period}'] = df[f'returns_{period}'].rolling(window=24).std()
    
    # High-Low Features (erweitert)
    if 'high' in df.columns and 'low' in df.columns:
        df['hl_ratio'] = df['high'] / df['low']
        df['price_range'] = df['high'] - df['low']
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        # True Range und ATR (mehrere Zeiträume)
        df['tr'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                np.abs(df['high'] - df['close'].shift()),
                np.abs(df['low'] - df['close'].shift())
            )
        )
        
        for window in [6, 14, 24, 48]:
            df[f'atr_{window}'] = df['tr'].rolling(window=window).mean()
            df[f'atr_ratio_{window}'] = df[f'atr_{window}'] / df['close']
    
    # === BOLLINGER BANDS (erweitert) ===
    print("   📊 Erweiterte Bollinger Bands...")
    
    for window in [12, 20, 24, 48]:
        for std_mult in [1.5, 2.0, 2.5]:
            bb_middle = df['close'].rolling(window=window).mean()
            bb_std = df['close'].rolling(window=window).std()
            
            df[f'bb_upper_{window}_{std_mult}'] = bb_middle + std_mult * bb_std
            df[f'bb_lower_{window}_{std_mult}'] = bb_middle - std_mult * bb_std
            df[f'bb_width_{window}_{std_mult}'] = df[f'bb_upper_{window}_{std_mult}'] - df[f'bb_lower_{window}_{std_mult}']
            df[f'bb_position_{window}_{std_mult}'] = (df['close'] - df[f'bb_lower_{window}_{std_mult}']) / df[f'bb_width_{window}_{std_mult}']
            
            # Bollinger Band Squeeze
            df[f'bb_squeeze_{window}_{std_mult}'] = df[f'bb_width_{window}_{std_mult}'].rolling(window=20).rank(pct=True)
    
    # === VOLUME FEATURES (erweitert) ===
    print("   📦 Erweiterte Volume Features...")
    if 'volume' in df.columns:
        for window in [6, 12, 24, 48, 72]:
            df[f'volume_sma_{window}'] = df['volume'].rolling(window=window).mean()
            df[f'volume_ratio_{window}'] = df['volume'] / df[f'volume_sma_{window}']
            df[f'volume_std_{window}'] = df['volume'].rolling(window=window).std()
        
        # Volume-Price Trend
        df['vpt'] = (df['volume'] * df['close'].pct_change()).cumsum()
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()
        
        # Volume Weighted Average Price
        for window in [12, 24, 48]:
            df[f'vwap_{window}'] = (df['close'] * df['volume']).rolling(window=window).sum() / df['volume'].rolling(window=window).sum()
            df[f'vwap_deviation_{window}'] = (df['close'] - df[f'vwap_{window}']) / df[f'vwap_{window}']
    
    # === LAG FEATURES (erweitert) ===
    print("   🔄 Erweiterte Lag Features...")
    for lag in [1, 2, 3, 6, 12, 24, 48, 72]:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
        df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
        df[f'volatility_lag_{lag}'] = df['volatility_24'].shift(lag)
        
        if 'volume' in df.columns:
            df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
    
    # === TIME FEATURES (erweitert) ===
    print("   🕐 Erweiterte Time Features...")
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['month'] = df.index.month
    df['quarter'] = df.index.quarter
    df['day_of_month'] = df.index.day
    df['week_of_year'] = df.index.isocalendar().week
    
    # Market Sessions
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    df['is_asian_session'] = ((df.index.hour >= 0) & (df.index.hour <= 8)).astype(int)
    df['is_european_session'] = ((df.index.hour >= 8) & (df.index.hour <= 16)).astype(int)
    df['is_us_session'] = ((df.index.hour >= 14) & (df.index.hour <= 22)).astype(int)
    
    # Cyclical encoding (erweitert)
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
    df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
    df['day_of_month_sin'] = np.sin(2 * np.pi * df['day_of_month'] / 31)
    df['day_of_month_cos'] = np.cos(2 * np.pi * df['day_of_month'] / 31)
    
    # === MARKET REGIME FEATURES (erweitert) ===
    print("   🏛️ Erweiterte Market Regime Features...")
    
    # Volatility Regimes
    for window in [24, 48, 168]:
        df[f'vol_regime_{window}'] = df[f'volatility_{window}'].rolling(window=window*2).rank(pct=True)
        
        # Regime Changes
        df[f'vol_regime_change_{window}'] = df[f'vol_regime_{window}'].diff()
    
    # Trend Regimes
    for short, long in [(12, 48), (24, 168), (48, 336)]:
        short_ma = df[f'sma_{short}'] if f'sma_{short}' in df.columns else df['close'].rolling(window=short).mean()
        long_ma = df[f'sma_{long}'] if f'sma_{long}' in df.columns else df['close'].rolling(window=long).mean()
        
        df[f'trend_regime_{short}_{long}'] = np.where(short_ma > long_ma * 1.01, 1,
                                                     np.where(short_ma < long_ma * 0.99, -1, 0))
        df[f'trend_strength_{short}_{long}'] = (short_ma - long_ma) / long_ma
    
    print(f"✅ Erweiterte Features erstellt: {df.shape[1]} Spalten")
    return df.dropna()

def prepare_optimized_data(df, sequence_length=96):  # Längere Sequenzen für bessere Muster
    """Optimierte Datenvorbereitung"""
    print(f"🔄 Bereite optimierte Daten vor...")
    
    feature_cols = [col for col in df.columns if col != 'close']
    features = df[feature_cols].values
    target = df['close'].values
    
    # Robuste Skalierung für bessere Stabilität
    feature_scaler = RobustScaler()
    target_scaler = MinMaxScaler()
    
    features_scaled = feature_scaler.fit_transform(features)
    target_scaled = target_scaler.fit_transform(target.reshape(-1, 1)).flatten()
    
    # Sequenzen erstellen
    X, y = [], []
    for i in range(sequence_length, len(features_scaled)):
        X.append(features_scaled[i-sequence_length:i])
        y.append(target_scaled[i])
    
    X, y = np.array(X), np.array(y)
    
    # Train/Validation/Test Split
    train_size = int(len(X) * 0.7)
    val_size = int(len(X) * 0.15)
    
    X_train = X[:train_size]
    y_train = y[:train_size]
    X_val = X[train_size:train_size+val_size]
    y_val = y[train_size:train_size+val_size]
    X_test = X[train_size+val_size:]
    y_test = y[train_size+val_size:]
    
    last_sequence = features_scaled[-sequence_length:]
    
    print(f"✅ Optimierte Daten vorbereitet:")
    print(f"   Train: {X_train.shape}")
    print(f"   Validation: {X_val.shape}")
    print(f"   Test: {X_test.shape}")
    print(f"   Features: {X_train.shape[2]}")
    
    return (X_train, y_train), (X_val, y_val), (X_test, y_test), last_sequence, (feature_scaler, target_scaler)

def train_optimized_models(train_data, val_data, test_data):
    """Optimierte Modelle für realistische Prognosen"""
    print(f"\n🚀 Trainiere optimierte Modelle für realistische Prognosen...")

    X_train, y_train = train_data
    X_val, y_val = val_data
    X_test, y_test = test_data

    X_train_flat = X_train.reshape(X_train.shape[0], -1)
    X_val_flat = X_val.reshape(X_val.shape[0], -1)
    X_test_flat = X_test.reshape(X_test.shape[0], -1)

    # Optimierte Modelle für realistische Vorhersagen
    models = {
        'ExtraTrees_OPTIMIZED': ExtraTreesRegressor(
            n_estimators=500,  # Mehr Bäume für bessere Genauigkeit
            max_depth=30,      # Tiefere Bäume für komplexere Muster
            min_samples_split=2,
            min_samples_leaf=1,
            max_features=0.8,  # Mehr Features für Diversität
            n_jobs=PARALLEL_JOBS,
            random_state=42,
            bootstrap=True
        ),
        'RandomForest_OPTIMIZED': RandomForestRegressor(
            n_estimators=400,
            max_depth=25,
            min_samples_split=3,
            min_samples_leaf=2,
            max_features=0.7,
            n_jobs=PARALLEL_JOBS,
            random_state=42,
            bootstrap=True
        ),
        'GradientBoosting_OPTIMIZED': GradientBoostingRegressor(
            n_estimators=200,
            learning_rate=0.08,  # Langsameres Lernen für bessere Generalisierung
            max_depth=10,
            subsample=0.85,
            max_features=0.8,
            random_state=42
        )
    }

    results = {}

    for model_name, model in models.items():
        print(f"\n🤖 Trainiere {model_name}...")

        start_time = time.time()
        model.fit(X_train_flat, y_train)
        training_time = time.time() - start_time

        # Vorhersagen auf Validation und Test
        y_pred_val = model.predict(X_val_flat)
        y_pred_test = model.predict(X_test_flat)

        # Metriken
        mse_val = mean_squared_error(y_val, y_pred_val)
        mse_test = mean_squared_error(y_test, y_pred_test)
        mae_test = mean_absolute_error(y_test, y_pred_test)
        r2_val = r2_score(y_val, y_pred_val)
        r2_test = r2_score(y_test, y_pred_test)

        results[model_name] = {
            'model': model,
            'training_time': training_time,
            'mse_val': mse_val,
            'mse_test': mse_test,
            'mae_test': mae_test,
            'rmse_test': np.sqrt(mse_test),
            'r2_val': r2_val,
            'r2_test': r2_test,
            'y_pred_test': y_pred_test,
            'y_test': y_test
        }

        print(f"✅ {model_name}:")
        print(f"   Validation R²: {r2_val:.4f}")
        print(f"   Test R²: {r2_test:.4f}")
        print(f"   Test RMSE: {np.sqrt(mse_test):.4f}")
        print(f"   Training Zeit: {training_time:.1f}s")

    return results

def run_realistic_monte_carlo(args):
    """Realistische Monte Carlo Simulation mit verbesserter Marktdynamik"""
    model, last_sequence, target_hour, n_simulations, current_price_scaled, historical_volatility = args

    predictions = []

    for sim in range(n_simulations):
        # Adaptive Noise basierend auf historischer Volatilität
        base_noise = historical_volatility * 0.1  # 10% der historischen Volatilität
        time_decay = np.sqrt(target_hour / 24)    # Zeitabhängige Unsicherheit
        noise_level = base_noise * time_decay

        # Verschiedene Volatilitäts-Regime simulieren
        vol_regime = np.random.choice([0.5, 1.0, 1.5, 2.5], p=[0.4, 0.4, 0.15, 0.05])
        noise_level *= vol_regime

        # Korrelierte Noise für realistische Preisbewegungen
        noise = np.random.normal(0, noise_level, last_sequence.shape)

        # Autokorrelation in der Noise (Momentum-Effekte)
        for i in range(1, len(noise)):
            noise[i] += 0.3 * noise[i-1]  # 30% Autokorrelation

        noisy_sequence = last_sequence + noise
        current_sequence = noisy_sequence.copy()

        # Realistische iterative Vorhersage
        step_size = 1  # Stündliche Schritte für mehr Realismus

        for step in range(0, target_hour, step_size):
            # Modell-Vorhersage
            pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]

            # Marktdynamik hinzufügen
            if step > 0:
                prev_price = current_sequence[-1, 0]

                # Realistische Constraints (weniger restriktiv)
                max_hourly_change = 0.05  # Max 5% pro Stunde (realistisch für Bitcoin)

                # Mean Reversion Tendenz
                mean_reversion_factor = 0.02
                long_term_mean = np.mean(current_sequence[-24:, 0]) if len(current_sequence) >= 24 else current_price_scaled
                mean_reversion = (long_term_mean - prev_price) * mean_reversion_factor

                # Momentum Effekt
                if len(current_sequence) >= 3:
                    recent_change = current_sequence[-1, 0] - current_sequence[-3, 0]
                    momentum = recent_change * 0.1  # 10% Momentum
                else:
                    momentum = 0

                # Volatilitäts-Clustering
                recent_volatility = np.std(current_sequence[-6:, 0]) if len(current_sequence) >= 6 else noise_level
                vol_adjustment = np.random.normal(0, recent_volatility * 0.1)

                # Kombiniere alle Effekte
                pred_scaled = pred_scaled + mean_reversion + momentum + vol_adjustment

                # Sanfte Constraints (nicht zu restriktiv)
                pred_scaled = np.clip(pred_scaled,
                                    prev_price * (1 - max_hourly_change),
                                    prev_price * (1 + max_hourly_change))

            # Sequence aktualisieren mit realistischen Markteffekten
            new_row = current_sequence[-1].copy()
            new_row[0] = pred_scaled

            # Weitere Features basierend auf neuer Preisprognose aktualisieren
            # (vereinfacht - in Realität würden alle Features neu berechnet)
            if len(new_row) > 1:
                # Volatilität Feature aktualisieren
                if len(current_sequence) >= 6:
                    recent_vol = np.std(current_sequence[-6:, 0])
                    new_row[1] = recent_vol  # Annahme: Index 1 ist Volatilität

                # Momentum Feature aktualisieren
                if len(current_sequence) >= 2:
                    momentum_feature = (pred_scaled - current_sequence[-2, 0]) / current_sequence[-2, 0]
                    new_row[2] = momentum_feature  # Annahme: Index 2 ist Momentum

            current_sequence = np.vstack([current_sequence[1:], new_row])

        # Finale Vorhersage mit zusätzlicher Marktdynamik
        final_pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]

        # Finale Anpassungen für Realismus
        # Overnight/Weekend Effekte
        if target_hour >= 24:
            weekend_effect = np.random.normal(0, 0.01)  # Kleine Weekend-Volatilität
            final_pred_scaled += weekend_effect

        # News/Event Simulation (seltene große Bewegungen)
        if np.random.random() < 0.02:  # 2% Chance auf News Event
            news_impact = np.random.normal(0, 0.05)  # ±5% News Impact
            final_pred_scaled += news_impact

        predictions.append(final_pred_scaled)

    return predictions

def predict_realistic_48h(best_models, last_sequence, target_scaler, current_time, current_price, historical_data):
    """Realistische 48h Vorhersage mit verbesserter Marktdynamik"""
    print(f"🔮 Erstelle realistische 48h Vorhersage mit {MAX_CORES} Kernen...")
    print(f"   Monte Carlo Simulationen: {MONTE_CARLO_SIMS}")
    print(f"   Fokus: Nicht-geradlinige, realistische Prognosen")

    # Detaillierte Zeitpunkte für bessere Granularität
    key_hours = [1, 2, 3, 4, 6, 8, 12, 16, 18, 24, 30, 36, 42, 48]
    predictions = {}

    # Historische Volatilität berechnen
    recent_returns = historical_data['close'].pct_change().dropna()
    historical_volatility = recent_returns.rolling(window=168).std().iloc[-1]  # 7-Tage Volatilität

    # Aktueller Preis in skalierter Form
    current_price_scaled = target_scaler.transform([[current_price]])[0, 0]

    for hour in key_hours:
        print(f"📈 Berechne +{hour}h mit {MONTE_CARLO_SIMS} realistischen Simulationen...")

        all_model_predictions = []

        # Für jedes Modell
        for model_name, model_data in best_models.items():
            model = model_data['model']

            # Parallele realistische Monte Carlo Simulation
            batch_size = MONTE_CARLO_SIMS // MAX_CORES

            # Argumente für parallele Verarbeitung
            args_list = []
            for batch in range(MAX_CORES):
                start_sim = batch * batch_size
                end_sim = (batch + 1) * batch_size if batch < MAX_CORES - 1 else MONTE_CARLO_SIMS
                n_sims_batch = end_sim - start_sim

                args_list.append((model, last_sequence, hour, n_sims_batch,
                                current_price_scaled, historical_volatility))

            # Parallele Ausführung
            with Pool(processes=MAX_CORES) as pool:
                batch_results = pool.map(run_realistic_monte_carlo, args_list)

            # Ergebnisse sammeln
            model_predictions = []
            for batch_result in batch_results:
                model_predictions.extend(batch_result)

            all_model_predictions.extend(model_predictions)

        # Zurück transformieren
        all_predictions = np.array(all_model_predictions)
        all_predictions_orig = target_scaler.inverse_transform(all_predictions.reshape(-1, 1)).flatten()

        # Erweiterte Wahrscheinlichkeitsberechnung
        predictions[hour] = {
            'datetime': current_time + timedelta(hours=hour),
            'mean': np.mean(all_predictions_orig),
            'median': np.median(all_predictions_orig),
            'mode': float(pd.Series(all_predictions_orig).mode().iloc[0]) if len(pd.Series(all_predictions_orig).mode()) > 0 else np.median(all_predictions_orig),
            'std': np.std(all_predictions_orig),
            'skewness': pd.Series(all_predictions_orig).skew(),
            'kurtosis': pd.Series(all_predictions_orig).kurtosis(),
            'min': np.min(all_predictions_orig),
            'max': np.max(all_predictions_orig),

            # Detaillierte Perzentile
            'q01': np.percentile(all_predictions_orig, 1),
            'q05': np.percentile(all_predictions_orig, 5),
            'q10': np.percentile(all_predictions_orig, 10),
            'q25': np.percentile(all_predictions_orig, 25),
            'q75': np.percentile(all_predictions_orig, 75),
            'q90': np.percentile(all_predictions_orig, 90),
            'q95': np.percentile(all_predictions_orig, 95),
            'q99': np.percentile(all_predictions_orig, 99),

            # Erweiterte Wahrscheinlichkeiten
            'prob_above_current': np.mean(all_predictions_orig > current_price) * 100,
            'prob_above_0_5pct': np.mean(all_predictions_orig > current_price * 1.005) * 100,
            'prob_above_1pct': np.mean(all_predictions_orig > current_price * 1.01) * 100,
            'prob_above_2pct': np.mean(all_predictions_orig > current_price * 1.02) * 100,
            'prob_above_3pct': np.mean(all_predictions_orig > current_price * 1.03) * 100,
            'prob_above_5pct': np.mean(all_predictions_orig > current_price * 1.05) * 100,
            'prob_above_10pct': np.mean(all_predictions_orig > current_price * 1.10) * 100,
            'prob_above_15pct': np.mean(all_predictions_orig > current_price * 1.15) * 100,

            'prob_below_current': np.mean(all_predictions_orig < current_price) * 100,
            'prob_below_0_5pct': np.mean(all_predictions_orig < current_price * 0.995) * 100,
            'prob_below_1pct': np.mean(all_predictions_orig < current_price * 0.99) * 100,
            'prob_below_2pct': np.mean(all_predictions_orig < current_price * 0.98) * 100,
            'prob_below_3pct': np.mean(all_predictions_orig < current_price * 0.97) * 100,
            'prob_below_5pct': np.mean(all_predictions_orig < current_price * 0.95) * 100,
            'prob_below_10pct': np.mean(all_predictions_orig < current_price * 0.90) * 100,
            'prob_below_15pct': np.mean(all_predictions_orig < current_price * 0.85) * 100,

            # Änderungen
            'change_pct': ((np.mean(all_predictions_orig) / current_price) - 1) * 100,
            'change_abs': np.mean(all_predictions_orig) - current_price,
            'change_median_pct': ((np.median(all_predictions_orig) / current_price) - 1) * 100,

            # Risiko-Metriken
            'var_95': np.percentile(all_predictions_orig, 5) - current_price,
            'var_99': np.percentile(all_predictions_orig, 1) - current_price,
            'cvar_95': np.mean(all_predictions_orig[all_predictions_orig <= np.percentile(all_predictions_orig, 5)]) - current_price,
            'cvar_99': np.mean(all_predictions_orig[all_predictions_orig <= np.percentile(all_predictions_orig, 1)]) - current_price,

            # Upside Potential
            'upside_95': np.percentile(all_predictions_orig, 95) - current_price,
            'upside_99': np.percentile(all_predictions_orig, 99) - current_price,

            # Volatilität der Vorhersagen
            'prediction_volatility': np.std(all_predictions_orig) / current_price * 100,

            'all_predictions': all_predictions_orig
        }

    return predictions

def create_detailed_visualization(df, results, predictions, current_time, current_price, is_real_data):
    """Detaillierte Visualisierung mit verbesserter Lesbarkeit"""
    print("📊 Erstelle detaillierte Visualisierung mit optimierter Lesbarkeit...")

    fig = plt.figure(figsize=(32, 24))
    fig.patch.set_facecolor('#0a0a0a')

    title = "🚀 OPTIMIZED REALISTIC 48H BITCOIN PREDICTION"
    subtitle = f"📅 Ab: {current_time.strftime('%Y-%m-%d %H:%M:%S')} | 💰 Aktuell: ${current_price:,.2f} | 🎯 Realistische Marktdynamik"
    fig.suptitle(f'{title}\n{subtitle}',
                 fontsize=24, color='white', fontweight='bold', y=0.97)

    # === 1. HAUPTCHART: Historisch + Realistische Zukunft ===
    ax1 = plt.subplot2grid((6, 8), (0, 0), colspan=5, rowspan=3)

    # Historische Daten (letzte 14 Tage für besseren Kontext)
    recent_data = df.tail(336)  # 14 Tage
    ax1.plot(recent_data.index, recent_data['close'],
             color='#00D4FF', linewidth=3, label='Historischer Preis', alpha=0.9)

    # Jetzt markieren
    ax1.axvline(x=current_time, color='#FF6B6B', linestyle='-', linewidth=4,
                label='JETZT', alpha=0.9)

    # Realistische Zukunftsprognose
    future_times = [pred['datetime'] for pred in predictions.values()]
    future_means = [pred['mean'] for pred in predictions.values()]
    future_medians = [pred['median'] for pred in predictions.values()]
    future_q01 = [pred['q01'] for pred in predictions.values()]
    future_q05 = [pred['q05'] for pred in predictions.values()]
    future_q10 = [pred['q10'] for pred in predictions.values()]
    future_q25 = [pred['q25'] for pred in predictions.values()]
    future_q75 = [pred['q75'] for pred in predictions.values()]
    future_q90 = [pred['q90'] for pred in predictions.values()]
    future_q95 = [pred['q95'] for pred in predictions.values()]
    future_q99 = [pred['q99'] for pred in predictions.values()]

    # Mehrere Prognose-Linien für bessere Einschätzung
    ax1.plot(future_times, future_means, color='#FFD700', linewidth=5,
             label='Erwartungswert (Mittel)', alpha=0.9, marker='o', markersize=8)
    ax1.plot(future_times, future_medians, color='#FFA500', linewidth=3,
             label='Median', alpha=0.8, marker='s', markersize=6, linestyle='--')

    # Detaillierte Konfidenzintervalle
    ax1.fill_between(future_times, future_q01, future_q99,
                     color='#FFD700', alpha=0.08, label='98% Konfidenz (Extrem)')
    ax1.fill_between(future_times, future_q05, future_q95,
                     color='#FFD700', alpha=0.12, label='90% Konfidenz (Hoch)')
    ax1.fill_between(future_times, future_q10, future_q90,
                     color='#FFD700', alpha=0.16, label='80% Konfidenz (Mittel)')
    ax1.fill_between(future_times, future_q25, future_q75,
                     color='#FFD700', alpha=0.25, label='50% Konfidenz (Wahrscheinlich)')

    # Detaillierte Zeitpunkt-Markierungen
    important_hours = [1, 4, 8, 12, 18, 24, 36, 48]
    for hour in important_hours:
        if hour in predictions:
            pred = predictions[hour]
            ax1.axvline(x=pred['datetime'], color='#FF9500', linestyle=':',
                       alpha=0.6, linewidth=2)

            # Detaillierte Labels mit Preis und Zeit
            label_text = f"+{hour}h\n${pred['mean']:,.0f}\n{pred['datetime'].strftime('%H:%M')}"
            ax1.text(pred['datetime'], ax1.get_ylim()[1]*0.95, label_text,
                    rotation=0, color='#FF9500', fontsize=10, ha='center',
                    fontweight='bold', bbox=dict(boxstyle='round,pad=0.3',
                    facecolor='#FF9500', alpha=0.2))

    ax1.set_title('Bitcoin: Vergangenheit → REALISTISCHE ZUKUNFT',
                  fontsize=18, color='white', fontweight='bold')
    ax1.set_ylabel('Preis (USD)', color='white', fontsize=14)
    ax1.legend(loc='upper left', fontsize=11, framealpha=0.9)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(colors='white', labelsize=11)

    # Verbesserte X-Achsen-Formatierung
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d\n%H:%M'))
    ax1.xaxis.set_major_locator(mdates.HourLocator(interval=6))
    ax1.xaxis.set_minor_locator(mdates.HourLocator(interval=2))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=0, ha='center')

    # === 2. DETAILLIERTE WAHRSCHEINLICHKEITS-TABELLE ===
    ax2 = plt.subplot2grid((6, 8), (0, 5), colspan=3, rowspan=3)
    ax2.axis('off')

    table_data = []
    headers = ['Zeit', 'Datum/Uhrzeit', 'Erwartung', 'Median', 'Änderung', 'Wahrsch. ↑', 'Wahrsch. +5%', 'VaR 95%']

    key_display_hours = [1, 4, 8, 12, 18, 24, 36, 48]
    for hour in key_display_hours:
        if hour in predictions:
            pred = predictions[hour]

            table_data.append([
                f"+{hour}h",
                pred['datetime'].strftime('%m-%d %H:%M'),
                f"${pred['mean']:,.0f}",
                f"${pred['median']:,.0f}",
                f"{pred['change_pct']:+.1f}%",
                f"{pred['prob_above_current']:.0f}%",
                f"{pred['prob_above_5pct']:.0f}%",
                f"${pred['var_95']:,.0f}"
            ])

    table = ax2.table(cellText=table_data, colLabels=headers,
                     cellLoc='center', loc='center',
                     colWidths=[0.08, 0.15, 0.12, 0.12, 0.1, 0.1, 0.1, 0.12])

    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1, 2.8)

    # Erweiterte Tabellen-Styling
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#333333')
        table[(0, i)].set_text_props(weight='bold', color='white', fontsize=8)

    for i in range(1, len(table_data) + 1):
        for j in range(len(headers)):
            table[(i, j)].set_facecolor('#1a1a1a')
            table[(i, j)].set_text_props(color='white', fontsize=8)

            # Detaillierte Farbkodierung
            if j == 4:  # Änderung %
                change_val = float(table_data[i-1][j].replace('%', '').replace('+', ''))
                if change_val > 5:
                    table[(i, j)].set_facecolor('#1a5a1a')
                elif change_val > 2:
                    table[(i, j)].set_facecolor('#2d5a2d')
                elif change_val > 0:
                    table[(i, j)].set_facecolor('#3d4a2d')
                elif change_val > -2:
                    table[(i, j)].set_facecolor('#4a3d2d')
                elif change_val > -5:
                    table[(i, j)].set_facecolor('#5a2d2d')
                else:
                    table[(i, j)].set_facecolor('#5a1a1a')
            elif j in [5, 6]:  # Wahrscheinlichkeiten
                prob_val = float(table_data[i-1][j].replace('%', ''))
                if prob_val > 75:
                    table[(i, j)].set_facecolor('#1a5a1a')
                elif prob_val > 65:
                    table[(i, j)].set_facecolor('#2d5a2d')
                elif prob_val > 50:
                    table[(i, j)].set_facecolor('#3d4a2d')
                elif prob_val > 35:
                    table[(i, j)].set_facecolor('#4a4a2d')
                elif prob_val > 25:
                    table[(i, j)].set_facecolor('#5a3d2d')
                else:
                    table[(i, j)].set_facecolor('#5a1a1a')

    ax2.set_title('Detaillierte Wahrscheinlichkeits-Analyse', color='white', fontweight='bold', fontsize=14)

    return fig

def main():
    """Optimierte Hauptfunktion für realistische Prognosen"""
    print("\n🚀" * 24)
    print("OPTIMIZED REALISTIC 48H BITCOIN PREDICTION")
    print("🚀" * 24)

    start_time = time.time()

    try:
        # 1. Optimierte Datensammlung
        print("\n" + "="*65)
        print("PHASE 1: OPTIMIERTE DATENSAMMLUNG")
        print("="*65)
        df, is_real_data = get_bitcoin_data_optimized()
        current_time = df.index[-1]
        current_price = df['close'].iloc[-1]

        # 2. Erweiterte Features
        print("\n" + "="*65)
        print("PHASE 2: ERWEITERTE FEATURE ENGINEERING")
        print("="*65)
        df_features = create_enhanced_features(df)

        # 3. Optimierte Datenvorbereitung
        print("\n" + "="*65)
        print("PHASE 3: OPTIMIERTE DATENAUFBEREITUNG")
        print("="*65)
        train_data, val_data, test_data, last_sequence, scalers = prepare_optimized_data(df_features)
        feature_scaler, target_scaler = scalers

        # 4. Optimierte Modelle
        print("\n" + "="*65)
        print("PHASE 4: OPTIMIERTE MODEL TRAINING")
        print("="*65)
        results = train_optimized_models(train_data, val_data, test_data)

        # 5. Beste Modelle auswählen
        sorted_results = sorted(results.items(), key=lambda x: x[1]['r2_test'], reverse=True)
        best_models = dict(sorted_results[:2])  # Top 2 Modelle

        print(f"\n🏆 Top Modelle für realistische Vorhersage:")
        for name, result in best_models.items():
            print(f"   {name}: Test R²={result['r2_test']:.4f}, Val R²={result['r2_val']:.4f}")

        # 6. Realistische 48h Vorhersage
        print("\n" + "="*65)
        print("PHASE 5: REALISTISCHE 48H VORHERSAGE")
        print("="*65)

        predictions = predict_realistic_48h(
            best_models, last_sequence, target_scaler, current_time, current_price, df
        )

        # 7. Detaillierte Visualisierung
        print("\n" + "="*65)
        print("PHASE 6: DETAILLIERTE VISUALISIERUNG")
        print("="*65)

        fig = create_detailed_visualization(
            df_features, results, predictions, current_time, current_price, is_real_data
        )

        # 8. Zusammenfassung
        total_time = time.time() - start_time
        print_detailed_summary(results, predictions, current_time, current_price, total_time, is_real_data)

        # Speichern
        os.makedirs('ultimate_plots', exist_ok=True)
        filename = 'optimized_realistic_48h_prediction.png'
        plt.savefig(f'ultimate_plots/{filename}',
                    facecolor='#0a0a0a', dpi=300, bbox_inches='tight')

        print(f"✅ Optimierte Visualisierung: ultimate_plots/{filename}")
        plt.show()

        print(f"\n🎉 OPTIMIZED REALISTIC 48H ANALYSE ABGESCHLOSSEN in {total_time:.1f}s! 🎉")

        return {
            'results': results,
            'predictions': predictions,
            'current_time': current_time,
            'current_price': current_price,
            'total_time': total_time,
            'cpu_cores_used': MAX_CORES,
            'monte_carlo_sims': MONTE_CARLO_SIMS,
            'optimization_focus': 'realistic_non_linear_predictions'
        }

    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()
        return None

def print_detailed_summary(results, predictions, current_time, current_price, total_time, is_real_data):
    """Detaillierte Zusammenfassung mit verbesserter Lesbarkeit"""
    print("\n" + "="*85)
    print("🚀 OPTIMIZED REALISTIC 48H BITCOIN PREDICTION RESULTS 🚀")
    print("="*85)

    data_type = "ECHTE LIVE-DATEN" if is_real_data else "OPTIMIERTE REALISTISCHE DATEN"
    print(f"\n📊 DATENQUELLE: {data_type}")
    print(f"📅 PROGNOSE AB: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💰 AKTUELLER PREIS: ${current_price:,.2f}")
    print(f"🔮 MONTE CARLO SIMS: {MONTE_CARLO_SIMS:,}")
    print(f"🎯 OPTIMIERUNG: Realistische, nicht-geradlinige Prognosen")

    # Modell-Performance
    best_model = max(results.keys(), key=lambda x: results[x]['r2_test'])
    print(f"\n🏆 BESTES MODELL: {best_model}")
    print(f"   Test R² Score: {results[best_model]['r2_test']:.4f} ({results[best_model]['r2_test']*100:.1f}%)")
    print(f"   Validation R² Score: {results[best_model]['r2_val']:.4f} ({results[best_model]['r2_val']*100:.1f}%)")
    print(f"   Test RMSE: {results[best_model]['rmse_test']:.4f}")

    # Detaillierte 48h Vorhersagen
    print(f"\n🔮 DETAILLIERTE REALISTISCHE 48H VORHERSAGEN:")
    print(f"{'Zeit':<6} | {'Datum/Zeit':<16} | {'Erwartung':<12} | {'Median':<12} | {'Änderung':<10} | {'Wahrsch. ↑':<12} | {'Wahrsch. +5%':<12} | {'Volatilität':<10}")
    print("-" * 110)

    key_hours = [1, 4, 8, 12, 18, 24, 36, 48]
    for hour in key_hours:
        if hour in predictions:
            pred = predictions[hour]
            print(f"{hour:>4}h | {pred['datetime'].strftime('%m-%d %H:%M'):<16} | "
                  f"${pred['mean']:>10,.0f} | ${pred['median']:>10,.0f} | "
                  f"{pred['change_pct']:>+7.1f}% | {pred['prob_above_current']:>10.0f}% | "
                  f"{pred['prob_above_5pct']:>10.0f}% | {pred['prediction_volatility']:>8.1f}%")

    # 48h Spezial-Analyse
    if 48 in predictions:
        pred_48h = predictions[48]

        print(f"\n🎯 48H DETAILLIERTE ANALYSE:")
        print(f"   Erwartungswert: ${pred_48h['mean']:,.0f}")
        print(f"   Median: ${pred_48h['median']:,.0f}")
        print(f"   Änderung (Mittel): {pred_48h['change_pct']:+.1f}%")
        print(f"   Änderung (Median): {pred_48h['change_median_pct']:+.1f}%")
        print(f"   Standardabweichung: ${pred_48h['std']:,.0f}")
        print(f"   Vorhersage-Volatilität: {pred_48h['prediction_volatility']:.1f}%")

        print(f"\n📊 KONFIDENZINTERVALLE:")
        print(f"   99% Konfidenz: ${pred_48h['q01']:,.0f} - ${pred_48h['q99']:,.0f}")
        print(f"   95% Konfidenz: ${pred_48h['q05']:,.0f} - ${pred_48h['q95']:,.0f}")
        print(f"   90% Konfidenz: ${pred_48h['q10']:,.0f} - ${pred_48h['q90']:,.0f}")
        print(f"   50% Konfidenz: ${pred_48h['q25']:,.0f} - ${pred_48h['q75']:,.0f}")

        print(f"\n📈 DETAILLIERTE WAHRSCHEINLICHKEITEN:")
        print(f"   Preis steigt: {pred_48h['prob_above_current']:.1f}%")
        print(f"   Gewinn >1%: {pred_48h['prob_above_1pct']:.1f}%")
        print(f"   Gewinn >3%: {pred_48h['prob_above_3pct']:.1f}%")
        print(f"   Gewinn >5%: {pred_48h['prob_above_5pct']:.1f}%")
        print(f"   Gewinn >10%: {pred_48h['prob_above_10pct']:.1f}%")
        print(f"   Verlust >5%: {pred_48h['prob_below_5pct']:.1f}%")
        print(f"   Verlust >10%: {pred_48h['prob_below_10pct']:.1f}%")

        print(f"\n⚠️ RISIKO-ANALYSE:")
        print(f"   Value at Risk (95%): ${pred_48h['var_95']:,.0f}")
        print(f"   Value at Risk (99%): ${pred_48h['var_99']:,.0f}")
        print(f"   Conditional VaR (95%): ${pred_48h['cvar_95']:,.0f}")
        print(f"   Upside Potential (95%): ${pred_48h['upside_95']:,.0f}")

        # Erweiterte Trading-Empfehlung
        prob_up = pred_48h['prob_above_current']
        prob_5pct = pred_48h['prob_above_5pct']
        change_48h = pred_48h['change_pct']
        volatility = pred_48h['prediction_volatility']

        if prob_up > 75 and prob_5pct > 50 and change_48h > 5:
            recommendation = "STARKER KAUF 🚀🚀🚀"
            confidence = "SEHR HOCH"
        elif prob_up > 70 and prob_5pct > 40 and change_48h > 3:
            recommendation = "KAUF 📈📈"
            confidence = "HOCH"
        elif prob_up > 65 and change_48h > 1:
            recommendation = "SCHWACHER KAUF 📈"
            confidence = "MITTEL"
        elif prob_up > 55:
            recommendation = "LEICHTER KAUF 📈"
            confidence = "NIEDRIG"
        elif prob_up > 45:
            recommendation = "HALTEN ⚖️"
            confidence = "NEUTRAL"
        elif prob_up > 35:
            recommendation = "LEICHTER VERKAUF 📉"
            confidence = "NIEDRIG"
        elif prob_up > 25:
            recommendation = "VERKAUF 📉📉"
            confidence = "MITTEL"
        else:
            recommendation = "STARKER VERKAUF 🔻🔻🔻"
            confidence = "HOCH"

        risk_level = "NIEDRIG" if volatility < 3 else "MITTEL" if volatility < 6 else "HOCH"

        print(f"\n💡 OPTIMIERTE TRADING-EMPFEHLUNG: {recommendation}")
        print(f"   Konfidenz: {confidence}")
        print(f"   Risiko-Level: {risk_level}")
        print(f"   Basierend auf: {prob_up:.1f}% Aufwärts-Wahrscheinlichkeit")
        print(f"   Erwartete Volatilität: {volatility:.1f}%")

    print(f"\n⚡ PERFORMANCE-STATISTIKEN:")
    print(f"   Gesamtzeit: {total_time:.1f}s")
    print(f"   CPU-Kerne genutzt: {MAX_CORES}")
    print(f"   Monte Carlo Simulationen: {MONTE_CARLO_SIMS:,}")
    print(f"   Optimierung: Realistische Marktdynamik")

    print("="*85)

if __name__ == "__main__":
    main()
