#!/usr/bin/env python3
"""
🚀 PERFECT OPTIMIZED 48H BITCOIN PREDICTION 🚀
===============================================
BESTE GESCHWINDIGKEIT + GENAUIGKEIT + REALISTISCHE VOLATILITÄT
Kombiniert ALLE besten Optimierungen aus der gesamten Script-Sammlung
"""

import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor
import yfinance as yf
from concurrent.futures import ThreadPoolExecutor

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

# PERFECT OPTIMIZED KONFIGURATION
MONTE_CARLO_SIMS = 200  # Optimiert für Geschwindigkeit vs. Genauigkeit
N_JOBS = -1
MAX_THREADS = 4
SEQUENCE_LENGTH = 36  # Reduziert für Geschwindigkeit

print("🚀 PERFECT OPTIMIZED 48H BITCOIN PREDICTION")
print("=" * 44)
print(f"🔮 Monte Carlo: {MONTE_CARLO_SIMS} Simulationen")
print(f"⚡ PERFECT: Beste Geschwindigkeit + Genauigkeit")
print(f"📊 FOKUS: Realistische Volatilität + Schnelligkeit")
print(f"🕐 Start: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def get_bitcoin_data_perfect():
    """Perfekte Bitcoin-Datensammlung - schnell und genau"""
    print("📊 Lade Bitcoin-Daten (PERFECT OPTIMIZED)...")
    
    try:
        btc = yf.Ticker("BTC-USD")
        df = btc.history(period="2mo", interval="1h")  # Optimiert: 2 Monate
        
        if len(df) > 100:
            df.columns = [col.lower() for col in df.columns]
            print(f"✅ Echte Bitcoin-Daten: {len(df)} Stunden")
            print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
            return df, True
        else:
            raise Exception("Zu wenig Daten")
            
    except Exception as e:
        print(f"⚠️ API-Fehler, generiere perfekte realistische Daten...")
        
        # Perfekte realistische Bitcoin-Datengeneration
        end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        start_time = end_time - timedelta(days=60)
        dates = pd.date_range(start=start_time, end=end_time, freq='H')
        
        n_points = len(dates)
        np.random.seed(42)
        
        base_price = 67000
        
        # Perfekte Volatilitäts-Clustering
        volatility_regime = np.random.choice([0.5, 1.2, 2.5, 5.0], 
                                           n_points//24, p=[0.3, 0.4, 0.25, 0.05])
        volatility_regime = np.repeat(volatility_regime, 24)[:n_points]
        
        # Perfekte Trend-Generation
        trend_changes = np.random.choice([-2, -1, 0, 1, 2], 
                                       n_points//48, p=[0.2, 0.25, 0.1, 0.25, 0.2])
        trend = np.repeat(trend_changes, 48)[:n_points] * np.random.uniform(1000, 3000, n_points)
        trend = np.cumsum(trend)
        
        # Perfekte Volatilität
        daily_vol = np.random.normal(0, 2000, n_points) * volatility_regime
        
        # Perfekte Zyklen
        weekly_cycle = 1000 * np.sin(2 * np.pi * np.arange(n_points) / (24 * 7))
        
        # Perfekte News Events
        news_events = np.random.choice([0, 1], n_points, p=[0.99, 0.01])
        news_impact = news_events * np.random.normal(0, 10000, n_points)
        
        prices = base_price + trend + daily_vol + weekly_cycle + news_impact
        prices = np.maximum(prices, 25000)
        
        # Perfekte OHLCV
        high_mult = np.random.uniform(1.003, 1.08, n_points)
        low_mult = np.random.uniform(0.92, 0.997, n_points)
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * high_mult,
            'low': prices * low_mult,
            'open': prices * np.random.uniform(0.98, 1.02, n_points),
            'volume': np.random.lognormal(15, 0.4, n_points)
        }, index=dates)
        
        print(f"✅ Perfekte realistische Daten: {len(df)} Stunden")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
        return df, False

def create_perfect_features(df):
    """Perfekte Features - optimiert für Geschwindigkeit und Genauigkeit"""
    print("🔧 Erstelle PERFECT Features...")
    
    df = df.copy()
    
    # === CORE VOLATILITY (optimiert) ===
    print("   📊 Core Volatility...")
    for window in [12, 24, 48]:
        df[f'volatility_{window}'] = df['close'].rolling(window=window).std()
        df[f'vol_ratio_{window}'] = df[f'volatility_{window}'] / df['close']
    
    # Perfekte GARCH-ähnliche Volatilität
    returns = df['close'].pct_change()
    df['returns'] = returns
    df['returns_squared'] = returns ** 2
    
    for span in [12, 24, 48]:
        df[f'ewm_vol_{span}'] = returns.ewm(span=span).std()
    
    # === CORE MOMENTUM (optimiert) ===
    print("   ⚡ Core Momentum...")
    
    # Perfekte RSI
    for period in [14, 24]:
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=period).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
        rs = gain / loss
        df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
    
    # Perfekte MACD
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    macd = ema_12 - ema_26
    signal = macd.ewm(span=9).mean()
    
    df['macd'] = macd
    df['macd_signal'] = signal
    df['macd_histogram'] = macd - signal
    
    # === CORE TREND (optimiert) ===
    print("   📈 Core Trend...")
    for window in [12, 24, 48, 72]:
        df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
        df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
        df[f'trend_strength_{window}'] = (df['close'] - df[f'sma_{window}']) / df[f'sma_{window}']
    
    # === CORE PRICE ACTION (optimiert) ===
    print("   💰 Core Price Action...")
    
    # Perfekte Returns
    for period in [1, 6, 24]:
        df[f'returns_{period}'] = df['close'].pct_change(periods=period)
        df[f'log_returns_{period}'] = np.log(df['close'] / df['close'].shift(period))
    
    # Perfekte High-Low Features
    if 'high' in df.columns and 'low' in df.columns:
        df['hl_ratio'] = df['high'] / df['low']
        df['price_range'] = df['high'] - df['low']
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        # Perfekte ATR
        df['tr'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                np.abs(df['high'] - df['close'].shift()),
                np.abs(df['low'] - df['close'].shift())
            )
        )
        df['atr_14'] = df['tr'].rolling(window=14).mean()
    
    # === PERFEKTE BOLLINGER BANDS ===
    print("   📊 Bollinger Bands...")
    bb_window = 20
    bb_middle = df['close'].rolling(window=bb_window).mean()
    bb_std = df['close'].rolling(window=bb_window).std()
    
    df['bb_upper'] = bb_middle + 2 * bb_std
    df['bb_lower'] = bb_middle - 2 * bb_std
    df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    
    # === PERFEKTE VOLUME FEATURES ===
    print("   📦 Volume...")
    if 'volume' in df.columns:
        df['volume_sma_24'] = df['volume'].rolling(window=24).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma_24']
    
    # === PERFEKTE LAG FEATURES ===
    print("   🔄 Lag Features...")
    for lag in [1, 6, 24]:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
        df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
    
    # === PERFEKTE TIME FEATURES ===
    print("   🕐 Time Features...")
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    
    # Perfekte cyclical encoding
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    
    # === PERFEKTE MARKET REGIME ===
    print("   🏛️ Market Regime...")
    df['vol_regime'] = df['volatility_24'].rolling(window=48).rank(pct=True)
    
    short_ma = df['sma_12']
    long_ma = df['sma_24']
    df['trend_regime'] = np.where(short_ma > long_ma * 1.01, 1,
                                 np.where(short_ma < long_ma * 0.99, -1, 0))
    
    print(f"✅ PERFECT Features erstellt: {df.shape[1]} Spalten")
    return df.dropna()

def prepare_perfect_data(df, sequence_length=SEQUENCE_LENGTH):
    """Perfekte Datenvorbereitung"""
    print(f"🔄 Bereite PERFECT Daten vor...")
    
    feature_cols = [col for col in df.columns if col != 'close']
    features = df[feature_cols].values
    target = df['close'].values
    
    # Perfekte Skalierung
    feature_scaler = RobustScaler()
    target_scaler = RobustScaler()  # Geändert zu RobustScaler für bessere Stabilität
    
    features_scaled = feature_scaler.fit_transform(features)
    target_scaled = target_scaler.fit_transform(target.reshape(-1, 1)).flatten()
    
    # Perfekte Sequenzen
    X, y = [], []
    for i in range(sequence_length, len(features_scaled)):
        X.append(features_scaled[i-sequence_length:i])
        y.append(target_scaled[i])
    
    X, y = np.array(X), np.array(y)
    
    # Perfekter Train/Test Split
    train_size = int(len(X) * 0.8)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]
    
    last_sequence = features_scaled[-sequence_length:]
    
    print(f"✅ PERFECT Daten vorbereitet:")
    print(f"   Train: {X_train.shape}")
    print(f"   Test: {X_test.shape}")
    print(f"   Features: {X_train.shape[2]}")
    
    return (X_train, y_train), (X_test, y_test), last_sequence, (feature_scaler, target_scaler)

def train_perfect_models(train_data, test_data):
    """Perfekte Modelle - optimiert für beste Performance"""
    print(f"\n🚀 Trainiere PERFECT Modelle...")

    X_train, y_train = train_data
    X_test, y_test = test_data

    X_train_flat = X_train.reshape(X_train.shape[0], -1)
    X_test_flat = X_test.reshape(X_test.shape[0], -1)

    # Perfekte Modelle - optimiert basierend auf besten Ergebnissen
    models = {
        'RandomForest_PERFECT': RandomForestRegressor(
            n_estimators=100,  # Optimiert für Geschwindigkeit vs. Genauigkeit
            max_depth=15,      # Optimiert
            min_samples_split=3,
            min_samples_leaf=2,
            max_features=0.7,
            n_jobs=N_JOBS,
            random_state=42,
            bootstrap=True
        ),
        'ExtraTrees_PERFECT': ExtraTreesRegressor(
            n_estimators=80,   # Optimiert für Geschwindigkeit
            max_depth=12,      # Optimiert
            min_samples_split=2,
            min_samples_leaf=1,
            max_features=0.8,
            n_jobs=N_JOBS,
            random_state=42,
            bootstrap=True
        )
    }

    results = {}

    for model_name, model in models.items():
        print(f"\n🤖 Trainiere {model_name}...")

        start_time = time.time()
        model.fit(X_train_flat, y_train)
        training_time = time.time() - start_time

        # Perfekte Vorhersagen
        y_pred = model.predict(X_test_flat)

        # Perfekte Metriken
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        # Richtungsgenauigkeit
        direction_accuracy = np.mean(np.sign(np.diff(y_test)) == np.sign(np.diff(y_pred))) * 100

        results[model_name] = {
            'model': model,
            'training_time': training_time,
            'mse': mse,
            'rmse': np.sqrt(mse),
            'r2': r2,
            'direction_accuracy': direction_accuracy,
            'y_pred': y_pred,
            'y_test': y_test
        }

        print(f"✅ {model_name}: R²={r2:.4f}, Direction={direction_accuracy:.1f}%, Zeit={training_time:.1f}s")

    return results

def run_perfect_monte_carlo_batch(args):
    """PERFECT Monte Carlo - optimiert für realistische Volatilität und Geschwindigkeit"""
    model, last_sequence, target_hour, batch_size, current_price_scaled, historical_volatility = args

    predictions = []

    for sim in range(batch_size):
        # PERFECT FIX 1: Optimierte Volatilität (nicht zu extrem, aber realistisch)
        base_noise = historical_volatility * 0.6  # Optimiert für Realismus
        time_decay = np.sqrt(target_hour / 24)
        noise_level = base_noise * time_decay

        # PERFECT FIX 2: Optimierte Volatilitäts-Regime
        vol_regime = np.random.choice([0.8, 1.5, 2.5, 4.0], p=[0.4, 0.35, 0.2, 0.05])
        noise_level *= vol_regime

        # PERFECT FIX 3: Optimierte Autokorrelation
        noise = np.random.normal(0, noise_level, last_sequence.shape)
        for i in range(1, min(len(noise), 15)):  # Optimiert
            noise[i] += 0.6 * noise[i-1]  # Optimiert

        noisy_sequence = last_sequence + noise
        current_sequence = noisy_sequence.copy()

        # PERFECT FIX 4: Optimierte Schrittweite
        if target_hour <= 6:
            step_size = 1
        elif target_hour <= 24:
            step_size = 2
        else:
            step_size = 3

        for step in range(0, target_hour, step_size):
            pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]

            if step > 0:
                prev_price = current_sequence[-1, 0]

                # PERFECT FIX 5: Optimierte Constraints
                max_change = 0.20 * step_size  # 20% pro Stunde - realistisch aber nicht extrem

                # PERFECT FIX 6: Optimierte Mean Reversion
                mean_reversion_factor = 0.002  # Minimal aber vorhanden
                long_term_mean = np.mean(current_sequence[-24:, 0]) if len(current_sequence) >= 24 else current_price_scaled
                mean_reversion = (long_term_mean - prev_price) * mean_reversion_factor

                # PERFECT FIX 7: Optimierter Momentum
                momentum = 0
                if len(current_sequence) >= 3:
                    recent_change = current_sequence[-1, 0] - current_sequence[-3, 0]
                    momentum = recent_change * 0.7  # Optimiert

                # PERFECT FIX 8: Optimierte Volatilitäts-Anpassung
                recent_volatility = np.std(current_sequence[-6:, 0]) if len(current_sequence) >= 6 else noise_level
                vol_adjustment = np.random.normal(0, recent_volatility * 0.5)  # Optimiert

                # PERFECT FIX 9: Optimierte Marktschocks
                market_shock = 0
                if np.random.random() < 0.10:  # 10% Chance - realistisch
                    market_shock = np.random.normal(0, 0.15)  # ±15% - realistisch

                # PERFECT FIX 10: Optimierte Trend-Dynamik
                trend_factor = 0
                if len(current_sequence) >= 6:
                    recent_trend = np.polyfit(range(6), current_sequence[-6:, 0], 1)[0]

                    # Trend-Verstärkung
                    trend_acceleration = recent_trend * 0.5

                    # Gelegentliche Trend-Umkehrung
                    if np.random.random() < 0.08:  # 8% Chance
                        trend_reversal = -recent_trend * 0.4
                        trend_factor = trend_acceleration + trend_reversal
                    else:
                        trend_factor = trend_acceleration

                # PERFECT FIX 11: Optimierte Events

                # News Events
                news_event = 0
                if np.random.random() < 0.15:  # 15% Chance
                    news_event = np.random.normal(0, 0.12)  # ±12%

                # Whale Movements
                whale_movement = 0
                if np.random.random() < 0.04:  # 4% Chance
                    whale_movement = np.random.normal(0, 0.10)  # ±10%

                # FOMO/FUD Cycles
                sentiment_cycle = 0
                if np.random.random() < 0.12:  # 12% Chance
                    sentiment_cycle = np.random.normal(0, 0.08)  # ±8%

                # Alle Effekte kombinieren
                pred_scaled = (pred_scaled + mean_reversion + momentum + vol_adjustment +
                              market_shock + trend_factor + news_event + whale_movement +
                              sentiment_cycle)

                # PERFECT FIX 12: Optimierte Constraints
                pred_scaled = np.clip(pred_scaled,
                                    prev_price * (1 - max_change),
                                    prev_price * (1 + max_change))

            # Optimierte Sequence-Updates
            new_row = current_sequence[-1].copy()
            new_row[0] = pred_scaled

            # Optimierte Feature-Updates
            if len(new_row) > 3:
                # Volatilität
                if len(current_sequence) >= 6:
                    new_row[1] = np.std(current_sequence[-6:, 0])

                # Momentum
                if len(current_sequence) >= 2:
                    new_row[2] = (pred_scaled - current_sequence[-2, 0]) / current_sequence[-2, 0]

                # Trend
                if len(current_sequence) >= 12:
                    trend_ma = np.mean(current_sequence[-12:, 0])
                    new_row[3] = (pred_scaled - trend_ma) / trend_ma

            current_sequence = np.vstack([current_sequence[1:], new_row])

        # PERFECT FIX 13: Finale optimierte Anpassungen
        final_pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]

        # Weekend/Overnight Effekte
        if target_hour >= 24:
            weekend_effect = np.random.normal(0, 0.05)  # ±5% - realistisch
            final_pred_scaled += weekend_effect

        # News/Event Simulation
        if np.random.random() < 0.18:  # 18% Chance
            news_impact = np.random.normal(0, 0.15)  # ±15% - realistisch
            final_pred_scaled += news_impact

        # Extreme Events (selten)
        if np.random.random() < 0.03:  # 3% Chance
            extreme_event = np.random.normal(0, 0.25)  # ±25%
            final_pred_scaled += extreme_event

        predictions.append(final_pred_scaled)

    return predictions

def predict_perfect_48h(best_models, last_sequence, target_scaler, current_time, current_price, historical_data):
    """Perfekte 48h Vorhersage mit optimierter Volatilität"""
    print(f"🔮 Erstelle PERFECT 48h Vorhersage...")
    print(f"   Monte Carlo Simulationen: {MONTE_CARLO_SIMS}")
    print(f"   🚀 PERFECT: Optimierte Geschwindigkeit + Realistische Volatilität")

    # Optimierte Zeitpunkte
    key_hours = [1, 3, 6, 12, 18, 24, 36, 48]  # Reduziert für Geschwindigkeit
    predictions = {}

    # Historische Volatilität
    recent_returns = historical_data['close'].pct_change().dropna()
    historical_volatility = recent_returns.rolling(window=168).std().iloc[-1]

    # Aktueller Preis in skalierter Form
    current_price_scaled = target_scaler.transform([[current_price]])[0, 0]

    for hour in key_hours:
        print(f"📈 Berechne +{hour}h mit {MONTE_CARLO_SIMS} PERFECT Simulationen...")

        all_model_predictions = []

        # Für jedes Modell
        for model_name, model_data in best_models.items():
            model = model_data['model']

            # Threading für Geschwindigkeit
            batch_size = MONTE_CARLO_SIMS // MAX_THREADS

            # Argumente für Threading
            args_list = []
            for thread in range(MAX_THREADS):
                thread_batch_size = batch_size if thread < MAX_THREADS - 1 else MONTE_CARLO_SIMS - (thread * batch_size)
                args_list.append((model, last_sequence, hour, thread_batch_size,
                                current_price_scaled, historical_volatility))

            # Threading Ausführung
            with ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
                batch_results = list(executor.map(run_perfect_monte_carlo_batch, args_list))

            # Ergebnisse sammeln
            model_predictions = []
            for batch_result in batch_results:
                model_predictions.extend(batch_result)

            all_model_predictions.extend(model_predictions)

        # Zurück transformieren
        all_predictions = np.array(all_model_predictions)
        all_predictions_orig = target_scaler.inverse_transform(all_predictions.reshape(-1, 1)).flatten()

        # Perfekte Statistiken
        predictions[hour] = {
            'datetime': current_time + timedelta(hours=hour),
            'mean': np.mean(all_predictions_orig),
            'median': np.median(all_predictions_orig),
            'std': np.std(all_predictions_orig),
            'min': np.min(all_predictions_orig),
            'max': np.max(all_predictions_orig),
            'q05': np.percentile(all_predictions_orig, 5),
            'q25': np.percentile(all_predictions_orig, 25),
            'q75': np.percentile(all_predictions_orig, 75),
            'q95': np.percentile(all_predictions_orig, 95),

            # Wahrscheinlichkeiten
            'prob_above_current': np.mean(all_predictions_orig > current_price) * 100,
            'prob_above_2pct': np.mean(all_predictions_orig > current_price * 1.02) * 100,
            'prob_above_5pct': np.mean(all_predictions_orig > current_price * 1.05) * 100,
            'prob_above_10pct': np.mean(all_predictions_orig > current_price * 1.10) * 100,
            'prob_below_2pct': np.mean(all_predictions_orig < current_price * 0.98) * 100,
            'prob_below_5pct': np.mean(all_predictions_orig < current_price * 0.95) * 100,
            'prob_below_10pct': np.mean(all_predictions_orig < current_price * 0.90) * 100,

            # Änderungen
            'change_pct': ((np.mean(all_predictions_orig) / current_price) - 1) * 100,
            'change_abs': np.mean(all_predictions_orig) - current_price,

            # Volatilität der Vorhersagen
            'prediction_volatility': np.std(all_predictions_orig) / current_price * 100,

            'all_predictions': all_predictions_orig
        }

    return predictions

def main():
    """PERFECT OPTIMIZED Hauptfunktion"""
    print("\n🚀" * 22)
    print("PERFECT OPTIMIZED 48H BITCOIN PREDICTION")
    print("🚀" * 22)

    start_time = time.time()

    try:
        # 1. Perfekte Datensammlung
        print("\n" + "="*50)
        print("PHASE 1: PERFECT DATENSAMMLUNG")
        print("="*50)
        df, is_real_data = get_bitcoin_data_perfect()
        current_time = df.index[-1]
        current_price = df['close'].iloc[-1]

        # 2. Perfekte Features
        print("\n" + "="*50)
        print("PHASE 2: PERFECT FEATURE ENGINEERING")
        print("="*50)
        df_features = create_perfect_features(df)

        # 3. Perfekte Datenvorbereitung
        print("\n" + "="*50)
        print("PHASE 3: PERFECT DATENAUFBEREITUNG")
        print("="*50)
        train_data, test_data, last_sequence, scalers = prepare_perfect_data(df_features)
        feature_scaler, target_scaler = scalers

        # 4. Perfekte Modelle
        print("\n" + "="*50)
        print("PHASE 4: PERFECT MODEL TRAINING")
        print("="*50)
        results = train_perfect_models(train_data, test_data)

        # 5. Beste Modelle
        sorted_results = sorted(results.items(), key=lambda x: x[1]['r2'], reverse=True)
        best_models = dict(sorted_results)  # Alle Modelle verwenden

        print(f"\n🏆 PERFECT Modelle:")
        for name, result in best_models.items():
            print(f"   {name}: R²={result['r2']:.4f}, Direction={result['direction_accuracy']:.1f}%, Zeit={result['training_time']:.1f}s")

        # 6. Perfekte 48h Vorhersage
        print("\n" + "="*50)
        print("PHASE 5: PERFECT 48H VORHERSAGE")
        print("="*50)

        predictions = predict_perfect_48h(
            best_models, last_sequence, target_scaler, current_time, current_price, df
        )

        # 7. Zusammenfassung
        total_time = time.time() - start_time
        print_perfect_summary(results, predictions, current_time, current_price, total_time, is_real_data)

        print(f"\n🎉 PERFECT 48H ANALYSE ABGESCHLOSSEN in {total_time:.1f}s! 🎉")

        return {
            'results': results,
            'predictions': predictions,
            'current_time': current_time,
            'current_price': current_price,
            'total_time': total_time,
            'optimization': 'PERFECT_optimized_speed_accuracy_volatility'
        }

    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()
        return None

def print_perfect_summary(results, predictions, current_time, current_price, total_time, is_real_data):
    """PERFECT Zusammenfassung"""
    print("\n" + "="*80)
    print("🚀 PERFECT OPTIMIZED 48H BITCOIN PREDICTION RESULTS 🚀")
    print("="*80)

    data_type = "ECHTE LIVE-DATEN" if is_real_data else "PERFECT REALISTISCHE DATEN"
    print(f"\n📊 DATENQUELLE: {data_type}")
    print(f"📅 PROGNOSE AB: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💰 AKTUELLER PREIS: ${current_price:,.2f}")
    print(f"🎯 PERFECT OPTIMIERUNG: Beste Geschwindigkeit + Genauigkeit + Realistische Volatilität")

    # Modell-Performance
    best_model = max(results.keys(), key=lambda x: results[x]['r2'])
    print(f"\n🏆 BESTES MODELL: {best_model}")
    print(f"   R² Score: {results[best_model]['r2']:.4f} ({results[best_model]['r2']*100:.1f}%)")
    print(f"   Direction Accuracy: {results[best_model]['direction_accuracy']:.1f}%")
    print(f"   RMSE: {results[best_model]['rmse']:.4f}")
    print(f"   Training Zeit: {results[best_model]['training_time']:.1f}s")

    # PERFECT 48h Vorhersagen
    print(f"\n🔮 PERFECT 48H VORHERSAGEN:")
    print(f"{'Zeit':<6} | {'Datum/Zeit':<16} | {'Erwartung':<12} | {'Median':<12} | {'Änderung':<10} | {'Wahrsch. ↑':<12} | {'Volatilität':<10}")
    print("-" * 95)

    key_hours = [3, 6, 12, 18, 24, 36, 48]
    for hour in key_hours:
        if hour in predictions:
            pred = predictions[hour]
            print(f"{hour:>4}h | {pred['datetime'].strftime('%m-%d %H:%M'):<16} | "
                  f"${pred['mean']:>10,.0f} | ${pred['median']:>10,.0f} | "
                  f"{pred['change_pct']:>+7.1f}% | {pred['prob_above_current']:>10.0f}% | "
                  f"{pred['prediction_volatility']:>8.1f}%")

    # 48h Spezial-Analyse
    if 48 in predictions:
        pred_48h = predictions[48]

        print(f"\n🎯 48H PERFECT ANALYSE:")
        print(f"   Erwartungswert: ${pred_48h['mean']:,.0f}")
        print(f"   Median: ${pred_48h['median']:,.0f}")
        print(f"   Änderung: {pred_48h['change_pct']:+.1f}%")
        print(f"   Vorhersage-Volatilität: {pred_48h['prediction_volatility']:.1f}%")
        print(f"   Range: ${pred_48h['min']:,.0f} - ${pred_48h['max']:,.0f}")
        print(f"   Konfidenz (90%): ${pred_48h['q05']:,.0f} - ${pred_48h['q95']:,.0f}")

        # Trading-Empfehlung
        prob_up = pred_48h['prob_above_current']
        volatility = pred_48h['prediction_volatility']
        change_48h = pred_48h['change_pct']

        if prob_up > 70 and change_48h > 3:
            recommendation = "STARKER KAUF 🚀🚀"
            confidence = "HOCH"
        elif prob_up > 60 and change_48h > 1:
            recommendation = "KAUF 📈"
            confidence = "MITTEL"
        elif prob_up > 55:
            recommendation = "LEICHTER KAUF 📈"
            confidence = "NIEDRIG"
        elif prob_up > 45:
            recommendation = "HALTEN ⚖️"
            confidence = "NEUTRAL"
        elif prob_up > 35:
            recommendation = "LEICHTER VERKAUF 📉"
            confidence = "NIEDRIG"
        elif prob_up > 25:
            recommendation = "VERKAUF 📉📉"
            confidence = "MITTEL"
        else:
            recommendation = "STARKER VERKAUF 🔻🔻"
            confidence = "HOCH"

        risk_level = "HOCH" if volatility > 15 else "MITTEL" if volatility > 8 else "NIEDRIG"

        print(f"\n💡 PERFECT TRADING-EMPFEHLUNG: {recommendation}")
        print(f"   Konfidenz: {confidence} ({prob_up:.1f}% Aufwärts-Wahrscheinlichkeit)")
        print(f"   Risiko-Level: {risk_level} (Volatilität: {volatility:.1f}%)")

        print(f"\n📈 WAHRSCHEINLICHKEITEN:")
        print(f"   Preis steigt: {pred_48h['prob_above_current']:.1f}%")
        print(f"   Gewinn >2%: {pred_48h['prob_above_2pct']:.1f}%")
        print(f"   Gewinn >5%: {pred_48h['prob_above_5pct']:.1f}%")
        print(f"   Gewinn >10%: {pred_48h['prob_above_10pct']:.1f}%")
        print(f"   Verlust >5%: {pred_48h['prob_below_5pct']:.1f}%")
        print(f"   Verlust >10%: {pred_48h['prob_below_10pct']:.1f}%")

        print(f"\n🚀 PERFECT OPTIMIERUNGEN:")
        print(f"   ✅ Optimierte Volatilität (60% Basis, nicht extrem)")
        print(f"   ✅ 20% max. Bewegung pro Stunde (realistisch)")
        print(f"   ✅ Optimierte Volatilitäts-Regime")
        print(f"   ✅ Realistische Marktschocks (10% Chance, ±15%)")
        print(f"   ✅ News-Events (18% Chance, ±15%)")
        print(f"   ✅ Extreme Events (3% Chance, ±25%)")
        print(f"   ✅ Whale Movements (4% Chance, ±10%)")
        print(f"   ✅ FOMO/FUD Cycles (12% Chance, ±8%)")
        print(f"   ✅ Optimierte Autokorrelation")
        print(f"   ✅ Minimale aber vorhandene Mean-Reversion")
        print(f"   ✅ Optimierte Constraints für Realismus")
        print(f"   ✅ Reduzierte Features für Geschwindigkeit")
        print(f"   ✅ Optimierte Modell-Parameter")
        print(f"   ✅ Threading für Monte Carlo")

    print(f"\n⚡ PERFORMANCE: {total_time:.1f}s | Monte Carlo: {MONTE_CARLO_SIMS} | Threading: {MAX_THREADS}")
    print("="*80)

if __name__ == "__main__":
    main()
