#!/usr/bin/env python3
"""
🚀 MODULARES BITCOIN TRADING DASHBOARD
=====================================
Umfangreiches Dashboard mit 3 Berechnungsmodellen und Live-Updates

✨ FEATURES:
- 3 verschiedene Berechnungsmodelle (Technical, Trends, Volume)
- Modulare Architektur - erweiterbar
- Live Bitcoin-Kurs mit Intervall-Updates
- Einzelne Start/Stop-Kontrollen pro Modul
- Gesamtanalyse-Vergleich
- Minimale Datenspeicherung
- Robuste Fehlerbehandlung
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import requests
import json
from datetime import datetime, timedelta
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
from collections import deque

class BitcoinDataProvider:
    """📡 Bitcoin-Datenlieferant mit API-Rotation"""

    def __init__(self):
        self.apis = {
            'coinbase': {
                'url': 'https://api.coinbase.com/v2/exchange-rates?currency=BTC',
                'extract': lambda data: float(data['data']['rates']['USD'])
            },
            'coingecko': {
                'url': 'https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd',
                'extract': lambda data: float(data['bitcoin']['usd'])
            },
            'binance': {
                'url': 'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT',
                'extract': lambda data: float(data['price'])
            }
        }

        self.current_api = 'coinbase'
        self.price_history = deque(maxlen=100)  # Nur 100 Werte im Speicher
        self.last_price = 0
        self.last_update = None

    def get_current_price(self):
        """💰 Aktuellen Bitcoin-Preis abrufen"""
        for api_name, api_config in self.apis.items():
            try:
                response = requests.get(api_config['url'], timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    price = api_config['extract'](data)

                    # Preis-Historie aktualisieren
                    self.price_history.append({
                        'price': price,
                        'timestamp': datetime.now(),
                        'source': api_name
                    })

                    self.last_price = price
                    self.last_update = datetime.now()
                    self.current_api = api_name

                    return price

            except Exception as e:
                print(f"❌ API {api_name} Fehler: {e}")
                continue

        return self.last_price  # Fallback auf letzten Preis

    def get_price_history(self, minutes=60):
        """📈 Preis-Historie der letzten X Minuten"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [entry for entry in self.price_history
                if entry['timestamp'] > cutoff_time]

class TechnicalAnalysisModule:
    """📊 Modul 1: Technische Analyse (RSI, MACD, Bollinger Bands)"""

    def __init__(self, data_provider):
        self.data_provider = data_provider
        self.is_running = False
        self.results = {
            'rsi': 50,
            'macd_signal': 'NEUTRAL',
            'bollinger_position': 'MIDDLE',
            'overall_signal': 'HALTEN',
            'confidence': 0.5,
            'last_update': None
        }

    def calculate_rsi(self, prices, period=14):
        """📈 RSI berechnen"""
        if len(prices) < period + 1:
            return 50

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def calculate_macd(self, prices):
        """📊 MACD berechnen"""
        if len(prices) < 26:
            return 'NEUTRAL'

        ema12 = np.mean(prices[-12:])
        ema26 = np.mean(prices[-26:])
        macd_line = ema12 - ema26

        if macd_line > 0:
            return 'BULLISH'
        elif macd_line < 0:
            return 'BEARISH'
        else:
            return 'NEUTRAL'

    def analyze(self):
        """🔍 Technische Analyse durchführen"""
        try:
            history = self.data_provider.get_price_history(60)
            if len(history) < 10:
                return self.results

            prices = [entry['price'] for entry in history]

            # Berechnungen
            rsi = self.calculate_rsi(prices)
            macd_signal = self.calculate_macd(prices)

            # Gesamtsignal bestimmen
            signals = []
            if rsi > 70:
                signals.append('VERKAUFEN')
            elif rsi < 30:
                signals.append('KAUFEN')
            else:
                signals.append('HALTEN')

            if macd_signal == 'BULLISH':
                signals.append('KAUFEN')
            elif macd_signal == 'BEARISH':
                signals.append('VERKAUFEN')
            else:
                signals.append('HALTEN')

            # Mehrheitsentscheidung
            buy_votes = signals.count('KAUFEN')
            sell_votes = signals.count('VERKAUFEN')

            if buy_votes > sell_votes:
                overall_signal = 'KAUFEN'
                confidence = 0.6 + (buy_votes * 0.1)
            elif sell_votes > buy_votes:
                overall_signal = 'VERKAUFEN'
                confidence = 0.6 + (sell_votes * 0.1)
            else:
                overall_signal = 'HALTEN'
                confidence = 0.5

            self.results = {
                'rsi': round(rsi, 2),
                'macd_signal': macd_signal,
                'bollinger_position': 'MIDDLE',
                'overall_signal': overall_signal,
                'confidence': min(0.9, confidence),
                'last_update': datetime.now().strftime('%H:%M:%S')
            }

        except Exception as e:
            print(f"❌ Technical Analysis Fehler: {e}")

        return self.results

class MovingAverageTrendsModule:
    """📈 Modul 2: Moving Average Trends (SMA, EMA, Momentum)"""

    def __init__(self, data_provider):
        self.data_provider = data_provider
        self.is_running = False
        self.results = {
            'sma_20': 0,
            'ema_12': 0,
            'trend_direction': 'NEUTRAL',
            'momentum': 0,
            'overall_signal': 'HALTEN',
            'confidence': 0.5,
            'last_update': None
        }

    def calculate_sma(self, prices, period):
        """📊 Simple Moving Average"""
        if len(prices) < period:
            return prices[-1] if prices else 0
        return np.mean(prices[-period:])

    def calculate_ema(self, prices, period):
        """📈 Exponential Moving Average"""
        if len(prices) < period:
            return prices[-1] if prices else 0

        multiplier = 2 / (period + 1)
        ema = prices[0]

        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))

        return ema

    def analyze(self):
        """🔍 Trend-Analyse durchführen"""
        try:
            history = self.data_provider.get_price_history(60)
            if len(history) < 20:
                return self.results

            prices = [entry['price'] for entry in history]
            current_price = prices[-1]

            # Moving Averages berechnen
            sma_20 = self.calculate_sma(prices, 20)
            ema_12 = self.calculate_ema(prices, 12)

            # Trend-Richtung bestimmen
            if current_price > sma_20 and current_price > ema_12:
                trend_direction = 'AUFWÄRTS'
                signal = 'KAUFEN'
                confidence = 0.7
            elif current_price < sma_20 and current_price < ema_12:
                trend_direction = 'ABWÄRTS'
                signal = 'VERKAUFEN'
                confidence = 0.7
            else:
                trend_direction = 'SEITWÄRTS'
                signal = 'HALTEN'
                confidence = 0.5

            # Momentum berechnen
            if len(prices) >= 10:
                momentum = (prices[-1] - prices[-10]) / prices[-10] * 100
            else:
                momentum = 0

            self.results = {
                'sma_20': round(sma_20, 2),
                'ema_12': round(ema_12, 2),
                'trend_direction': trend_direction,
                'momentum': round(momentum, 2),
                'overall_signal': signal,
                'confidence': confidence,
                'last_update': datetime.now().strftime('%H:%M:%S')
            }

        except Exception as e:
            print(f"❌ Moving Average Trends Fehler: {e}")

        return self.results

class VolumeAndPriceActionModule:
    """📊 Modul 3: Volume & Price Action (Volumen-Analyse, Support/Resistance)"""

    def __init__(self, data_provider):
        self.data_provider = data_provider
        self.is_running = False
        self.results = {
            'volume_trend': 'NORMAL',
            'price_action': 'NEUTRAL',
            'support_level': 0,
            'resistance_level': 0,
            'overall_signal': 'HALTEN',
            'confidence': 0.5,
            'last_update': None
        }

    def analyze_price_action(self, prices):
        """📈 Price Action analysieren"""
        if len(prices) < 5:
            return 'NEUTRAL'

        recent_prices = prices[-5:]

        # Aufwärtstrend erkennen
        upward_moves = sum(1 for i in range(1, len(recent_prices))
                          if recent_prices[i] > recent_prices[i-1])

        if upward_moves >= 3:
            return 'BULLISH'
        elif upward_moves <= 1:
            return 'BEARISH'
        else:
            return 'NEUTRAL'

    def find_support_resistance(self, prices):
        """🎯 Support und Resistance Levels finden"""
        if len(prices) < 20:
            current_price = prices[-1] if prices else 0
            return current_price * 0.95, current_price * 1.05

        # Vereinfachte Support/Resistance Berechnung
        min_price = min(prices[-20:])
        max_price = max(prices[-20:])

        support = min_price + (max_price - min_price) * 0.2
        resistance = max_price - (max_price - min_price) * 0.2

        return support, resistance

    def analyze(self):
        """🔍 Volume & Price Action Analyse"""
        try:
            history = self.data_provider.get_price_history(60)
            if len(history) < 10:
                return self.results

            prices = [entry['price'] for entry in history]

            # Price Action analysieren
            price_action = self.analyze_price_action(prices)

            # Support/Resistance finden
            support, resistance = self.find_support_resistance(prices)

            # Signal bestimmen
            current_price = prices[-1]

            if price_action == 'BULLISH' and current_price > support:
                signal = 'KAUFEN'
                confidence = 0.7
            elif price_action == 'BEARISH' and current_price < resistance:
                signal = 'VERKAUFEN'
                confidence = 0.7
            else:
                signal = 'HALTEN'
                confidence = 0.5

            self.results = {
                'volume_trend': 'NORMAL',  # Vereinfacht, da keine Volumen-Daten
                'price_action': price_action,
                'support_level': round(support, 2),
                'resistance_level': round(resistance, 2),
                'overall_signal': signal,
                'confidence': confidence,
                'last_update': datetime.now().strftime('%H:%M:%S')
            }

        except Exception as e:
            print(f"❌ Volume & Price Action Fehler: {e}")

        return self.results

class ModularTradingDashboard:
    """🚀 Hauptdashboard mit modularer Architektur"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 Modulares Bitcoin Trading Dashboard")
        self.root.geometry("1400x900")
        self.root.configure(bg='#1a1a1a')

        # Datenlieferant
        self.data_provider = BitcoinDataProvider()

        # Module
        self.modules = {
            'technical': TechnicalAnalysisModule(self.data_provider),
            'trends': MovingAverageTrendsModule(self.data_provider),
            'volume': VolumeAndPriceActionModule(self.data_provider)
        }

        # Update-System
        self.update_interval = 30  # 30 Sekunden
        self.is_running = False
        self.update_thread = None

        # GUI erstellen
        self.create_gui()

        # Erste Daten laden
        self.update_price_display()

    def create_gui(self):
        """🎨 GUI erstellen"""
        # Header mit Bitcoin-Preis
        self.create_header()

        # Notebook für Tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)

        # Tabs erstellen
        self.create_overview_tab()
        self.create_module_tabs()

        # Footer mit Kontrollen
        self.create_footer()

    def create_header(self):
        """📊 Header mit Bitcoin-Preis"""
        header_frame = tk.Frame(self.root, bg='#2d2d2d', height=80)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)

        # Bitcoin-Preis
        self.price_label = tk.Label(header_frame,
                                   text="Bitcoin: $0.00",
                                   font=('Arial', 24, 'bold'),
                                   bg='#2d2d2d', fg='#00ff00')
        self.price_label.pack(side='left', padx=20, pady=20)

        # Status
        self.status_label = tk.Label(header_frame,
                                    text="🔴 Gestoppt",
                                    font=('Arial', 12),
                                    bg='#2d2d2d', fg='#ff6b6b')
        self.status_label.pack(side='right', padx=20, pady=20)

    def create_overview_tab(self):
        """📊 Übersichts-Tab"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="📊 Gesamtübersicht")

        # Gesamtanalyse-Bereich
        analysis_frame = tk.LabelFrame(overview_frame, text="🎯 Gesamtanalyse",
                                      font=('Arial', 12, 'bold'),
                                      bg='#f0f0f0', fg='#333')
        analysis_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Ergebnisse der Module
        self.overview_labels = {}
        modules_info = [
            ('technical', '📊 Technical Analysis'),
            ('trends', '📈 Moving Average Trends'),
            ('volume', '📊 Volume & Price Action')
        ]

        for i, (module_key, module_name) in enumerate(modules_info):
            frame = tk.Frame(analysis_frame, bg='#f0f0f0')
            frame.pack(fill='x', padx=10, pady=5)

            tk.Label(frame, text=module_name, font=('Arial', 11, 'bold'),
                    bg='#f0f0f0', fg='#333').pack(side='left')

            self.overview_labels[module_key] = tk.Label(frame, text="HALTEN (50%)",
                                                       font=('Arial', 11),
                                                       bg='#f0f0f0', fg='#666')
            self.overview_labels[module_key].pack(side='right')

        # Gesamtempfehlung
        self.overall_recommendation = tk.Label(analysis_frame,
                                              text="🎯 GESAMTEMPFEHLUNG: HALTEN",
                                              font=('Arial', 14, 'bold'),
                                              bg='#f0f0f0', fg='#333')
        self.overall_recommendation.pack(pady=20)

    def create_module_tabs(self):
        """🔧 Tabs für einzelne Module"""
        module_configs = [
            ('technical', '📊 Technical Analysis', 'RSI, MACD, Bollinger Bands'),
            ('trends', '📈 Moving Averages', 'SMA, EMA, Trend-Erkennung'),
            ('volume', '📊 Volume & Price Action', 'Support/Resistance, Price Patterns')
        ]

        self.module_frames = {}
        self.module_labels = {}
        self.module_buttons = {}

        for module_key, tab_name, description in module_configs:
            # Tab erstellen
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text=tab_name)
            self.module_frames[module_key] = frame

            # Beschreibung
            desc_label = tk.Label(frame, text=description,
                                 font=('Arial', 12),
                                 bg='white', fg='#666')
            desc_label.pack(pady=10)

            # Ergebnisse-Bereich
            results_frame = tk.LabelFrame(frame, text="📊 Aktuelle Ergebnisse",
                                         font=('Arial', 11, 'bold'))
            results_frame.pack(fill='both', expand=True, padx=20, pady=10)

            # Labels für Ergebnisse
            self.module_labels[module_key] = {}

            # Start/Stop Button
            self.module_buttons[module_key] = tk.Button(frame,
                                                       text=f"▶️ {tab_name} starten",
                                                       font=('Arial', 11, 'bold'),
                                                       bg='#4CAF50', fg='white',
                                                       command=lambda mk=module_key: self.toggle_module(mk))
            self.module_buttons[module_key].pack(pady=10)

    def create_footer(self):
        """🎮 Footer mit Hauptkontrollen"""
        footer_frame = tk.Frame(self.root, bg='#2d2d2d', height=60)
        footer_frame.pack(fill='x', padx=10, pady=5)
        footer_frame.pack_propagate(False)

        # Hauptstart-Button
        self.main_button = tk.Button(footer_frame,
                                    text="🚀 ALLE MODULE STARTEN",
                                    font=('Arial', 14, 'bold'),
                                    bg='#4CAF50', fg='white',
                                    command=self.toggle_all_modules)
        self.main_button.pack(side='left', padx=20, pady=10)

        # Intervall-Einstellung
        tk.Label(footer_frame, text="Update-Intervall:",
                font=('Arial', 10), bg='#2d2d2d', fg='white').pack(side='right', padx=5)

        self.interval_var = tk.StringVar(value="30")
        interval_combo = ttk.Combobox(footer_frame, textvariable=self.interval_var,
                                     values=["10", "30", "60", "120"], width=5)
        interval_combo.pack(side='right', padx=5)
        interval_combo.bind('<<ComboboxSelected>>', self.update_interval_changed)

    def toggle_module(self, module_key):
        """🔄 Einzelnes Modul starten/stoppen"""
        module = self.modules[module_key]

        if module.is_running:
            module.is_running = False
            self.module_buttons[module_key].config(text=f"▶️ Modul starten", bg='#4CAF50')
            print(f"🛑 {module_key} Modul gestoppt")
        else:
            module.is_running = True
            self.module_buttons[module_key].config(text=f"⏹️ Modul stoppen", bg='#f44336')
            print(f"🚀 {module_key} Modul gestartet")

    def toggle_all_modules(self):
        """🚀 Alle Module starten/stoppen"""
        if self.is_running:
            self.stop_all_modules()
        else:
            self.start_all_modules()

    def start_all_modules(self):
        """▶️ Alle Module starten"""
        self.is_running = True

        # Alle Module aktivieren
        for module_key, module in self.modules.items():
            module.is_running = True
            self.module_buttons[module_key].config(text=f"⏹️ Modul stoppen", bg='#f44336')

        # Update-Thread starten
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()

        # GUI aktualisieren
        self.main_button.config(text="🛑 ALLE MODULE STOPPEN", bg='#f44336')
        self.status_label.config(text="🟢 Läuft", fg='#4CAF50')

        print("🚀 Alle Module gestartet")

    def stop_all_modules(self):
        """⏹️ Alle Module stoppen"""
        self.is_running = False

        # Alle Module deaktivieren
        for module_key, module in self.modules.items():
            module.is_running = False
            self.module_buttons[module_key].config(text=f"▶️ Modul starten", bg='#4CAF50')

        # GUI aktualisieren
        self.main_button.config(text="🚀 ALLE MODULE STARTEN", bg='#4CAF50')
        self.status_label.config(text="🔴 Gestoppt", fg='#ff6b6b')

        print("🛑 Alle Module gestoppt")

    def update_interval_changed(self, event):
        """⏰ Update-Intervall geändert"""
        try:
            self.update_interval = int(self.interval_var.get())
            print(f"⏰ Update-Intervall: {self.update_interval}s")
        except ValueError:
            self.interval_var.set("30")
            self.update_interval = 30

    def update_loop(self):
        """🔄 Haupt-Update-Schleife"""
        while self.is_running:
            try:
                # Bitcoin-Preis aktualisieren
                self.update_price_display()

                # Module aktualisieren
                self.update_modules()

                # Gesamtanalyse aktualisieren
                self.update_overview()

                # Warten
                time.sleep(self.update_interval)

            except Exception as e:
                print(f"❌ Update-Loop Fehler: {e}")
                time.sleep(5)

    def update_price_display(self):
        """💰 Bitcoin-Preis-Anzeige aktualisieren"""
        try:
            price = self.data_provider.get_current_price()
            self.price_label.config(text=f"Bitcoin: ${price:,.2f}")
        except Exception as e:
            print(f"❌ Preis-Update Fehler: {e}")

    def update_modules(self):
        """🔄 Alle aktiven Module aktualisieren"""
        for module_key, module in self.modules.items():
            if module.is_running:
                try:
                    results = module.analyze()
                    self.update_module_display(module_key, results)
                except Exception as e:
                    print(f"❌ {module_key} Update Fehler: {e}")

    def update_module_display(self, module_key, results):
        """📊 Modul-Anzeige aktualisieren"""
        # Übersicht aktualisieren
        if module_key in self.overview_labels:
            signal = results.get('overall_signal', 'HALTEN')
            confidence = results.get('confidence', 0.5)
            self.overview_labels[module_key].config(
                text=f"{signal} ({confidence*100:.0f}%)"
            )

    def update_overview(self):
        """🎯 Gesamtübersicht aktualisieren"""
        try:
            signals = []
            confidences = []

            for module in self.modules.values():
                if module.is_running:
                    signals.append(module.results.get('overall_signal', 'HALTEN'))
                    confidences.append(module.results.get('confidence', 0.5))

            if signals:
                # Mehrheitsentscheidung
                buy_votes = signals.count('KAUFEN')
                sell_votes = signals.count('VERKAUFEN')
                hold_votes = signals.count('HALTEN')

                if buy_votes > sell_votes and buy_votes > hold_votes:
                    overall_signal = 'KAUFEN'
                elif sell_votes > buy_votes and sell_votes > hold_votes:
                    overall_signal = 'VERKAUFEN'
                else:
                    overall_signal = 'HALTEN'

                avg_confidence = np.mean(confidences) if confidences else 0.5

                self.overall_recommendation.config(
                    text=f"🎯 GESAMTEMPFEHLUNG: {overall_signal} ({avg_confidence*100:.0f}%)"
                )

        except Exception as e:
            print(f"❌ Übersicht-Update Fehler: {e}")

    def run(self):
        """🚀 Dashboard starten"""
        print("🚀 Modulares Bitcoin Trading Dashboard gestartet")
        print("📊 3 Berechnungsmodelle verfügbar")
        print("🔧 Modulare Architektur - erweiterbar")
        self.root.mainloop()

def main():
    """🚀 Hauptfunktion"""
    try:
        dashboard = ModularTradingDashboard()
        dashboard.run()
    except Exception as e:
        print(f"❌ Dashboard-Start Fehler: {e}")
        messagebox.showerror("Fehler", f"Dashboard konnte nicht gestartet werden:\n{e}")

if __name__ == "__main__":
    main()