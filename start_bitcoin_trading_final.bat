@echo off
title Bitcoin Trading System - FINAL VERSION
color 0A

echo ================================================================================
echo BITCOIN TRADING SYSTEM - FINAL VERSION STARTER
echo ================================================================================
echo.
echo Starte Bitcoin Trading System...
echo.

cd /d "e:\Dev"

echo Pruefe Python Installation...
python --version
if errorlevel 1 (
    echo FEHLER: Python ist nicht installiert oder nicht im PATH!
    echo Bitte installieren Sie Python 3.8+ von https://python.org
    pause
    exit /b 1
)

echo.
echo Pruefe erforderliche Pakete...
python -c "import yfinance, pandas, numpy, sklearn, requests" 2>nul
if errorlevel 1 (
    echo Installiere erforderliche Pakete...
    pip install yfinance pandas numpy scikit-learn requests matplotlib seaborn
)

echo.
echo ================================================================================
echo STARTE BITCOIN TRADING SYSTEM - FINAL VERSION
echo ================================================================================
echo.

python bitcoin_trading_final.py

echo.
echo ================================================================================
echo Bitcoin Trading System beendet.
echo ================================================================================
pause
