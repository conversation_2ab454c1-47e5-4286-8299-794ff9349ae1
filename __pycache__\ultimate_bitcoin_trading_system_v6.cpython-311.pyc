�

    �peh	�  �                   �  � d Z ddlZddlZddlZddlZddlZddl	m	Z	m
Z
 ddlZddlZddl
Z
 e
j        d�  �         ddlmZ ddlmZ ddlmZmZ ddlmZ ddlZ	 ddlmZ d	Zn
# e$ r d
ZY nw xY w G d� d�  �        Zd
� Z d� Z!d� Z"e#dk    r~ e �   �         Z$e$rt e%d�  �          e%d�  �          e%d�  �          e!e$�  �        Z& e"�   �         Z' e%d�  �          e%de&rdnd� ��  �          e%de'rdnd� ��  �          e%d�  �         dS dS dS )u�  
ULTIMATE BITCOIN TRADING SYSTEM V6.0 - LIVE DATA EDITION
========================================================
VÖLLIG ÜBERARBEITETES SYSTEM MIT PRÄZISEN LIVE-DATEN
- Echte Live-Bitcoin-Preise von mehreren APIs
- Korrekte Scan-Funktionalität für Prognose-Berechnung
- Präzise Datenvisualisierung im Diagramm
- Alle Verbesserungen und Features integriert

ULTIMATE TRADING SYSTEM V6.0 - PERFEKTION IN DATENGENAUIGKEIT!
�    N)�datetime�	timedelta�ignore)�RobustScaler)�RandomForestRegressor)�mean_squared_error�r2_score)�statsTFc                   �  � e Zd ZdZd� Zdefd�Zdej        fd�Z	dej        fd�Z
dej        dedej        fd	�Zdej        dej        fd
�Zdefd�Z
dej        defd�Zdej        d
edefd�Zdej        d
edej        fd�Zdej        dej        fd�Zdej        d
edefd�Zd
edefd�Zdej        d
ededefd�Zdej        dededefd�Zdefd�ZdS )�UltimateBitcoinTradingSystemV6u�   
    ULTIMATE BITCOIN TRADING SYSTEM V6.0 - LIVE DATA EDITION
    ========================================================
    Völlig überarbeitetes System mit präzisen Live-Daten
    c           
      ��  � d| _         d| _        d| _        t          j        �   �         | _        ddddd�| _        t          j        �   �         | _	        i | _
        d | _        d	| _        i | _
        i | _        t          �   �         | _        g | _        d
| _        d | _        g | _        g | _        g | _        d | _        | j        �                    �   �         d
d
ddd
dddd�	| _        t5          d
�  �         t5          d| j         � ��  �         t5          d| j        �                    d�  �        � ��  �         t5          dt9          | j        �  �        � d��  �         t5          d�  �         t5          d�  �         d S )Nz%Ultimate_Trading_System_v6.0_LiveDatazBTC-USD�BTCUSDTz:https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDTz7https://api.coinbase.com/v2/exchange-rates?currency=BTCzKhttps://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usdz2https://api.kraken.com/0/public/Ticker?pair=XBTUSD)�binance�coinbase�	coingecko�kraken�<   r   �        )	�script_start_time�total_scans�successful_scans�current_accuracy�
best_accuracy�api_calls_count�live_data_quality�prediction_accuracy�total_analysis_timez2Ultimate Bitcoin Trading System V6.0 initialisiertz	Version: zStart-Zeit: z%d.%m.%Y %H:%M:%SzLive-Data APIs: z Quellenz3Scan-System: Prognose-Berechnung und Visualisierungu/   Datengenauigkeit: Präzise Live-Daten aktiviert)�VERSION�SYMBOL�BINANCE_SYMBOLr   �nowr   �
api_endpoints�pd�	DataFrame�market_data�live_price_cache�last_cache_time�cache_duration�	ml_models�model_performancer   �scaler�scan_results�scan_counter�last_scan_result�prediction_visualizations�hourly_predictions�prediction_history�current_prediction�	isoformat�
session_stats�print�strftime�len)�selfs    �,e:\Dev\ultimate_bitcoin_trading_system_v6.py�__init__z'UltimateBitcoinTradingSystemV6.__init__0   s�  � �>������'��� "*������ T�Q�f�J�	
� 
��� �<�>�>��� "���#��� ��� ���!#���"�n�n��� ������ $���)+��&� #%���"$���"&��� "&�!7�!A�!A�!C�!C�� !� #� � �!$�#&�#&�

� 

��� 	�C�D�D�D�
�(�$�,�(�(�)�)�)�
�S�T�3�<�<�=P�Q�Q�S�S�T�T�T�
�B��T�%7�!8�!8�B�B�B�C�C�C�
�D�E�E�E�
�@�A�A�A�A�A�    �returnc                 �(  � 	 t          d�  �         t          j        �   �         }i }d}	 t          j        | j        d         d��  �        }|j        dk    rJ|�                    �   �         }t          |d         �  �        |d<   |dz
  }t          d	|d         d
���  �         n)# t          $ r}t          d|� ��  �         Y d}~nd}~ww xY w	 t          j        | j        d
         d��  �        }|j        dk    rV|�                    �   �         }t          |d         d         d         �  �        |d
<   |dz
  }t          d|d
         d
���  �         n)# t          $ r}t          d|� ��  �         Y d}~nd}~ww xY w	 t          j        | j        d         d��  �        }|j        dk    rP|�                    �   �         }t          |d         d         �  �        |d<   |dz
  }t          d|d         d
���  �         n)# t          $ r}t          d|� ��  �         Y d}~nd}~ww xY w	 t          j        | j        d         d��  �        }|j        dk    r}|�                    �   �         }t          |d         �
                    �   �         �  �        d         }t          |d         d         �  �        |d<   |dz
  }t          d|d         d
���  �         n)# t          $ r}t          d|� ��  �         Y d}~nd}~ww xY w|�rvt          |�
                    �   �         �  �        }t          j        |�  �        }	t          j
        |�  �        }
t          |�  �        t          |�  �        z
  }t          d|t!          | j        �  �        z  �  �        }t          dd|
|	z  z
  �  �        }
||
z   dz  }t          j        �   �         |z
  }|	||t!          | j        �  �        |
|||t#          j        �   �         �                    �   �         d �	}| j        d!xx         |z
  cc<   || j        d"<   t          d#|	d
���  �         t          d$|d%�d&|� d't!          | j        �  �        � d(��  �         t          d)|d*�d+��  �         |S t          d,�  �        �# t          $ �r
}t          d-|� ��  �         	 t+          j        | j        �  �        }|j        }|�                    d.d�  �        }|dk    rI|d/|iddd0t          j        �   �         |z
  t#          j        �   �         �                    �   �         d1d2�cY d}~S n#  Y nxY wd3d4d3idt!          | j        �  �        dt          j        �   �         |z
  t#          j        �   �         �                    �   �         t3          |�  �        d5�cY d}~S d}~ww xY w)6u�   
        LIVE BITCOIN PREIS V6.0
        =======================
        Sammelt präzise Live-Preise von mehreren APIs
        z/Sammle Live-Bitcoin-Preise von mehreren APIs...r   r   �
   )�timeout��   �price�   u   ✅ Binance: $�,.2fu   ❌ Binance API Fehler: Nr   �data�rates�USDu   ✅ Coinbase: $u   ❌ Coinbase API Fehler: r   �bitcoin�usdu   ✅ CoinGecko: $u   ❌ CoinGecko API Fehler: r   �result�cu
   ✅ Kraken: $u   ❌ Kraken API Fehler: �      �?r   �   )	�consensus_price�individual_prices�successful_apis�
total_apis�	price_std�price_range�data_quality�
fetch_time�	timestampr   r   u   ✅ Live-Preis Konsensus: $u   📊 Datenqualität: �.1%� (�/z APIs)u   ⏱️ Fetch-Zeit: �.2f�sz#Keine Live-Preise von APIs erhaltenu$   ❌ FEHLER bei Live-Preis Sammlung: �regularMarketPrice�
yahoo_finance皙�����?T)rM   rN   rO   rP   rS   rT   rU   �fallbackg    �>�@r^   )rM   rN   rO   rP   rS   rT   rU   �error)r5   �time�requests�getr"   �status_code�json�float�	Exception�list�values�np�median�std�max�minr7   r   r!   r3   r4   �yf�Tickerr   �info�str)r8   �
start_time�live_pricesrO   �responserD   �e�ticker_data�prices_listrM   rQ   rR   rS   �price_consistency�overall_qualityrT   rI   �btcrp   �
current_prices                       r9   �get_live_bitcoin_price_v6z8UltimateBitcoinTradingSystemV6.get_live_bitcoin_price_v6k   sy  � �|	��C�D�D�D�����J��K��O�
6�#�<��(:�9�(E�r�R�R�R���'�3�.�.�#�=�=�?�?�D�-2�4��=�-A�-A�K�	�*�#�q�(�O��H�;�y�+A�H�H�H�I�I�I���� 
6� 
6� 
6��4��4�4�5�5�5�5�5�5�5�5�����
6����
7�#�<��(:�:�(F�PR�S�S�S���'�3�.�.�#�=�=�?�?�D�.3�D��L��4I�%�4P�.Q�.Q�K�
�+�#�q�(�O��J�K�
�,C�J�J�J�K�K�K���� 
7� 
7� 
7��5�!�5�5�6�6�6�6�6�6�6�6�����
7����
8�#�<��(:�;�(G�QS�T�T�T���'�3�.�.�#�=�=�?�?�D�/4�T�)�_�U�5K�/L�/L�K��,�#�q�(�O��L�[��-E�L�L�L�M�M�M���� 
8� 
8� 
8��6�1�6�6�7�7�7�7�7�7�7�7�����
8����	
5�#�<��(:�8�(D�b�Q�Q�Q���'�3�.�.�#�=�=�?�?�D�"&�t�H�~�'<�'<�'>�'>�"?�"?��"B�K�,1�+�c�2B�1�2E�,F�,F�K��)�#�q�(�O��F�+�h�*?�F�F�F�G�G�G���� 
5� 
5� 
5��3��3�3�4�4�4�4�4�4�4�4�����
5���� � $
G�"�;�#5�#5�#7�#7�8�8��"$�)�K�"8�"8���F�;�/�/�	�!�+�.�.��[�1A�1A�A��  #�3��#�d�>P�:Q�:Q�(Q�R�R��$'��S�I��4O�-P�$Q�$Q�!�#/�2C�#C�q�"H��!�Y�[�[�:�5�
� (7�)4�'6�"%�d�&8�"9�"9�!*�#.�$3�",�!)����!9�!9�!;�!;�
� 
�� �"�#4�5�5�5��H�5�5�5�:I��"�#6�7��J�O�J�J�J�K�K�K��v�o�v�v�v�_�v�v�WZ�[_�[m�Wn�Wn�v�v�v�w�w�w��=�J�=�=�=�=�>�>�>��
�  � E�F�F�F��� !	� !	� !	��<��<�<�=�=�=�
��i���,�,���x�� $���)=�q� A� A�
� �1�$�$�+8�.=�}�-M�+,�&'�(+�&*�i�k�k�J�&>�%-�\�^�^�%=�%=�%?�%?�$(�	� 	� 	� 	� 	� 	� 	� 	� %��
������ $,�&0�(�%;�#$�!�$�"4�5�5� #�"�i�k�k�J�6�%�\�^�^�5�5�7�7��Q���	� 	� 	
� 	
� 	
� 	
� 	
� 	
�����1!	���s�   �&Q9 �A6B  �Q9 � 
C�*C�<Q9 �C�Q9 �
BE
 �Q9 �
E3�E.�)Q9 �.E3�3Q9 �7A<G4 �3Q9 �4
H�>H�Q9 �H�Q9 �B)K �Q9 �
K.�K)�$Q9 �)K.�.E;Q9 �*Q9 �9V�V�A?T�V�V�T!�A'V�V�Vc           
      �t  � 	 t          d�  �         t          j        �   �         }| j        rvt          j        �   �         | j        z
  t          | j        ��  �        k     rD| j        j        s8t          dt          j        �   �         | j        z
  j	        � d��  �         | j        S 	 t          j        | j        �  �        }|�
                    dd��  �        }|j        rt          d�  �        �t          d	t          |�  �        � d
��  �         n=# t          $ r0}t          d|� ��  �         | �                    �   �         }Y d}~nd}~ww xY w| �                    �   �         }|d
         }|j        s�|dk    r�t          j        �   �         }|d         j        d         }||z
  |z  }	|t'          ||dt)          |	�  �        dz  z   z  �  �        t+          ||dt)          |	�  �        dz  z
  z  �  �        ||d         j        dd�         �                    �   �         d�}
t/          j        |�  �        }|
�                    �   �         D ]\  }}
|
|j        ||f<   �t          d|d���  �         | �                    ||�  �        }| �                    |�  �        }|| _        t          j        �   �         | _        t          j        �   �         |z
  }t          dt          |�  �        � d|d�d��  �         |S # t          $ rH}t          d|� ��  �         | j        j        s| j        cY d}~S | �                    �   �         cY d}~S d}~ww xY w)u~   
        PRÄZISE MARKTDATEN V6.0
        =======================
        Sammelt präzise historische und Live-Daten
        u"   Sammle präzise Marktdaten V6.0...)�secondszVerwende Cache-Daten (Alter: zs)�7d�1h)�period�intervalz"Keine Yahoo Finance Daten erhaltenzYahoo Finance Daten: � DatenpunktezYahoo Finance Fehler: NrM   r   �Close�����rB   �      �?�Volumei������Open�High�Lowr�   r�   zLive-Preis integriert: $rC   u   Präzise Marktdaten V6.0: z Datenpunkte in rY   rZ   u*   ❌ FEHLER bei präzisen Marktdaten V6.0: )r5   r`   r'   r   r!   r   r(   r%   �emptyr~   rn   ro   r   �historyrf   r7   �$_generate_realistic_fallback_data_v6r|   �ilocrl   �absrm   �meanr#   �	Timestamp�items�loc�_validate_and_clean_data_v6�_calculate_precise_metrics_v6)r8   rr   rz   �histru   �	live_datar{   �current_time�
last_close�price_change�new_row�	new_index�col�value�dfrT   s                   r9   �get_precise_market_data_v6z9UltimateBitcoinTradingSystemV6.get_precise_market_data_v6�   s�  � �L	C��6�7�7�7�����J� �$� 
(������!5�5�	�$�J]�8^�8^�8^�^�^��$�*� _��i�x�|�~�~��H\�7\�6e�i�i�i�j�j�j��'�'�
C��i���,�,���{�{�$��{�>�>���:� J�#�$H�I�I�I��E�c�$�i�i�E�E�E�F�F�F�F��� 
C� 
C� 
C��2�q�2�2�3�3�3��@�@�B�B�����������
C���� �6�6�8�8�I�%�&7�8�M� �:� 
G�-�!�"3�"3�'�|�~�~��!�'�]�/��3�
� !.�
� :�j�H�� '��
�z�Q��\�AR�AR�UX�AX�=X�/Y�Z�Z��}�j�A��L�@Q�@Q�TW�@W�<W�.X�Y�Y�*�"�8�n�1�#�$�$�7�<�<�>�>�� �� �L��6�6�	�")�-�-�/�/� 5� 5�J�C��/4�D�H�Y��^�,�,��E��E�E�E�F�F�F� �1�1�$�	�B�B�B� �3�3�B�7�7�B�  "�D��#+�<�>�>�D� �����z�1�J��Y�s�2�w�w�Y�Y�
�Y�Y�Y�Y�Z�Z�Z��I��� 	C� 	C� 	C��B�q�B�B�C�C�C� �#�)� 
C��'�'�'�'�'�'�'��@�@�B�B�B�B�B�B�B�B�����	C���sV   �BK% �"A&D	 �K% �	
E�&D>�9K% �>E�F!K% �%
L7�/$L2�L7�L2�,L7�2L7c                 �@  � 	 t          d�  �         t          j        t          j        �   �         t          d��  �        z
  t          j        �   �         d��  �        }	 | �                    �   �         }|d         }n	#  d}Y nxY wg }|dz  }t          |�  �        D �]\  }}|t          |�  �        d	z
  k    r|}nFt          j
        �                    d
d�  �        }t          dt          d
|�  �        �  �        }|d	|z   z  }t          j
        �                    dd�  �        }	|d	|	z   z  }
|d	|	z
  z  }|d	t          j
        �                    d
d�  �        z   z  }|�                    |t          |
||�  �        t          |||�  �        |t          j
        �                    dd�  �        d��  �         ��t          j        ||��  �        }
t          dt          |
�  �        � d��  �         |
S # t"          $ r/}t          d|� ��  �         t          j        �   �         cY d}~S d}~ww xY w)z*Generiere realistische Fallback-Daten V6.0z-Generiere realistische Fallback-Daten V6.0...�   )�days�H)�start�end�freqrM   �� g\���(\�?rB   r   g�~j�t�h?g{�G�z���{�G�z�?g����MbP?�{�G�zt?i�  i@  r�   ��indexz'Realistische Fallback-Daten generiert: r�   zFEHLER bei Fallback-Daten: N)r5   r#   �
date_ranger   r!   r   r|   �	enumerater7   ri   �random�normalrl   rm   �uniform�appendr$   rf   )r8   �datesr�   �
base_price�
price_datar{   �i�date�change�
volatility�high�low�
open_pricer�   ru   s                  r9   r�   zCUltimateBitcoinTradingSystemV6._generate_realistic_fallback_data_v6C  sP  � �1	"��A�B�B�B� �M�������9J�9J�9J�(J�%-�\�^�^�#�?� ?� ?�E�
$� �:�:�<�<�	�&�'8�9�
�
��
$�#�
�
�
���� �J�&��-�M�$�U�+�+� 
� 
���4���E�
�
�Q��&�&�$.�M�M�  �Y�-�-�a��7�7�F� ���D�&�(9�(9�:�:�F�!�a�&�j�1�M�  �Y�.�.�u�e�<�<�
�$��J��7��#�q�:�~�6��*�a�"�)�2B�2B�1�e�2L�2L�.L�M�
��!�!�&���m�Z�@�@��s�M�:�>�>�*� �i�/�/��d�;�;�#� #� � � � � ��j��6�6�6�B��Q�C��G�G�Q�Q�Q�R�R�R��I��� 	"� 	"� 	"��3��3�3�4�4�4��<�>�>�!�!�!�!�!�!�����	"���s7   �AG$ �A9 �8G$ �9A?�=E&G$ �$
H�.$H�H�Hr�   r�   c                 ��  �� 	 �j         r�S t          d�  �         ��                    �   �         �dD ]:}|�j        v r/�|         �                    �   �         �|<   ��|         dk             ��;t          �fd�dD �   �         �  �        rB�g d�         �                    d��  �        �d<   �g d�         �                    d��  �        �d<   t          rKt          ��  �        d	k    r8t          j        t          j        �d
         �  �        �  �        }�|dk              �d�j        v rK�d         �                    �   �         �d<   �d         �
                    �   �         �j        �d         d
k    df<   t          dt          ��  �        dz  �  �        }|�                    dd�  �        }||z   dz  }t          dt          ��  �        � d|d���  �         �S # t           $ r}t          d|� ��  �         �cY d}~S d}~ww xY w)z"Validiere und bereinige Daten V6.0z%Validiere und bereinige Marktdaten...)r�   r�   r�   r�   ��  c              3   �*   �K  � | ]
}|�j         v V � �d S �N��columns��.0r�   r�   s     �r9   �	<genexpr>zMUltimateBitcoinTradingSystemV6._validate_and_clean_data_v6.<locals>.<genexpr>�  s*   �� � � �Q�Q��3�"�*�$�Q�Q�Q�Q�Q�Qr;   rB   ��axisr�   r�   r>   r�   �   r�   r   rK   �   rS   r   rL   zDaten validiert: u    Datenpunkte, Qualität: rV   zFEHLER bei Datenvalidierung: N)r�   r5   �dropnar�   r�   �allrl   rm   �SCIPY_AVAILABLEr7   ri   r
   �zscorerj   r�   rb   rf   )	r8   r�   r�   r�   �z_scores�data_quality_score�live_qualityry   ru   s	    `       r9   r�   z:UltimateBitcoinTradingSystemV6._validate_and_clean_data_v6x  s5  �� �+	��x� 
��	��9�:�:�:� �����B� 8� 
,� 
,���"�*�$�$� ��g�k�k�m�m�B�s�G��B�s�G�d�N�+�B�� �Q�Q�Q�Q�0P�Q�Q�Q�Q�Q� 
M�� @� @� @�A�E�E�1�E�M�M��6�
��?�?�?�@�D�D�!�D�L�L��5�	� � 
&�3�r�7�7�R�<�<��6�%�,�r�'�{�";�";�<�<����1��%�� �2�:�%�%�!�(�|�/�/�1�1��8��68��l�6I�6I�6K�6K���r�(�|�q�(�(�2�3� "%�S�#�b�'�'�C�-�!8�!8��$�=�=���=�=�L�1�L�@�A�E�O��]�c�"�g�g�]�]��]�]�]�^�^�^��I��� 	� 	� 	��5�!�5�5�6�6�6��I�I�I�I�I�I�����	���s#   �G �GG �
G:�G5�/G:�5G:c                 �X  �� 	 �j         r�S t          d�  �         t          �fd�dD �   �         �  �        r��d         �d         z
  }t          j        �d         �d         �                    d�  �        z
  �  �        }t          j        �d         �d         �                    d�  �        z
  �  �        }t
          j        |||gd��  �        �                    d��  �        �d	<   �d         �	                    �   �         �d
<   t          j
        �d         �d         �                    d�  �        z  �  �        �d<   �d
         �                    d�  �        �                    �   �         �d
<   �d
         �                    d�  �        �                    �   �         �d<   �d         �d         �                    d�  �        z  dz
  �d<   �d         �d         �                    d�  �        z  dz
  �d<   d�j
        v ri�d         �d         z  �                    d�  �        �                    �   �         �d<   �d         �                    d�  �        �                    �   �         �d<   t          dt          ��  �        � d��  �         �S # t           $ r}t          d|� ��  �         �cY d}~S d}~ww xY w)u   Berechne präzise Metriken V6.0u(   Berechne präzise technische Metriken...c              3   �*   �K  � | ]
}|�j         v V � �d S r�   r�   r�   s     �r9   r�   zOUltimateBitcoinTradingSystemV6._calculate_precise_metrics_v6.<locals>.<genexpr>�  s*   �� � � �I�I��3�"�*�$�I�I�I�I�I�Ir;   )r�   r�   r�   r�   r�   r�   rB   r�   �
True_Range�Returns�Log_Returnsr>   �
Volatility_10�   �
Volatility_24�   �
Momentum_5�Momentum_24r�   �Volume_Price_Trend�   �Volume_MA_20u!   Präzise Metriken berechnet für r�   u   FEHLER bei präzisen Metriken: N)r�   r5   r�   ri   r�   �shiftr#   �concatrl   �
pct_change�log�rollingrk   r�   r�   r7   rf   )r8   r�   �high_low�
high_close�	low_closeru   s    `    r9   r�   z<UltimateBitcoinTradingSystemV6._calculate_precise_metrics_v6�  s�  �� �#	��x� 
��	��<�=�=�=� �I�I�I�I�0H�I�I�I�I�I� 
d��f�:��5�	�1���V�B�v�J��G��1B�1B�1�1E�1E�$E�F�F�
��F�2�e�9�r�'�{�/@�/@��/C�/C�#C�D�D�	�#%�9�h�
�I�-N�UV�#W�#W�#W�#[�#[�ab�#[�#c�#c��<� � �w�K�2�2�4�4�B�y�M� "��r�'�{�R��[�5F�5F�q�5I�5I�'I� J� J�B�}�� #%�Y�-�"7�"7��";�";�"?�"?�"A�"A�B���"$�Y�-�"7�"7��";�";�"?�"?�"A�"A�B���  "�'�{�R��[�->�->�q�-A�-A�A�A�E�B�|�� "�7��b��k�.?�.?��.C�.C� C�a� G�B�}�� �2�:�%�%�,.�x�L�2�g�;�,F�+O�+O�PR�+S�+S�+X�+X�+Z�+Z��'�(�%'��\�%9�%9�"�%=�%=�%B�%B�%D�%D��>�"��K�c�"�g�g�K�K�K�L�L�L��I��� 	� 	� 	��7�A�7�7�8�8�8��I�I�I�I�I�I�����	���s#   �J �I4J �
J)�J$�J)�$J)c                 ��  � 	 t          d�  �         t          d�  �         t          d�  �         t          d�  �         t          j        �   �         }| xj        dz
  c_        | �                    �   �         }|j        rt          d�  �        �|d         j        d         }t          dt          |�  �        � d	|d
���  �         | �                    |�  �        }t          dt          |�  �        � d��  �         t          | j	        �  �        d
k    s| j        dz  d
k    r,| �
                    ||�  �        }t          d|rdnd� ��  �         | �                    ||�  �        }| �                    |||�  �        }| �
                    |||�  �        }t          j        �   �         |z
  }	| j        t          j        �   �         �                    �   �         |	t          |�  �        || j        �                    dd�  �        ||||| �                    �   �         | j        �                    dd
�  �        | j        �                    dd�  �        dd�d�}
| j        �                    |
�  �         |
| _        || _        | j        dxx         dz
  cc<   | j        dxx         dz
  cc<   | j        dxx         |	z
  cc<   |�                    dd
�  �        dk    r4t3          d| j        �                    d d!�  �        d"z   �  �        | j        d <   t          d#| j        � d$|	d%�d&��  �         t          d'|�                    d(d)�  �        � d*|�                    dd
�  �        d+�d,��  �         t          d-|d
���  �         t          d.|�                    d/i �  �        �                    d0d
�  �        d
���  �         t          d1| j        �                    dd
�  �        d+���  �         |
S # t
          $ r�}t          d2|� ��  �         d
d3l}|�                    �   �          | j        t          j        �   �         �                    �   �         t9          |�  �        d4t;          �   �         v rt          j        �   �         |z
  nd
d
d5d6dd7�d8�cY d3}~S d3}~ww xY w)9u�   
        PROGNOSE-SCAN V6.0
        ==================
        Führt Prognose-Berechnung durch und visualisiert im Diagramm
        �<============================================================zSTARTE PROGNOSE-SCAN V6.0...z2Berechnung und Visualisierung der Bitcoin-PrognoserB   u/   Keine präzisen Marktdaten für Scan verfügbarr�   r�   zAktuelle Daten: z Punkte, Preis: $rC   zTechnische Indikatoren: z
 berechnetr   r�   z
ML-Training: u   ✅ Erfolgreichu   ❌ Fehlgeschlagenr   r   rO   rP   �   �Live)rO   rP   �data_freshness)�scan_idrU   �	scan_time�data_pointsr{   r   �technical_indicators�
prediction�hourly_forecast�visualization_datar*   �data_sourcesr   r   r   �
confidence�ffffff�?�ffffff�?r   g      �?r�   u   ✅ Prognose-Scan #z abgeschlossen in rY   rZ   u   📊 PROGNOSE: �signal�N/A�
 (Konfidenz: rV   �)u   💰 AKTUELLER PREIS: $u   🎯 24H-ZIEL: $�
target_24hrA   u   📈 DATENQUALITÄT: u$   ❌ FEHLER beim Prognose-Scan V6.0: N�scan_start_timer�   �FEHLER)r�   r�   )r�   rU   r_   r�   r�   r{   r�   )r5   r`   r-   r�   r�   rf   r�   r7   �)calculate_precise_technical_indicators_v6r)   �train_precise_ml_model_v6�calculate_precise_prediction_v6�calculate_24h_forecast_v6�"create_prediction_visualization_v6r   r!   r3   r4   rb   �!_get_model_performance_summary_v6r&   r,   r�   r.   r2   rm   �	traceback�	print_excrq   �locals)
r8   r�   r�   r{   �
indicators�training_success�prediction_resultr�   r�   r�   �scan_resultru   r  s
                r9   �run_prediction_scan_v6z5UltimateBitcoinTradingSystemV6.run_prediction_scan_v6�  s1  � �^	��(�O�O�O��0�1�1�1��F�G�G�G��(�O�O�O�"�i�k�k�O�����"��� �0�0�2�2�B��x� 
S�� Q�R�R�R��w�K�,�R�0�M��S�S��W�W�S�S�}�S�S�S�T�T�T� �G�G��K�K�J��H�S��_�_�H�H�H�I�I�I� �4�>�"�"�a�'�'�4�+<�q�+@�A�+E�+E�#'�#A�#A�"�j�#Q�#Q� ��g�;K�&e�&7�&7�Qe�g�g�h�h�h� !%� D� D�R�� T� T�� #�<�<�R��M^�_�_�O� "&�!H�!H��M^�`o�!p�!p�� �	���o�5�I�  �,�%�\�^�^�5�5�7�7�&�"�2�w�w�!.�%)�%7�%;�%;�<O�QT�%U�%U�(2�/�#2�&8�%)�%K�%K�%M�%M�'+�'<�'@�'@�AR�TU�'V�'V�"&�"7�";�";�L�!�"L�"L�&,�!� !�� �K�( 
��$�$�[�1�1�1�$/�D�!�&7�D�#� 
��}�-�-�-��2�-�-�-���1�2�2�2�a�7�2�2�2���4�5�5�5��B�5�5�5� !�$�$�\�1�5�5��;�;�<?���&�*�*�+@�$�G�G�$�N�=P� =P��"�#8�9� 
�]��(9�]�]�Y�]�]�]�]�^�^�^��  G�$5�$9�$9�(�E�$J�$J�  G�  G�Yj�Yn�Yn�o{�}~�Y�Y�  G�  G�  G�  G�  
H�  
H�  
H��@�M�@�@�@�A�A�A��a�_�%8�%8��r�%J�%J�%N�%N�w�XY�%Z�%Z�a�a�a�b�b�b��^�$�*<�*@�*@�AT�VW�*X�*X�^�^�^�_�_�_����� 	� 	� 	��<��<�<�=�=�=��������!�!�!�  �,�%�\�^�^�5�5�7�7��Q���>O�SY�S[�S[�>[�>[�T�Y�[�[�?�:�:�ab� �!'�)1��E�E�� � 
� 
� 
� 
� 
� 
�����
	���s   �OO �
Q9�BQ4�.Q9�4Q9c                 �  � 	 |j         st          |�  �        dk     rt          d�  �         i S t          d�  �         i }|d         }|d         }|d         }d|j        v r|d         n$t	          j        dgt          |�  �        z  �  �        }|�                    d	�  �        �                    �   �         j        d
         |d<   |�                    d�  �        �                    �   �         j        d
         |d<   |�	                    d
��  �        �                    �   �         j        d
         |d<   |�	                    d��  �        �                    �   �         j        d
         |d<   |�
                    �   �         }|�                    |dk    d�  �        �                    d��  �        �                    �   �         }|�                    |dk     d�  �         �                    d��  �        �                    �   �         }	||	z  }
ddd|
j        d
         z   z  z
  |d<   |�	                    d
��  �        �                    �   �         }|�	                    d��  �        �                    �   �         }||z
  }
|
�	                    d��  �        �                    �   �         }|
j        d
         |d<   |j        d
         |d<   |
|z
  j        d
         |d<   d}|�                    |�  �        �                    �   �         }|�                    |�  �        �                    �   �         }||dz  z   }||dz  z
  }|j        d
         |d<   |j        d
         |d<   |j        d
         |d<   |j        d
         |j        d
         z
  |j        d
         |j        d
         z
  z  |d <   d!|j        v r<|d!         �                    d�  �        �                    �   �         j        d
         |d"<   n:||z
  }|�                    d�  �        �                    �   �         j        d
         |d"<   |�                    d�  �        �                    �   �         j        d
         |d#<   |j        d
         |d#         z  |d$<   t          |�  �        d	k    r|j        d
         |j        d%         z  dz
  nd|d&<   t          |�  �        d'k    r|j        d
         |j        d(         z  dz
  nd|d)<   |�
                    �   �         �                    �   �         }t          |�  �        d	k    r2|�                    d	�  �        �                    �   �         j        d
         nd*|d+<   t          |�  �        d'k    r2|�                    d'�  �        �                    �   �         j        d
         nd*|d,<   |�                    �   �         D ]Z\  }}t	          j        |�  �        st#          j        |�  �        r-d-|v rd.||<   �7d |v rd/||<   �Ad$|v rd0||<   �Kd1|v rd*||<   �Ud2||<   �[t          d3t          |�  �        � d4��  �         |S # t&          $ r%}t          d5|� ��  �         d.d2d/d6d0d*d7�cY d8}~S d8}~ww xY w)9u-   Berechne präzise technische Indikatoren V6.0r�   u1   Nicht genügend Daten für technische Indikatorenu0   Berechne präzise technische Indikatoren V6.0...r�   r�   r�   r�   r�   r>   r�   �sma_10�sma_20�   )�span�ema_12�   �ema_26r   �   )�window�d   rB   �rsi_14�	   �macd�macd_signal�macd_histogramrL   �bb_upper�	bb_middle�bb_lower�bb_positionr�   �atr_14�
volume_sma_20�volume_ratioi�����momentum_10r�   i�����momentum_24�{�G�z�?�
volatility_10�
volatility_24�rsig      I@r�   rK   r�   r   u+   Präzise technische Indikatoren berechnet: z Indikatorenu.   FEHLER bei präzisen technischen Indikatoren: g     @�@)r  r  r   r!  r#  r(  N)r�   r7   r5   r�   r#   �Seriesr�   r�   r�   �ewm�diff�whererk   r�   r�   r�   �isnari   �isinfrf   )r8   r�   r  �prices�highs�lows�volumes�delta�gain�loss�rsr  r  �	macd_liner  �	bb_periodr  �
bb_std_devr  r  r�   �returns�keyr�   ru   s                            r9   r�   zHUltimateBitcoinTradingSystemV6.calculate_precise_technical_indicators_v64  sj  � �^	��x� 
�3�r�7�7�R�<�<��I�J�J�J��	��D�E�E�E��J���[�F��v�J�E��e�9�D�&.�"�*�&<�&<�b��l�l�"�)�T�F�UX�Y[�U\�U\�L\�B]�B]�G� $*�>�>�"�#5�#5�#:�#:�#<�#<�#A�"�#E�J�x� �#)�>�>�"�#5�#5�#:�#:�#<�#<�#A�"�#E�J�x� �#)�:�:�2�:�#6�#6�#;�#;�#=�#=�#B�2�#F�J�x� �#)�:�:�2�:�#6�#6�#;�#;�#=�#=�#B�2�#F�J�x� � �K�K�M�M�E��K�K���	�1�-�-�6�6�b�6�A�A�F�F�H�H�D��[�[����A�.�.�.�7�7�r�7�B�B�G�G�I�I�D����B�#&�#��R�W�R�[��*A�#B�J�x� � �Z�Z�R�Z�(�(�-�-�/�/�F��Z�Z�R�Z�(�(�-�-�/�/�F����I�#�-�-�Q�-�/�/�4�4�6�6�K�!*���!3�J�v��(3�(8��(<�J�}�%�,5��,C�+I�"�+M�J�'�(� �I����y�1�1�6�6�8�8�I����	�2�2�6�6�8�8�J� �J��N�3�H� �J��N�3�H�%-�]�2�%6�J�z�"�&/�n�R�&8�J�{�#�%-�]�2�%6�J�z�"�)/��R��8�=��;L�)L�QY�Q^�_a�Qb�em�er�su�ev�Qv�(w�J�}�%� �r�z�)�)�')�,�'7�'?�'?��'C�'C�'H�'H�'J�'J�'O�PR�'S�
�8�$�$� �4�<��'/�'7�'7��';�';�'@�'@�'B�'B�'G��'K�
�8�$� +2�/�/�"�*=�*=�*B�*B�*D�*D�*I�"�*M�J��'�)0��b�)9�J��<W�)W�J�~�&� UX�X^�T_�T_�bd�Td�Td���R��6�;�s�;K�)K�a�)O�)O�jk�J�}�%�TW�X^�T_�T_�bd�Td�Td���R��6�;�s�;K�)K�a�)O�)O�jk�J�}�%� �'�'�)�)�0�0�2�2�G�PS�T[�P\�P\�_a�Pa�Pa�'�/�/�"�*=�*=�*A�*A�*C�*C�*H��*L�*L�gk�J��'�PS�T[�P\�P\�_a�Pa�Pa�'�/�/�"�*=�*=�*A�*A�*C�*C�*H��*L�*L�gk�J��'� )�.�.�0�0� 
.� 
.�
��U��7�5�>�>� 
.�R�X�e�_�_� 
.���|�|�*.�
�3���&�#�-�-�*-�
�3���'�3�.�.�*-�
�3���%��,�,�*.�
�3���*-�
�3����]��J���]�]�]�^�^�^����� 		� 		� 		��F�1�F�F�G�G�G���"� � #�!%�
� � 
� 
� 
� 
� 
� 
�����		���s#   �*X �W%X �
Y�X=�7Y�=Yr  c           	      ��  � 	 t          d�  �         t          |�  �        dk     r"t          dt          |�  �        � d��  �         dS | �                    ||�  �        }|j        rt          d�  �         dS | �                    |�  �        }|j        rt          d�  �         dS t          t          |�  �        t          |�  �        �  �        }|j        d|�         }|j        d|�         }t          |�  �        d	k     rt          d
�  �         dS | j        �                    |�  �        }t          ddd
ddd��  �        }|�
                    ||�  �         d| j        � �}|| j        |<   |�
                    |�  �        }	t          ||	�  �        }
t          ||	�  �        }| j        �                    dd�  �        }d|dz  z   }
t          dt%          d|
|dz  z   �  �        �  �        }|
||t'          j        �   �         t          |j        �  �        t          |�  �        || j        d�| j        |<   || j        d<   || j        d         k    r
|| j        d<   t          d�  �         t          d|d���  �         t          d |d!���  �         t          d"t          |j        �  �        � ��  �         t          d#t          |�  �        � ��  �         d$S # t,          $ r}t          d%|� ��  �         Y d}~dS d}~ww xY w)&u"   Trainiere präzises ML-Modell V6.0u%   Trainiere präzises ML-Modell V6.0...�2   u(   Nicht genügend Daten für ML-Training: z < 50Fu*   Keine Features für ML-Training verfügbaru'   Kein Target für ML-Training verfügbarN�   u.   Nicht genügend aligned Daten für ML-Trainingr  �   r�   rL   �*   rB   )�n_estimators�	max_depth�min_samples_split�min_samples_leaf�random_state�n_jobs�precise_rf_v6_scan_r   r]   g�(\����?�333333�?r�   r�   皙�����?)�mser	   �accuracy�
training_time�
features_used�training_samplesrS   r�   r   r   u"   ✅ Präzises ML-Modell trainiert:u      R² Score: �.3fz   Genauigkeit: rV   z
   Features: z   Samples: Tu'   ❌ FEHLER beim präzisen ML-Training: )r5   r7   �_create_precise_ml_features_v6r�   �_create_precise_target_v6rm   r�   r+   �
fit_transformr   �fitr-   r)   �predictr   r	   r4   rb   rl   r`   r�   r*   rf   )r8   r�   r  �features�target�
min_length�features_scaled�rf_model�
model_name�predictionsrK  �r2rS   �
base_accuracyrL  ru   s                   r9   r   z8UltimateBitcoinTradingSystemV6.train_precise_ml_model_v6�  sJ  � �S	��9�:�:�:��2�w�w��|�|��O��R���O�O�O�P�P�P��u� �:�:�2�z�J�J�H��~� 
��B�C�C�C��u� �3�3�B�7�7�F��|� 
��?�@�@�@��u� �S��]�]�C��K�K�8�8�J��}�[�j�[�1�H��[��*��-�F��8�}�}�r�!�!��F�G�G�G��u� #�k�7�7��A�A�O� -� ��"#�!"���
� � �H� 
�L�L��&�1�1�1� C�t�/@�B�B�J�)1�D�N�:�&� #�*�*�?�;�;�K�$�V�[�9�9�C��&�+�.�.�B�  �-�1�1�2E�s�K�K�L� �L�4�$7�8�M��4��T�=�B��H�+E�!F�!F�G�G�H� ��$�!%����!$�X�%5�!6�!6�$'��M�M� ,��,�	2� 	2�D�"�:�.� 6>�D��1�2��$�,�_�=�=�=�6>��"�?�3��7�8�8�8��+�2�+�+�+�,�,�,��3�X�3�3�3�4�4�4��9�#�h�&6�"7�"7�9�9�:�:�:��0��X���0�0�1�1�1��4��� 	� 	� 	��?�A�?�?�@�@�@��5�5�5�5�5�����	���s2   �AK	 �,K	 �4+K	 �!A*K	 �
F:K	 �	
K0�K+�+K0c                 ��  � 	 t          j        |j        ��  �        }|d         |d         �                    d�  �        �                    �   �         z  |d<   |d         |d         z  |d<   |d         |d         z  |d	<   |�                    �   �         D ]=\  }}t
          |t          t          f�  �        rt          j	        |�  �        s||d
|� �<   �>|d         �
                    �   �         |d<   |d         �
                    d�  �        |d
<   |d         �
                    d�  �        |d<   |d         �
                    �   �         �                    d�  �        �                    �   �         |d<   |d         �
                    �   �         �                    d�  �        �                    �   �         |d<   d|j        v rV|d         |d         �                    d�  �        �                    �   �         z  |d<   |d         �
                    �   �         |d<   |d         |d         �
                    d�  �        z  dz
  |d<   |d         |d         �
                    d�  �        z  dz
  |d<   |d         |d         �
                    d�  �        k    �                    t          �  �        |d<   |d         |d         �
                    d�  �        k    �                    t          �  �        |d<   |�                    �   �         }t!          dt#          |j        �  �        � dt#          |�  �        � d��  �         |S # t$          $ r/}t!          d |� ��  �         t          j        �   �         cY d!}~S d!}~ww xY w)"u"   Erstelle präzise ML-Features V6.0r�   r�   r>  �price_normalizedr�   r�   �high_low_ratior�   �close_open_ratio�
indicator_�
returns_1hr�   �
returns_4hr�   �returns_24hr�   �volatility_5r>   r'  r�   r�   �volume_normalized�volume_trendrB   �
momentum_5r$  �trend_5�trend_10u   Präzise ML-Features erstellt: z Features, � Samplesu"   FEHLER bei präzisen ML-Features: N)r#   r$   r�   r�   r�   r�   �
isinstance�intre   r.  r�   rk   r�   r�   �astyper�   r5   r7   rf   )r8   r�   r  rV  r<  r�   ru   s          r9   rQ  z=UltimateBitcoinTradingSystemV6._create_precise_ml_features_v6�  sP  � �+	"��|�"�(�3�3�3�H� ,.�g�;��G��9L�9L�R�9P�9P�9U�9U�9W�9W�+W�H�'�(�)+�F��b��i�)?�H�%�&�+-�g�;��F��+C�H�'�(� )�.�.�0�0� 
9� 
9�
��U��e�c�5�\�2�2� 9�2�7�5�>�>� 9�38�H�/�#�/�/�0�� &(��[�%;�%;�%=�%=�H�\�"�%'��[�%;�%;�A�%>�%>�H�\�"�&(��k�&<�&<�R�&@�&@�H�]�#� (*�'�{�'=�'=�'?�'?�'G�'G��'J�'J�'N�'N�'P�'P�H�^�$�(*�7��(>�(>�(@�(@�(H�(H��(L�(L�(P�(P�(R�(R�H�_�%� �2�:�%�%�02�8��r�(�|�?S�?S�TV�?W�?W�?\�?\�?^�?^�0^��,�-�+-�h�<�+B�+B�+D�+D���(� &(��[�2�g�;�3D�3D�Q�3G�3G�%G�!�%K�H�\�"�&(��k�B�w�K�4E�4E�b�4I�4I�&I�A�&M�H�]�#� $&�g�;��G��1B�1B�1�1E�1E�#E�"M�"M�c�"R�"R�H�Y��$&�w�K�"�W�+�2C�2C�B�2G�2G�$G�#O�#O�PS�#T�#T�H�Z� �  ���(�(�H��m�C��8H�4I�4I�m�m�VY�Zb�Vc�Vc�m�m�m�n�n�n��O��� 	"� 	"� 	"��:�q�:�:�;�;�;��<�>�>�!�!�!�!�!�!�����	"���s   �L'L* �*
M#�4$M�M#�M#c                 �j  � 	 |d         �                     d�  �        }|d         }||k    �                    t          �  �        }|�                    �   �         }t	          dt          |�  �        � d��  �         |S # t          $ r/}t	          d|� ��  �         t          j        �   �         cY d}~S d}~ww xY w)u   Erstelle präzises Target V6.0r�   r�   u   Präzises Target erstellt: rm  u   FEHLER bei präzisem Target: N)	r�   rp  ro  r�   r5   r7   rf   r#   r*  )r8   r�   �future_pricer{   rW  ru   s         r9   rR  z8UltimateBitcoinTradingSystemV6._create_precise_target_v6  s�   � �	��g�;�,�,�R�0�0�L��w�K�M� #�]�2�:�:�3�?�?�F� �]�]�_�_�F��E��F���E�E�E�F�F�F��M��� 	� 	� 	��5�!�5�5�6�6�6��9�;�;�����������	���s   �A6A9 �9
B2�$B-�'B2�-B2c           
      �  � � 	 t          d�  �         d}d}� j        r�	 t          � j        �                    �   �         � fd���  �        }� j        |         }� �                    ||�  �        }|j        sK|j        dd�         j        }� j        �	                    |�  �        }	|�
                    |	�  �        d         }|}n)# t          $ r}
t          d	|
� ��  �         Y d}
~
nd}
~
ww xY w� �                    |�  �        }|d
z  |dz  z   }|dk    rd
}
t          dd|dz
  dz  z   �  �        }n<|dk     rd}
t          ddd|z
  dz  z   �  �        }nd}
dt          |dz
  �  �        dz  z   }� j        �                    dd�  �        }||z  }|
|||||t#          j        �   �         �                    �   �         |d         j        d         ddddd�d�	}|�                    dd�  �        }|dk    rd|d         d<   n|dk     rd |d         d<   |�                    d!d�  �        }|�                    d"d�  �        }||k    rd#|d         d"<   n||k     rd$|d         d"<   |�                    d%d�  �        }|dk    rd&|d         d'<   n|d(k     rd)|d         d'<   |�                    d*d+�  �        }|dk    rd,|d         d-<   n|dk     rd.|d         d-<   t          d/|
� d0|d1�d2��  �         |S # t          $ rX}
t          d3|
� ��  �         ddddd4dt#          j        �   �         �                    �   �         d5t)          |
�  �        d6�	cY d}
~
S d}
~
ww xY w)7u!   Berechne präzise Vorhersage V6.0u$   Berechne präzise Vorhersage V6.0...r�   zTechnische Analysec                 �b   �� �j         �                    | i �  �        �                    dd�  �        S )Nr�   r   �r*   rb   ��kr8   s    �r9   �<lambda>zPUltimateBitcoinTradingSystemV6.calculate_precise_prediction_v6.<locals>.<lambda>=  s.   �� ��8N�8R�8R�ST�VX�8Y�8Y�8]�8]�^g�ij�8k�8k� r;   �r<  r�   Nr   zML-Vorhersage Fehler: r�   �333333�?g�������?�KAUFENg�������?g      �?gffffff�?�	VERKAUFEN�HALTENr]   r   r�   �Neutral)�
rsi_signalr  �	bb_signal�
volume_signal)	r�   r�   �
ml_prediction�technical_score�
model_usedrS   rU   r{   �factorsr  r>  �F   u
   Überkauftr�  r  r?  u
   Überverkauftr  r  �Bullish�Bearishr   z
Obere Bandr�  皙�����?zUntere Bandr#  rK   �Hochr�  �Niedrigu   Präzise Vorhersage: r�   rV   r�   u!   FEHLER bei präziser Vorhersage: �Fallbackr�   )	r�   r�   r�  r�  r�  rS   rU   r{   r_   )r5   r)   rl   �keysrQ  r�   r�   rh   r+   �	transformrU  rf   �_calculate_technical_score_v6rm   r�   r4   rb   r   r!   r3   rq   )r8   r�   r  r�  r�  �latest_model_name�latest_modelrV  �latest_featuresrY  ru   r�  �combined_predictionr�   r�   rS   r�   r)  r  r  r   r#  s   `                     r9   r  z>UltimateBitcoinTradingSystemV6.calculate_precise_prediction_v60  sF  �� �h	��8�9�9�9�  �M�-�J��~� 
8�8�(+�D�N�,?�,?�,A�,A�.k�.k�.k�.k�)m� )m� )m�%�#'�>�2C�#D�L�  $�B�B�2�z�R�R�H�#�>� 7�*2�-����*<�*C��*.�+�*?�*?��*P�*P��(4�(<�(<�_�(M�(M�a�(P�
�%6�
��� � 8� 8� 8��6�1�6�6�7�7�7�7�7�7�7�7�����8���� #�@�@��L�L�O� $1�3�#6�?�S�;P�"Q�� #�T�)�)�!�� ��c�-@�3�-F�#�,M�&M�N�N�
�
�$�t�+�+�$�� ��c�S�3F�-F�#�,M�&M�N�N�
�
�!�� �3�':�S�'@�#A�#A�C�#G�G�
�  �-�1�1�2E�s�K�K�L��,�&�J� !�(�!4�#2�(� ,�%�\�^�^�5�5�7�7�!#�G��!1�"�!5�"+�#,�!*�%.�	� �� �J�$ �.�.��2�.�.�C��R�x�x�6B�
�9�%�l�3�3��r���6E�
�9�%�l�3��>�>�&�!�,�,�D�$�.�.���:�:�K��k�!�!�7@�
�9�%�m�4�4���#�#�7@�
�9�%�m�4�$�.�.���<�<�K��S� � �5A�
�9�%�k�2�2��s�"�"�5B�
�9�%�k�2�%�>�>�.�#�>�>�L��c�!�!�9?�
�9�%�o�6�6���#�#�9B�
�9�%�o�6��P�&�P�P�z�P�P�P�P�Q�Q�Q����� 	� 	� 	��9�a�9�9�:�:�:�"�!�!$�#&�(� #�%�\�^�^�5�5�7�7�!'��Q���
� 
� 

� 

� 

� 

� 

� 

�����	���sI   �K �B B? �>K �?
C%�	C �K � C%�%G8K �
M �(A
L;�5M �;M c                 �  � 	 d}|�                     dd�  �        }|dk    r|dz  }n!|dk     r|dz
  }nd|cxk    rdk    rn n|d	z
  }|�                     d
d�  �        }|�                     dd�  �        }||k    r|d
z
  }n|d
z  }|�                     dd�  �        }|dk    r|d
z  }n|dk     r|d
z
  }|�                     dd�  �        }|dk    r|d	z
  }n|dk     r|d	z  }|�                     dd�  �        }|dk    r|d	z
  }n|dk     r|d	z  }t          dt          d|�  �        �  �        S # t          $ r}	Y d}	~	dS d}	~	ww xY w)zBerechne technischen Score V6.0r�   r  r>  r�  rI  r?  �(   r   皙�����?r  r   r  rJ  r   r]   r�  r#  rK   g333333�?r%  r&  g{�G�z��r   N)rb   rl   rm   rf   )
r8   r  �scorer)  r  r  r   r#  r%  ru   s
             r9   r�  z<UltimateBitcoinTradingSystemV6._calculate_technical_score_v6�  s�  � �,	��E� �.�.��2�.�.�C��R�x�x���
����r�����
����s�����b��������
�� �>�>�&�!�,�,�D�$�.�.���:�:�K��k�!�!���������� %�.�.���<�<�K��S� � �������s�"�"����� &�>�>�.�#�>�>�L��c�!�!���
�����#�#���
�� %�.�.���:�:�K��T�!�!���
����u�$�$���
���s�C��U�O�O�,�,�,��� 	� 	� 	��3�3�3�3�3�����	���s   �D&D) �)
D>�9D>r�   c                 �>  � 	 t          d�  �         |d         j        d         }t          j        �   �         }|�                    dd�  �        }t          |�                    dd�  �        �  �        }|�                    dd	�  �        }g }	t
          d
d�  �        D �]x}
|t          |
��  �        z   }|�                    d
�  �        dk    r|dz  |
z  dz  }n(|�                    d
�  �        dk    r
| dz  |
z  dz  }nd}t          j	        �
                    d|dz  �  �        }
t          j        |
t          j        z  dz  �  �        dz  }|�                    d|�  �        }||z
  |z  dz  |
z  dz  }||
z   |z   |z   }t          dt          d|�  �        �  �        }|
d
k    r|}n|	d         d         }|d
|z   z  }||z
  |z  }|d
|
dz  z
  z  }t          d|�  �        }|
|�                    �   �         |||dz  |||
||d�d�}|	�                    |�  �         ��z|	d         }||d         |d         |d         d�|	|||�                    d
�  �        |d �t          d!� |	D �   �         �  �        t          d"� |	D �   �         �  �        t          j        d#� |	D �   �         �  �        d$�d%�}t          d&|d         d'�d(|d         d)�d*��  �         |S # t$          $ rN}t          d+|� ��  �         |j        s|d         j        d         nd,d,dd	d�g t)          |�  �        d-�cY d.}~S d.}~ww xY w)/zBerechne 24h-Prognose V6.0zBerechne 24h-Prognose V6.0...r�   r�   r(  r&  r%  r   r�   r�   rB   �   )�hoursr�   r{  r�   r�   r|  r�   r  g����Mb`?r  rJ  g{�G�zt�r�   rA   rz  r  )�trendr�   �
time_cycle�mean_reversion)�hourrU   rA   r�   �price_change_percentr�   r�  r�  )rA   �change_percentr�   )�base_volatility�trend_strength�prediction_signal�prediction_confidencec                 �   � g | ]
}|d          ��S �rA   � �r�   �fs     r9   �
<listcomp>zLUltimateBitcoinTradingSystemV6.calculate_24h_forecast_v6.<locals>.<listcomp>*  �   � �E�E�E�q��'�
�E�E�Er;   c                 �   � g | ]
}|d          ��S r�  r�  r�  s     r9   r�  zLUltimateBitcoinTradingSystemV6.calculate_24h_forecast_v6.<locals>.<listcomp>+  r�  r;   c                 �   � g | ]
}|d          ��S r�  r�  r�  s     r9   r�  zLUltimateBitcoinTradingSystemV6.calculate_24h_forecast_v6.<locals>.<listcomp>,  s   � �#I�#I�#I�1�A�g�J�#I�#I�#Ir;   )rm   rl   �avg)r{   r�   �hourly_forecasts�forecast_parameters�price_range_24hz24h-Prognose: $rC   rW   �+.1fz%)zFEHLER bei 24h-Prognose: r�   )r{   r�   r�  r_   N)r5   r�   r   r!   rb   r�   �ranger   ri   r�   r�   �sin�pirl   rm   r3   r�   r�   rf   r�   rq   )r8   r�   r  r�   r{   r�   r�  r�  r�   r�  r�  �
forecast_time�trend_factor�volatility_factor�time_factorr  r�  �total_change�previous_price�	new_pricer�   �forecast_confidencer�   r�   �forecast_summaryru   s                             r9   r  z8UltimateBitcoinTradingSystemV6.calculate_24h_forecast_v6�  s  � �l	��1�2�2�2��w�K�,�R�0�M�#�<�>�>�L� )�n�n�_�d�C�C�O� ����
�q�!A�!A�B�B�N�#����c�:�:�J�!���a���� :
9� :
9�� ,�y�t�/D�/D�/D� D�
� �>�>�(�+�+�x�7�7�#-��#4�t�#;�b�#@�L�L��^�^�H�-�-��<�<�$.�;��#5��#<�r�#A�L�L�#$�L� %'�I�$4�$4�Q��!�8K�$L�$L�!� !�f�T�B�E�\�B�%6�7�7�%�?�� $����-�@�@��"(�=�"8�M�!I�C�!O�RV�!V�Y[�![��  ,�.?�?�+�M�P^�^��  #�6�3�u�l�+C�+C�D�D�� �1�9�9�%2�N�N�%5�b�%9�'�%B�N�*�a�,�.>�?�	� !*�M� 9�]�J�� '1�A��t��O�&D�#�&)�#�/B�&C�&C�#� !�!.�!8�!8�!:�!:�&�$0�,8�3�,>�"5�!-�&7�&1�*8�	 �  �
#� 
#�� !�'�'��8�8�8�8� *�"�-�J� "/�'��0�&0�1G�&H�",�\�":�� �
 %5�'6�&4�)3����)A�)A�-7�	(� (� �E�E�4D�E�E�E�F�F��E�E�4D�E�E�E�F�F��7�#I�#I�8H�#I�#I�#I�J�J�$� $� �  ��* 
�k�J�w�$7�k�k�k�
�Ka�@b�k�k�k�k�l�l�l�#�#��� 	� 	� 	��1�a�1�1�2�2�2�=?�X�!Q��G��!1�"�!5�!5�6�(.�!�SV�W�W�$&��Q���	� � 
� 
� 
� 
� 
� 
�����	���s   �KK �
L�AL�L�L�forecastc                 �  � 	 t          d�  �         d� |j        dd�         D �   �         |d         j        dd�         �                    �   �         d|j        v r'|d         j        dd�         �                    �   �         ndgdz  d	�}d
� |�                    dg �  �        D �   �         d� |�                    dg �  �        D �   �         g d
�}|�                    dg �  �        D ]B}|d         }|d         }|dz  d|z
  z  }	|d         �                    ||	z   ||	z
  |d��  �         �Cdgt          |d         �  �        z  dgt          |d         �  �        z  g g g d�}
t          |�  �        dk    r�|d         j        dd�         }|�                    d�  �        �	                    �   �         }|�                    d�  �        �
                    �   �         }
||
dz  z   }||
dz  z
  }|�                    d��  �        �                    �   �         |
d<   |�                    d��  �        �                    �   �         |
d<   |�                    d��  �        �                    �   �         |
d<   d| j        � �d |�                    d!�  �        � d"|�                    dd�  �        d#�d$�|d         j        d%         |�                    d&i �  �        �                    dd�  �        | j
        �                    d'd(�  �        d)d*d+d,d-�d.�}|||
|| j        t          j        �   �         �                    �   �         ||�                    d&i �  �        t          |�  �        d/�d0�}t          d1t          |d         �  �        � d2t          |d         �  �        � d3��  �         |S # t"          $ r;}t          d4|� ��  �         g g g d	�g g g d
�i d5d6it%          |�  �        d7�cY d}~S d}~ww xY w)8z%Erstelle Prognose-Visualisierung V6.0z(Erstelle Prognose-Visualisierung V6.0...c                 �6   � g | ]}|�                     �   �         ��S r�  )r3   )r�   �ts     r9   r�  zUUltimateBitcoinTradingSystemV6.create_prediction_visualization_v6.<locals>.<listcomp>C  s    � �E�E�E��q�{�{�}�}�E�E�Er;   i����Nr�   r�   r�   �0   )�
timestampsr0  r3  c                 �   � g | ]
}|d          ��S )rU   r�  r�  s     r9   r�  zUUltimateBitcoinTradingSystemV6.create_prediction_visualization_v6.<locals>.<listcomp>J  s   � �\�\�\�!�q��~�\�\�\r;   r�  c                 �   � g | ]
}|d          ��S r�  r�  r�  s     r9   r�  zUUltimateBitcoinTradingSystemV6.create_prediction_visualization_v6.<locals>.<listcomp>K  s   � �T�T�T�!�1�W�:�T�T�Tr;   )r�  r0  �confidence_bandsrA   r�   r&  rB   r�  )�upper�lowerr�   r>  r�  r   )r)  r  r  r  r  r�   rL   �bfill)�methodr  r  r  zBitcoin Prognose-Scan #zSignal: r�   r�   rV   r�   r�   r�   r   r]   z#00ff88z#ff8800z#ffaa00z#0088ff)�
historicalr�  �confidence_bandr  )�title�subtitler{   r�   rS   �colors)r�   rU   r�   r�  r�   )�historical_data�
forecast_data�indicators_data�chart_config�	scan_infoz"Prognose-Visualisierung erstellt: z historische + z Prognose-Punktez$FEHLER bei Prognose-Visualisierung: r�  zFehler bei Visualisierung)r�  r�  r�  r�  r_   )r5   r�   r�   �tolistr�   rb   r�   r7   r�   r�   rk   �fillnar-   r4   r   r!   r3   rf   rq   )r8   r�   r�   r�  r�  r�  r�  rA   r�   �
band_widthr�  r0  r  �bb_stdr  r  r�  �
visualizationru   s                      r9   r  zAUltimateBitcoinTradingSystemV6.create_prediction_visualization_v6<  s�  � �Z	��<�=�=�=� F�E�b�h�s�t�t�n�E�E�E��W�+�*�3�4�4�0�7�7�9�9�?G�2�:�?U�?U�2�h�<�,�S�T�T�2�9�9�;�;�;�\`�[a�df�[f�� �O� ]�\�x�|�|�DV�XZ�7[�7[�\�\�\�T�T�x�|�|�<N�PR�/S�/S�T�T�T�$&�� �M� �\�\�"4�b�9�9� 	
� 	
���'�
���|�_�
�"�T�\�Q��^�<�
��0�1�8�8�"�Z�/�"�Z�/�",�:� :� � � � � �t�c�/�,�"?�@�@�@���c�/�,�"?�@�@�@����� �O� �2�w�w�"�}�}��G��)�#�$�$�/��"�N�N�2�.�.�3�3�5�5�	�����+�+�/�/�1�1��$���
�3��$���
�3��.6�o�o�W�o�.M�.M�.T�.T�.V�.V��
�+�.6�o�o�W�o�.M�.M�.T�.T�.V�.V��
�+�/8�/?�/?�w�/?�/O�/O�/V�/V�/X�/X���,� G�4�3D�F�F�t�z�~�~�h�'?�'?�t�t�j�n�n�]i�kl�Nm�Nm�t�t�t�t�!#�G��!1�"�!5�&�l�l�<��<�<�@�@��!�L�L� $� 2� 6� 6�7J�C� P� P�"+� )�'0�"+�	� �
� �L� $3�!.�#2� ,�#�0�!)����!9�!9�!;�!;�",�(0���\�2�(F�(F�#&�r�7�7�� �� �M� 
�  ]�s�?�<�;X�7Y�7Y�  ]�  ]�jm�n{�  }I�  oJ�  kK�  kK�  ]�  ]�  ]�  
^�  
^�  
^� � ��� 	� 	� 	��<��<�<�=�=�=�24��r�#R�#R�02�b�VX�!Y�!Y�#%�!(�*E� F��Q���� � 
� 
� 
� 
� 
� 
�����	���s   �NN �
O�0O�O�Oc           
      �  � � 	 � j         sddd�S t          � j         �                    �   �         � fd���  �        }t          � j         �  �        |� j         |         �                    dd�  �        t          j        d� � j         �                    �   �         D �   �         �  �        |t          j        d� � j         �                    �   �         D �   �         �  �        d	�}|S # t          $ r}ddt          |�  �        d
�cY d}~S d}~ww xY w)z,Hole Modell-Performance Zusammenfassung V6.0r   r   )�models_countr   c                 �F   �� �j         |          �                    dd�  �        S )NrL  r   ru  rv  s    �r9   rx  zRUltimateBitcoinTradingSystemV6._get_model_performance_summary_v6.<locals>.<lambda>�  s    �� ��)?��)B�)F�)F�z�ST�)U�)U� r;   ry  rL  c                 �:   � g | ]}|�                     d d�  �        ��S )rL  r   �rb   �r�   �ms     r9   r�  zTUltimateBitcoinTradingSystemV6._get_model_performance_summary_v6.<locals>.<listcomp>�  s&   � �,k�,k�,k�a�Q�U�U�:�q�-A�-A�,k�,k�,kr;   c                 �:   � g | ]}|�                     d d�  �        ��S )rS   r]   r�  r�  s     r9   r�  zTUltimateBitcoinTradingSystemV6._get_model_performance_summary_v6.<locals>.<listcomp>�  s&   � �,q�,q�,q�A�Q�U�U�>�3�-G�-G�,q�,q�,qr;   )r�  �
best_modelr   �average_accuracyr�  �data_quality_avg)r�  r   r_   N)
r*   rl   r�  r7   rb   ri   r�   rh   rf   rq   )r8   r�  �summaryru   s   `   r9   r  z@UltimateBitcoinTradingSystemV6._get_model_performance_summary_v6�  s;  �� �	N��)� 
A�()�C�@�@�@��T�3�8�8�:�:�U�U�U�U�W� W� W�J� !$�D�$:� ;� ;�(�!%�!7�
�!C�!G�!G�
�TW�!X�!X�$&�G�,k�,k�4�Ka�Kh�Kh�Kj�Kj�,k�,k�,k�$l�$l� *�$&�G�,q�,q�QU�Qg�Qn�Qn�Qp�Qp�,q�,q�,q�$r�$r�
� �G� �N��� 	N� 	N� 	N�$%��c�!�f�f�M�M�M�M�M�M�M�M�����	N���s#   �C �CC �
D�&C>�8D�>DN)�__name__�
__module__�__qualname__�__doc__r:   �dictr|   r#   r$   r�   r�   r�   r�   r  r�   �boolr   rQ  r*  rR  r  re   r�  r  r  r  r�  r;   r9   r   r   )   s�  � � � � � �� �9B� 9B� 9B�vB�4� B� B� B� B�HRC�B�L� RC� RC� RC� RC�h3"�b�l� 3"� 3"� 3"� 3"�j-�b�l� -�t� -�PR�P\� -� -� -� -�^%��� %��� %� %� %� %�Nd�� d� d� d� d�L`�B�L� `�T� `� `� `� `�DU�B�L� U�d� U�t� U� U� U� U�n-"��� -"�4� -"�TV�T`� -"� -"� -"� -"�^�B�L� �R�Y� � � � �(j�"�,� j�D� j�UY� j� j� j� j�X.�� .�� .� .� .� .�`n�B�L� n�d� n�X\� n�ae� n� n� n� n�`\�R�\� \�t� \�_c� \�hl� \� \� \� \�|N�4� N� N� N� N� N� Nr;   r   c            	      �8  � t          d�  �         t          d�  �         t          d�  �         t          d�  �         	 t          �   �         } | �                    �   �         }t          d�  �         t          d�  �         t          d�  �         t          d�  �         t          d|�                    dd	�  �        d
���  �         t          d|�                    dd	�  �        d
���  �         t          d|�                    dd	�  �        � ��  �         t          d|�                    dd	�  �        d�d��  �         |�                    di �  �        }t          d�  �         t          d|�                    dd�  �        � ��  �         t          d|�                    dd	�  �        d
���  �         t          d|�                    dd	�  �        d���  �         t          d|�                    dd�  �        � ��  �         |�                    d i �  �        }|�                    d!i �  �        }t          d"�  �         t          d#|�                    d$d	�  �        d
���  �         t          d%|�                    d&d	�  �        d'�d(��  �         t          d|�                    dd	�  �        d
���  �         |�                    d)i �  �        }t          d*�  �         t          d+|�                    d,d	�  �        � ��  �         t          d-|�                    d.d	�  �        d
���  �         t          d/|�                    d0d	�  �        d
���  �         |�                    d1i �  �        }t          d2�  �         t          d3|�                    d4d	�  �        � d5|�                    d6d7�  �        � ��  �         t          d8|�                    d9d�  �        � ��  �         t          d:�  �         |S # t          $ r5}t          d;|� ��  �         d	d<l}|�                    �   �          Y d<}~d<S d<}~ww xY w)=u7   Hauptfunktion für Ultimate Bitcoin Trading System V6.0zP================================================================================z8ULTIMATE BITCOIN TRADING SYSTEM V6.0 - LIVE DATA EDITIONuQ   PRÄZISE LIVE-DATEN • KORREKTE SCAN-FUNKTIONALITÄT • PROGNOSE-VISUALISIERUNGzQ
================================================================================z6ULTIMATE BITCOIN TRADING SYSTEM V6.0 - SCAN-ERGEBNISSEz
LIVE-MARKTDATEN:z   Bitcoin-Preis: $r{   r   rC   u      Datenqualität: r   rV   z   Datenpunkte: r�   z   Scan-Zeit: r�   rY   rZ   r�   u   
PRÄZISE PROGNOSE:z   Signal: r�   r�   z   Konfidenz: r�   z   ML-Prediction: r�  rP  z   Modell: r�  r�   r�   z
24H-PROGNOSE:z   Ziel-Preis: $rA   u      Änderung: r�  r�  �%r*   z
MODELL-PERFORMANCE:z   Modelle: r�  z   Beste Genauigkeit: r   z   Durchschnitt: r�  r�   z
DATENQUELLEN:z   APIs erfolgreich: rO   rX   rP   r�   z   Daten-Frische: r�   zF
ULTIMATE BITCOIN TRADING SYSTEM V6.0 - LIVE DATA EDITION ERFOLGREICH!z2FEHLER beim Ultimate Bitcoin Trading System V6.0: N)r5   r   r  rb   rf   r  r  )	�systemrI   r�   r�  r�   �
model_perfr�   ru   r  s	            r9   �&run_ultimate_bitcoin_trading_system_v6r�  �  s;  � �	�(�O�O�O�	�
D�E�E�E�	�
]�^�^�^�	�(�O�O�O�3�/�1�1�� �.�.�0�0�� 	�o����
�F�G�G�G�
�h����
�#�$�$�$�
�I�F�J�J���$B�$B�I�I�I�J�J�J�
�L�F�J�J�/B�A�$F�$F�L�L�L�M�M�M�
�?����M�1�!=�!=�?�?�@�@�@�
�@�v�z�z�+�q�9�9�@�@�@�@�A�A�A��Z�Z��b�1�1�
�
�%�&�&�&�
�=�J�N�N�8�U�;�;�=�=�>�>�>�
�D�z�~�~�l�A�>�>�D�D�D�E�E�E�
�K�:�>�>�/�1�#E�#E�K�K�K�L�L�L�
�A�J�N�N�<��?�?�A�A�B�B�B��:�:�/��4�4���\�\�,��3�3�
�
� �!�!�!�
�B������!;�!;�B�B�B�C�C�C�
�J�z�~�~�.>��B�B�J�J�J�J�K�K�K�
�D�z�~�~�l�A�>�>�D�D�D�E�E�E��Z�Z� 3�R�8�8�
�
�&�'�'�'�
�@�Z�^�^�N�A�>�>�@�@�A�A�A�
�O�z�~�~�o�q�'I�'I�O�O�O�P�P�P�
�M�*�.�.�1C�Q�"G�"G�M�M�M�N�N�N��z�z�.�"�5�5��
� �!�!�!�
�r�l�&6�&6�7H�!�&L�&L�r�r�|�O_�O_�`l�no�Op�Op�r�r�s�s�s�
�N�<�#3�#3�4D�e�#L�#L�N�N�O�O�O�
�X�Y�Y�Y��
��� � � �
�F�1�F�F�G�G�G������������t�t�t�t�t�����	���s   �NO �
P�$*P�Pc           	      �  �� t          d�  �         	 ddlm} ddlm}  |j        d��  �        }|�                    dd�	�  �        }| �                    d
d�  �        �t          d�  �        }�fd
�|D �   �         }d� |D �   �         }|�	                    ||||ddd��  �        }|�
                    d�  �         |�                    d�  �         |�                    d�  �         |�
                    d�  �         |�                    dd�	�  �        }	d� |D �   �         }
d� |D �   �         }g }|
D ]O}
|
dk     r|�                    d�  �         �|
dk    r|�                    d�  �         �:|�                    d�  �         �P|	�	                    |
|||dd �!�  �         |	�
                    d"�  �         |	�                    d#�  �         |	�                    d�  �         |	�
                    d$�  �         |�                    d%d�	�  �        }d&� |D �   �         }d'� |D �   �         }|�	                    ||||d(d�)�  �         |�
                    d*�  �         |�                    d+�  �         |�                    d�  �         |�
                    d,�  �         |�                    d-d�	�  �        }d.� |D �   �         }d/� |D �   �         }d0� |D �   �         }|�	                    ||||d1d�)�  �         |�
                    d2�  �         |�                    d3�  �         |�                    d4�  �         |�
                    d5�  �          |j        �   �          t!          j        �   �         �                    d6�  �        }d7|� d8�} |j        |d9d:�;�  �         t          d<|� ��  �          |j        �   �          |S # t*          $ r}t          d=|� ��  �         Y d}~dS d}~ww xY w)>u9   Erstelle 3D-Visualisierung aller Trading-Faktoren für V6z(Erstelle 3D-Trading-Visualisierung V6...r   N)�Axes3D)r@  r>   )�figsize��   �3d)�
projectionr{   iȯ  r�   c                 �\   �� g | ](}�d t           j        �                    dd�  �        z   z  ��)S )rB   r   r&  �ri   r�   r�   )r�   �_r{   s     �r9   r�  z6create_3d_trading_visualization_v6.<locals>.<listcomp>�  s5   �� �W�W�W�a�-�1�r�y�'7�'7��4�'@�'@�#@�A�W�W�Wr;   c                 �N   � g | ]"}t           j        �                    d d�  �        ��#S )i@B i@KL �ri   r�   r�   �r�   r�  s     r9   r�  z6create_3d_trading_visualization_v6.<locals>.<listcomp>   s*   � �L�L�L�1�2�9�$�$�W�g�6�6�L�L�Lr;   �viridisr>  r�   )rJ   �cmaprZ   �alphazZeit (Stunden)zPreis (USD)�Volumenz3D Preis-Volumen-Zeit Analyse��   c                 �N   � g | ]"}t           j        �                    d d�  �        ��#S )r�   �P   r�  r�  s     r9   r�  z6create_3d_trading_visualization_v6.<locals>.<listcomp>  s*   � �E�E�E�A�b�i�'�'��B�/�/�E�E�Er;   c                 �N   � g | ]"}t           j        �                    d d�  �        ��#S )r   r�   r�  r�  s     r9   r�  z6create_3d_trading_visualization_v6.<locals>.<listcomp>  s*   � �E�E�E�A�r�y�'�'��3�/�/�E�E�Er;   r?  �greenr�  �red�bluer   r]   )rJ   rZ   r   �RSI�MACDu3   3D Trading-Signale (Grün=BUY, Rot=SELL, Blau=HOLD)��   c                 �h   � g | ]/}t          t          j        �                    d d�  �        �  �        ��0S )r   r&  )r�   ri   r�   r�   r�  s     r9   r�  z6create_3d_trading_visualization_v6.<locals>.<listcomp>$  s2   � �J�J�J��c�"�)�*�*�1�d�3�3�4�4�J�J�Jr;   c                 �N   � g | ]"}t           j        �                    d d�  �        ��#S )r   r�   r�  r�  s     r9   r�  z6create_3d_trading_visualization_v6.<locals>.<listcomp>%  s*   � �C�C�C�!�B�I�$�$�Q��-�-�C�C�Cr;   �plasma)rJ   r�  rZ   u   Volatilität�Momentumu   3D Volatilität-Momentum-Preis��   c                 �N   � g | ]"}t           j        �                    d d�  �        ��#S )r�   rJ  r�  r�  s     r9   r�  z6create_3d_trading_visualization_v6.<locals>.<listcomp>0  s*   � �I�I�I��r�y�(�(��s�3�3�I�I�Ir;   c                 �N   � g | ]"}t           j        �                    d d�  �        ��#S )r&  r�  r�  r�  s     r9   r�  z6create_3d_trading_visualization_v6.<locals>.<listcomp>1  s*   � �E�E�E�A�2�9�#�#�D�$�/�/�E�E�Er;   c                 �N   � g | ]"}t           j        �                    d d�  �        ��#S )r�   r�   r�  r�  s     r9   r�  z6create_3d_trading_visualization_v6.<locals>.<listcomp>2  s*   � �H�H�H�q�b�i�'�'��T�2�2�H�H�Hr;   �coolwarm�RisikozErwartete Rendite�	Konfidenzz3D Risiko-Rendite-Konfidenzz
%Y%m%d_%H%M%S�bitcoin_3d_trading_analysis_v6_z.pngi,  �tight)�dpi�bbox_inchesz3D-Visualisierung gespeichert: zFehler bei 3D-Visualisierung: )r5   �matplotlib.pyplot�pyplot�mpl_toolkits.mplot3dr�  �figure�add_subplotrb   r�  �scatter�
set_xlabel�
set_ylabel�
set_zlabel�	set_titler�   �tight_layoutr   r!   r6   �savefig�showrf   )�
system_result�pltr�  �fig�ax1�time_pointsr0  r3  r   �ax2�
rsi_values�macd_values�
signal_colorsr)  �ax3r�   �momentum�ax4�risk_levelsr;  r�   rU   �filenameru   r{   s                           @r9   �"create_3d_trading_visualization_v6r6  �  s`  �� �	�
4�5�5�5�V�'�'�'�'�'�'�/�/�/�/�/�/� �c�j��*�*�*�� �o�o�c�d�o�3�3�� &�)�)�/�5�A�A�
��B�i�i��W�W�W�W�;�W�W�W��L�L��L�L�L�� �+�+�k�6�7�$�9��#� � G� G�����'�(�(�(����}�%�%�%����y�!�!�!��
�
�5�6�6�6� �o�o�c�d�o�3�3�� F�E��E�E�E�
�E�E��E�E�E�� �
�� 	-� 	-�C��R�x�x��$�$�W�-�-�-�-��r����$�$�U�+�+�+�+��$�$�V�,�,�,�,����J��V�}��RU��V�V�V����u�������v�������}�%�%�%��
�
�K�L�L�L� �o�o�c�d�o�3�3��J�J�k�J�J�J�
�C�C�{�C�C�C�����J��&�K�h�RT��U�U�U����~�&�&�&����z�"�"�"����}�%�%�%��
�
�6�7�7�7� �o�o�c�d�o�3�3��I�I�[�I�I�I��E�E��E�E�E��H�H�K�H�H�H�
����K��*�
��WY��Z�Z�Z����x� � � ����*�+�+�+����{�#�#�#��
�
�3�4�4�4������� �L�N�N�+�+�O�<�<�	�D�Y�D�D�D�����H�#�7�;�;�;�;�
�:��:�:�;�;�;����
�
�
����� � � �
�2�q�2�2�3�3�3��t�t�t�t�t��������s   �N
N  � 
O�*O�Oc                  �,  � t          d�  �         	 ddl} | j        �                    | j        �                    d�  �        d�  �        }| j        �                    |�  �        s| j        �                    d�  �        } | j        �   �         }d|� d�}| j        �                    |d�  �        }t          |d	�  �        5 }|�                    |�  �         ddd�  �         n# 1 swxY w Y   d
|� d�}| j        �                    |d�  �        }t          |d	�  �        5 }|�                    |�  �         ddd�  �         n# 1 swxY w Y   d
|� d�}| j        �                    |d�  �        }	t          |	d	�  �        5 }|�                    |�  �         ddd�  �         n# 1 swxY w Y   t          d�  �         t          d|� ��  �         t          d|� ��  �         t          d|	� ��  �         dS # t          $ r}
t          d|
� ��  �         Y d}
~
dS d}
~
ww xY w)u.   Erstelle Desktop-Verknüpfungen für V6 Systemu*   Erstelle Desktop-Verknüpfungen für V6...r   N�~�DesktopzE@echo off
title Ultimate Bitcoin Trading System V6.0
color 0A
cd /d "a8  "
echo.
echo ===============================================
echo ULTIMATE BITCOIN TRADING SYSTEM V6.0
echo LIVE DATA EDITION
echo ===============================================
echo.
echo Starte Live-Bitcoin-Analyse...
echo.
python ultimate_bitcoin_trading_system_v6.py
echo.
echo Analyse abgeschlossen!
pause
zBitcoin_Trading_System_V6.bat�wzE@echo off
title Bitcoin 3D Trading Visualisierung V6
color 0B
cd /d "a,  "
echo.
echo ===============================================
echo BITCOIN 3D TRADING VISUALISIERUNG V6
echo ===============================================
echo.
echo Starte 3D-Analyse...
echo.
python -c "
from ultimate_bitcoin_trading_system_v6 import UltimateBitcoinTradingSystemV6, create_3d_trading_visualization_v6
system = UltimateBitcoinTradingSystemV6()
result = system.run_prediction_scan_v6()
viz_file = create_3d_trading_visualization_v6(result)
print('3D-Visualisierung erstellt:', viz_file)
"
echo.
echo 3D-Visualisierung abgeschlossen!
pause
z Bitcoin_3D_Visualisierung_V6.batz;@echo off
title Bitcoin Live-Monitoring V6
color 0E
cd /d "a  "
echo.
echo ===============================================
echo BITCOIN LIVE-MONITORING V6
echo ===============================================
echo.
echo Starte kontinuierliches Monitoring...
echo Druecken Sie Ctrl+C zum Beenden
echo.
python -c "
from ultimate_bitcoin_trading_system_v6 import UltimateBitcoinTradingSystemV6
import time
system = UltimateBitcoinTradingSystemV6()
while True:
    try:
        result = system.run_prediction_scan_v6()
        print(f'Live-Preis: ${result.get("current_price", 0):,.2f} - Signal: {result.get("prediction", {}).get("signal", "N/A")}')
        time.sleep(60)  # 1 Minute warten
    except KeyboardInterrupt:
        print('Monitoring beendet.')
        break
    except Exception as e:
        print(f'Fehler: {e}')
        time.sleep(10)
"
pause
zBitcoin_Live_Monitoring_V6.batu    Desktop-Verknüpfungen erstellt:z1. z2. z3. Tu2   Fehler beim Erstellen der Desktop-Verknüpfungen: F)
r5   �os�path�join�
expanduser�exists�getcwd�open�writerf   )r;  �desktop�current_dir�
main_batch�	main_pathr�  �	viz_batch�viz_path�
monitor_batch�monitor_pathru   s              r9   �create_desktop_shortcuts_v6rK  J  s  � �	�
6�7�7�7�j��	�	�	� �'�,�,�r�w�1�1�#�6�6�	�B�B���w�~�~�g�&�&� 	.��g�(�(��-�-�G��b�i�k�k��� 	�� � �
�$ �G�L�L��*I�J�J�	�
�)�S�
!�
!� 	 �Q�
�G�G�J����	 � 	 � 	 � 	 � 	 � 	 � 	 � 	 � 	 � 	 � 	 ���� 	 � 	 � 	 � 	 �� 	�� � �	�. �7�<�<��)K�L�L��
�(�C�
 �
 � 	�A�
�G�G�I����	� 	� 	� 	� 	� 	� 	� 	� 	� 	� 	���� 	� 	� 	� 	�� 	�� � �
�> �w�|�|�G�-M�N�N��
�,��
$�
$� 	#��
�G�G�M�"�"�"�	#� 	#� 	#� 	#� 	#� 	#� 	#� 	#� 	#� 	#� 	#���� 	#� 	#� 	#� 	#� 	�1�2�2�2�
��I��� � � �
��H������
�"�L�"�"�#�#�#��t��� � � �
�F�1�F�F�G�G�G��u�u�u�u�u��������s�   �B+G, �<C�G, �C"�"G, �%C"�&4G, �D<�0G, �<E � G, �E �4G, �8F�G, �F�G, �!F�"AG, �,
H�6H�H�__main__z=
============================================================zERWEITERTE FUNKTIONENr�   z%
ERWEITERTE FUNKTIONEN ABGESCHLOSSEN:z- 3D-Visualisierung: �Erstellt�Fehleru   - Desktop-Verknüpfungen: u?   
ULTIMATE BITCOIN TRADING SYSTEM V6.0 - VOLLSTÄNDIG ERWEITERT!)(r�  �yfinancern   �pandasr#   �numpyri   ra   r`   r   r   rd   r;  �warnings�filterwarnings�sklearn.preprocessingr   �sklearn.ensembler   �sklearn.metricsr   r	   r  r  r)  �seaborn�sns�scipyr
   r�   �ImportErrorr   r�  r6  rK  r�  rI   r5   �viz_file�shortcuts_createdr�  r;   r9   �<module>r]     s�  ��
� 
� � � � � � � � � � � � � ���� ���� (� (� (� (� (� (� (� (� ���� 	�	�	�	� ���� �� �� !� !� !� /� .� .� .� .� .� 2� 2� 2� 2� 2� 2� 8� 8� 8� 8� 8� 8� 8� 8�  � � � � � � � � � ���������O�O��� � � ��O�O�O�����FN� FN� FN� FN� FN� FN� FN� FN�R$:� :� :�xZ� Z� Z�xn� n� n�` �z���
3�
3�
5�
5�F�
� S�
��m����
��%�&�&�&�
��f�
�
�
� 6�5�f�=�=�� 8�7�9�9��
��7�8�8�8�
��L�H�&J�j�j�(�L�L�M�M�M�
��Z�9J�+X�:�:�PX�Z�Z�[�[�[�
��Q�R�R�R�R�R�% ��S� Ss   �A# �#A-�,A-