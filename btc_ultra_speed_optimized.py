#!/usr/bin/env python3
"""
⚡ ULTRA-SPEED OPTIMIZED 48H BITCOIN PREDICTION ⚡
=================================================
DRASTISCH BESCHLEUNIGT + VERBESSERTE LESBARKEIT + ALLE BESTEN FEATURES
"""

import os
import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge
import yfinance as yf
from concurrent.futures import ThreadPoolExecutor
import threading

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

# ULTRA-SPEED KONFIGURATION
MONTE_CARLO_SIMS = 300  # Reduziert für Geschwindigkeit
N_JOBS = -1
MAX_THREADS = 4  # Für Threading

print("⚡ ULTRA-SPEED OPTIMIZED 48H BITCOIN PREDICTION")
print("=" * 48)
print(f"🔮 Monte Carlo: {MONTE_CARLO_SIMS} Simulationen")
print(f"⚡ ULTRA-SPEED: Drastisch beschleunigt")
print(f"📊 LESBARKEIT: Optimiert")
print(f"🕐 Start: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def get_bitcoin_data_fast():
    """Schnelle Bitcoin-Datensammlung"""
    print("📊 Lade Bitcoin-Daten (ULTRA-SPEED)...")
    
    try:
        btc = yf.Ticker("BTC-USD")
        df = btc.history(period="2mo", interval="1h")  # Reduziert von 3mo auf 2mo
        
        if len(df) > 100:
            df.columns = [col.lower() for col in df.columns]
            print(f"✅ Echte Bitcoin-Daten: {len(df)} Stunden")
            print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
            return df, True
        else:
            raise Exception("Zu wenig Daten")
            
    except Exception as e:
        print(f"⚠️ API-Fehler, generiere schnelle realistische Daten...")
        
        # Schnelle realistische Datengeneration
        end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        start_time = end_time - timedelta(days=60)  # Reduziert von 90 auf 60 Tage
        dates = pd.date_range(start=start_time, end=end_time, freq='H')
        
        n_points = len(dates)
        np.random.seed(42)
        
        # Schnelle Bitcoin-Preisgeneration
        base_price = 67000
        
        # Vereinfachte Volatilitäts-Clustering
        volatility_regime = np.random.choice([0.8, 1.5, 3.0], n_points//24, p=[0.5, 0.4, 0.1])
        volatility_regime = np.repeat(volatility_regime, 24)[:n_points]
        
        # Schnelle Trend-Generation
        trend_changes = np.random.choice([-1, 0, 1], n_points//48, p=[0.3, 0.4, 0.3])
        trend = np.repeat(trend_changes, 48)[:n_points] * np.random.uniform(500, 2000, n_points)
        trend = np.cumsum(trend)
        
        # Schnelle Volatilität
        daily_vol = np.random.normal(0, 1500, n_points) * volatility_regime
        
        # Vereinfachte Zyklen
        weekly_cycle = 800 * np.sin(2 * np.pi * np.arange(n_points) / (24 * 7))
        
        # Seltene News Events
        news_events = np.random.choice([0, 1], n_points, p=[0.995, 0.005])
        news_impact = news_events * np.random.normal(0, 8000, n_points)
        
        prices = base_price + trend + daily_vol + weekly_cycle + news_impact
        prices = np.maximum(prices, 25000)
        
        # Schnelle OHLCV
        high_mult = np.random.uniform(1.002, 1.05, n_points)
        low_mult = np.random.uniform(0.95, 0.998, n_points)
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * high_mult,
            'low': prices * low_mult,
            'open': prices * np.random.uniform(0.99, 1.01, n_points),
            'volume': np.random.lognormal(15, 0.3, n_points)
        }, index=dates)
        
        print(f"✅ Schnelle realistische Daten: {len(df)} Stunden")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
        return df, False

def create_ultra_speed_features(df):
    """ULTRA-SPEED Features - nur die wichtigsten für maximale Geschwindigkeit"""
    print("🔧 Erstelle ULTRA-SPEED Features...")
    
    df = df.copy()
    
    # === CORE VOLATILITY FEATURES (reduziert) ===
    print("   📊 Core Volatility...")
    for window in [12, 24, 48]:  # Reduziert von 5 auf 3 Fenster
        df[f'volatility_{window}'] = df['close'].rolling(window=window).std()
        df[f'vol_ratio_{window}'] = df[f'volatility_{window}'] / df['close']
    
    # Schnelle GARCH-ähnliche Volatilität
    returns = df['close'].pct_change()
    df['returns'] = returns
    df['returns_squared'] = returns ** 2
    
    for span in [12, 24]:  # Reduziert
        df[f'ewm_vol_{span}'] = returns.ewm(span=span).std()
    
    # === CORE MOMENTUM FEATURES (reduziert) ===
    print("   ⚡ Core Momentum...")
    
    # Schnelle RSI (nur wichtigste Perioden)
    for period in [14, 24]:  # Reduziert von 3 auf 2
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=period).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
        rs = gain / loss
        df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
    
    # Schnelle MACD (nur wichtigste)
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    macd = ema_12 - ema_26
    signal = macd.ewm(span=9).mean()
    
    df['macd'] = macd
    df['macd_signal'] = signal
    df['macd_histogram'] = macd - signal
    
    # === CORE TREND FEATURES (reduziert) ===
    print("   📈 Core Trend...")
    for window in [12, 24, 48]:  # Reduziert
        df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
        df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
        df[f'trend_strength_{window}'] = (df['close'] - df[f'sma_{window}']) / df[f'sma_{window}']
    
    # === CORE PRICE ACTION (reduziert) ===
    print("   💰 Core Price Action...")
    
    # Wichtigste Returns
    for period in [1, 6, 24]:  # Reduziert
        df[f'returns_{period}'] = df['close'].pct_change(periods=period)
        df[f'log_returns_{period}'] = np.log(df['close'] / df['close'].shift(period))
    
    # Schnelle High-Low Features
    if 'high' in df.columns and 'low' in df.columns:
        df['hl_ratio'] = df['high'] / df['low']
        df['price_range'] = df['high'] - df['low']
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        # Schnelle ATR (nur wichtigste)
        df['tr'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                np.abs(df['high'] - df['close'].shift()),
                np.abs(df['low'] - df['close'].shift())
            )
        )
        df['atr_14'] = df['tr'].rolling(window=14).mean()
    
    # === SCHNELLE BOLLINGER BANDS ===
    print("   📊 Schnelle Bollinger Bands...")
    bb_window = 20
    bb_middle = df['close'].rolling(window=bb_window).mean()
    bb_std = df['close'].rolling(window=bb_window).std()
    
    df['bb_upper'] = bb_middle + 2 * bb_std
    df['bb_lower'] = bb_middle - 2 * bb_std
    df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    
    # === SCHNELLE VOLUME FEATURES ===
    print("   📦 Schnelle Volume...")
    if 'volume' in df.columns:
        df['volume_sma_24'] = df['volume'].rolling(window=24).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma_24']
        df['vpt'] = (df['volume'] * df['close'].pct_change()).cumsum()
    
    # === WICHTIGSTE LAG FEATURES ===
    print("   🔄 Wichtigste Lags...")
    for lag in [1, 6, 24]:  # Reduziert
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
        df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
    
    # === SCHNELLE TIME FEATURES ===
    print("   🕐 Schnelle Time Features...")
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    
    # Wichtigste cyclical encoding
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    
    # === SCHNELLE MARKET REGIME ===
    print("   🏛️ Schnelle Market Regime...")
    df['vol_regime'] = df['volatility_24'].rolling(window=48).rank(pct=True)
    
    short_ma = df['sma_12']
    long_ma = df['sma_24']
    df['trend_regime'] = np.where(short_ma > long_ma * 1.01, 1,
                                 np.where(short_ma < long_ma * 0.99, -1, 0))
    
    print(f"✅ ULTRA-SPEED Features erstellt: {df.shape[1]} Spalten")
    return df.dropna()

def prepare_ultra_speed_data(df, sequence_length=48):  # Reduziert von 60 auf 48
    """ULTRA-SPEED Datenvorbereitung"""
    print(f"🔄 Bereite ULTRA-SPEED Daten vor...")
    
    feature_cols = [col for col in df.columns if col != 'close']
    features = df[feature_cols].values
    target = df['close'].values
    
    # Schnelle Skalierung
    feature_scaler = RobustScaler()
    target_scaler = MinMaxScaler()
    
    features_scaled = feature_scaler.fit_transform(features)
    target_scaled = target_scaler.fit_transform(target.reshape(-1, 1)).flatten()
    
    # Schnelle Sequenzen
    X, y = [], []
    for i in range(sequence_length, len(features_scaled)):
        X.append(features_scaled[i-sequence_length:i])
        y.append(target_scaled[i])
    
    X, y = np.array(X), np.array(y)
    
    # Schneller Train/Test Split
    train_size = int(len(X) * 0.8)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]
    
    last_sequence = features_scaled[-sequence_length:]
    
    print(f"✅ ULTRA-SPEED Daten vorbereitet:")
    print(f"   Train: {X_train.shape}")
    print(f"   Test: {X_test.shape}")
    print(f"   Features: {X_train.shape[2]}")
    
    return (X_train, y_train), (X_test, y_test), last_sequence, (feature_scaler, target_scaler)

def train_ultra_speed_models(train_data, test_data):
    """ULTRA-SPEED Modelle - optimiert für maximale Geschwindigkeit"""
    print(f"\n🚀 Trainiere ULTRA-SPEED Modelle...")

    X_train, y_train = train_data
    X_test, y_test = test_data

    X_train_flat = X_train.reshape(X_train.shape[0], -1)
    X_test_flat = X_test.reshape(X_test.shape[0], -1)

    # ULTRA-SPEED Modelle (drastisch reduzierte Parameter für Geschwindigkeit)
    models = {
        'ExtraTrees_ULTRA': ExtraTreesRegressor(
            n_estimators=80,   # Drastisch reduziert von 150 auf 80
            max_depth=15,      # Reduziert von 25 auf 15
            min_samples_split=3,
            min_samples_leaf=2,
            max_features=0.7,
            n_jobs=N_JOBS,
            random_state=42,
            bootstrap=True
        ),
        'RandomForest_ULTRA': RandomForestRegressor(
            n_estimators=60,   # Drastisch reduziert von 120 auf 60
            max_depth=12,      # Reduziert von 20 auf 12
            min_samples_split=4,
            min_samples_leaf=3,
            max_features=0.6,
            n_jobs=N_JOBS,
            random_state=42,
            bootstrap=True
        ),
        'Ridge_ULTRA': Ridge(
            alpha=1.0,
            solver='auto'
        )
    }

    results = {}

    for model_name, model in models.items():
        print(f"\n🤖 Trainiere {model_name}...")

        start_time = time.time()
        model.fit(X_train_flat, y_train)
        training_time = time.time() - start_time

        # Schnelle Vorhersagen
        y_pred = model.predict(X_test_flat)

        # Schnelle Metriken
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        results[model_name] = {
            'model': model,
            'training_time': training_time,
            'mse': mse,
            'rmse': np.sqrt(mse),
            'r2': r2,
            'y_pred': y_pred,
            'y_test': y_test
        }

        print(f"✅ {model_name}: R²={r2:.4f}, RMSE={np.sqrt(mse):.4f}, Zeit={training_time:.1f}s")

    return results

def run_ultra_speed_monte_carlo_batch(args):
    """ULTRA-SPEED Monte Carlo Batch - für Threading optimiert"""
    model, last_sequence, target_hour, batch_size, current_price_scaled, historical_volatility = args

    predictions = []

    for sim in range(batch_size):
        # ULTRA-SPEED: Höhere Volatilität aber weniger Komplexität
        base_noise = historical_volatility * 0.3
        time_decay = np.sqrt(target_hour / 24)
        noise_level = base_noise * time_decay

        # Schnelle Volatilitäts-Regime
        vol_regime = np.random.choice([1.0, 2.0, 4.0], p=[0.5, 0.4, 0.1])
        noise_level *= vol_regime

        # Schnelle Autokorrelation
        noise = np.random.normal(0, noise_level, last_sequence.shape)
        for i in range(1, min(len(noise), 10)):  # Begrenzt auf 10 für Geschwindigkeit
            noise[i] += 0.4 * noise[i-1]

        noisy_sequence = last_sequence + noise
        current_sequence = noisy_sequence.copy()

        # ULTRA-SPEED: Größere Schritte für längere Zeiträume
        if target_hour <= 6:
            step_size = 1
        elif target_hour <= 24:
            step_size = 2
        else:
            step_size = 4

        for step in range(0, target_hour, step_size):
            pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]

            if step > 0:
                prev_price = current_sequence[-1, 0]

                # ULTRA-SPEED: Weniger restriktive Constraints
                max_change = 0.15 * step_size  # Angepasst an Schrittgröße

                # Schnelle Marktdynamik
                momentum = 0
                if len(current_sequence) >= 3:
                    recent_change = current_sequence[-1, 0] - current_sequence[-3, 0]
                    momentum = recent_change * 0.3

                # Schnelle Volatilitäts-Anpassung
                vol_adjustment = np.random.normal(0, noise_level * 0.2)

                # Schnelle Marktschocks (seltener)
                market_shock = 0
                if np.random.random() < 0.05:
                    market_shock = np.random.normal(0, 0.1)

                # Kombiniere Effekte
                pred_scaled = pred_scaled + momentum + vol_adjustment + market_shock

                # Schnelle Constraints
                pred_scaled = np.clip(pred_scaled,
                                    prev_price * (1 - max_change),
                                    prev_price * (1 + max_change))

            # Schnelle Sequence-Update
            new_row = current_sequence[-1].copy()
            new_row[0] = pred_scaled

            # Minimale Feature-Updates für Geschwindigkeit
            if len(new_row) > 1:
                new_row[1] = np.std(current_sequence[-6:, 0]) if len(current_sequence) >= 6 else noise_level

            current_sequence = np.vstack([current_sequence[1:], new_row])

        # Finale Vorhersage
        final_pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]

        # Schnelle finale Anpassungen
        if target_hour >= 24:
            final_pred_scaled += np.random.normal(0, 0.02)

        if np.random.random() < 0.06:  # News Events
            final_pred_scaled += np.random.normal(0, 0.08)

        predictions.append(final_pred_scaled)

    return predictions

def predict_ultra_speed_48h(best_models, last_sequence, target_scaler, current_time, current_price, historical_data):
    """ULTRA-SPEED 48h Vorhersage mit Threading"""
    print(f"🔮 Erstelle ULTRA-SPEED 48h Vorhersage...")
    print(f"   Monte Carlo Simulationen: {MONTE_CARLO_SIMS}")
    print(f"   🚀 ULTRA-SPEED: Threading optimiert")

    # Reduzierte Zeitpunkte für Geschwindigkeit
    key_hours = [1, 4, 8, 12, 18, 24, 36, 48]  # Reduziert von 14 auf 8
    predictions = {}

    # Historische Volatilität
    recent_returns = historical_data['close'].pct_change().dropna()
    historical_volatility = recent_returns.rolling(window=168).std().iloc[-1]

    # Aktueller Preis in skalierter Form
    current_price_scaled = target_scaler.transform([[current_price]])[0, 0]

    for hour in key_hours:
        print(f"📈 Berechne +{hour}h mit {MONTE_CARLO_SIMS} ULTRA-SPEED Simulationen...")

        all_model_predictions = []

        # Für jedes Modell
        for model_name, model_data in best_models.items():
            model = model_data['model']

            # ULTRA-SPEED: Threading für parallele Ausführung
            batch_size = MONTE_CARLO_SIMS // MAX_THREADS

            # Argumente für Threading
            args_list = []
            for thread in range(MAX_THREADS):
                thread_batch_size = batch_size if thread < MAX_THREADS - 1 else MONTE_CARLO_SIMS - (thread * batch_size)
                args_list.append((model, last_sequence, hour, thread_batch_size,
                                current_price_scaled, historical_volatility))

            # Threading Ausführung
            with ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
                batch_results = list(executor.map(run_ultra_speed_monte_carlo_batch, args_list))

            # Ergebnisse sammeln
            model_predictions = []
            for batch_result in batch_results:
                model_predictions.extend(batch_result)

            all_model_predictions.extend(model_predictions)

        # Zurück transformieren
        all_predictions = np.array(all_model_predictions)
        all_predictions_orig = target_scaler.inverse_transform(all_predictions.reshape(-1, 1)).flatten()

        # Schnelle Wahrscheinlichkeitsberechnung
        predictions[hour] = {
            'datetime': current_time + timedelta(hours=hour),
            'mean': np.mean(all_predictions_orig),
            'median': np.median(all_predictions_orig),
            'std': np.std(all_predictions_orig),
            'min': np.min(all_predictions_orig),
            'max': np.max(all_predictions_orig),
            'q05': np.percentile(all_predictions_orig, 5),
            'q25': np.percentile(all_predictions_orig, 25),
            'q75': np.percentile(all_predictions_orig, 75),
            'q95': np.percentile(all_predictions_orig, 95),

            # Wichtigste Wahrscheinlichkeiten
            'prob_above_current': np.mean(all_predictions_orig > current_price) * 100,
            'prob_above_2pct': np.mean(all_predictions_orig > current_price * 1.02) * 100,
            'prob_above_5pct': np.mean(all_predictions_orig > current_price * 1.05) * 100,
            'prob_above_10pct': np.mean(all_predictions_orig > current_price * 1.10) * 100,
            'prob_below_2pct': np.mean(all_predictions_orig < current_price * 0.98) * 100,
            'prob_below_5pct': np.mean(all_predictions_orig < current_price * 0.95) * 100,
            'prob_below_10pct': np.mean(all_predictions_orig < current_price * 0.90) * 100,

            # Änderungen
            'change_pct': ((np.mean(all_predictions_orig) / current_price) - 1) * 100,
            'change_abs': np.mean(all_predictions_orig) - current_price,

            # Risiko-Metriken
            'var_95': np.percentile(all_predictions_orig, 5) - current_price,
            'upside_95': np.percentile(all_predictions_orig, 95) - current_price,

            # Volatilität der Vorhersagen
            'prediction_volatility': np.std(all_predictions_orig) / current_price * 100,

            'all_predictions': all_predictions_orig
        }

    return predictions

def create_ultra_speed_visualization(df, results, predictions, current_time, current_price, is_real_data):
    """ULTRA-SPEED Visualisierung mit optimierter Lesbarkeit"""
    print("📊 Erstelle ULTRA-SPEED Visualisierung mit optimierter Lesbarkeit...")

    fig = plt.figure(figsize=(28, 18))  # Kompakter für bessere Lesbarkeit
    fig.patch.set_facecolor('#0a0a0a')

    title = "⚡ ULTRA-SPEED OPTIMIZED 48H BITCOIN PREDICTION"
    subtitle = f"📅 Ab: {current_time.strftime('%Y-%m-%d %H:%M:%S')} | 💰 Aktuell: ${current_price:,.2f} | ⚡ ULTRA-SPEED + Optimierte Lesbarkeit"
    fig.suptitle(f'{title}\n{subtitle}',
                 fontsize=20, color='white', fontweight='bold', y=0.95)

    # === 1. HAUPTCHART: Kompakte historische Daten + Zukunft ===
    ax1 = plt.subplot2grid((5, 8), (0, 0), colspan=5, rowspan=3)

    # OPTIMIERTE LESBARKEIT: Nur letzte 5 Tage statt 10 für bessere Übersicht
    recent_data = df.tail(120)  # 5 Tage = 120 Stunden
    ax1.plot(recent_data.index, recent_data['close'],
             color='#00D4FF', linewidth=2.5, label='Historisch (5 Tage)', alpha=0.9)

    # Jetzt markieren
    ax1.axvline(x=current_time, color='#FF6B6B', linestyle='-', linewidth=3,
                label='JETZT', alpha=0.9)

    # ULTRA-SPEED Zukunftsprognose
    future_times = [pred['datetime'] for pred in predictions.values()]
    future_means = [pred['mean'] for pred in predictions.values()]
    future_medians = [pred['median'] for pred in predictions.values()]
    future_q05 = [pred['q05'] for pred in predictions.values()]
    future_q95 = [pred['q95'] for pred in predictions.values()]
    future_q25 = [pred['q25'] for pred in predictions.values()]
    future_q75 = [pred['q75'] for pred in predictions.values()]

    # Klare Prognose-Linien
    ax1.plot(future_times, future_means, color='#FFD700', linewidth=4,
             label='ULTRA-SPEED Erwartung', alpha=0.9, marker='o', markersize=6)
    ax1.plot(future_times, future_medians, color='#FFA500', linewidth=2.5,
             label='ULTRA-SPEED Median', alpha=0.8, marker='s', markersize=4, linestyle='--')

    # Konfidenzintervalle
    ax1.fill_between(future_times, future_q05, future_q95,
                     color='#FFD700', alpha=0.15, label='90% Konfidenz')
    ax1.fill_between(future_times, future_q25, future_q75,
                     color='#FFD700', alpha=0.25, label='50% Konfidenz')

    # VERBESSERTE LESBARKEIT: Weniger aber klarere Zeitpunkt-Markierungen
    important_hours = [4, 12, 24, 48]  # Reduziert für bessere Lesbarkeit
    for hour in important_hours:
        if hour in predictions:
            pred = predictions[hour]
            ax1.axvline(x=pred['datetime'], color='#FF9500', linestyle=':',
                       alpha=0.6, linewidth=1.5)

            # Klarere, größere Labels
            label_text = f"+{hour}h\n${pred['mean']:,.0f}"
            ax1.text(pred['datetime'], ax1.get_ylim()[1]*0.92, label_text,
                    rotation=0, color='#FF9500', fontsize=11, ha='center',
                    fontweight='bold', bbox=dict(boxstyle='round,pad=0.4',
                    facecolor='#FF9500', alpha=0.3))

    ax1.set_title('Bitcoin: Kompakte Historie → ULTRA-SPEED ZUKUNFT',
                  fontsize=16, color='white', fontweight='bold')
    ax1.set_ylabel('Preis (USD)', color='white', fontsize=12)
    ax1.legend(loc='upper left', fontsize=10, framealpha=0.9)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(colors='white', labelsize=10)

    # VERBESSERTE X-ACHSEN-FORMATIERUNG
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d\n%H:%M'))
    ax1.xaxis.set_major_locator(mdates.HourLocator(interval=12))  # Weniger Ticks
    ax1.xaxis.set_minor_locator(mdates.HourLocator(interval=6))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=0, ha='center')

    # === 2. KOMPAKTE WAHRSCHEINLICHKEITS-TABELLE ===
    ax2 = plt.subplot2grid((5, 8), (0, 5), colspan=3, rowspan=3)
    ax2.axis('off')

    table_data = []
    headers = ['Zeit', 'Datum/Zeit', 'Erwartung', 'Änderung', 'Wahrsch. ↑', 'Wahrsch. +5%', 'Volatilität']

    # Reduzierte Anzeige für bessere Lesbarkeit
    key_display_hours = [4, 12, 24, 48]  # Nur die wichtigsten
    for hour in key_display_hours:
        if hour in predictions:
            pred = predictions[hour]

            table_data.append([
                f"+{hour}h",
                pred['datetime'].strftime('%m-%d %H:%M'),
                f"${pred['mean']:,.0f}",
                f"{pred['change_pct']:+.1f}%",
                f"{pred['prob_above_current']:.0f}%",
                f"{pred['prob_above_5pct']:.0f}%",
                f"{pred['prediction_volatility']:.1f}%"
            ])

    table = ax2.table(cellText=table_data, colLabels=headers,
                     cellLoc='center', loc='center',
                     colWidths=[0.1, 0.18, 0.15, 0.12, 0.12, 0.12, 0.12])

    table.auto_set_font_size(False)
    table.set_fontsize(10)  # Größere Schrift für bessere Lesbarkeit
    table.scale(1, 3.0)     # Höhere Tabelle

    # VERBESSERTE FARBKODIERUNG
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#444444')  # Hellerer Header
        table[(0, i)].set_text_props(weight='bold', color='white', fontsize=10)

    for i in range(1, len(table_data) + 1):
        for j in range(len(headers)):
            table[(i, j)].set_facecolor('#2a2a2a')  # Hellerer Hintergrund
            table[(i, j)].set_text_props(color='white', fontsize=10)

            # Klarere Farbkodierung
            if j == 3:  # Änderung %
                change_val = float(table_data[i-1][j].replace('%', '').replace('+', ''))
                if change_val > 5:
                    table[(i, j)].set_facecolor('#2d7d2d')  # Heller grün
                elif change_val > 2:
                    table[(i, j)].set_facecolor('#4d6d4d')
                elif change_val > 0:
                    table[(i, j)].set_facecolor('#5d5d3d')
                elif change_val > -2:
                    table[(i, j)].set_facecolor('#6d5d3d')
                elif change_val > -5:
                    table[(i, j)].set_facecolor('#7d4d3d')
                else:
                    table[(i, j)].set_facecolor('#7d2d2d')  # Heller rot
            elif j in [4, 5]:  # Wahrscheinlichkeiten
                prob_val = float(table_data[i-1][j].replace('%', ''))
                if prob_val > 70:
                    table[(i, j)].set_facecolor('#2d7d2d')
                elif prob_val > 60:
                    table[(i, j)].set_facecolor('#4d6d4d')
                elif prob_val > 50:
                    table[(i, j)].set_facecolor('#5d5d3d')
                elif prob_val > 40:
                    table[(i, j)].set_facecolor('#6d6d3d')
                else:
                    table[(i, j)].set_facecolor('#7d5d3d')

    ax2.set_title('ULTRA-SPEED Wahrscheinlichkeits-Analyse', color='white', fontweight='bold', fontsize=14)

    return fig

def main():
    """ULTRA-SPEED Hauptfunktion"""
    print("\n⚡" * 24)
    print("ULTRA-SPEED OPTIMIZED 48H BITCOIN PREDICTION")
    print("⚡" * 24)

    start_time = time.time()

    try:
        # 1. ULTRA-SPEED Datensammlung
        print("\n" + "="*50)
        print("PHASE 1: ULTRA-SPEED DATENSAMMLUNG")
        print("="*50)
        df, is_real_data = get_bitcoin_data_fast()
        current_time = df.index[-1]
        current_price = df['close'].iloc[-1]

        # 2. ULTRA-SPEED Features
        print("\n" + "="*50)
        print("PHASE 2: ULTRA-SPEED FEATURE ENGINEERING")
        print("="*50)
        df_features = create_ultra_speed_features(df)

        # 3. ULTRA-SPEED Datenvorbereitung
        print("\n" + "="*50)
        print("PHASE 3: ULTRA-SPEED DATENAUFBEREITUNG")
        print("="*50)
        train_data, test_data, last_sequence, scalers = prepare_ultra_speed_data(df_features)
        feature_scaler, target_scaler = scalers

        # 4. ULTRA-SPEED Modelle
        print("\n" + "="*50)
        print("PHASE 4: ULTRA-SPEED MODEL TRAINING")
        print("="*50)
        results = train_ultra_speed_models(train_data, test_data)

        # 5. Beste Modelle
        sorted_results = sorted(results.items(), key=lambda x: x[1]['r2'], reverse=True)
        best_models = dict(sorted_results[:2])  # Top 2 Modelle für Geschwindigkeit

        print(f"\n🏆 Top Modelle für ULTRA-SPEED Vorhersage:")
        for name, result in best_models.items():
            print(f"   {name}: R²={result['r2']:.4f}, Zeit={result['training_time']:.1f}s")

        # 6. ULTRA-SPEED 48h Vorhersage
        print("\n" + "="*50)
        print("PHASE 5: ULTRA-SPEED 48H VORHERSAGE")
        print("="*50)

        predictions = predict_ultra_speed_48h(
            best_models, last_sequence, target_scaler, current_time, current_price, df
        )

        # 7. ULTRA-SPEED Visualisierung
        print("\n" + "="*50)
        print("PHASE 6: ULTRA-SPEED VISUALISIERUNG")
        print("="*50)

        fig = create_ultra_speed_visualization(
            df_features, results, predictions, current_time, current_price, is_real_data
        )

        # 8. Zusammenfassung
        total_time = time.time() - start_time
        print_ultra_speed_summary(results, predictions, current_time, current_price, total_time, is_real_data)

        # Speichern
        os.makedirs('ultimate_plots', exist_ok=True)
        filename = 'ultra_speed_48h_prediction.png'
        plt.savefig(f'ultimate_plots/{filename}',
                    facecolor='#0a0a0a', dpi=300, bbox_inches='tight')

        print(f"✅ ULTRA-SPEED Visualisierung: ultimate_plots/{filename}")
        plt.show()

        print(f"\n🎉 ULTRA-SPEED 48H ANALYSE ABGESCHLOSSEN in {total_time:.1f}s! 🎉")

        return {
            'results': results,
            'predictions': predictions,
            'current_time': current_time,
            'current_price': current_price,
            'total_time': total_time,
            'optimization': 'ULTRA_SPEED_optimized_readability'
        }

    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()
        return None

def print_ultra_speed_summary(results, predictions, current_time, current_price, total_time, is_real_data):
    """ULTRA-SPEED Zusammenfassung mit optimierter Lesbarkeit"""
    print("\n" + "="*70)
    print("⚡ ULTRA-SPEED 48H BITCOIN PREDICTION RESULTS ⚡")
    print("="*70)

    data_type = "ECHTE LIVE-DATEN" if is_real_data else "ULTRA-SPEED REALISTISCHE DATEN"
    print(f"\n📊 DATENQUELLE: {data_type}")
    print(f"📅 PROGNOSE AB: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💰 AKTUELLER PREIS: ${current_price:,.2f}")
    print(f"⚡ ULTRA-SPEED OPTIMIERUNGEN: Drastisch beschleunigt + Optimierte Lesbarkeit")

    # Modell-Performance
    best_model = max(results.keys(), key=lambda x: results[x]['r2'])
    print(f"\n🏆 BESTES MODELL: {best_model}")
    print(f"   R² Score: {results[best_model]['r2']:.4f} ({results[best_model]['r2']*100:.1f}%)")
    print(f"   RMSE: {results[best_model]['rmse']:.4f}")
    print(f"   Training Zeit: {results[best_model]['training_time']:.1f}s")

    # ULTRA-SPEED 48h Vorhersagen (kompakt für bessere Lesbarkeit)
    print(f"\n🔮 ULTRA-SPEED 48H VORHERSAGEN (KOMPAKT):")
    print(f"{'Zeit':<6} | {'Datum/Zeit':<16} | {'Erwartung':<12} | {'Änderung':<10} | {'Wahrsch. ↑':<12} | {'Volatilität':<10}")
    print("-" * 80)

    key_hours = [4, 12, 24, 48]  # Reduziert für bessere Lesbarkeit
    for hour in key_hours:
        if hour in predictions:
            pred = predictions[hour]
            print(f"{hour:>4}h | {pred['datetime'].strftime('%m-%d %H:%M'):<16} | "
                  f"${pred['mean']:>10,.0f} | "
                  f"{pred['change_pct']:>+7.1f}% | {pred['prob_above_current']:>10.0f}% | "
                  f"{pred['prediction_volatility']:>8.1f}%")

    # 48h Spezial-Analyse
    if 48 in predictions:
        pred_48h = predictions[48]

        print(f"\n🎯 48H ULTRA-SPEED ANALYSE:")
        print(f"   Erwartungswert: ${pred_48h['mean']:,.0f}")
        print(f"   Änderung: {pred_48h['change_pct']:+.1f}%")
        print(f"   Vorhersage-Volatilität: {pred_48h['prediction_volatility']:.1f}%")
        print(f"   Konfidenz (90%): ${pred_48h['q05']:,.0f} - ${pred_48h['q95']:,.0f}")

        # Trading-Empfehlung
        prob_up = pred_48h['prob_above_current']
        volatility = pred_48h['prediction_volatility']
        change_48h = pred_48h['change_pct']

        if prob_up > 70 and change_48h > 3:
            recommendation = "STARKER KAUF 🚀🚀"
            confidence = "HOCH"
        elif prob_up > 60 and change_48h > 1:
            recommendation = "KAUF 📈"
            confidence = "MITTEL"
        elif prob_up > 40:
            recommendation = "HALTEN ⚖️"
            confidence = "NEUTRAL"
        else:
            recommendation = "VERKAUF 📉"
            confidence = "MITTEL"

        risk_level = "HOCH" if volatility > 8 else "MITTEL" if volatility > 4 else "NIEDRIG"

        print(f"\n💡 ULTRA-SPEED TRADING-EMPFEHLUNG: {recommendation}")
        print(f"   Konfidenz: {confidence} ({prob_up:.1f}% Aufwärts-Wahrscheinlichkeit)")
        print(f"   Risiko-Level: {risk_level} (Volatilität: {volatility:.1f}%)")

        print(f"\n📈 WICHTIGSTE WAHRSCHEINLICHKEITEN:")
        print(f"   Preis steigt: {pred_48h['prob_above_current']:.1f}%")
        print(f"   Gewinn >2%: {pred_48h['prob_above_2pct']:.1f}%")
        print(f"   Gewinn >5%: {pred_48h['prob_above_5pct']:.1f}%")
        print(f"   Gewinn >10%: {pred_48h['prob_above_10pct']:.1f}%")
        print(f"   Verlust >5%: {pred_48h['prob_below_5pct']:.1f}%")
        print(f"   Verlust >10%: {pred_48h['prob_below_10pct']:.1f}%")

        print(f"\n⚡ ULTRA-SPEED VERBESSERUNGEN:")
        print(f"   ✅ Drastisch reduzierte Modell-Parameter")
        print(f"   ✅ Threading für Monte Carlo Simulationen")
        print(f"   ✅ Reduzierte Zeitpunkte (8 statt 14)")
        print(f"   ✅ Kompakte historische Daten (5 statt 10 Tage)")
        print(f"   ✅ Optimierte Feature-Anzahl")
        print(f"   ✅ Verbesserte Visualisierungs-Lesbarkeit")
        print(f"   ✅ Größere Schrittweiten für längere Zeiträume")

    print(f"\n⚡ PERFORMANCE: {total_time:.1f}s | Monte Carlo: {MONTE_CARLO_SIMS} | Threading: {MAX_THREADS}")
    print("="*70)

if __name__ == "__main__":
    main()
