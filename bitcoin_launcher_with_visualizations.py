#!/usr/bin/env python3
"""
BITCOIN LAUNCHER WITH VISUALIZATIONS - BENUTZERFREUNDLICH
========================================================
OPTIMIERTER LAUNCHER MIT INTEGRIERTEN VISUALISIERUNGEN
- Erweiterte GUI mit Live-Charts
- Integrierte Visualisierungen
- Real-time Dashboard
- Benutzerfreundliche Bedienung
- Professionelle Darstellung

LAUNCHER WITH VISUALIZATIONS - PROFESSIONELL!
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import threading
import time
import os
import sys
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import pandas as pd

class BitcoinLauncherWithVisualizations:
    """
    BITCOIN LAUNCHER WITH VISUALIZATIONS
    ===================================
    Optimierter Launcher mit integrierten Visualisierungen
    und benutzerfreundlicher Bedienung.
    """
    
    def __init__(self):
        # GUI SETUP
        self.root = tk.Tk()
        self.root.title("Bitcoin Trading Launcher - Mit Visualisierungen")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#1a1a1a')
        
        # STYLE KONFIGURATION
        self.setup_styles()
        
        # DIE 3 BESTEN MODELLE
        self.models = {
            'favorit': {
                'name': 'FAVORIT - Das Bewaehrte System',
                'file': 'ultimate_complete_bitcoin_trading_FAVORITE_NO_EMOJI.py',
                'description': 'Das bewaehrte System mit kontinuierlichem Lernen',
                'color': '#00ff88',
                'status': 'Bereit',
                'process': None,
                'recommended': True
            },
            'optimized': {
                'name': 'OPTIMIERT - Das Schnelle System',
                'file': 'btc_ultimate_optimized_complete_NO_EMOJI.py',
                'description': 'Das optimierte System fuer schnelle Analysen',
                'color': '#ff6600',
                'status': 'Bereit',
                'process': None,
                'recommended': False
            },
            'simple': {
                'name': 'SIMPLE - Das Zuverlaessige System',
                'file': 'bitcoin_trading_simple_fixed_NO_EMOJI.py',
                'description': 'Das zuverlaessige System ohne Abhaengigkeiten',
                'color': '#3366cc',
                'status': 'Bereit',
                'process': None,
                'recommended': False
            }
        }
        
        # LAUNCHER ZUSTAND
        self.running_processes = {}
        self.script_directory = os.getcwd()
        self.current_data = None
        
        # GUI KOMPONENTEN ERSTELLEN
        self.create_visualization_gui()
        
        # DEMO-DATEN GENERIEREN
        self.generate_demo_data()
        
        print("Bitcoin Trading Launcher mit Visualisierungen initialisiert")
        print("Erweiterte GUI mit Live-Charts und Dashboard")
    
    def setup_styles(self):
        """Setup GUI-Styles"""
        plt.style.use('dark_background')
        
        # TTK Styles
        style = ttk.Style()
        style.theme_use('clam')
        
        # Custom Styles
        style.configure('Title.TLabel', 
                       background='#1a1a1a', 
                       foreground='#00ff88',
                       font=('Arial', 16, 'bold'))
    
    def create_visualization_gui(self):
        """Erstelle erweiterte GUI mit Visualisierungen"""
        
        # HAUPTCONTAINER
        main_container = tk.Frame(self.root, bg='#1a1a1a')
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # TITEL
        title_frame = tk.Frame(main_container, bg='#1a1a1a')
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = tk.Label(
            title_frame,
            text="BITCOIN TRADING LAUNCHER - MIT VISUALISIERUNGEN",
            font=('Arial', 18, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="Professioneller Launcher * Live-Charts * Real-time Dashboard",
            font=('Arial', 11),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        subtitle_label.pack()
        
        # HAUPTBEREICH - 2 SPALTEN
        content_frame = tk.Frame(main_container, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # LINKE SPALTE - KONTROLLEN
        left_frame = tk.Frame(content_frame, bg='#1a1a1a', width=600)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)
        
        # RECHTE SPALTE - VISUALISIERUNGEN
        right_frame = tk.Frame(content_frame, bg='#1a1a1a')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # ERSTELLE LINKE SPALTE
        self.create_control_panel(left_frame)
        
        # ERSTELLE RECHTE SPALTE
        self.create_visualization_panel(right_frame)
    
    def create_control_panel(self, parent):
        """Erstelle Kontroll-Panel"""
        
        # MODELLE BEREICH
        models_frame = tk.LabelFrame(
            parent,
            text="Bitcoin Trading Modelle",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d',
            bd=2,
            relief=tk.RAISED
        )
        models_frame.pack(fill=tk.X, pady=(0, 10))
        
        # MODELL BUTTONS
        for i, (key, model) in enumerate(self.models.items()):
            self.create_model_button_with_viz(models_frame, key, model, i)
        
        # HAUPT-KONTROLLEN
        control_frame = tk.LabelFrame(
            parent,
            text="Haupt-Kontrollen",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d',
            bd=2,
            relief=tk.RAISED
        )
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # KONTROLL-BUTTONS
        button_frame = tk.Frame(control_frame, bg='#2d2d2d')
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # ALLE STARTEN
        start_all_btn = tk.Button(
            button_frame,
            text="ALLE STARTEN",
            command=self.start_all_models,
            font=('Arial', 11, 'bold'),
            bg='#00aa44',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        start_all_btn.pack(fill=tk.X, pady=(0, 5))
        
        # ALLE STOPPEN
        stop_all_btn = tk.Button(
            button_frame,
            text="ALLE STOPPEN",
            command=self.stop_all_models,
            font=('Arial', 11, 'bold'),
            bg='#cc3333',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        stop_all_btn.pack(fill=tk.X, pady=(0, 5))
        
        # CHARTS AKTUALISIEREN
        refresh_btn = tk.Button(
            button_frame,
            text="CHARTS AKTUALISIEREN",
            command=self.refresh_visualizations,
            font=('Arial', 11, 'bold'),
            bg='#ff6600',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        refresh_btn.pack(fill=tk.X)
        
        # STATUS LOG
        log_frame = tk.LabelFrame(
            parent,
            text="Live Status & Logs",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d',
            bd=2,
            relief=tk.RAISED
        )
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.status_log = scrolledtext.ScrolledText(
            log_frame,
            height=12,
            font=('Consolas', 8),
            bg='#1a1a1a',
            fg='#00ff88',
            insertbackground='#00ff88',
            wrap=tk.WORD
        )
        self.status_log.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_model_button_with_viz(self, parent, key, model, index):
        """Erstelle Modell-Button mit Visualisierung"""
        
        # MODELL CONTAINER
        model_frame = tk.Frame(parent, bg='#2d2d2d', relief=tk.RAISED, bd=1)
        model_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # MODELL INFO
        info_frame = tk.Frame(model_frame, bg='#2d2d2d')
        info_frame.pack(fill=tk.X, padx=10, pady=8)
        
        # NAME UND STATUS
        header_frame = tk.Frame(info_frame, bg='#2d2d2d')
        header_frame.pack(fill=tk.X)
        
        name_label = tk.Label(
            header_frame,
            text=model['name'],
            font=('Arial', 10, 'bold'),
            fg=model['color'],
            bg='#2d2d2d'
        )
        name_label.pack(side=tk.LEFT)
        
        # EMPFOHLEN BADGE
        if model.get('recommended'):
            rec_label = tk.Label(
                header_frame,
                text="*** EMPFOHLEN ***",
                font=('Arial', 7, 'bold'),
                fg='#ffd700',
                bg='#2d2d2d'
            )
            rec_label.pack(side=tk.RIGHT)
        
        # BESCHREIBUNG
        desc_label = tk.Label(
            info_frame,
            text=model['description'],
            font=('Arial', 8),
            fg='#cccccc',
            bg='#2d2d2d',
            justify=tk.LEFT
        )
        desc_label.pack(anchor=tk.W, pady=(3, 0))
        
        # STATUS
        status_label = tk.Label(
            info_frame,
            text=f"Status: {model['status']}",
            font=('Arial', 8),
            fg='#cccccc',
            bg='#2d2d2d'
        )
        status_label.pack(anchor=tk.W, pady=(3, 0))
        model['status_label'] = status_label
        
        # BUTTON
        start_button = tk.Button(
            info_frame,
            text=">> STARTEN",
            command=lambda k=key: self.start_model_with_viz(k),
            font=('Arial', 9, 'bold'),
            bg=model['color'],
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5,
            cursor='hand2'
        )
        start_button.pack(anchor=tk.W, pady=(5, 0))
        model['start_button'] = start_button
    
    def create_visualization_panel(self, parent):
        """Erstelle Visualisierungs-Panel"""
        
        # VISUALISIERUNG TITEL
        viz_title = tk.Label(
            parent,
            text="LIVE BITCOIN TRADING DASHBOARD",
            font=('Arial', 14, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        viz_title.pack(pady=(0, 10))
        
        # NOTEBOOK FUER TABS
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # TAB 1: PREIS-CHART
        self.create_price_chart_tab()
        
        # TAB 2: PERFORMANCE-DASHBOARD
        self.create_performance_tab()
        
        # TAB 3: LIVE-STATISTIKEN
        self.create_statistics_tab()
    
    def create_price_chart_tab(self):
        """Erstelle Preis-Chart Tab"""
        
        # FRAME FUER PREIS-CHART
        price_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(price_frame, text="Bitcoin Preis-Chart")
        
        # MATPLOTLIB FIGURE
        self.price_fig, self.price_ax = plt.subplots(figsize=(10, 6), facecolor='#1a1a1a')
        self.price_ax.set_facecolor('#1a1a1a')
        
        # CANVAS
        self.price_canvas = FigureCanvasTkAgg(self.price_fig, price_frame)
        self.price_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # INITIAL CHART
        self.update_price_chart()
    
    def create_performance_tab(self):
        """Erstelle Performance Tab"""
        
        # FRAME FUER PERFORMANCE
        perf_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(perf_frame, text="Model Performance")
        
        # MATPLOTLIB FIGURE
        self.perf_fig, self.perf_axes = plt.subplots(2, 2, figsize=(10, 6), facecolor='#1a1a1a')
        self.perf_fig.suptitle('Model Performance Dashboard', color='white', fontsize=14)
        
        for ax in self.perf_axes.flat:
            ax.set_facecolor('#1a1a1a')
        
        # CANVAS
        self.perf_canvas = FigureCanvasTkAgg(self.perf_fig, perf_frame)
        self.perf_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # INITIAL CHARTS
        self.update_performance_charts()
    
    def create_statistics_tab(self):
        """Erstelle Statistiken Tab"""
        
        # FRAME FUER STATISTIKEN
        stats_frame = tk.Frame(self.notebook, bg='#1a1a1a')
        self.notebook.add(stats_frame, text="Live Statistiken")
        
        # STATISTIKEN GRID
        stats_grid = tk.Frame(stats_frame, bg='#1a1a1a')
        stats_grid.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # STATISTIK KARTEN
        self.create_stat_cards(stats_grid)
    
    def create_stat_cards(self, parent):
        """Erstelle Statistik-Karten"""
        
        # GRID LAYOUT
        for i in range(3):
            parent.grid_columnconfigure(i, weight=1)
        for i in range(2):
            parent.grid_rowconfigure(i, weight=1)
        
        # KARTE 1: AKTUELLER PREIS
        price_card = self.create_stat_card(parent, "Aktueller Bitcoin-Preis", "$106,234.56", "#00ff88")
        price_card.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        
        # KARTE 2: 24H AENDERUNG
        change_card = self.create_stat_card(parent, "24h Aenderung", "*****%", "#00ff88")
        change_card.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        
        # KARTE 3: TRADING-SIGNAL
        signal_card = self.create_stat_card(parent, "Trading-Signal", "KAUFEN", "#ff6600")
        signal_card.grid(row=0, column=2, padx=10, pady=10, sticky="nsew")
        
        # KARTE 4: KONFIDENZ
        conf_card = self.create_stat_card(parent, "Konfidenz", "78.5%", "#3366cc")
        conf_card.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
        
        # KARTE 5: AKTIVE MODELLE
        models_card = self.create_stat_card(parent, "Aktive Modelle", "0 / 3", "#cccccc")
        models_card.grid(row=1, column=1, padx=10, pady=10, sticky="nsew")
        
        # KARTE 6: LAUFZEIT
        runtime_card = self.create_stat_card(parent, "Laufzeit", "00:00:00", "#cccccc")
        runtime_card.grid(row=1, column=2, padx=10, pady=10, sticky="nsew")
    
    def create_stat_card(self, parent, title, value, color):
        """Erstelle einzelne Statistik-Karte"""
        
        card = tk.Frame(parent, bg='#2d2d2d', relief=tk.RAISED, bd=2)
        
        title_label = tk.Label(
            card,
            text=title,
            font=('Arial', 10, 'bold'),
            fg='#cccccc',
            bg='#2d2d2d'
        )
        title_label.pack(pady=(15, 5))
        
        value_label = tk.Label(
            card,
            text=value,
            font=('Arial', 16, 'bold'),
            fg=color,
            bg='#2d2d2d'
        )
        value_label.pack(pady=(0, 15))
        
        return card

    def generate_demo_data(self):
        """Generiere Demo-Daten fuer Visualisierungen"""

        # Bitcoin-Preis Demo-Daten
        dates = pd.date_range(start='2025-01-01', periods=30, freq='D')
        base_price = 106000

        # Realistische Preisbewegung
        price_changes = np.random.normal(0, 0.02, 30)
        prices = [base_price]

        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            new_price = max(95000, min(120000, new_price))
            prices.append(new_price)

        self.current_data = {
            'dates': dates,
            'prices': prices,
            'volume': np.random.uniform(1e9, 3e9, 30),
            'rsi': np.random.uniform(30, 70, 30),
            'macd': np.random.normal(0, 100, 30)
        }

    def update_price_chart(self):
        """Aktualisiere Preis-Chart"""

        if self.current_data is None:
            return

        self.price_ax.clear()

        # Preis-Linie
        self.price_ax.plot(self.current_data['dates'], self.current_data['prices'],
                          color='#00ff88', linewidth=2, label='Bitcoin Preis')

        # Moving Average
        ma_20 = pd.Series(self.current_data['prices']).rolling(7).mean()
        self.price_ax.plot(self.current_data['dates'], ma_20,
                          color='#ff6600', linewidth=1, alpha=0.7, label='MA 7')

        # Styling
        self.price_ax.set_title('Bitcoin Preis-Entwicklung (30 Tage)', color='white', fontsize=12)
        self.price_ax.set_xlabel('Datum', color='white')
        self.price_ax.set_ylabel('Preis (USD)', color='white')
        self.price_ax.tick_params(colors='white')
        self.price_ax.legend()
        self.price_ax.grid(True, alpha=0.3)

        # Format Y-Achse
        self.price_ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

        # Tight layout
        self.price_fig.tight_layout()
        self.price_canvas.draw()

    def update_performance_charts(self):
        """Aktualisiere Performance-Charts"""

        if self.current_data is None:
            return

        # Chart 1: RSI
        ax1 = self.perf_axes[0, 0]
        ax1.clear()
        ax1.plot(self.current_data['dates'], self.current_data['rsi'], color='#3366cc', linewidth=2)
        ax1.axhline(y=70, color='red', linestyle='--', alpha=0.7)
        ax1.axhline(y=30, color='green', linestyle='--', alpha=0.7)
        ax1.set_title('RSI Indikator', color='white', fontsize=10)
        ax1.tick_params(colors='white', labelsize=8)
        ax1.grid(True, alpha=0.3)

        # Chart 2: Volume
        ax2 = self.perf_axes[0, 1]
        ax2.clear()
        ax2.bar(self.current_data['dates'], self.current_data['volume'],
               color='#ff6600', alpha=0.7, width=0.8)
        ax2.set_title('Handelsvolumen', color='white', fontsize=10)
        ax2.tick_params(colors='white', labelsize=8)
        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1e9:.1f}B'))

        # Chart 3: MACD
        ax3 = self.perf_axes[1, 0]
        ax3.clear()
        ax3.plot(self.current_data['dates'], self.current_data['macd'], color='#00ff88', linewidth=2)
        ax3.axhline(y=0, color='white', linestyle='-', alpha=0.5)
        ax3.set_title('MACD', color='white', fontsize=10)
        ax3.tick_params(colors='white', labelsize=8)
        ax3.grid(True, alpha=0.3)

        # Chart 4: Model Accuracy
        ax4 = self.perf_axes[1, 1]
        ax4.clear()
        models = ['FAVORIT', 'OPTIMIERT', 'SIMPLE']
        accuracies = [95.2, 87.8, 92.1]
        colors = ['#00ff88', '#ff6600', '#3366cc']
        bars = ax4.bar(models, accuracies, color=colors, alpha=0.8)
        ax4.set_title('Model Genauigkeit (%)', color='white', fontsize=10)
        ax4.set_ylim(0, 100)
        ax4.tick_params(colors='white', labelsize=8)

        # Werte auf Balken
        for bar, acc in zip(bars, accuracies):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{acc}%', ha='center', va='bottom', color='white', fontsize=8)

        # Tight layout
        self.perf_fig.tight_layout()
        self.perf_canvas.draw()

    def refresh_visualizations(self):
        """Aktualisiere alle Visualisierungen"""
        self.log_message("Aktualisiere Visualisierungen...")

        # Generiere neue Demo-Daten
        self.generate_demo_data()

        # Update Charts
        self.update_price_chart()
        self.update_performance_charts()

        self.log_message("Visualisierungen aktualisiert")

    def log_message(self, message):
        """Fuege Nachricht zum Status-Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.status_log.insert(tk.END, log_entry)
        self.status_log.see(tk.END)
        self.root.update()

        print(log_entry.strip())

    def start_model_with_viz(self, model_key):
        """Starte Modell mit Visualisierung"""
        if model_key not in self.models:
            self.log_message(f"FEHLER: Ungueltiges Modell: {model_key}")
            return

        model = self.models[model_key]

        # Pruefe ob bereits laeuft
        if model_key in self.running_processes:
            self.log_message(f"WARNUNG: {model['name']} laeuft bereits")
            return

        # Pruefe Datei
        if not os.path.exists(model['file']):
            self.log_message(f"FEHLER: Datei nicht gefunden: {model['file']}")
            return

        try:
            self.log_message(f"STARTE: {model['name']}")

            # Update GUI
            model['status'] = 'Laeuft'
            model['status_label'].config(text=f"Status: {model['status']}")

            # Starte Prozess
            process = subprocess.Popen(
                [sys.executable, model['file']],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.script_directory,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )

            self.running_processes[model_key] = process
            model['process'] = process

            # Starte Monitoring
            monitor_thread = threading.Thread(
                target=self.monitor_model_with_viz,
                args=(model_key, process),
                daemon=True
            )
            monitor_thread.start()

            self.log_message(f"ERFOLGREICH: {model['name']} gestartet (PID: {process.pid})")

            # Update Visualisierungen
            self.refresh_visualizations()

        except Exception as e:
            self.log_message(f"FEHLER beim Starten von {model['name']}: {e}")
            model['status'] = 'Fehler'
            model['status_label'].config(text=f"Status: {model['status']}")

    def monitor_model_with_viz(self, model_key, process):
        """Ueberwache Modell mit Visualisierung"""
        model = self.models[model_key]

        try:
            stdout, stderr = process.communicate()

            # Cleanup
            if model_key in self.running_processes:
                del self.running_processes[model_key]

            # Update GUI
            self.root.after(0, lambda: self.model_finished_with_viz(model_key, process.returncode, stdout))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"FEHLER: Monitor-Fehler: {e}"))

    def model_finished_with_viz(self, model_key, return_code, stdout):
        """Modell beendet mit Visualisierung"""
        model = self.models[model_key]

        # Update GUI
        model['process'] = None
        model['status'] = 'Beendet' if return_code == 0 else 'Fehler'
        model['status_label'].config(text=f"Status: {model['status']}")

        if return_code == 0:
            self.log_message(f"ERFOLGREICH: {model['name']} erfolgreich beendet")

            # Zeige wichtige Ergebnisse
            if stdout:
                lines = stdout.split('\n')
                for line in lines:
                    if any(keyword in line.lower() for keyword in
                          ['vorhersage', 'signal', 'preis', 'kaufen', 'verkaufen', 'halten']):
                        if line.strip():
                            self.log_message(f"ERGEBNIS: {line.strip()}")
                            break
        else:
            self.log_message(f"WARNUNG: {model['name']} mit Fehler beendet")

        # Update Visualisierungen
        self.refresh_visualizations()

    def start_all_models(self):
        """Starte alle Modelle"""
        self.log_message("STARTE ALLE BITCOIN TRADING MODELLE...")

        for model_key in self.models.keys():
            if model_key not in self.running_processes:
                self.start_model_with_viz(model_key)
                time.sleep(1)

        self.log_message("Alle verfuegbaren Modelle gestartet")

    def stop_all_models(self):
        """Stoppe alle Modelle"""
        if not self.running_processes:
            self.log_message("INFO: Keine Modelle laufen")
            return

        self.log_message("STOPPE ALLE MODELLE...")

        for model_key in list(self.running_processes.keys()):
            process = self.running_processes[model_key]
            model = self.models[model_key]

            try:
                process.terminate()
                process.wait(timeout=5)

                del self.running_processes[model_key]
                model['process'] = None
                model['status'] = 'Gestoppt'
                model['status_label'].config(text=f"Status: {model['status']}")

                self.log_message(f"GESTOPPT: {model['name']}")

            except Exception as e:
                self.log_message(f"FEHLER beim Stoppen: {e}")

        self.log_message("Alle Modelle gestoppt")

    def run(self):
        """Starte GUI mit Visualisierungen"""
        self.log_message("Bitcoin Trading Launcher mit Visualisierungen bereit!")
        self.log_message("Erweiterte GUI mit Live-Charts und Dashboard")
        self.root.mainloop()

def main():
    """Hauptfunktion"""
    try:
        app = BitcoinLauncherWithVisualizations()
        app.run()
    except Exception as e:
        print(f"FEHLER: GUI-Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
