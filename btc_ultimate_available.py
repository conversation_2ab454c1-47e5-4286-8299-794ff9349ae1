#!/usr/bin/env python3
"""
ULTIMATE BITCOIN PREDICTION MODEL - AVAILABLE LIBRARIES
Maximale Performance mit verfügbaren Bibliotheken
Vergleicht alle Modelle und gibt finale Prognose
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, Bidirectional, BatchNormalization
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers import <PERSON>, RMSprop
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, <PERSON>, Lasso
from concurrent.futures import ThreadPoolExecutor
import time
import os
import warnings
warnings.filterwarnings('ignore')

# MAXIMALE HARDWARE-AUSLASTUNG
print(f"🚀 ULTIMATE BITCOIN PREDICTION - MAXIMUM PERFORMANCE!")
print(f"💻 CPU-Kerne: {os.cpu_count()}")

# GPU-Konfiguration
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"🎮 GPU gefunden: {len(gpus)} GPU(s) aktiviert!")
    except RuntimeError as e:
        print(f"GPU-Konfiguration: {e}")
else:
    print("💻 Nur CPU verfügbar - maximiere CPU-Performance!")

# CPU-Optimierung
tf.config.threading.set_intra_op_parallelism_threads(0)
tf.config.threading.set_inter_op_parallelism_threads(0)
os.environ['OMP_NUM_THREADS'] = str(os.cpu_count())

# ULTIMATE KONFIGURATION
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.75,
    'look_back': 48,
    'batch_size': 64,
    'epochs': 100,
    'patience': 15,
    'ensemble_size': 5,
    'monte_carlo_sims': 500,
    'future_hours': [6, 12, 24, 48, 72]
}

def load_and_create_comprehensive_features():
    """Umfassende Feature-Engineering"""
    print("📊 Erstelle umfassendes Feature-Set...")
    
    df = pd.read_csv(CONFIG['data_file'])
    df['time'] = pd.to_datetime(df['time'])
    df.set_index('time', inplace=True)
    
    print(f"✅ {len(df)} Datenpunkte geladen")
    print(f"   Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
    
    # Basis OHLCV
    features = df[['open', 'high', 'low', 'close', 'volume']].copy()
    
    # === MOVING AVERAGES ===
    ma_periods = [5, 10, 14, 20, 21, 26, 50, 100]
    for period in ma_periods:
        if period <= len(df):
            features[f'sma_{period}'] = df['close'].rolling(period).mean()
            features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
    
    # === MACD FAMILIE ===
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    features['macd'] = ema_12 - ema_26
    features['macd_signal'] = features['macd'].ewm(span=9).mean()
    features['macd_histogram'] = features['macd'] - features['macd_signal']
    features['macd_slope'] = features['macd'].diff()
    
    # === RSI FAMILIE ===
    for period in [14, 21]:
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
        rs = gain / loss
        features[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        features[f'rsi_{period}_slope'] = features[f'rsi_{period}'].diff()
    
    # === BOLLINGER BANDS ===
    for period in [20, 50]:
        sma = df['close'].rolling(period).mean()
        std = df['close'].rolling(period).std()
        features[f'bb_upper_{period}'] = sma + (std * 2)
        features[f'bb_lower_{period}'] = sma - (std * 2)
        features[f'bb_width_{period}'] = (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}']) / sma
        features[f'bb_position_{period}'] = (df['close'] - features[f'bb_lower_{period}']) / (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}'])
    
    # === STOCHASTIC ===
    for period in [14, 21]:
        low_min = df['low'].rolling(period).min()
        high_max = df['high'].rolling(period).max()
        features[f'stoch_k_{period}'] = 100 * ((df['close'] - low_min) / (high_max - low_min))
        features[f'stoch_d_{period}'] = features[f'stoch_k_{period}'].rolling(3).mean()
    
    # === ATR ===
    high_low = df['high'] - df['low']
    high_close = (df['high'] - df['close'].shift()).abs()
    low_close = (df['low'] - df['close'].shift()).abs()
    true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    features['atr'] = true_range.rolling(14).mean()
    features['atr_percent'] = features['atr'] / df['close'] * 100
    
    # === VOLUMEN INDIKATOREN ===
    # OBV
    obv = [0]
    for i in range(1, len(df)):
        if df['close'].iloc[i] > df['close'].iloc[i-1]:
            obv.append(obv[-1] + df['volume'].iloc[i])
        elif df['close'].iloc[i] < df['close'].iloc[i-1]:
            obv.append(obv[-1] - df['volume'].iloc[i])
        else:
            obv.append(obv[-1])
    features['obv'] = obv
    features['obv_ema'] = features['obv'].ewm(span=20).mean()
    
    # Volume Ratios
    features['volume_sma_20'] = df['volume'].rolling(20).mean()
    features['volume_ratio'] = df['volume'] / features['volume_sma_20']
    
    # === MOMENTUM ===
    for period in [5, 10, 20]:
        features[f'roc_{period}'] = df['close'].pct_change(periods=period) * 100
        features[f'momentum_{period}'] = df['close'] - df['close'].shift(period)
    
    # === VOLATILITÄT ===
    for period in [10, 20]:
        features[f'volatility_{period}'] = df['close'].pct_change().rolling(period).std() * 100
    
    # === PREIS PATTERN ===
    features['high_low_ratio'] = df['high'] / df['low']
    features['close_open_ratio'] = df['close'] / df['open']
    features['body_size'] = abs(df['close'] - df['open']) / df['open'] * 100
    
    # === TREND INDIKATOREN ===
    features['trend_strength_20'] = (df['close'] - features['sma_20']) / features['sma_20'] * 100
    features['trend_strength_50'] = (df['close'] - features['sma_50']) / features['sma_50'] * 100
    
    # === ZYKLISCHE FEATURES ===
    features['hour'] = df.index.hour
    features['day_of_week'] = df.index.dayofweek
    features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
    features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
    features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
    features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
    
    print(f"📈 {len(features.columns)} Features erstellt")
    return features.dropna()

def create_sequences(data, target, look_back):
    """Sequenz-Erstellung"""
    X, y = [], []
    for i in range(look_back, len(data)):
        X.append(data[i-look_back:i])
        y.append(target[i])
    return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)

def build_ensemble_models(input_shape):
    """Ensemble von verschiedenen Modellen"""
    models = {}
    
    # 1. Bidirectional LSTM
    models['bidirectional'] = Sequential([
        Bidirectional(LSTM(64, return_sequences=True, dropout=0.2), input_shape=input_shape),
        BatchNormalization(),
        LSTM(32, return_sequences=False, dropout=0.2),
        Dense(32, activation='relu'),
        Dropout(0.3),
        Dense(1)
    ])
    
    # 2. Deep LSTM
    models['deep_lstm'] = Sequential([
        LSTM(96, return_sequences=True, dropout=0.2, input_shape=input_shape),
        LSTM(48, return_sequences=True, dropout=0.2),
        LSTM(24, return_sequences=False, dropout=0.2),
        Dense(32, activation='relu'),
        Dropout(0.3),
        Dense(1)
    ])
    
    # 3. GRU Model
    models['gru'] = Sequential([
        GRU(64, return_sequences=True, dropout=0.2, input_shape=input_shape),
        GRU(32, return_sequences=False, dropout=0.2),
        Dense(32, activation='relu'),
        Dropout(0.3),
        Dense(1)
    ])
    
    # 4. Wide LSTM
    models['wide_lstm'] = Sequential([
        LSTM(128, return_sequences=False, dropout=0.3, input_shape=input_shape),
        Dense(64, activation='relu'),
        Dropout(0.4),
        Dense(32, activation='relu'),
        Dense(1)
    ])
    
    # 5. Simple LSTM
    models['simple_lstm'] = Sequential([
        LSTM(32, return_sequences=False, dropout=0.2, input_shape=input_shape),
        Dense(16, activation='relu'),
        Dense(1)
    ])
    
    # Kompiliere alle Modelle
    optimizers = [Adam(0.001), Adam(0.0005), RMSprop(0.001)]
    
    for i, (name, model) in enumerate(models.items()):
        optimizer = optimizers[i % len(optimizers)]
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae'])
        print(f"✅ {name}: {model.count_params():,} Parameter")
    
    return models

def train_models_parallel(models, X_train, y_train, X_val, y_val):
    """Paralleles Training"""
    print("🎯 Starte paralleles Training...")
    
    def train_single_model(model_data):
        name, model = model_data
        print(f"   Training {name}...")
        
        callbacks = [
            EarlyStopping(patience=CONFIG['patience'], restore_best_weights=True, verbose=0),
            ReduceLROnPlateau(factor=0.5, patience=10, min_lr=1e-7, verbose=0)
        ]
        
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            callbacks=callbacks,
            verbose=0
        )
        
        val_loss = min(history.history['val_loss'])
        print(f"   ✅ {name}: Val Loss = {val_loss:.6f}")
        
        return name, model, val_loss
    
    # Paralleles Training
    with ThreadPoolExecutor(max_workers=min(len(models), os.cpu_count())) as executor:
        results = list(executor.map(train_single_model, models.items()))
    
    # Sortiere nach Performance
    results.sort(key=lambda x: x[2])
    
    trained_models = {}
    performance = {}
    
    for name, model, val_loss in results:
        trained_models[name] = model
        performance[name] = val_loss
    
    print(f"🏆 Modell-Ranking:")
    for i, (name, _, val_loss) in enumerate(results):
        medal = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1}."
        print(f"   {medal} {name}: {val_loss:.6f}")
    
    return trained_models, performance

def create_ml_ensemble(X_train_flat, y_train, X_val_flat, y_val):
    """Traditionelle ML-Modelle"""
    print("🤖 Trainiere ML-Modelle...")
    
    ml_models = {}
    
    # Random Forest
    print("   Training Random Forest...")
    rf = RandomForestRegressor(n_estimators=100, max_depth=15, n_jobs=-1, random_state=42)
    rf.fit(X_train_flat, y_train)
    ml_models['random_forest'] = rf
    
    # Gradient Boosting
    print("   Training Gradient Boosting...")
    gb = GradientBoostingRegressor(n_estimators=100, max_depth=8, random_state=42)
    gb.fit(X_train_flat, y_train)
    ml_models['gradient_boosting'] = gb
    
    # Linear Models
    ridge = Ridge(alpha=1.0)
    ridge.fit(X_train_flat, y_train)
    ml_models['ridge'] = ridge
    
    lasso = Lasso(alpha=0.1)
    lasso.fit(X_train_flat, y_train)
    ml_models['lasso'] = lasso
    
    # Evaluiere ML-Modelle
    ml_performance = {}
    for name, model in ml_models.items():
        pred = model.predict(X_val_flat)
        mse = mean_squared_error(y_val, pred)
        ml_performance[name] = mse
        print(f"   ✅ {name}: MSE = {mse:.6f}")
    
    return ml_models, ml_performance

def create_super_ensemble(dl_models, ml_models, X_test, y_test, X_test_flat):
    """Super Ensemble aus allen Modellen"""
    print("🏆 Erstelle Super Ensemble...")
    
    # Deep Learning Predictions
    dl_predictions = []
    dl_weights = []
    
    for name, model in dl_models.items():
        pred = model.predict(X_test, verbose=0).flatten()
        mse = mean_squared_error(y_test, pred)
        weight = 1.0 / (1.0 + mse)
        dl_predictions.append(pred)
        dl_weights.append(weight)
    
    # ML Predictions
    ml_predictions = []
    ml_weights = []
    
    for name, model in ml_models.items():
        pred = model.predict(X_test_flat)
        mse = mean_squared_error(y_test, pred)
        weight = 1.0 / (1.0 + mse)
        ml_predictions.append(pred)
        ml_weights.append(weight)
    
    # Normalisiere Gewichte
    dl_weights = np.array(dl_weights) / np.sum(dl_weights)
    ml_weights = np.array(ml_weights) / np.sum(ml_weights)
    
    # Gewichtete Ensemble-Vorhersage
    dl_ensemble = np.average(dl_predictions, axis=0, weights=dl_weights)
    ml_ensemble = np.average(ml_predictions, axis=0, weights=ml_weights)
    
    # 70% Deep Learning, 30% Traditional ML
    super_ensemble = 0.7 * dl_ensemble + 0.3 * ml_ensemble
    
    ensemble_mse = mean_squared_error(y_test, super_ensemble)
    ensemble_r2 = r2_score(y_test, super_ensemble)
    
    print(f"🎯 Super Ensemble Performance:")
    print(f"   MSE: {ensemble_mse:.6f}")
    print(f"   R²: {ensemble_r2:.4f}")
    
    return super_ensemble

def monte_carlo_prediction(models, ml_models, last_sequence, last_features, scaler, n_hours=24):
    """Monte Carlo Zukunftsprognose"""
    print(f"🔮 Monte Carlo Prognose für {n_hours}h...")

    all_predictions = []

    for sim in range(CONFIG['monte_carlo_sims']):
        if sim % 100 == 0:
            print(f"   Simulation {sim+1}/{CONFIG['monte_carlo_sims']}")

        current_seq = last_sequence.copy()
        current_features = last_features.copy()
        sim_predictions = []

        for hour in range(n_hours):
            # Deep Learning Predictions
            dl_preds = []
            for model in models.values():
                pred = model.predict(current_seq.reshape(1, *current_seq.shape), verbose=0)[0, 0]
                dl_preds.append(pred)

            # ML Predictions
            ml_preds = []
            for model in ml_models.values():
                pred = model.predict(current_features.reshape(1, -1))[0]
                ml_preds.append(pred)

            # Ensemble Prediction
            ensemble_pred = 0.7 * np.mean(dl_preds) + 0.3 * np.mean(ml_preds)

            # Füge Rauschen hinzu
            volatility = 0.02 * (1 + hour * 0.001)
            noise = np.random.normal(0, volatility)
            final_pred = ensemble_pred * (1 + noise)

            sim_predictions.append(final_pred)

            # Update für nächste Iteration
            new_features = current_features.copy()
            new_features[3] = final_pred  # close price

            # Update mit Rauschen
            for i in range(len(new_features)):
                if i != 3:
                    feature_noise = np.random.normal(0, volatility * 0.3)
                    new_features[i] = new_features[i] * (1 + feature_noise)

            # Update Sequenzen
            current_seq = np.vstack([current_seq[1:], new_features[:current_seq.shape[1]].reshape(1, -1)])
            current_features = new_features

        all_predictions.append(sim_predictions)

    # Statistiken
    all_predictions = np.array(all_predictions)

    stats = {
        'mean': np.mean(all_predictions, axis=0),
        'median': np.median(all_predictions, axis=0),
        'std': np.std(all_predictions, axis=0),
        'q25': np.percentile(all_predictions, 25, axis=0),
        'q75': np.percentile(all_predictions, 75, axis=0),
        'q05': np.percentile(all_predictions, 5, axis=0),
        'q95': np.percentile(all_predictions, 95, axis=0)
    }

    # Skalierung rückgängig machen
    for key in stats:
        dummy_data = np.zeros((len(stats[key]), scaler.n_features_in_))
        dummy_data[:, 3] = stats[key]
        stats[key] = scaler.inverse_transform(dummy_data)[:, 3]

    return stats

def plot_comprehensive_analysis(df, train_size, val_size, y_test, ensemble_pred, future_stats, all_performance):
    """Umfassende Analyse-Visualisierung"""
    plt.figure(figsize=(20, 15))

    # 1. Hauptpreis-Chart
    plt.subplot(3, 3, (1, 3))
    dates = df.index

    plt.plot(dates[:train_size], df['close'].iloc[:train_size], 'b-', label='Training', alpha=0.7, linewidth=1)
    plt.plot(dates[train_size:train_size+val_size], df['close'].iloc[train_size:train_size+val_size],
             'orange', label='Validation', alpha=0.7, linewidth=1)

    test_start = train_size + val_size
    test_dates = dates[test_start:test_start+len(y_test)]
    plt.plot(test_dates, y_test, 'g-', label='Actual', linewidth=2)
    plt.plot(test_dates, ensemble_pred, 'r--', label='Super Ensemble', linewidth=2)

    # Zukunftsprognose
    future_dates = pd.date_range(start=dates[-1], periods=len(future_stats['mean'])+1, freq='1H')[1:]
    plt.plot(future_dates, future_stats['mean'], 'purple', linewidth=3, label='Future Prediction')
    plt.fill_between(future_dates, future_stats['q05'], future_stats['q95'],
                     alpha=0.2, color='purple', label='90% Confidence')
    plt.fill_between(future_dates, future_stats['q25'], future_stats['q75'],
                     alpha=0.3, color='purple', label='50% Confidence')

    plt.title('ULTIMATE BITCOIN PREDICTION - SUPER ENSEMBLE', fontsize=16, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. Performance Vergleich
    plt.subplot(3, 3, 4)
    models = list(all_performance.keys())
    scores = list(all_performance.values())
    colors = plt.cm.viridis(np.linspace(0, 1, len(models)))

    bars = plt.barh(models, scores, color=colors)
    plt.title('Model Performance Comparison')
    plt.xlabel('Validation Loss (Lower = Better)')

    # Beste 3 markieren
    sorted_indices = np.argsort(scores)
    for i, idx in enumerate(sorted_indices[:3]):
        bars[idx].set_color('gold' if i == 0 else 'silver' if i == 1 else '#CD7F32')

    plt.grid(True, alpha=0.3)

    # 3. Zukunftsprognose Detail
    plt.subplot(3, 3, 5)
    hours = range(1, len(future_stats['mean']) + 1)
    plt.plot(hours, future_stats['mean'], 'purple', linewidth=2, label='Mean')
    plt.plot(hours, future_stats['median'], 'orange', linewidth=2, label='Median')
    plt.fill_between(hours, future_stats['q25'], future_stats['q75'], alpha=0.3, color='blue')
    plt.title('Future Prediction Statistics')
    plt.xlabel('Hours Ahead')
    plt.ylabel('Price (USD)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 4. Volatilität
    plt.subplot(3, 3, 6)
    volatility = future_stats['std'] / future_stats['mean'] * 100
    plt.plot(hours, volatility, 'red', linewidth=2)
    plt.title('Predicted Volatility')
    plt.xlabel('Hours Ahead')
    plt.ylabel('Volatility %')
    plt.grid(True, alpha=0.3)

    # 5. Residuals
    plt.subplot(3, 3, 7)
    residuals = y_test - ensemble_pred
    plt.scatter(ensemble_pred, residuals, alpha=0.6, s=20)
    plt.axhline(y=0, color='red', linestyle='--')
    plt.title('Residuals')
    plt.xlabel('Predicted')
    plt.ylabel('Residuals')
    plt.grid(True, alpha=0.3)

    # 6. Error Distribution
    plt.subplot(3, 3, 8)
    residuals = y_test - ensemble_pred
    plt.hist(residuals, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('Error Distribution')
    plt.xlabel('Error')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)

    # 7. Confidence Intervals
    plt.subplot(3, 3, 9)
    current_price = df['close'].iloc[-1]
    price_changes = (future_stats['mean'] / current_price - 1) * 100
    plt.plot(hours, price_changes, 'green', linewidth=2)
    plt.title('Expected Price Change %')
    plt.xlabel('Hours Ahead')
    plt.ylabel('Change %')
    plt.axhline(y=0, color='red', linestyle='--')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

def calculate_comprehensive_metrics(y_true, y_pred):
    """Umfassende Metriken"""
    metrics = {
        'mse': mean_squared_error(y_true, y_pred),
        'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
        'mae': mean_absolute_error(y_true, y_pred),
        'r2': r2_score(y_true, y_pred),
        'mape': np.mean(np.abs((y_true - y_pred) / y_true)) * 100,
        'explained_variance': 1 - (np.var(y_true - y_pred) / np.var(y_true))
    }

    # Richtungsgenauigkeit
    if len(y_true) > 1:
        true_direction = np.diff(y_true) > 0
        pred_direction = np.diff(y_pred) > 0
        metrics['direction_accuracy'] = np.mean(true_direction == pred_direction) * 100
    else:
        metrics['direction_accuracy'] = 0

    return metrics

def main():
    """ULTIMATE MAIN FUNCTION"""
    print("\n" + "="*70)
    print("🚀 ULTIMATE BITCOIN PREDICTION - MAXIMUM PERFORMANCE")
    print("="*70)
    print(f"🎯 Ziel: Maximale Genauigkeit mit verfügbaren Bibliotheken")

    total_start = time.time()

    try:
        # 1. Feature Engineering
        print("\n📊 PHASE 1: COMPREHENSIVE FEATURE ENGINEERING")
        feature_start = time.time()
        df = load_and_create_comprehensive_features()
        feature_time = time.time() - feature_start
        print(f"⚡ Feature Engineering: {feature_time:.1f}s")

        # 2. Daten vorbereiten
        print("\n🔄 PHASE 2: DATA PREPARATION")
        prep_start = time.time()

        X = df.drop('close', axis=1)
        y = df['close'].values

        # Skalierung
        feature_scaler = RobustScaler()
        target_scaler = StandardScaler()

        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()

        # Sequenzen erstellen
        X_seq, y_seq = create_sequences(X_scaled, y_scaled, CONFIG['look_back'])

        # Train-Val-Test Split
        train_size = int(len(X_seq) * CONFIG['train_split'])
        val_size = int(len(X_seq) * 0.15)

        X_train = X_seq[:train_size]
        y_train = y_seq[:train_size]
        X_val = X_seq[train_size:train_size+val_size]
        y_val = y_seq[train_size:train_size+val_size]
        X_test = X_seq[train_size+val_size:]
        y_test = y_seq[train_size+val_size:]

        print(f"📊 Datenaufteilung: Train {len(X_train)}, Val {len(X_val)}, Test {len(X_test)}")

        prep_time = time.time() - prep_start
        print(f"⚡ Data Preparation: {prep_time:.1f}s")

        # 3. Deep Learning Modelle
        print("\n🧠 PHASE 3: DEEP LEARNING ENSEMBLE")
        dl_start = time.time()

        input_shape = (X_train.shape[1], X_train.shape[2])
        dl_models = build_ensemble_models(input_shape)
        trained_dl_models, dl_performance = train_models_parallel(dl_models, X_train, y_train, X_val, y_val)

        dl_time = time.time() - dl_start
        print(f"⚡ Deep Learning Training: {dl_time:.1f}s")

        # 4. Traditional ML Modelle
        print("\n🤖 PHASE 4: TRADITIONAL ML ENSEMBLE")
        ml_start = time.time()

        X_train_flat = X_train.reshape(X_train.shape[0], -1)
        X_val_flat = X_val.reshape(X_val.shape[0], -1)
        X_test_flat = X_test.reshape(X_test.shape[0], -1)

        ml_models, ml_performance = create_ml_ensemble(X_train_flat, y_train, X_val_flat, y_val)

        ml_time = time.time() - ml_start
        print(f"⚡ ML Training: {ml_time:.1f}s")

        # 5. Super Ensemble
        print("\n🏆 PHASE 5: SUPER ENSEMBLE CREATION")
        ensemble_start = time.time()

        super_ensemble_pred = create_super_ensemble(trained_dl_models, ml_models, X_test, y_test, X_test_flat)

        ensemble_time = time.time() - ensemble_start
        print(f"⚡ Super Ensemble: {ensemble_time:.1f}s")

        # 6. Skalierung rückgängig machen
        y_test_orig = target_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        ensemble_pred_orig = target_scaler.inverse_transform(super_ensemble_pred.reshape(-1, 1)).flatten()

        # 7. Metriken berechnen
        metrics = calculate_comprehensive_metrics(y_test_orig, ensemble_pred_orig)

        print(f"\n📊 SUPER ENSEMBLE PERFORMANCE:")
        print(f"R²: {metrics['r2']:.4f} ({metrics['r2']*100:.1f}%)")
        print(f"RMSE: ${metrics['rmse']:.2f}")
        print(f"MAE: ${metrics['mae']:.2f}")
        print(f"MAPE: {metrics['mape']:.2f}%")
        print(f"Direction Accuracy: {metrics['direction_accuracy']:.1f}%")

        # 8. Zukunftsprognosen
        print("\n🔮 PHASE 6: FUTURE PREDICTIONS")
        future_start = time.time()

        last_sequence = X_test[-1]
        last_features = X_test_flat[-1]

        future_predictions = {}
        for hours in CONFIG['future_hours']:
            print(f"\n🔮 {hours}h Prognose...")
            future_stats = monte_carlo_prediction(
                trained_dl_models, ml_models, last_sequence, last_features, target_scaler, hours
            )
            future_predictions[hours] = future_stats

        future_time = time.time() - future_start
        print(f"⚡ Future Predictions: {future_time:.1f}s")

        # 9. Visualisierung
        print("\n📈 PHASE 7: COMPREHENSIVE VISUALIZATION")
        viz_start = time.time()

        all_performance = {**dl_performance, **ml_performance}
        plot_comprehensive_analysis(df, train_size, val_size, y_test_orig, ensemble_pred_orig,
                                   future_predictions[24], all_performance)

        viz_time = time.time() - viz_start
        print(f"⚡ Visualization: {viz_time:.1f}s")

        # 10. FINALE PROGNOSE
        print("\n" + "="*70)
        print("🎯 ULTIMATE BITCOIN PROGNOSE")
        print("="*70)

        current_price = df['close'].iloc[-1]

        for hours in CONFIG['future_hours']:
            stats = future_predictions[hours]

            mean_price = stats['mean'][-1]
            median_price = stats['median'][-1]
            q25_price = stats['q25'][-1]
            q75_price = stats['q75'][-1]
            q05_price = stats['q05'][-1]
            q95_price = stats['q95'][-1]

            mean_change = (mean_price / current_price - 1) * 100

            print(f"\n📅 In {hours}h:")
            print(f"   💰 Erwarteter Preis: ${mean_price:.2f} ({mean_change:+.2f}%)")
            print(f"   📊 Median: ${median_price:.2f}")
            print(f"   📈 50% Konfidenz: ${q25_price:.2f} - ${q75_price:.2f}")
            print(f"   📈 90% Konfidenz: ${q05_price:.2f} - ${q95_price:.2f}")

            # Trend-Bewertung
            if mean_change > 5:
                trend = "🚀 STARK BULLISH"
            elif mean_change > 2:
                trend = "📈 BULLISH"
            elif mean_change > -2:
                trend = "➡️  SEITWÄRTS"
            elif mean_change > -5:
                trend = "📉 BEARISH"
            else:
                trend = "💥 STARK BEARISH"

            print(f"   🎯 Trend: {trend}")

        # Finale Statistiken
        total_time = time.time() - total_start

        print(f"\n" + "="*70)
        print("✅ ULTIMATE PREDICTION ABGESCHLOSSEN!")
        print("="*70)
        print(f"⚡ Gesamtzeit: {total_time:.1f} Sekunden")
        print(f"🎯 Finale Genauigkeit: {metrics['r2']*100:.1f}%")
        print(f"📊 Features: {X_scaled.shape[1]}")
        print(f"🤖 DL-Modelle: {len(trained_dl_models)}")
        print(f"🔧 ML-Modelle: {len(ml_models)}")
        print(f"🔮 Monte Carlo Sims: {CONFIG['monte_carlo_sims']}")
        print(f"💻 Hardware: {os.cpu_count()} CPU-Kerne + {'GPU' if gpus else 'CPU only'}")

        # Performance-Ranking
        print(f"\n🏆 TOP 5 MODELLE:")
        sorted_performance = sorted(all_performance.items(), key=lambda x: x[1])
        for i, (name, score) in enumerate(sorted_performance[:5]):
            medal = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1}."
            print(f"   {medal} {name}: {score:.6f}")

        if metrics['r2'] >= 0.80:
            print(f"\n🎉🎉🎉 ZIEL ERREICHT! 🎉🎉🎉")
            print(f"Genauigkeit: {metrics['r2']*100:.1f}% ≥ 80%")
        else:
            print(f"\n💪 STARKE PERFORMANCE!")
            print(f"Genauigkeit: {metrics['r2']*100:.1f}%")

    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
