��o      }�(�performance_history��collections��deque���)M���R�(}�(�session�K �accuracy��numpy.core.multiarray��scalar����numpy��dtype����f8�����R�(K�<�NNNJ����J����K t�bC      �?���R��	timestamp��datetime��datetime���C
�/_���R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R��48h�hhC      �?���R�u�learning_momentum�G?�      u}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
�;E𔅔R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R��48h�hhC      �?���R�u�learning_momentum�G?�      �reward_score�hhC      $@���R�u}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
��Y���R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�ffffff�reward_score�hRu}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
��K���R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?��G�z�reward_score�hRu}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
�!
Pޔ��R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�o��-U�reward_score�hRu}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
�3�h���R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�oiDg7�reward_score�hRu}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
�64<����R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?���=��A�reward_score�hRu}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
�#�����R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?��m�@��reward_score�hRu}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
�*=����R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�X�u	�V�reward_score�hRu}�(�session�K	�accuracy�hhC      �?���R��	timestamp�hC
�'n{���R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�:�{�OŌreward_score�hRu}�(�session�K
�accuracy�hhC      �?���R��	timestamp�hC
�4]����R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�+ �b�a�reward_score�hRu}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
�3
Yq���R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�(�^��reward_score�hRu}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
�&,�󔅔R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�3�@I@�reward_score�hRu}�(�session�K
�accuracy�hhC      �?���R��	timestamp�hC
�9+ sє��R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�J�=d�reward_score�hRu}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
� U���R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�mL`kG�reward_score�hRu}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
�wZ���R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�      �reward_score�hRu}�(�session�K�accuracy�hhC      �?���R��	timestamp�hC
�#����R��ensemble_results�}�(�1h�hhC      �?���R��6h�hhC      �?���R��24h�hhC      �?���R�u�learning_momentum�G?�      �reward_score�hRuej�  G?�      �
session_count�K�hyperparameters�}�(�rf_1h�}�(�n_estimators�K2�	max_depth�K
u�rf_48h�}�(j�  K2j�  K
u�rf_24h�}�(j�  K2j�  K
u�rf_6h�}�(j�  K2j�  K
uu�
best_accuracy�h7j�  hR�feature_importance_global�}�j�  �2025-07-02T08:16:33.317091�u.