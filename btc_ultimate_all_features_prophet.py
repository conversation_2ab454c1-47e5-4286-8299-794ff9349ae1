#!/usr/bin/env python3
"""
🚀 ULTIMATE ALL-FEATURES TRADING PROPHET 🚀
===========================================
ALLE FEATURES + MAXIMALE OPTIMIERUNG + LIVE TESTING
"""

import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.model_selection import cross_val_score
import yfinance as yf
import multiprocessing
import os
from concurrent.futures import ThreadPoolExecutor
import joblib

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

# ULTIMATE ALL-FEATURES KONFIGURATION
MAX_CORES = multiprocessing.cpu_count()
UPDATE_INTERVAL = 45  # Alle 45 Sekunden für mehr Features
LOOKBACK_DAYS = 14    # 14 Tage für mehr Daten
PREDICTION_HORIZONS = [3, 6, 12, 24]  # Multiple Horizonte

print("🚀 ULTIMATE ALL-FEATURES TRADING PROPHET")
print("=" * 45)
print(f"💻 CPU: {MAX_CORES} Kerne (MAXIMAL)")
print(f"🎯 FOKUS: ALLE Features + Optimierung")
print(f"⏱️ Update: Alle {UPDATE_INTERVAL}s")
print(f"📊 Horizonte: {PREDICTION_HORIZONS}h")
print(f"🕐 Start: {datetime.now().strftime('%H:%M:%S')}")

# Verzeichnisse erstellen
os.makedirs('./ultimate_prophet', exist_ok=True)

class UltimateAllFeaturesProphet:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        self.prediction_history = []
        self.accuracy_history = []
        self.ensemble_weights = {}
        
    def get_enhanced_bitcoin_data(self):
        """Erweiterte Bitcoin-Datensammlung"""
        try:
            btc = yf.Ticker("BTC-USD")
            df = btc.history(period=f"{LOOKBACK_DAYS}d", interval="1h")
            
            if len(df) > 100:
                df.columns = [col.lower() for col in df.columns]
                return df.dropna(), True
            else:
                raise Exception("Zu wenig Daten")
                
        except Exception as e:
            # Erweiterte Fallback-Daten
            end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
            start_time = end_time - timedelta(days=LOOKBACK_DAYS)
            dates = pd.date_range(start=start_time, end=end_time, freq='H')
            
            n_points = len(dates)
            np.random.seed(int(time.time()) % 1000)
            
            # Erweiterte Preismodellierung
            base_price = 67000
            
            # Multi-Trend Komponenten
            long_trend = np.cumsum(np.random.normal(0, 100, n_points))
            medium_trend = np.cumsum(np.random.normal(0, 200, n_points))
            short_trend = np.cumsum(np.random.normal(0, 300, n_points))
            
            # Volatilitäts-Regime
            vol_regime = np.random.choice([0.5, 1.0, 2.0, 4.0], n_points//24, p=[0.4, 0.35, 0.2, 0.05])
            vol_regime = np.repeat(vol_regime, 24)[:n_points]
            volatility = np.random.normal(0, 600, n_points) * vol_regime
            
            # Zyklen
            daily_cycle = 400 * np.sin(2 * np.pi * np.arange(n_points) / 24)
            weekly_cycle = 600 * np.sin(2 * np.pi * np.arange(n_points) / (24 * 7))
            
            # News Events
            news_events = np.random.choice([0, 1], n_points, p=[0.995, 0.005])
            news_impact = news_events * np.random.normal(0, 2000, n_points)
            
            prices = (base_price + long_trend + medium_trend + short_trend + 
                     volatility + daily_cycle + weekly_cycle + news_impact)
            prices = np.maximum(prices, 30000)
            
            # Erweiterte OHLCV
            high_mult = np.random.uniform(1.001, 1.025, n_points)
            low_mult = np.random.uniform(0.975, 0.999, n_points)
            
            df = pd.DataFrame({
                'close': prices,
                'high': prices * high_mult,
                'low': prices * low_mult,
                'open': prices * np.random.uniform(0.998, 1.002, n_points),
                'volume': np.random.lognormal(15, 0.3, n_points)
            }, index=dates)
            
            return df, False
    
    def create_ultimate_features(self, df):
        """ALLE möglichen Trading-Features - ultimative Sammlung"""
        df = df.copy()
        
        print("   🔧 Erstelle ULTIMATE Features...")
        
        # === PREIS-BASIERTE FEATURES ===
        
        # Returns (alle wichtigen Zeiträume)
        for period in [1, 2, 3, 4, 6, 8, 12, 18, 24, 36, 48, 72]:
            df[f'returns_{period}h'] = df['close'].pct_change(periods=period)
            df[f'log_returns_{period}h'] = np.log(df['close'] / df['close'].shift(period))
            df[f'momentum_{period}h'] = df['close'] / df['close'].shift(period) - 1
        
        # Moving Averages (umfassend)
        for window in [3, 6, 9, 12, 18, 24, 36, 48, 72, 96, 144, 168]:
            df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
            df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
            df[f'wma_{window}'] = df['close'].rolling(window=window).apply(
                lambda x: np.average(x, weights=np.arange(1, len(x)+1)), raw=True)
            
            # MA Signale
            df[f'price_above_sma_{window}'] = (df['close'] > df[f'sma_{window}']).astype(float)
            df[f'price_above_ema_{window}'] = (df['close'] > df[f'ema_{window}']).astype(float)
            df[f'sma_slope_{window}'] = df[f'sma_{window}'].diff()
            df[f'ema_slope_{window}'] = df[f'ema_{window}'].diff()
        
        # Golden/Death Cross Signale (alle Kombinationen)
        ma_pairs = [(6, 24), (12, 48), (24, 72), (48, 144)]
        for short, long in ma_pairs:
            df[f'golden_cross_{short}_{long}'] = (df[f'sma_{short}'] > df[f'sma_{long}']).astype(float)
            df[f'ema_cross_{short}_{long}'] = (df[f'ema_{short}'] > df[f'ema_{long}']).astype(float)
        
        # === VOLATILITÄTS-FEATURES ===
        
        # Volatilität (alle Zeiträume)
        for window in [6, 12, 18, 24, 36, 48, 72, 96, 144]:
            df[f'volatility_{window}'] = df['close'].rolling(window=window).std()
            df[f'vol_ratio_{window}'] = df[f'volatility_{window}'] / df['close']
            df[f'vol_percentile_{window}'] = df[f'volatility_{window}'].rolling(window=168).rank(pct=True)
            
            # GARCH-ähnliche Features
            returns = df['close'].pct_change()
            df[f'ewm_vol_{window}'] = returns.ewm(span=window).std()
            df[f'vol_of_vol_{window}'] = df[f'volatility_{window}'].rolling(window=24).std()
        
        # Volatilitäts-Regime
        df['vol_regime_low'] = (df['volatility_24'] < df['volatility_24'].rolling(window=168).quantile(0.33)).astype(float)
        df['vol_regime_high'] = (df['volatility_24'] > df['volatility_24'].rolling(window=168).quantile(0.67)).astype(float)
        
        # === TECHNISCHE INDIKATOREN ===
        
        # RSI (multiple Perioden)
        for period in [9, 14, 21, 28]:
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / (loss + 1e-10)
            df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
            
            # RSI Signale
            df[f'rsi_{period}_oversold'] = (df[f'rsi_{period}'] < 30).astype(float)
            df[f'rsi_{period}_overbought'] = (df[f'rsi_{period}'] > 70).astype(float)
            df[f'rsi_{period}_divergence'] = (df[f'rsi_{period}'].diff() * df['returns_1h'] < 0).astype(float)
        
        # MACD (multiple Kombinationen)
        macd_params = [(12, 26, 9), (8, 21, 5), (19, 39, 9)]
        for fast, slow, signal in macd_params:
            ema_fast = df['close'].ewm(span=fast).mean()
            ema_slow = df['close'].ewm(span=slow).mean()
            df[f'macd_{fast}_{slow}'] = ema_fast - ema_slow
            df[f'macd_signal_{fast}_{slow}'] = df[f'macd_{fast}_{slow}'].ewm(span=signal).mean()
            df[f'macd_histogram_{fast}_{slow}'] = df[f'macd_{fast}_{slow}'] - df[f'macd_signal_{fast}_{slow}']
            df[f'macd_bullish_{fast}_{slow}'] = (df[f'macd_{fast}_{slow}'] > df[f'macd_signal_{fast}_{slow}']).astype(float)
        
        # Bollinger Bands (multiple Perioden)
        for window in [20, 50]:
            for std_mult in [1.5, 2.0, 2.5]:
                bb_middle = df['close'].rolling(window=window).mean()
                bb_std = df['close'].rolling(window=window).std()
                df[f'bb_upper_{window}_{std_mult}'] = bb_middle + std_mult * bb_std
                df[f'bb_lower_{window}_{std_mult}'] = bb_middle - std_mult * bb_std
                df[f'bb_position_{window}_{std_mult}'] = ((df['close'] - df[f'bb_lower_{window}_{std_mult}']) / 
                                                        (df[f'bb_upper_{window}_{std_mult}'] - df[f'bb_lower_{window}_{std_mult}']))
                df[f'bb_width_{window}_{std_mult}'] = ((df[f'bb_upper_{window}_{std_mult}'] - df[f'bb_lower_{window}_{std_mult}']) / bb_middle)
                df[f'bb_squeeze_{window}_{std_mult}'] = (df[f'bb_width_{window}_{std_mult}'] < 
                                                       df[f'bb_width_{window}_{std_mult}'].rolling(window=50).quantile(0.2)).astype(float)
        
        # Stochastic Oscillator
        for k_period in [14, 21]:
            for d_period in [3, 5]:
                low_min = df['low'].rolling(window=k_period).min()
                high_max = df['high'].rolling(window=k_period).max()
                df[f'stoch_k_{k_period}'] = 100 * (df['close'] - low_min) / (high_max - low_min)
                df[f'stoch_d_{k_period}_{d_period}'] = df[f'stoch_k_{k_period}'].rolling(window=d_period).mean()
                df[f'stoch_oversold_{k_period}'] = (df[f'stoch_k_{k_period}'] < 20).astype(float)
                df[f'stoch_overbought_{k_period}'] = (df[f'stoch_k_{k_period}'] > 80).astype(float)
        
        # Williams %R
        for period in [14, 21]:
            high_max = df['high'].rolling(window=period).max()
            low_min = df['low'].rolling(window=period).min()
            df[f'williams_r_{period}'] = -100 * (high_max - df['close']) / (high_max - low_min)
        
        # === HIGH-LOW-FEATURES ===
        
        # High-Low Ratios
        df['hl_ratio'] = df['high'] / df['low']
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        df['high_low_spread'] = (df['high'] - df['low']) / df['close']
        
        # ATR (Average True Range)
        df['tr'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                np.abs(df['high'] - df['close'].shift()),
                np.abs(df['low'] - df['close'].shift())
            )
        )
        for window in [14, 21, 28]:
            df[f'atr_{window}'] = df['tr'].rolling(window=window).mean()
            df[f'atr_ratio_{window}'] = df[f'atr_{window}'] / df['close']
            df[f'atr_percentile_{window}'] = df[f'atr_{window}'].rolling(window=168).rank(pct=True)
        
        # Support/Resistance Levels
        for window in [12, 24, 48]:
            df[f'local_high_{window}'] = (df['close'].rolling(window=window, center=True).max() == df['close']).astype(float)
            df[f'local_low_{window}'] = (df['close'].rolling(window=window, center=True).min() == df['close']).astype(float)
            df[f'resistance_strength_{window}'] = df[f'local_high_{window}'].rolling(window=168).sum()
            df[f'support_strength_{window}'] = df[f'local_low_{window}'].rolling(window=168).sum()
        
        # === VOLUME-FEATURES ===
        
        # Volume Indikatoren
        for window in [6, 12, 24, 48]:
            df[f'volume_sma_{window}'] = df['volume'].rolling(window=window).mean()
            df[f'volume_ratio_{window}'] = df['volume'] / df[f'volume_sma_{window}']
            df[f'volume_spike_{window}'] = (df['volume'] > df[f'volume_sma_{window}'] * 2).astype(float)
        
        # OBV (On-Balance Volume)
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).cumsum()
        for window in [12, 24, 48]:
            df[f'obv_sma_{window}'] = df['obv'].rolling(window=window).mean()
            df[f'obv_ratio_{window}'] = df['obv'] / df[f'obv_sma_{window}']
        
        # VWAP (Volume Weighted Average Price)
        df['vwap'] = (df['close'] * df['volume']).cumsum() / df['volume'].cumsum()
        df['price_vs_vwap'] = df['close'] / df['vwap'] - 1
        
        # Volume-Price Trend
        df['vpt'] = ((df['close'].diff() / df['close'].shift()) * df['volume']).cumsum()
        
        # === ZEIT-FEATURES ===
        
        # Zeit-basierte Features
        df['hour'] = df.index.hour
        df['day_of_week'] = df.index.dayofweek
        df['day_of_month'] = df.index.day
        df['month'] = df.index.month
        df['quarter'] = df.index.quarter
        
        # Cyclical Encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Trading Sessions
        df['is_us_trading'] = ((df['hour'] >= 9) & (df['hour'] <= 16)).astype(float)
        df['is_asian_session'] = ((df['hour'] >= 0) & (df['hour'] <= 8)).astype(float)
        df['is_european_session'] = ((df['hour'] >= 8) & (df['hour'] <= 16)).astype(float)
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(float)
        
        # === MARKT-MIKROSTRUKTUR ===
        
        # Bid-Ask Spread Proxy
        df['spread_proxy'] = (df['high'] - df['low']) / df['close']
        
        # Price Impact
        df['price_impact'] = abs(df['close'].diff()) / df['volume']
        
        # Trend Strength
        for window in [12, 24, 48]:
            df[f'trend_strength_{window}'] = abs(df[f'returns_{window}h'])
            df[f'strong_trend_{window}'] = (df[f'trend_strength_{window}'] > 
                                          df[f'trend_strength_{window}'].rolling(window=168).quantile(0.8)).astype(float)
        
        # === LAG FEATURES ===
        
        # Lag Features (wichtige Verzögerungen)
        for lag in [1, 2, 3, 6, 12, 24]:
            df[f'close_lag_{lag}'] = df['close'].shift(lag)
            df[f'returns_lag_{lag}'] = df['returns_1h'].shift(lag)
            df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
            df[f'volatility_lag_{lag}'] = df['volatility_24'].shift(lag)
        
        print(f"   ✅ ULTIMATE Features erstellt: {df.shape[1]} Spalten")
        
        # ULTIMATE Bereinigung
        df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        df = df.replace([np.inf, -np.inf], 0)
        
        # Alle boolean zu float
        for col in df.columns:
            if df[col].dtype == 'bool':
                df[col] = df[col].astype(float)

        return df

    def create_ultimate_labels(self, df, horizon_hours):
        """ULTIMATE Trading-Labels für verschiedene Horizonte"""
        future_price = df['close'].shift(-horizon_hours)
        current_price = df['close']
        future_return = (future_price / current_price - 1).fillna(0)

        # Adaptive Schwellenwerte je nach Horizont
        threshold = 0.005 + (horizon_hours - 3) * 0.001  # 0.5% + 0.1% pro Stunde

        # Labels: 1 = KAUF (Gewinn erwartet), 0 = VERKAUF
        labels = (future_return > threshold).astype(int)

        return labels

    def train_ultimate_models(self, df):
        """ULTIMATE Modell-Training mit allen Features"""

        print("   🤖 Trainiere ULTIMATE Modelle...")

        # Features erstellen
        df_features = self.create_ultimate_features(df)

        # Feature-Auswahl (nur numerische, keine OHLCV)
        exclude_cols = ['close', 'high', 'low', 'open', 'volume']
        feature_cols = [col for col in df_features.columns if col not in exclude_cols]

        results = {}

        for horizon in PREDICTION_HORIZONS:
            print(f"     📈 Training für {horizon}h Horizont...")

            # Labels für diesen Horizont
            labels = self.create_ultimate_labels(df_features, horizon)

            X = df_features[feature_cols].values
            y = labels.values

            # Bereinigung
            valid_mask = ~(np.isnan(X).any(axis=1) | np.isnan(y) | np.isinf(X).any(axis=1))
            X = X[valid_mask]
            y = y[valid_mask]

            if len(X) < 100:
                continue

            # Skalierung
            scaler = RobustScaler()
            X_scaled = scaler.fit_transform(X)
            self.scalers[f'{horizon}h'] = scaler

            # Train/Test Split
            split_idx = int(len(X_scaled) * 0.8)
            X_train, X_test = X_scaled[:split_idx], X_scaled[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]

            # ULTIMATE Modelle (reduziert für Geschwindigkeit)
            models = {
                f'RandomForest_{horizon}h': RandomForestClassifier(
                    n_estimators=100,  # Reduziert für Geschwindigkeit
                    max_depth=15,
                    min_samples_split=5,
                    max_features='sqrt',
                    n_jobs=MAX_CORES,
                    random_state=42
                ),
                f'ExtraTrees_{horizon}h': ExtraTreesClassifier(
                    n_estimators=80,   # Reduziert für Geschwindigkeit
                    max_depth=12,
                    min_samples_split=3,
                    max_features='sqrt',
                    n_jobs=MAX_CORES,
                    random_state=42
                )
            }

            horizon_results = {}

            for name, model in models.items():
                try:
                    # Training
                    model.fit(X_train, y_train)

                    # Evaluierung
                    y_pred = model.predict(X_test)
                    accuracy = accuracy_score(y_test, y_pred)

                    horizon_results[name] = {
                        'model': model,
                        'accuracy': accuracy,
                        'feature_cols': feature_cols
                    }

                    print(f"       ✅ {name}: {accuracy:.3f}")

                except Exception as e:
                    print(f"       ❌ {name}: Fehler - {e}")

            if horizon_results:
                results[f'{horizon}h'] = horizon_results

        self.models = results
        return len(results) > 0

    def predict_ultimate_signals(self, df):
        """ULTIMATE Trading-Signal Vorhersage für alle Horizonte"""

        if not self.models:
            return None

        # Features erstellen
        df_features = self.create_ultimate_features(df)

        predictions = {}

        for horizon_key, horizon_models in self.models.items():
            horizon = int(horizon_key.replace('h', ''))

            # Features für diesen Horizont
            feature_cols = list(horizon_models.values())[0]['feature_cols']
            X_latest = df_features[feature_cols].iloc[-1:].values

            # Bereinigung
            if np.isnan(X_latest).any() or np.isinf(X_latest).any():
                X_latest = np.nan_to_num(X_latest, 0)

            # Skalierung
            scaler = self.scalers.get(horizon_key)
            if scaler is None:
                continue

            X_scaled = scaler.transform(X_latest)

            # Ensemble-Vorhersage
            ensemble_predictions = []
            ensemble_weights = []

            for model_name, model_data in horizon_models.items():
                try:
                    pred_proba = model_data['model'].predict_proba(X_scaled)[0]
                    buy_probability = pred_proba[1]

                    ensemble_predictions.append(buy_probability)
                    ensemble_weights.append(model_data['accuracy'])

                except Exception as e:
                    continue

            if not ensemble_predictions:
                continue

            # Gewichtetes Ensemble
            weights = np.array(ensemble_weights)
            weights = weights / weights.sum()
            ensemble_prob = np.average(ensemble_predictions, weights=weights)

            # Trading-Signal für diesen Horizont
            if ensemble_prob > 0.65:
                signal = "STARKER KAUF 🔥🔥"
                confidence = ensemble_prob
            elif ensemble_prob > 0.55:
                signal = "KAUF 🔥"
                confidence = ensemble_prob
            elif ensemble_prob < 0.35:
                signal = "STARKER VERKAUF 🔻🔻"
                confidence = 1 - ensemble_prob
            elif ensemble_prob < 0.45:
                signal = "VERKAUF 🔻"
                confidence = 1 - ensemble_prob
            else:
                signal = "HALTEN ⚖️"
                confidence = 0.5

            predictions[f'{horizon}h'] = {
                'signal': signal,
                'probability': ensemble_prob,
                'confidence': confidence,
                'models_used': len(ensemble_predictions)
            }

        # Gesamtsignal basierend auf gewichteten Horizonten
        if predictions:
            # Gewichtung: kürzere Horizonte wichtiger
            horizon_weights = {3: 0.4, 6: 0.3, 12: 0.2, 24: 0.1}

            weighted_prob = 0
            total_weight = 0

            for horizon_key, pred in predictions.items():
                horizon = int(horizon_key.replace('h', ''))
                weight = horizon_weights.get(horizon, 0.1)
                weighted_prob += pred['probability'] * weight
                total_weight += weight

            if total_weight > 0:
                overall_prob = weighted_prob / total_weight

                if overall_prob > 0.6:
                    overall_signal = "KAUF 🔥"
                elif overall_prob < 0.4:
                    overall_signal = "VERKAUF 🔻"
                else:
                    overall_signal = "HALTEN ⚖️"

                predictions['OVERALL'] = {
                    'signal': overall_signal,
                    'probability': overall_prob,
                    'confidence': max(overall_prob, 1-overall_prob)
                }

        current_price = df['close'].iloc[-1]
        current_time = df.index[-1]

        return {
            'time': current_time,
            'price': current_price,
            'predictions': predictions
        }

    def display_ultimate_dashboard(self, prediction_result):
        """ULTIMATE Trading-Dashboard"""

        print("\n" + "="*90)
        print("🚀 ULTIMATE ALL-FEATURES TRADING PROPHET - LIVE DASHBOARD 🚀")
        print("="*90)

        if prediction_result and prediction_result['predictions']:
            predictions = prediction_result['predictions']

            print(f"\n📊 LIVE TRADING SIGNALS:")
            print(f"🕐 Zeit: {prediction_result['time'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"💰 Preis: ${prediction_result['price']:,.2f}")

            # Alle Horizonte anzeigen
            print(f"\n🔮 MULTI-HORIZONT SIGNALE:")
            print(f"{'Horizont':<10} | {'Signal':<20} | {'Wahrscheinlichkeit':<15} | {'Konfidenz':<10} | {'Modelle':<8}")
            print("-" * 80)

            for horizon_key, pred in predictions.items():
                if horizon_key != 'OVERALL':
                    print(f"{horizon_key:<10} | {pred['signal']:<20} | {pred['probability']:.1%}{'':>8} | "
                          f"{pred['confidence']:.1%}{'':>3} | {pred.get('models_used', 'N/A'):<8}")

            # Gesamtsignal
            if 'OVERALL' in predictions:
                overall = predictions['OVERALL']
                print(f"\n🎯 GESAMTSIGNAL: {overall['signal']}")
                print(f"📈 Wahrscheinlichkeit: {overall['probability']:.1%}")
                print(f"🎪 Konfidenz: {overall['confidence']:.1%}")

                # Klare Empfehlung
                if "KAUF" in overall['signal']:
                    action = "🔥 JETZT KAUFEN!"
                    reason = f"Ensemble sieht {overall['probability']:.1%} Chance auf Gewinn"
                elif "VERKAUF" in overall['signal']:
                    action = "🔻 JETZT VERKAUFEN!"
                    reason = f"Ensemble sieht {1-overall['probability']:.1%} Chance auf Verlust"
                else:
                    action = "⚖️ POSITION HALTEN"
                    reason = "Unklare Marktrichtung"

                print(f"\n💡 ULTIMATE EMPFEHLUNG: {action}")
                print(f"📝 Begründung: {reason}")
        else:
            print("\n❌ Keine Vorhersagen verfügbar")

        # Modell-Statistiken
        if self.models:
            print(f"\n📊 MODELL-PERFORMANCE:")
            for horizon_key, horizon_models in self.models.items():
                print(f"   {horizon_key}:")
                for model_name, model_data in horizon_models.items():
                    print(f"     {model_name}: {model_data['accuracy']:.1%}")

        print(f"\n⚡ ULTIMATE SYSTEM:")
        print(f"   🔄 Update-Intervall: {UPDATE_INTERVAL}s")
        print(f"   📊 Horizonte: {PREDICTION_HORIZONS}h")
        print(f"   💻 CPU: {MAX_CORES} Kerne (MAXIMAL)")
        print(f"   🎯 Features: ALLE verfügbaren")
        print("="*90)

def run_ultimate_all_features_prophet():
    """Hauptfunktion - ULTIMATE All-Features Trading-Prophet"""

    prophet = UltimateAllFeaturesProphet()
    iteration = 0

    print(f"\n🚀 STARTE ULTIMATE ALL-FEATURES TRADING PROPHET...")
    print(f"🔄 Kontinuierliches Training mit ALLEN Features alle {UPDATE_INTERVAL}s")

    while True:
        try:
            iteration += 1
            start_time = time.time()

            print(f"\n{'='*70}")
            print(f"🔄 ULTIMATE ITERATION {iteration} - {datetime.now().strftime('%H:%M:%S')}")
            print(f"{'='*70}")

            # 1. Erweiterte Daten sammeln
            print("📊 Sammle erweiterte Bitcoin-Daten...")
            df, is_real = prophet.get_enhanced_bitcoin_data()

            # 2. ULTIMATE Modelle trainieren
            print("🤖 Trainiere ULTIMATE Modelle mit ALLEN Features...")
            training_success = prophet.train_ultimate_models(df)

            if not training_success:
                print("❌ Training fehlgeschlagen - warte...")
                time.sleep(UPDATE_INTERVAL)
                continue

            # 3. ULTIMATE Signale vorhersagen
            print("🔮 Erstelle ULTIMATE Multi-Horizont Signale...")
            prediction_result = prophet.predict_ultimate_signals(df)

            # 4. ULTIMATE Dashboard anzeigen
            prophet.display_ultimate_dashboard(prediction_result)

            # 5. Timing
            elapsed_time = time.time() - start_time
            print(f"\n⚡ ULTIMATE Iteration {iteration} in {elapsed_time:.1f}s")

            # 6. Warten
            sleep_time = max(0, UPDATE_INTERVAL - elapsed_time)
            if sleep_time > 0:
                print(f"💤 Warte {sleep_time:.1f}s bis nächste ULTIMATE Iteration...")
                time.sleep(sleep_time)

        except KeyboardInterrupt:
            print(f"\n🛑 ULTIMATE ALL-FEATURES TRADING PROPHET gestoppt")
            break
        except Exception as e:
            print(f"❌ ULTIMATE Fehler: {e}")
            import traceback
            traceback.print_exc()
            time.sleep(UPDATE_INTERVAL)

if __name__ == "__main__":
    run_ultimate_all_features_prophet()
