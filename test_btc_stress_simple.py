#!/usr/bin/env python3
"""
VEREINFACHTER STRESS-TEST - Bitcoin Prediction Model
Testet Grenzen ohne externe Abhängigkeiten
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers.legacy import Adam
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, r2_score
import time
import gc
import warnings
warnings.filterwarnings('ignore')

print("💥 VEREINFACHTER STRESS-TEST - Bitcoin Prediction Model")
print("=" * 70)

class SimpleStressTester:
    def __init__(self):
        self.results = []
    
    def stress_test_data_sizes(self):
        """Teste verschiedene Datengrößen"""
        print("\n🔥 STRESS-TEST: Datengrößen")
        print("-" * 50)
        
        df = pd.read_csv('crypto_data.csv')
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        # Verschiedene Datengrößen
        data_sizes = [100, 300, 500, 1000, len(df)]
        
        for size in data_sizes:
            print(f"\n   📊 Teste mit {size} Datenpunkten...")
            
            try:
                start_time = time.time()
                
                # Daten begrenzen
                df_test = df.tail(size).copy()
                
                # Features erstellen
                features = self.create_features(df_test)
                
                if len(features) < 50:
                    print(f"      ⚠️  Zu wenig Daten nach Feature-Erstellung: {len(features)}")
                    continue
                
                # Modell trainieren
                success, metrics = self.train_test_model(features, epochs=10)
                
                end_time = time.time()
                
                if success:
                    result = {
                        'test_type': 'data_size',
                        'parameter': size,
                        'success': True,
                        'r2': metrics['r2'],
                        'rmse': metrics['rmse'],
                        'time': end_time - start_time,
                        'features': metrics['features'],
                        'samples': metrics['samples']
                    }
                    
                    print(f"      ✅ Erfolg: R² = {metrics['r2']:.4f}, RMSE = ${metrics['rmse']:.2f}")
                    print(f"         Zeit = {result['time']:.1f}s, Features = {metrics['features']}")
                    print(f"         Samples = {metrics['samples']}")
                else:
                    result = {
                        'test_type': 'data_size',
                        'parameter': size,
                        'success': False,
                        'time': end_time - start_time
                    }
                    print(f"      ❌ Fehlgeschlagen nach {result['time']:.1f}s")
                
                self.results.append(result)
                
                # Memory cleanup
                tf.keras.backend.clear_session()
                gc.collect()
                
            except Exception as e:
                print(f"      💥 Fehler: {e}")
                self.results.append({
                    'test_type': 'data_size',
                    'parameter': size,
                    'success': False,
                    'error': str(e)
                })
    
    def stress_test_model_sizes(self):
        """Teste verschiedene Modellgrößen"""
        print("\n🔥 STRESS-TEST: Modellgrößen")
        print("-" * 50)
        
        df = pd.read_csv('crypto_data.csv')
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        # Mittlere Datengröße für konsistente Tests
        features = self.create_features(df.tail(800))
        
        # Verschiedene LSTM-Größen
        lstm_sizes = [16, 32, 64, 128, 256]
        
        for lstm_size in lstm_sizes:
            print(f"\n   🤖 Teste LSTM mit {lstm_size} Units...")
            
            try:
                start_time = time.time()
                
                success, metrics = self.train_complex_model(features, lstm_size)
                
                end_time = time.time()
                
                if success:
                    result = {
                        'test_type': 'model_size',
                        'parameter': lstm_size,
                        'success': True,
                        'r2': metrics['r2'],
                        'rmse': metrics['rmse'],
                        'parameters': metrics['parameters'],
                        'time': end_time - start_time
                    }
                    
                    print(f"      ✅ Erfolg: R² = {metrics['r2']:.4f}, Parameter = {metrics['parameters']:,}")
                    print(f"         RMSE = ${metrics['rmse']:.2f}, Zeit = {result['time']:.1f}s")
                else:
                    result = {
                        'test_type': 'model_size',
                        'parameter': lstm_size,
                        'success': False,
                        'time': end_time - start_time
                    }
                    print(f"      ❌ Fehlgeschlagen nach {result['time']:.1f}s")
                
                self.results.append(result)
                
                # Memory cleanup
                tf.keras.backend.clear_session()
                gc.collect()
                
            except Exception as e:
                print(f"      💥 Fehler: {e}")
                self.results.append({
                    'test_type': 'model_size',
                    'parameter': lstm_size,
                    'success': False,
                    'error': str(e)
                })
    
    def stress_test_sequence_lengths(self):
        """Teste verschiedene Sequenzlängen"""
        print("\n🔥 STRESS-TEST: Sequenzlängen")
        print("-" * 50)
        
        df = pd.read_csv('crypto_data.csv')
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        features = self.create_features(df.tail(1000))
        
        # Verschiedene Look-Back Perioden
        look_backs = [6, 12, 24, 48, 72, 96]
        
        for look_back in look_backs:
            print(f"\n   📦 Teste Sequenzlänge {look_back}...")
            
            try:
                start_time = time.time()
                
                success, metrics = self.train_sequence_model(features, look_back)
                
                end_time = time.time()
                
                if success:
                    result = {
                        'test_type': 'sequence_length',
                        'parameter': look_back,
                        'success': True,
                        'r2': metrics['r2'],
                        'rmse': metrics['rmse'],
                        'sequences': metrics['sequences'],
                        'time': end_time - start_time
                    }
                    
                    print(f"      ✅ Erfolg: R² = {metrics['r2']:.4f}, Sequenzen = {metrics['sequences']}")
                    print(f"         RMSE = ${metrics['rmse']:.2f}, Zeit = {result['time']:.1f}s")
                else:
                    result = {
                        'test_type': 'sequence_length',
                        'parameter': look_back,
                        'success': False,
                        'time': end_time - start_time
                    }
                    print(f"      ❌ Fehlgeschlagen nach {result['time']:.1f}s")
                
                self.results.append(result)
                
                # Memory cleanup
                tf.keras.backend.clear_session()
                gc.collect()
                
            except Exception as e:
                print(f"      💥 Fehler: {e}")
                self.results.append({
                    'test_type': 'sequence_length',
                    'parameter': look_back,
                    'success': False,
                    'error': str(e)
                })
    
    def create_features(self, df):
        """Erstelle Features"""
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # Moving Averages
        features['sma_5'] = df['close'].rolling(5).mean()
        features['sma_10'] = df['close'].rolling(10).mean()
        features['sma_20'] = df['close'].rolling(20).mean()
        features['ema_12'] = df['close'].ewm(span=12).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        ema_26 = df['close'].ewm(span=26).mean()
        features['macd'] = features['ema_12'] - ema_26
        
        # Volatilität
        features['volatility'] = df['close'].pct_change().rolling(10).std()
        
        # Volume
        features['volume_sma'] = df['volume'].rolling(20).mean()
        features['volume_ratio'] = df['volume'] / features['volume_sma']
        
        return features.dropna()
    
    def create_sequences(self, data, target, look_back):
        """Erstelle Sequenzen"""
        X, y = [], []
        for i in range(look_back, len(data)):
            X.append(data[i-look_back:i])
            y.append(target[i])
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)
    
    def train_test_model(self, features, epochs=10):
        """Trainiere und teste Standard-Modell"""
        try:
            X = features.drop('close', axis=1)
            y = features['close'].values
            
            scaler_X = MinMaxScaler()
            scaler_y = MinMaxScaler()
            
            X_scaled = scaler_X.fit_transform(X)
            y_scaled = scaler_y.fit_transform(y.reshape(-1, 1)).flatten()
            
            # Sequenzen erstellen
            X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, 12)
            
            if len(X_seq) < 20:
                return False, None
            
            # Train-Test Split
            train_size = int(len(X_seq) * 0.8)
            X_train, X_test = X_seq[:train_size], X_seq[train_size:]
            y_train, y_test = y_seq[:train_size], y_seq[train_size:]
            
            # Modell
            model = Sequential([
                LSTM(32, input_shape=(X_train.shape[1], X_train.shape[2])),
                Dense(16, activation='relu'),
                Dense(1)
            ])
            
            model.compile(optimizer=Adam(0.01), loss='mse')
            
            # Training
            model.fit(X_train, y_train, epochs=epochs, batch_size=32, verbose=0)
            
            # Evaluation
            y_pred = model.predict(X_test, verbose=0)
            y_test_orig = scaler_y.inverse_transform(y_test.reshape(-1, 1)).flatten()
            y_pred_orig = scaler_y.inverse_transform(y_pred).flatten()
            
            r2 = r2_score(y_test_orig, y_pred_orig)
            rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
            
            return True, {
                'r2': r2,
                'rmse': rmse,
                'features': len(features.columns),
                'samples': len(X_test)
            }
            
        except Exception as e:
            return False, None
    
    def train_complex_model(self, features, lstm_size):
        """Trainiere komplexes Modell"""
        try:
            X = features.drop('close', axis=1)
            y = features['close'].values
            
            scaler_X = MinMaxScaler()
            scaler_y = MinMaxScaler()
            
            X_scaled = scaler_X.fit_transform(X)
            y_scaled = scaler_y.fit_transform(y.reshape(-1, 1)).flatten()
            
            X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, 24)
            
            if len(X_seq) < 20:
                return False, None
            
            train_size = int(len(X_seq) * 0.8)
            X_train, X_test = X_seq[:train_size], X_seq[train_size:]
            y_train, y_test = y_seq[:train_size], y_seq[train_size:]
            
            # Komplexes Modell
            model = Sequential([
                LSTM(lstm_size, return_sequences=True, dropout=0.2, input_shape=(X_train.shape[1], X_train.shape[2])),
                LSTM(lstm_size//2, return_sequences=False, dropout=0.2),
                Dense(lstm_size//4, activation='relu'),
                Dropout(0.3),
                Dense(1)
            ])
            
            model.compile(optimizer=Adam(0.001), loss='mse')
            
            # Training
            model.fit(X_train, y_train, epochs=15, batch_size=16, verbose=0)
            
            # Evaluation
            y_pred = model.predict(X_test, verbose=0)
            y_test_orig = scaler_y.inverse_transform(y_test.reshape(-1, 1)).flatten()
            y_pred_orig = scaler_y.inverse_transform(y_pred).flatten()
            
            r2 = r2_score(y_test_orig, y_pred_orig)
            rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
            
            return True, {
                'r2': r2,
                'rmse': rmse,
                'parameters': model.count_params()
            }
            
        except Exception as e:
            return False, None
    
    def train_sequence_model(self, features, look_back):
        """Trainiere Modell mit verschiedenen Sequenzlängen"""
        try:
            X = features.drop('close', axis=1)
            y = features['close'].values
            
            scaler_X = MinMaxScaler()
            scaler_y = MinMaxScaler()
            
            X_scaled = scaler_X.fit_transform(X)
            y_scaled = scaler_y.fit_transform(y.reshape(-1, 1)).flatten()
            
            X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, look_back)
            
            if len(X_seq) < 20:
                return False, None
            
            train_size = int(len(X_seq) * 0.8)
            X_train, X_test = X_seq[:train_size], X_seq[train_size:]
            y_train, y_test = y_seq[:train_size], y_seq[train_size:]
            
            # Modell
            model = Sequential([
                LSTM(64, input_shape=(X_train.shape[1], X_train.shape[2])),
                Dense(32, activation='relu'),
                Dense(1)
            ])
            
            model.compile(optimizer=Adam(0.001), loss='mse')
            
            # Training
            model.fit(X_train, y_train, epochs=12, batch_size=32, verbose=0)
            
            # Evaluation
            y_pred = model.predict(X_test, verbose=0)
            y_test_orig = scaler_y.inverse_transform(y_test.reshape(-1, 1)).flatten()
            y_pred_orig = scaler_y.inverse_transform(y_pred).flatten()
            
            r2 = r2_score(y_test_orig, y_pred_orig)
            rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
            
            return True, {
                'r2': r2,
                'rmse': rmse,
                'sequences': len(X_seq)
            }
            
        except Exception as e:
            return False, None
    
    def analyze_results(self):
        """Analysiere Stress-Test Ergebnisse"""
        print("\n" + "=" * 70)
        print("📊 STRESS-TEST ANALYSE")
        print("=" * 70)
        
        # Erfolgsrate pro Test-Typ
        test_types = ['data_size', 'model_size', 'sequence_length']
        
        for test_type in test_types:
            type_results = [r for r in self.results if r['test_type'] == test_type]
            if type_results:
                success_count = sum(1 for r in type_results if r['success'])
                total_count = len(type_results)
                success_rate = success_count / total_count * 100
                
                print(f"\n🔥 {test_type.upper().replace('_', ' ')}:")
                print(f"   Erfolgsrate: {success_count}/{total_count} ({success_rate:.1f}%)")
                
                # Beste Performance
                successful = [r for r in type_results if r['success']]
                if successful:
                    if 'r2' in successful[0]:
                        best = max(successful, key=lambda x: x['r2'])
                        print(f"   Beste R²: {best['r2']:.4f} (Parameter: {best['parameter']})")
                    
                    fastest = min(successful, key=lambda x: x['time'])
                    print(f"   Schnellste: {fastest['time']:.1f}s (Parameter: {fastest['parameter']})")
        
        # Gesamtstatistiken
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r['success'])
        
        print(f"\n📈 GESAMTSTATISTIKEN:")
        print(f"   Gesamte Tests: {total_tests}")
        print(f"   Erfolgreiche Tests: {successful_tests}")
        print(f"   Erfolgsrate: {successful_tests/total_tests*100:.1f}%")
        
        if successful_tests > 0:
            successful_results = [r for r in self.results if r['success']]
            avg_r2 = np.mean([r['r2'] for r in successful_results if 'r2' in r])
            avg_time = np.mean([r['time'] for r in successful_results])
            
            print(f"   Durchschnittliche R²: {avg_r2:.4f}")
            print(f"   Durchschnittliche Zeit: {avg_time:.1f}s")

def main():
    """Hauptfunktion für vereinfachten Stress-Test"""
    print(f"💻 System Info:")
    print(f"   TensorFlow Version: {tf.__version__}")
    print(f"   GPU verfügbar: {'Ja' if len(tf.config.experimental.list_physical_devices('GPU')) > 0 else 'Nein'}")
    
    tester = SimpleStressTester()
    
    # Alle Stress-Tests durchführen
    tester.stress_test_data_sizes()
    tester.stress_test_model_sizes()
    tester.stress_test_sequence_lengths()
    
    # Ergebnisse analysieren
    tester.analyze_results()
    
    print(f"\n✅ VEREINFACHTER STRESS-TEST ABGESCHLOSSEN!")
    print(f"   {len(tester.results)} Tests durchgeführt")

if __name__ == "__main__":
    main()
