#!/usr/bin/env python3
"""
🚀 FAST 48H BITCOIN FUTURE PREDICTION 🚀
=========================================
Optimiert für SCHNELLE echte 48h Vorhersagen (max 3 Minuten)
"""

import os
import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Ridge
import yfinance as yf

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

print("🚀 FAST 48H BITCOIN FUTURE PREDICTION")
print("=" * 42)
print(f"🕐 Aktuelle Zeit: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def get_bitcoin_data_fast():
    """Schnell Bitcoin-Daten laden"""
    print("📊 Lade Bitcoin-Daten...")
    
    try:
        btc = yf.Ticker("BTC-USD")
        df = btc.history(period="2mo", interval="1h")  # Reduziert auf 2 Monate
        
        if len(df) > 100:
            df.columns = [col.lower() for col in df.columns]
            print(f"✅ Echte Bitcoin-Daten: {len(df)} Stunden")
            print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
            return df, True
        else:
            raise Exception("Zu wenig Daten")
            
    except Exception as e:
        print(f"⚠️ Verwende lokale Daten...")
        
        # Schnelle Beispieldaten
        end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        start_time = end_time - timedelta(days=60)  # Reduziert auf 60 Tage
        dates = pd.date_range(start=start_time, end=end_time, freq='H')
        
        np.random.seed(42)
        base_price = 67000
        trend = np.linspace(-3000, 3000, len(dates))
        volatility = 1500 * np.random.normal(0, 1, len(dates))
        
        prices = base_price + trend + volatility
        prices = np.maximum(prices, 30000)
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * np.random.uniform(1.001, 1.02, len(dates)),
            'low': prices * np.random.uniform(0.98, 0.999, len(dates)),
            'open': prices * np.random.uniform(0.99, 1.01, len(dates)),
            'volume': np.random.lognormal(15, 0.2, len(dates))
        }, index=dates)
        
        print(f"✅ Beispieldaten: {len(df)} Stunden")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
        return df, False

def create_fast_features(df):
    """Schnelle, essenzielle Features"""
    print("🔧 Erstelle Fast Features...")
    
    df = df.copy()
    
    # Nur die wichtigsten Features für Geschwindigkeit
    # Moving Averages
    for window in [12, 24, 48]:
        df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
        df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
    
    # RSI
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0).rolling(window=14).mean()
    loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # MACD
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    df['macd'] = ema_12 - ema_26
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    
    # Volatilität
    df['volatility'] = df['close'].rolling(window=24).std()
    
    # Price Changes
    for period in [1, 6, 12, 24]:
        df[f'price_change_{period}'] = df['close'].pct_change(periods=period)
    
    # Lag Features (reduziert)
    for lag in [1, 2, 6, 12]:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
    
    # Time Features
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    
    print(f"✅ Features erstellt: {df.shape[1]} Spalten")
    return df.dropna()

def prepare_fast_data(df, sequence_length=48):  # Reduziert auf 48
    """Schnelle Datenvorbereitung"""
    print(f"🔄 Bereite Daten vor...")
    
    feature_cols = [col for col in df.columns if col != 'close']
    features = df[feature_cols].values
    target = df['close'].values
    
    feature_scaler = RobustScaler()
    target_scaler = MinMaxScaler()
    
    features_scaled = feature_scaler.fit_transform(features)
    target_scaled = target_scaler.fit_transform(target.reshape(-1, 1)).flatten()
    
    # Sequenzen erstellen
    X, y = [], []
    for i in range(sequence_length, len(features_scaled)):
        X.append(features_scaled[i-sequence_length:i])
        y.append(target_scaled[i])
    
    X, y = np.array(X), np.array(y)
    
    train_size = int(len(X) * 0.85)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]
    
    last_sequence = features_scaled[-sequence_length:]
    
    print(f"✅ Daten vorbereitet: Train={X_train.shape}, Test={X_test.shape}")
    
    return (X_train, y_train), (X_test, y_test), last_sequence, (feature_scaler, target_scaler)

def train_fast_models(train_data, test_data):
    """Nur die schnellsten, besten Modelle"""
    print("\n🚀 Trainiere Fast Models...")
    
    X_train, y_train = train_data
    X_test, y_test = test_data
    
    X_train_flat = X_train.reshape(X_train.shape[0], -1)
    X_test_flat = X_test.reshape(X_test.shape[0], -1)
    
    # Nur 2 schnelle, gute Modelle
    models = {
        'Ridge_Fast': Ridge(alpha=0.5),
        'RandomForest_Fast': RandomForestRegressor(
            n_estimators=50, max_depth=10, random_state=42, n_jobs=-1  # Reduziert
        )
    }
    
    results = {}
    
    for name, model in models.items():
        print(f"🤖 Trainiere {name}...")
        
        start_time = time.time()
        model.fit(X_train_flat, y_train)
        training_time = time.time() - start_time
        
        y_pred = model.predict(X_test_flat)
        
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        results[name] = {
            'model': model,
            'training_time': training_time,
            'r2': r2,
            'rmse': np.sqrt(mse),
            'y_pred': y_pred,
            'y_test': y_test
        }
        
        print(f"✅ {name}: R²={r2:.4f}, Zeit={training_time:.1f}s")
    
    return results

def predict_fast_48h(best_model, last_sequence, target_scaler, current_time, current_price):
    """SCHNELLE 48h Vorhersage mit reduzierten Simulationen"""
    print(f"🔮 Erstelle SCHNELLE 48h Vorhersage...")
    
    # Nur wichtige Zeitpunkte
    key_hours = [1, 3, 6, 12, 18, 24, 30, 36, 42, 48]
    n_simulations = 100  # Reduziert von 1000 auf 100
    
    predictions = {}
    
    for hour in key_hours:
        print(f"📈 +{hour}h...")
        
        # Schnelle Monte Carlo
        hour_predictions = []
        
        for sim in range(n_simulations):
            noise_level = 0.005 * (hour / 48)
            noisy_sequence = last_sequence + np.random.normal(0, noise_level, last_sequence.shape)
            current_sequence = noisy_sequence.copy()
            
            # Direkte Vorhersage ohne iterative Schritte für Geschwindigkeit
            for step in range(0, hour, 6):  # Springe in 6h Schritten
                pred_scaled = best_model.predict(current_sequence.reshape(1, -1))[0]
                
                new_row = current_sequence[-1].copy()
                new_row[0] = pred_scaled
                current_sequence = np.vstack([current_sequence[1:], new_row])
            
            final_pred_scaled = best_model.predict(current_sequence.reshape(1, -1))[0]
            hour_predictions.append(final_pred_scaled)
        
        # Zurück transformieren
        hour_predictions = np.array(hour_predictions)
        hour_predictions_orig = target_scaler.inverse_transform(hour_predictions.reshape(-1, 1)).flatten()
        
        predictions[hour] = {
            'datetime': current_time + timedelta(hours=hour),
            'mean': np.mean(hour_predictions_orig),
            'std': np.std(hour_predictions_orig),
            'q05': np.percentile(hour_predictions_orig, 5),
            'q95': np.percentile(hour_predictions_orig, 95),
            'prob_above_current': np.mean(hour_predictions_orig > current_price) * 100,
            'prob_below_current': np.mean(hour_predictions_orig < current_price) * 100,
            'change_pct': ((np.mean(hour_predictions_orig) / current_price) - 1) * 100
        }
    
    return predictions

def create_fast_visualization(df, results, predictions, current_time, current_price, is_real_data):
    """Schnelle, fokussierte Visualisierung"""
    print("📊 Erstelle Fast Visualisierung...")
    
    fig = plt.figure(figsize=(20, 12))
    fig.patch.set_facecolor('#0a0a0a')
    
    title = "🚀 FAST 48H BITCOIN ZUKUNFTSPROGNOSE"
    fig.suptitle(f'{title}\n📅 Ab: {current_time.strftime("%Y-%m-%d %H:%M")} | 💰 Aktuell: ${current_price:,.0f}', 
                 fontsize=18, color='white', fontweight='bold', y=0.95)
    
    # === 1. HAUPTCHART ===
    ax1 = plt.subplot2grid((3, 3), (0, 0), colspan=2, rowspan=2)
    
    # Historische Daten
    recent_data = df.tail(120)  # Letzte 5 Tage
    ax1.plot(recent_data.index, recent_data['close'], 
             color='#00D4FF', linewidth=3, label='Historisch', alpha=0.9)
    
    # Jetzt markieren
    ax1.axvline(x=current_time, color='#FF6B6B', linestyle='-', linewidth=3, 
                label='JETZT', alpha=0.9)
    
    # Zukunftsprognose
    future_times = [pred['datetime'] for pred in predictions.values()]
    future_means = [pred['mean'] for pred in predictions.values()]
    future_q05 = [pred['q05'] for pred in predictions.values()]
    future_q95 = [pred['q95'] for pred in predictions.values()]
    
    ax1.plot(future_times, future_means, color='#FFD700', linewidth=4, 
             label='48h Prognose', alpha=0.9, marker='o', markersize=6)
    ax1.fill_between(future_times, future_q05, future_q95, 
                     color='#FFD700', alpha=0.2, label='90% Konfidenz')
    
    ax1.set_title('Bitcoin: Vergangenheit → ZUKUNFT', fontsize=14, color='white', fontweight='bold')
    ax1.set_ylabel('Preis (USD)', color='white')
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(colors='white')
    
    # === 2. WAHRSCHEINLICHKEITS-TABELLE ===
    ax2 = plt.subplot2grid((3, 3), (0, 2), rowspan=2)
    ax2.axis('off')
    
    table_data = []
    headers = ['Zeit', 'Preis', 'Änderung', 'Wahrsch. ↑']
    
    for hour in [6, 12, 24, 48]:
        if hour in predictions:
            pred = predictions[hour]
            table_data.append([
                f"+{hour}h",
                f"${pred['mean']:,.0f}",
                f"{pred['change_pct']:+.1f}%",
                f"{pred['prob_above_current']:.0f}%"
            ])
    
    table = ax2.table(cellText=table_data, colLabels=headers, 
                     cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # Styling
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#333333')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    for i in range(1, len(table_data) + 1):
        for j in range(len(headers)):
            table[(i, j)].set_facecolor('#1a1a1a')
            table[(i, j)].set_text_props(color='white')
    
    ax2.set_title('Prognose-Details', color='white', fontweight='bold')
    
    # === 3. ZUSAMMENFASSUNG ===
    ax3 = plt.subplot2grid((3, 3), (2, 0), colspan=3)
    ax3.axis('off')
    
    best_model = max(results.keys(), key=lambda x: results[x]['r2'])
    pred_48h = predictions[48]
    
    # Trading-Empfehlung
    prob_up = pred_48h['prob_above_current']
    change_48h = pred_48h['change_pct']
    
    if prob_up > 65 and change_48h > 2:
        recommendation = "KAUF 📈"
        rec_color = "#00FF00"
    elif prob_up > 35:
        recommendation = "HALTEN ⚖️"
        rec_color = "#FFD700"
    else:
        recommendation = "VERKAUF 📉"
        rec_color = "#FF6B6B"
    
    summary_text = f"""
🚀 FAST 48H ZUKUNFTSPROGNOSE ZUSAMMENFASSUNG

📊 MODELL: {best_model.replace('_', ' ')} (R²: {results[best_model]['r2']:.3f})
📅 PROGNOSE AB: {current_time.strftime('%Y-%m-%d %H:%M')}

💰 AKTUELL: ${current_price:,.0f}  →  🔮 48H: ${pred_48h['mean']:,.0f} ({change_48h:+.1f}%)

📈 WAHRSCHEINLICHKEITEN:
   Preis steigt: {pred_48h['prob_above_current']:.0f}%  |  Preis fällt: {pred_48h['prob_below_current']:.0f}%

🎯 KONFIDENZ (90%): ${pred_48h['q05']:,.0f} - ${pred_48h['q95']:,.0f}
    """
    
    ax3.text(0.02, 0.8, summary_text, transform=ax3.transAxes, 
             fontsize=11, color='white', verticalalignment='top',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='#1a1a1a', alpha=0.9))
    
    # Empfehlung hervorheben
    ax3.text(0.75, 0.5, recommendation, transform=ax3.transAxes,
             fontsize=16, color=rec_color, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.5', facecolor=rec_color, alpha=0.2))
    
    plt.tight_layout()
    
    # Speichern
    os.makedirs('ultimate_plots', exist_ok=True)
    filename = 'fast_48h_future_prediction.png'
    plt.savefig(f'ultimate_plots/{filename}', 
                facecolor='#0a0a0a', dpi=300, bbox_inches='tight')
    
    print(f"✅ Visualisierung gespeichert: ultimate_plots/{filename}")
    plt.show()
    
    return fig

def main():
    """Hauptfunktion - OPTIMIERT FÜR GESCHWINDIGKEIT"""
    print("\n🚀" * 15)
    print("FAST 48H BITCOIN FUTURE PREDICTION")
    print("🚀" * 15)
    
    start_time = time.time()
    
    try:
        # 1. Daten laden (schnell)
        df, is_real_data = get_bitcoin_data_fast()
        current_time = df.index[-1]
        current_price = df['close'].iloc[-1]
        
        # 2. Features (reduziert)
        df_features = create_fast_features(df)
        
        # 3. Daten vorbereiten (schnell)
        train_data, test_data, last_sequence, scalers = prepare_fast_data(df_features)
        feature_scaler, target_scaler = scalers
        
        # 4. Modelle trainieren (nur 2 schnelle)
        results = train_fast_models(train_data, test_data)
        
        # 5. Bestes Modell
        best_model_name = max(results.keys(), key=lambda x: results[x]['r2'])
        best_model = results[best_model_name]['model']
        
        print(f"\n🏆 Bestes Modell: {best_model_name} (R²: {results[best_model_name]['r2']:.4f})")
        
        # 6. SCHNELLE 48h Vorhersage
        predictions = predict_fast_48h(best_model, last_sequence, target_scaler, current_time, current_price)
        
        # 7. Visualisierung
        fig = create_fast_visualization(df_features, results, predictions, current_time, current_price, is_real_data)
        
        # 8. Zusammenfassung
        total_time = time.time() - start_time
        
        print(f"\n🎉 FAST 48H ANALYSE ABGESCHLOSSEN in {total_time:.1f}s! 🎉")
        
        # Kurze Zusammenfassung
        pred_48h = predictions[48]
        print(f"\n💰 AKTUELL: ${current_price:,.0f}")
        print(f"🔮 48H PROGNOSE: ${pred_48h['mean']:,.0f} ({pred_48h['change_pct']:+.1f}%)")
        print(f"📈 WAHRSCHEINLICHKEIT STEIGT: {pred_48h['prob_above_current']:.0f}%")
        
        return {
            'results': results,
            'predictions': predictions,
            'current_time': current_time,
            'current_price': current_price,
            'total_time': total_time
        }
        
    except Exception as e:
        print(f"❌ Fehler: {e}")
        return None

if __name__ == "__main__":
    main()
