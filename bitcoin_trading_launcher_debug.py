#!/usr/bin/env python3
"""
🔧 BITCOIN TRADING LAUNCHER DEBUG - DETAILLIERTE FEHLERBEHEBUNG 🔧
================================================================
🏆 ERWEITERTE DIAGNOSE FÜR LAUNCHER-PROBLEME 🏆
✅ Detaillierte Fehlerbehebung und Status-Ausgabe
✅ Schritt-für-Schritt Diagnose aller möglichen Probleme
✅ Live-Debugging der Script-Starts
✅ Umfassende Umgebungs-Analyse
✅ Automatische Problemerkennung und Lösungsvorschläge
✅ Erweiterte Logging-Funktionalität

💡 DEBUG LAUNCHER - FINDET UND BEHEBT ALLE PROBLEME!
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import threading
import time
import os
import sys
import json
import platform
import psutil
from datetime import datetime
import traceback

class BitcoinTradingLauncherDebug:
    """
    🔧 BITCOIN TRADING LAUNCHER DEBUG
    ================================
    Erweiterte Diagnose-Version mit detaillierter
    Fehlerbehebung und Problemanalyse.
    """
    
    def __init__(self):
        # GUI SETUP
        self.root = tk.Tk()
        self.root.title("🔧 Bitcoin Trading Launcher DEBUG - Detaillierte Fehlerbehebung")
        self.root.geometry("1400x900")
        self.root.configure(bg='#1a1a1a')
        
        # DEBUG KONFIGURATION
        self.debug_mode = True
        self.verbose_logging = True
        self.auto_diagnose = True
        
        # DIE 3 BESTEN FUNKTIONIERENDEN MODELLE
        self.models = {
            'favorit': {
                'name': '🏅 FAVORIT - Das Bewährte System',
                'file': 'ultimate_complete_bitcoin_trading_FAVORITE.py',
                'description': 'Das bewährte System mit 100% Genauigkeit',
                'status': 'Unbekannt',
                'process': None,
                'working': False,
                'last_error': None,
                'start_attempts': 0,
                'recommended': True
            },
            'optimized': {
                'name': '🚀 OPTIMIERT - Das Schnelle System',
                'file': 'btc_ultimate_optimized_complete.py',
                'description': 'Das optimierte System für schnelle Analysen',
                'status': 'Unbekannt',
                'process': None,
                'working': False,
                'last_error': None,
                'start_attempts': 0,
                'recommended': False
            },
            'ai_system': {
                'name': '🧠 KI-SYSTEM - Das Intelligente System',
                'file': 'ultimate_self_learning_ai_bitcoin_trading.py',
                'description': 'Das revolutionäre KI-System mit Selbstlernen',
                'status': 'Unbekannt',
                'process': None,
                'working': False,
                'last_error': None,
                'start_attempts': 0,
                'recommended': False
            }
        }
        
        # LAUNCHER ZUSTAND
        self.running_processes = {}
        self.script_directory = os.getcwd()
        self.python_executable = sys.executable
        self.system_info = {}
        self.environment_checked = False
        
        # GUI KOMPONENTEN ERSTELLEN
        self.create_debug_gui()
        
        # SOFORTIGE SYSTEM-DIAGNOSE
        self.comprehensive_system_diagnosis()
        
        print("🔧 Bitcoin Trading Launcher DEBUG initialisiert")
        print("💡 Detaillierte Fehlerbehebung und Diagnose aktiv")
    
    def create_debug_gui(self):
        """Erstelle Debug-GUI"""
        
        # TITEL
        title_frame = tk.Frame(self.root, bg='#1a1a1a')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="🔧 Bitcoin Trading Launcher DEBUG",
            font=('Arial', 20, 'bold'),
            fg='#ff6600',
            bg='#1a1a1a'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="Detaillierte Fehlerbehebung • Erweiterte Diagnose • Problemlösung",
            font=('Arial', 12),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        subtitle_label.pack()
        
        # HAUPTBEREICH
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # SYSTEM-INFO BEREICH
        system_frame = tk.LabelFrame(
            main_frame,
            text="🖥️ System-Diagnose & Umgebung",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d',
            bd=2,
            relief=tk.RAISED
        )
        system_frame.pack(fill=tk.X, pady=(0, 10))
        
        # SYSTEM INFO BUTTONS
        system_buttons = tk.Frame(system_frame, bg='#2d2d2d')
        system_buttons.pack(fill=tk.X, padx=10, pady=10)
        
        diagnose_btn = tk.Button(
            system_buttons,
            text="🔍 VOLLSTÄNDIGE DIAGNOSE",
            command=self.comprehensive_system_diagnosis,
            font=('Arial', 10, 'bold'),
            bg='#ff6600',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        diagnose_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        env_btn = tk.Button(
            system_buttons,
            text="🌍 UMGEBUNG PRÜFEN",
            command=self.check_environment,
            font=('Arial', 10, 'bold'),
            bg='#0066cc',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        env_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        deps_btn = tk.Button(
            system_buttons,
            text="📦 ABHÄNGIGKEITEN",
            command=self.check_dependencies,
            font=('Arial', 10, 'bold'),
            bg='#6600cc',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        deps_btn.pack(side=tk.LEFT)
        
        # MODELLE BEREICH
        models_frame = tk.LabelFrame(
            main_frame,
            text="📊 Modelle - Erweiterte Diagnose",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d',
            bd=2,
            relief=tk.RAISED
        )
        models_frame.pack(fill=tk.X, pady=(0, 10))
        
        # MODELL BUTTONS ERSTELLEN
        for i, (key, model) in enumerate(self.models.items()):
            self.create_debug_model_button(models_frame, key, model, i)
        
        # DEBUG LOG BEREICH
        log_frame = tk.LabelFrame(
            main_frame,
            text="📋 Debug-Log & Detaillierte Fehlerbehebung",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#2d2d2d',
            bd=2,
            relief=tk.RAISED
        )
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # LOG KONTROLLEN
        log_controls = tk.Frame(log_frame, bg='#2d2d2d')
        log_controls.pack(fill=tk.X, padx=10, pady=10)
        
        clear_btn = tk.Button(
            log_controls,
            text="🗑️ LOG LÖSCHEN",
            command=self.clear_log,
            font=('Arial', 10, 'bold'),
            bg='#cc3333',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        clear_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        save_btn = tk.Button(
            log_controls,
            text="💾 LOG SPEICHERN",
            command=self.save_debug_log,
            font=('Arial', 10, 'bold'),
            bg='#00aa44',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        verbose_var = tk.BooleanVar(value=True)
        verbose_check = tk.Checkbutton(
            log_controls,
            text="🔍 Verbose Logging",
            variable=verbose_var,
            command=lambda: setattr(self, 'verbose_logging', verbose_var.get()),
            font=('Arial', 10),
            fg='#cccccc',
            bg='#2d2d2d',
            selectcolor='#2d2d2d'
        )
        verbose_check.pack(side=tk.RIGHT)
        
        # DEBUG LOG TEXT
        self.debug_log = scrolledtext.ScrolledText(
            log_frame,
            height=20,
            font=('Consolas', 9),
            bg='#1a1a1a',
            fg='#ff6600',
            insertbackground='#ff6600',
            wrap=tk.WORD
        )
        self.debug_log.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
    
    def create_debug_model_button(self, parent, key, model, index):
        """Erstelle Debug-Modell-Button"""
        
        # MODELL CONTAINER
        model_frame = tk.Frame(parent, bg='#2d2d2d', relief=tk.RAISED, bd=1)
        model_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # MODELL INFO
        info_frame = tk.Frame(model_frame, bg='#2d2d2d')
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # NAME UND STATUS
        header_frame = tk.Frame(info_frame, bg='#2d2d2d')
        header_frame.pack(fill=tk.X)
        
        name_label = tk.Label(
            header_frame,
            text=model['name'],
            font=('Arial', 11, 'bold'),
            fg='#00ff88' if model.get('recommended') else '#ffffff',
            bg='#2d2d2d'
        )
        name_label.pack(side=tk.LEFT)
        
        # STATUS LABEL
        status_label = tk.Label(
            header_frame,
            text=f"Status: {model['status']}",
            font=('Arial', 9),
            fg='#cccccc',
            bg='#2d2d2d'
        )
        status_label.pack(side=tk.RIGHT)
        model['status_label'] = status_label
        
        # DATEI UND VERSUCHE
        details_label = tk.Label(
            info_frame,
            text=f"📄 {model['file']} | Versuche: {model['start_attempts']}",
            font=('Arial', 8),
            fg='#cccccc',
            bg='#2d2d2d'
        )
        details_label.pack(anchor=tk.W, pady=(5, 0))
        model['details_label'] = details_label
        
        # BUTTON FRAME
        button_frame = tk.Frame(info_frame, bg='#2d2d2d')
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # DEBUG START BUTTON
        debug_start_btn = tk.Button(
            button_frame,
            text="🔧 DEBUG START",
            command=lambda k=key: self.debug_start_model(k),
            font=('Arial', 9, 'bold'),
            bg='#ff6600',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=6,
            cursor='hand2'
        )
        debug_start_btn.pack(side=tk.LEFT, padx=(0, 3))
        
        # NORMAL START BUTTON
        start_btn = tk.Button(
            button_frame,
            text="▶️ START",
            command=lambda k=key: self.start_model_normal(k),
            font=('Arial', 9, 'bold'),
            bg='#00aa44',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=6,
            cursor='hand2'
        )
        start_btn.pack(side=tk.LEFT, padx=(0, 3))
        
        # STOP BUTTON
        stop_btn = tk.Button(
            button_frame,
            text="⏹️ STOP",
            command=lambda k=key: self.stop_model_debug(k),
            font=('Arial', 9, 'bold'),
            bg='#cc3333',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=6,
            cursor='hand2'
        )
        stop_btn.pack(side=tk.LEFT, padx=(0, 3))
        
        # DIAGNOSE BUTTON
        diagnose_btn = tk.Button(
            button_frame,
            text="🔍 DIAGNOSE",
            command=lambda k=key: self.diagnose_model(k),
            font=('Arial', 9, 'bold'),
            bg='#0066cc',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=6,
            cursor='hand2'
        )
        diagnose_btn.pack(side=tk.LEFT)
        
        # SPEICHERE BUTTON REFERENZEN
        model['debug_start_btn'] = debug_start_btn
        model['start_btn'] = start_btn
        model['stop_btn'] = stop_btn
        model['diagnose_btn'] = diagnose_btn
    
    def debug_log_message(self, message, level="INFO"):
        """Füge Debug-Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        # Level-spezifische Farben
        colors = {
            "INFO": "#00ff88",
            "WARNING": "#ffaa00", 
            "ERROR": "#ff4444",
            "DEBUG": "#88aaff",
            "SUCCESS": "#44ff44"
        }
        
        color = colors.get(level, "#ffffff")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        # Füge zum GUI-Log hinzu
        self.debug_log.insert(tk.END, log_entry)
        self.debug_log.see(tk.END)
        
        # Auch in Konsole ausgeben
        print(log_entry.strip())
        
        # Update GUI
        self.root.update()
    
    def comprehensive_system_diagnosis(self):
        """Führe umfassende System-Diagnose durch"""
        self.debug_log_message("🔍 STARTE UMFASSENDE SYSTEM-DIAGNOSE...", "INFO")
        
        try:
            # SYSTEM-INFORMATIONEN
            self.debug_log_message("📊 Sammle System-Informationen...", "DEBUG")
            
            self.system_info = {
                'platform': platform.platform(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'python_version': sys.version,
                'python_executable': sys.executable,
                'current_directory': os.getcwd(),
                'script_directory': self.script_directory,
                'user': os.getenv('USERNAME', 'Unknown'),
                'path_env': os.getenv('PATH', ''),
                'pythonpath': os.getenv('PYTHONPATH', 'Not set')
            }
            
            # SYSTEM-INFO AUSGEBEN
            self.debug_log_message(f"🖥️ System: {self.system_info['system']} {self.system_info['release']}", "INFO")
            self.debug_log_message(f"🐍 Python: {self.system_info['python_version'].split()[0]}", "INFO")
            self.debug_log_message(f"📁 Arbeitsverzeichnis: {self.system_info['current_directory']}", "INFO")
            self.debug_log_message(f"👤 Benutzer: {self.system_info['user']}", "INFO")
            
            # SPEICHER UND CPU
            try:
                memory = psutil.virtual_memory()
                cpu_count = psutil.cpu_count()
                self.debug_log_message(f"💾 RAM: {memory.total // (1024**3)} GB (verfügbar: {memory.available // (1024**3)} GB)", "INFO")
                self.debug_log_message(f"⚡ CPU: {cpu_count} Kerne", "INFO")
            except:
                self.debug_log_message("⚠️ Konnte Speicher/CPU-Info nicht abrufen", "WARNING")
            
            # PYTHON-UMGEBUNG PRÜFEN
            self.debug_log_message("🐍 Prüfe Python-Umgebung...", "DEBUG")
            
            # Python-Pfad prüfen
            if os.path.exists(self.python_executable):
                self.debug_log_message(f"✅ Python-Executable gefunden: {self.python_executable}", "SUCCESS")
            else:
                self.debug_log_message(f"❌ Python-Executable nicht gefunden: {self.python_executable}", "ERROR")
            
            # ARBEITSVERZEICHNIS PRÜFEN
            self.debug_log_message("📁 Prüfe Arbeitsverzeichnis...", "DEBUG")
            
            if os.path.exists(self.script_directory):
                self.debug_log_message(f"✅ Script-Verzeichnis existiert: {self.script_directory}", "SUCCESS")
                
                # Dateien im Verzeichnis auflisten
                files = os.listdir(self.script_directory)
                py_files = [f for f in files if f.endswith('.py')]
                self.debug_log_message(f"📄 Python-Dateien gefunden: {len(py_files)}", "INFO")
                
            else:
                self.debug_log_message(f"❌ Script-Verzeichnis nicht gefunden: {self.script_directory}", "ERROR")
            
            # MODELL-DATEIEN PRÜFEN
            self.debug_log_message("📊 Prüfe Modell-Dateien...", "DEBUG")
            
            for key, model in self.models.items():
                file_path = os.path.join(self.script_directory, model['file'])
                
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    self.debug_log_message(f"✅ {model['file']}: {file_size:,} Bytes", "SUCCESS")
                    model['working'] = True
                    model['status'] = 'Verfügbar'
                else:
                    self.debug_log_message(f"❌ {model['file']}: Nicht gefunden", "ERROR")
                    model['working'] = False
                    model['status'] = 'Nicht gefunden'
                
                # Update GUI
                if 'status_label' in model:
                    model['status_label'].config(text=f"Status: {model['status']}")
            
            self.debug_log_message("✅ System-Diagnose abgeschlossen", "SUCCESS")
            self.environment_checked = True
            
        except Exception as e:
            self.debug_log_message(f"❌ Fehler bei System-Diagnose: {e}", "ERROR")
            self.debug_log_message(f"🔍 Traceback: {traceback.format_exc()}", "DEBUG")
    
    def check_environment(self):
        """Prüfe Umgebungsvariablen und Konfiguration"""
        self.debug_log_message("🌍 PRÜFE UMGEBUNGSVARIABLEN...", "INFO")
        
        try:
            # Wichtige Umgebungsvariablen
            important_vars = ['PATH', 'PYTHONPATH', 'PYTHON', 'USERNAME', 'USERPROFILE', 'TEMP']
            
            for var in important_vars:
                value = os.getenv(var, 'Nicht gesetzt')
                if len(value) > 100:
                    value = value[:100] + "..."
                self.debug_log_message(f"🔧 {var}: {value}", "DEBUG")
            
            # Python-Module prüfen
            self.debug_log_message("📦 Prüfe wichtige Python-Module...", "DEBUG")
            
            required_modules = ['tkinter', 'subprocess', 'threading', 'os', 'sys', 'json', 'datetime']
            optional_modules = ['pandas', 'numpy', 'matplotlib', 'sklearn', 'tensorflow', 'psutil']
            
            for module in required_modules:
                try:
                    __import__(module)
                    self.debug_log_message(f"✅ {module}: Verfügbar", "SUCCESS")
                except ImportError:
                    self.debug_log_message(f"❌ {module}: FEHLT (KRITISCH!)", "ERROR")
            
            for module in optional_modules:
                try:
                    __import__(module)
                    self.debug_log_message(f"✅ {module}: Verfügbar", "SUCCESS")
                except ImportError:
                    self.debug_log_message(f"⚠️ {module}: Nicht verfügbar (optional)", "WARNING")
            
            self.debug_log_message("✅ Umgebungsprüfung abgeschlossen", "SUCCESS")
            
        except Exception as e:
            self.debug_log_message(f"❌ Fehler bei Umgebungsprüfung: {e}", "ERROR")
    
    def check_dependencies(self):
        """Prüfe Abhängigkeiten der Modelle"""
        self.debug_log_message("📦 PRÜFE MODELL-ABHÄNGIGKEITEN...", "INFO")
        
        try:
            # Teste subprocess-Funktionalität
            self.debug_log_message("🔧 Teste subprocess-Funktionalität...", "DEBUG")
            
            test_result = subprocess.run(
                [self.python_executable, '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if test_result.returncode == 0:
                self.debug_log_message(f"✅ subprocess funktioniert: {test_result.stdout.strip()}", "SUCCESS")
            else:
                self.debug_log_message(f"❌ subprocess-Fehler: {test_result.stderr}", "ERROR")
            
            # Teste Threading
            self.debug_log_message("🧵 Teste Threading-Funktionalität...", "DEBUG")
            
            def test_thread():
                time.sleep(0.1)
                self.debug_log_message("✅ Threading funktioniert", "SUCCESS")
            
            thread = threading.Thread(target=test_thread, daemon=True)
            thread.start()
            thread.join(timeout=1)
            
            if thread.is_alive():
                self.debug_log_message("⚠️ Threading-Timeout", "WARNING")
            
            self.debug_log_message("✅ Abhängigkeitsprüfung abgeschlossen", "SUCCESS")
            
        except Exception as e:
            self.debug_log_message(f"❌ Fehler bei Abhängigkeitsprüfung: {e}", "ERROR")
    
    def diagnose_model(self, model_key):
        """Führe detaillierte Modell-Diagnose durch"""
        if model_key not in self.models:
            self.debug_log_message(f"❌ Ungültiges Modell: {model_key}", "ERROR")
            return
        
        model = self.models[model_key]
        self.debug_log_message(f"🔍 DIAGNOSE: {model['name']}", "INFO")
        
        try:
            file_path = os.path.join(self.script_directory, model['file'])
            
            # Datei-Existenz prüfen
            if not os.path.exists(file_path):
                self.debug_log_message(f"❌ Datei nicht gefunden: {file_path}", "ERROR")
                model['status'] = 'Datei fehlt'
                model['working'] = False
                return
            
            # Datei-Eigenschaften
            file_size = os.path.getsize(file_path)
            file_mtime = os.path.getmtime(file_path)
            file_date = datetime.fromtimestamp(file_mtime).strftime("%Y-%m-%d %H:%M:%S")
            
            self.debug_log_message(f"📄 Datei: {model['file']}", "DEBUG")
            self.debug_log_message(f"📊 Größe: {file_size:,} Bytes", "DEBUG")
            self.debug_log_message(f"📅 Geändert: {file_date}", "DEBUG")
            
            # Syntax-Prüfung
            self.debug_log_message("🔍 Prüfe Python-Syntax...", "DEBUG")
            
            syntax_result = subprocess.run(
                [self.python_executable, '-m', 'py_compile', file_path],
                capture_output=True,
                text=True,
                timeout=30,
                cwd=self.script_directory
            )
            
            if syntax_result.returncode == 0:
                self.debug_log_message("✅ Syntax-Prüfung erfolgreich", "SUCCESS")
                model['status'] = 'Syntax OK'
            else:
                self.debug_log_message(f"❌ Syntax-Fehler: {syntax_result.stderr}", "ERROR")
                model['status'] = 'Syntax-Fehler'
                model['working'] = False
                return
            
            # Import-Test
            self.debug_log_message("📦 Teste Imports...", "DEBUG")
            
            import_test_script = f'''
import sys
import os
sys.path.insert(0, r"{self.script_directory}")

try:
    # Teste grundlegende Imports
    import pandas as pd
    import numpy as np
    print("SUCCESS: Grundlegende Imports funktionieren")
except ImportError as e:
    print(f"WARNING: Import-Problem: {{e}}")
except Exception as e:
    print(f"ERROR: Unerwarteter Fehler: {{e}}")

print("IMPORT_TEST_COMPLETE")
'''
            
            import_result = subprocess.run(
                [self.python_executable, '-c', import_test_script],
                capture_output=True,
                text=True,
                timeout=30,
                cwd=self.script_directory
            )
            
            if 'SUCCESS' in import_result.stdout:
                self.debug_log_message("✅ Import-Test erfolgreich", "SUCCESS")
                model['status'] = 'Bereit'
                model['working'] = True
            elif 'WARNING' in import_result.stdout:
                self.debug_log_message("⚠️ Import-Warnungen vorhanden", "WARNING")
                model['status'] = 'Bereit (Warnungen)'
                model['working'] = True
            else:
                self.debug_log_message(f"❌ Import-Fehler: {import_result.stderr}", "ERROR")
                model['status'] = 'Import-Fehler'
                model['working'] = False
            
            # Update GUI
            if 'status_label' in model:
                model['status_label'].config(text=f"Status: {model['status']}")
            
            self.debug_log_message(f"✅ Diagnose abgeschlossen: {model['name']}", "SUCCESS")
            
        except Exception as e:
            self.debug_log_message(f"❌ Diagnose-Fehler für {model['name']}: {e}", "ERROR")
            model['status'] = 'Diagnose-Fehler'
            model['working'] = False

    def debug_start_model(self, model_key):
        """Starte Modell mit detailliertem Debug-Modus"""
        if model_key not in self.models:
            self.debug_log_message(f"❌ Ungültiges Modell: {model_key}", "ERROR")
            return

        model = self.models[model_key]
        model['start_attempts'] += 1

        self.debug_log_message(f"🔧 DEBUG START: {model['name']} (Versuch #{model['start_attempts']})", "INFO")

        try:
            # PRE-START DIAGNOSE
            self.debug_log_message("🔍 Pre-Start Diagnose...", "DEBUG")

            # Prüfe ob bereits läuft
            if model_key in self.running_processes:
                self.debug_log_message(f"⚠️ Modell läuft bereits (PID: {self.running_processes[model_key].pid})", "WARNING")
                return

            # Datei-Prüfung
            file_path = os.path.join(self.script_directory, model['file'])
            if not os.path.exists(file_path):
                self.debug_log_message(f"❌ Datei nicht gefunden: {file_path}", "ERROR")
                model['last_error'] = "Datei nicht gefunden"
                return

            self.debug_log_message(f"✅ Datei gefunden: {file_path}", "SUCCESS")

            # Python-Executable prüfen
            if not os.path.exists(self.python_executable):
                self.debug_log_message(f"❌ Python-Executable nicht gefunden: {self.python_executable}", "ERROR")
                model['last_error'] = "Python nicht gefunden"
                return

            self.debug_log_message(f"✅ Python-Executable: {self.python_executable}", "SUCCESS")

            # Arbeitsverzeichnis prüfen
            if not os.path.exists(self.script_directory):
                self.debug_log_message(f"❌ Arbeitsverzeichnis nicht gefunden: {self.script_directory}", "ERROR")
                model['last_error'] = "Arbeitsverzeichnis nicht gefunden"
                return

            self.debug_log_message(f"✅ Arbeitsverzeichnis: {self.script_directory}", "SUCCESS")

            # SUBPROCESS PARAMETER VORBEREITEN
            self.debug_log_message("🔧 Bereite subprocess-Parameter vor...", "DEBUG")

            cmd = [self.python_executable, model['file']]
            self.debug_log_message(f"📋 Kommando: {' '.join(cmd)}", "DEBUG")

            # Erweiterte subprocess-Parameter
            popen_kwargs = {
                'stdout': subprocess.PIPE,
                'stderr': subprocess.PIPE,
                'text': True,
                'cwd': self.script_directory,
                'bufsize': 1,
                'universal_newlines': True
            }

            # Windows-spezifische Parameter
            if os.name == 'nt':
                popen_kwargs['creationflags'] = subprocess.CREATE_NEW_PROCESS_GROUP
                self.debug_log_message("🪟 Windows-spezifische Flags gesetzt", "DEBUG")

            self.debug_log_message(f"⚙️ subprocess-Parameter: {popen_kwargs}", "DEBUG")

            # PROZESS STARTEN
            self.debug_log_message("🚀 Starte Prozess...", "INFO")

            process = subprocess.Popen(cmd, **popen_kwargs)

            # Prozess-Informationen
            self.debug_log_message(f"✅ Prozess gestartet - PID: {process.pid}", "SUCCESS")
            self.debug_log_message(f"📊 Prozess-Status: {process.poll()}", "DEBUG")

            # Prozess registrieren
            self.running_processes[model_key] = process
            model['process'] = process
            model['status'] = 'Läuft'
            model['last_error'] = None

            # GUI Update
            if 'status_label' in model:
                model['status_label'].config(text=f"Status: {model['status']}")
            if 'details_label' in model:
                model['details_label'].config(text=f"📄 {model['file']} | Versuche: {model['start_attempts']} | PID: {process.pid}")

            # MONITORING THREAD STARTEN
            self.debug_log_message("🔍 Starte Monitoring-Thread...", "DEBUG")

            monitor_thread = threading.Thread(
                target=self.debug_monitor_model,
                args=(model_key, process),
                daemon=True,
                name=f"Monitor-{model_key}"
            )
            monitor_thread.start()

            self.debug_log_message(f"✅ Monitoring-Thread gestartet: {monitor_thread.name}", "SUCCESS")
            self.debug_log_message(f"🎉 DEBUG START ERFOLGREICH: {model['name']}", "SUCCESS")

        except Exception as e:
            self.debug_log_message(f"❌ DEBUG START FEHLER: {e}", "ERROR")
            self.debug_log_message(f"🔍 Traceback: {traceback.format_exc()}", "DEBUG")

            model['last_error'] = str(e)
            model['status'] = 'Start-Fehler'

            # GUI Update
            if 'status_label' in model:
                model['status_label'].config(text=f"Status: {model['status']}")

    def start_model_normal(self, model_key):
        """Starte Modell normal (ohne Debug)"""
        if model_key not in self.models:
            self.debug_log_message(f"❌ Ungültiges Modell: {model_key}", "ERROR")
            return

        model = self.models[model_key]
        model['start_attempts'] += 1

        self.debug_log_message(f"▶️ NORMAL START: {model['name']}", "INFO")

        try:
            # Basis-Prüfungen
            if model_key in self.running_processes:
                self.debug_log_message(f"⚠️ Modell läuft bereits", "WARNING")
                return

            file_path = os.path.join(self.script_directory, model['file'])
            if not os.path.exists(file_path):
                self.debug_log_message(f"❌ Datei nicht gefunden: {file_path}", "ERROR")
                return

            # Einfacher Start
            process = subprocess.Popen(
                [self.python_executable, model['file']],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.script_directory
            )

            self.running_processes[model_key] = process
            model['process'] = process
            model['status'] = 'Läuft'

            self.debug_log_message(f"✅ Normal gestartet - PID: {process.pid}", "SUCCESS")

            # Einfaches Monitoring
            monitor_thread = threading.Thread(
                target=self.simple_monitor_model,
                args=(model_key, process),
                daemon=True
            )
            monitor_thread.start()

        except Exception as e:
            self.debug_log_message(f"❌ Normal Start Fehler: {e}", "ERROR")
            model['last_error'] = str(e)
            model['status'] = 'Start-Fehler'

    def debug_monitor_model(self, model_key, process):
        """Überwache Modell mit detailliertem Debug"""
        model = self.models[model_key]

        self.debug_log_message(f"🔍 Debug-Monitoring gestartet für {model['name']}", "DEBUG")

        try:
            start_time = time.time()
            output_lines = 0

            # Live-Output lesen
            while process.poll() is None:
                try:
                    # Stdout lesen
                    if process.stdout.readable():
                        line = process.stdout.readline()
                        if line:
                            output_lines += 1
                            line_clean = line.strip()
                            if line_clean:
                                self.root.after(0, lambda msg=line_clean:
                                               self.debug_log_message(f"📊 {model['name']}: {msg}", "INFO"))

                    # Stderr lesen
                    if process.stderr.readable():
                        error_line = process.stderr.readline()
                        if error_line:
                            error_clean = error_line.strip()
                            if error_clean:
                                self.root.after(0, lambda msg=error_clean:
                                               self.debug_log_message(f"⚠️ {model['name']} ERROR: {msg}", "WARNING"))

                    time.sleep(0.1)

                    # Timeout-Prüfung (nach 5 Minuten)
                    if time.time() - start_time > 300:
                        self.root.after(0, lambda:
                                       self.debug_log_message(f"⏰ {model['name']}: 5-Minuten-Timeout erreicht", "WARNING"))
                        break

                except Exception as e:
                    self.root.after(0, lambda err=str(e):
                                   self.debug_log_message(f"❌ Monitor-Lesefehler: {err}", "ERROR"))
                    break

            # Prozess beendet - finale Ausgabe
            try:
                stdout, stderr = process.communicate(timeout=10)

                if stdout:
                    for line in stdout.split('\n')[-10:]:  # Letzte 10 Zeilen
                        if line.strip():
                            self.root.after(0, lambda msg=line.strip():
                                           self.debug_log_message(f"📊 {model['name']} FINAL: {msg}", "INFO"))

                if stderr:
                    for line in stderr.split('\n')[-5:]:  # Letzte 5 Fehlerzeilen
                        if line.strip():
                            self.root.after(0, lambda msg=line.strip():
                                           self.debug_log_message(f"❌ {model['name']} FINAL ERROR: {msg}", "ERROR"))

            except subprocess.TimeoutExpired:
                self.root.after(0, lambda:
                               self.debug_log_message(f"⏰ {model['name']}: Timeout beim Lesen der finalen Ausgabe", "WARNING"))

            # Cleanup
            if model_key in self.running_processes:
                del self.running_processes[model_key]

            # Finale Status-Updates
            runtime = time.time() - start_time
            return_code = process.returncode

            self.root.after(0, lambda: self.debug_model_finished(model_key, return_code, runtime, output_lines))

        except Exception as e:
            self.root.after(0, lambda err=str(e):
                           self.debug_log_message(f"❌ Debug-Monitor Fehler für {model['name']}: {err}", "ERROR"))

    def simple_monitor_model(self, model_key, process):
        """Einfaches Modell-Monitoring"""
        model = self.models[model_key]

        try:
            stdout, stderr = process.communicate()

            # Cleanup
            if model_key in self.running_processes:
                del self.running_processes[model_key]

            # Status-Update
            self.root.after(0, lambda: self.simple_model_finished(model_key, process.returncode))

        except Exception as e:
            self.root.after(0, lambda err=str(e):
                           self.debug_log_message(f"❌ Simple Monitor Fehler: {err}", "ERROR"))

    def debug_model_finished(self, model_key, return_code, runtime, output_lines):
        """Modell beendet - Debug-Auswertung"""
        model = self.models[model_key]

        self.debug_log_message(f"🏁 {model['name']} BEENDET", "INFO")
        self.debug_log_message(f"📊 Return Code: {return_code}", "DEBUG")
        self.debug_log_message(f"⏱️ Laufzeit: {runtime:.1f}s", "DEBUG")
        self.debug_log_message(f"📄 Output-Zeilen: {output_lines}", "DEBUG")

        # Status-Update
        if return_code == 0:
            model['status'] = 'Erfolgreich beendet'
            self.debug_log_message(f"✅ {model['name']} erfolgreich beendet", "SUCCESS")
        else:
            model['status'] = f'Fehler (Code: {return_code})'
            self.debug_log_message(f"❌ {model['name']} mit Fehler beendet", "ERROR")

        model['process'] = None

        # GUI Update
        if 'status_label' in model:
            model['status_label'].config(text=f"Status: {model['status']}")
        if 'details_label' in model:
            model['details_label'].config(text=f"📄 {model['file']} | Versuche: {model['start_attempts']} | Laufzeit: {runtime:.1f}s")

    def simple_model_finished(self, model_key, return_code):
        """Modell beendet - Einfache Auswertung"""
        model = self.models[model_key]

        if return_code == 0:
            model['status'] = 'Beendet'
            self.debug_log_message(f"✅ {model['name']} beendet", "SUCCESS")
        else:
            model['status'] = f'Fehler (Code: {return_code})'
            self.debug_log_message(f"❌ {model['name']} Fehler", "ERROR")

        model['process'] = None

        # GUI Update
        if 'status_label' in model:
            model['status_label'].config(text=f"Status: {model['status']}")

    def stop_model_debug(self, model_key):
        """Stoppe Modell mit Debug-Ausgabe"""
        if model_key not in self.running_processes:
            self.debug_log_message(f"⚠️ Modell {model_key} läuft nicht", "WARNING")
            return

        model = self.models[model_key]
        process = self.running_processes[model_key]

        self.debug_log_message(f"⏹️ Stoppe {model['name']} (PID: {process.pid})", "INFO")

        try:
            # Graceful termination
            process.terminate()

            try:
                process.wait(timeout=10)
                self.debug_log_message(f"✅ {model['name']} ordnungsgemäß beendet", "SUCCESS")
            except subprocess.TimeoutExpired:
                self.debug_log_message(f"⏰ Timeout - zwangsbeende {model['name']}", "WARNING")
                process.kill()
                process.wait()
                self.debug_log_message(f"🔨 {model['name']} zwangsbeendet", "WARNING")

            # Cleanup
            del self.running_processes[model_key]
            model['process'] = None
            model['status'] = 'Gestoppt'

            # GUI Update
            if 'status_label' in model:
                model['status_label'].config(text=f"Status: {model['status']}")

        except Exception as e:
            self.debug_log_message(f"❌ Fehler beim Stoppen: {e}", "ERROR")

    def clear_log(self):
        """Lösche Debug-Log"""
        self.debug_log.delete(1.0, tk.END)
        self.debug_log_message("🗑️ Debug-Log gelöscht", "INFO")

    def save_debug_log(self):
        """Speichere Debug-Log"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"bitcoin_launcher_debug_{timestamp}.log"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.debug_log.get(1.0, tk.END))

            self.debug_log_message(f"💾 Debug-Log gespeichert: {filename}", "SUCCESS")

        except Exception as e:
            self.debug_log_message(f"❌ Fehler beim Speichern: {e}", "ERROR")

    def run(self):
        """Starte Debug-Launcher"""
        self.debug_log_message("🔧 Bitcoin Trading Launcher DEBUG bereit!", "SUCCESS")
        self.debug_log_message("💡 Detaillierte Fehlerbehebung und Diagnose aktiv", "INFO")
        self.debug_log_message("🎯 Verwenden Sie DEBUG START für detaillierte Analyse", "INFO")
        self.root.mainloop()

def main():
    """Hauptfunktion"""
    try:
        app = BitcoinTradingLauncherDebug()
        app.run()
    except Exception as e:
        print(f"❌ Debug-Launcher Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
