#!/usr/bin/env python3
"""
FINAL ULTIMATE BITCOIN PREDICTION MODEL
Robuste Version mit maximaler Performance und Genauigkeit
Behebt alle TensorFlow-Probleme
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, Bidirectional, BatchNormalization
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers.legacy import Adam, RMSprop  # Legacy Optimizers verwenden
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso
import time
import os
import warnings
warnings.filterwarnings('ignore')

# MAXIMALE HARDWARE-AUSLASTUNG
print(f"🚀 FINAL ULTIMATE BITCOIN PREDICTION MODEL!")
print(f"💻 CPU-Kerne: {os.cpu_count()}")

# GPU-Konfiguration
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"🎮 GPU gefunden: {len(gpus)} GPU(s) aktiviert!")
    except RuntimeError as e:
        print(f"GPU-Konfiguration: {e}")
else:
    print("💻 Nur CPU verfügbar - maximiere CPU-Performance!")

# CPU-Optimierung
tf.config.threading.set_intra_op_parallelism_threads(0)
tf.config.threading.set_inter_op_parallelism_threads(0)
os.environ['OMP_NUM_THREADS'] = str(os.cpu_count())

# FINALE KONFIGURATION
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.75,
    'look_back': 48,
    'batch_size': 64,
    'epochs': 100,
    'patience': 15,
    'monte_carlo_sims': 1000,
    'future_hours': [6, 12, 24, 48, 72]
}

def load_and_create_ultimate_features():
    """Ultimate Feature-Engineering"""
    print("📊 Erstelle Ultimate Feature-Set...")
    
    df = pd.read_csv(CONFIG['data_file'])
    df['time'] = pd.to_datetime(df['time'])
    df.set_index('time', inplace=True)
    
    print(f"✅ {len(df)} Datenpunkte geladen")
    print(f"   Zeitraum: {df.index[0]} bis {df.index[-1]}")
    print(f"   Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
    
    # Basis OHLCV
    features = df[['open', 'high', 'low', 'close', 'volume']].copy()
    
    # === MOVING AVERAGES ===
    ma_periods = [5, 10, 14, 20, 21, 26, 50, 100]
    for period in ma_periods:
        if period <= len(df):
            features[f'sma_{period}'] = df['close'].rolling(period).mean()
            features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
    
    # === MACD FAMILIE ===
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    features['macd'] = ema_12 - ema_26
    features['macd_signal'] = features['macd'].ewm(span=9).mean()
    features['macd_histogram'] = features['macd'] - features['macd_signal']
    features['macd_slope'] = features['macd'].diff()
    
    # === RSI FAMILIE ===
    for period in [14, 21]:
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
        rs = gain / loss
        features[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        features[f'rsi_{period}_slope'] = features[f'rsi_{period}'].diff()
    
    # === BOLLINGER BANDS ===
    for period in [20, 50]:
        sma = df['close'].rolling(period).mean()
        std = df['close'].rolling(period).std()
        features[f'bb_upper_{period}'] = sma + (std * 2)
        features[f'bb_lower_{period}'] = sma - (std * 2)
        features[f'bb_width_{period}'] = (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}']) / sma
        features[f'bb_position_{period}'] = (df['close'] - features[f'bb_lower_{period}']) / (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}'])
    
    # === STOCHASTIC ===
    for period in [14, 21]:
        low_min = df['low'].rolling(period).min()
        high_max = df['high'].rolling(period).max()
        features[f'stoch_k_{period}'] = 100 * ((df['close'] - low_min) / (high_max - low_min))
        features[f'stoch_d_{period}'] = features[f'stoch_k_{period}'].rolling(3).mean()
    
    # === ATR ===
    high_low = df['high'] - df['low']
    high_close = (df['high'] - df['close'].shift()).abs()
    low_close = (df['low'] - df['close'].shift()).abs()
    true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    features['atr'] = true_range.rolling(14).mean()
    features['atr_percent'] = features['atr'] / df['close'] * 100
    
    # === VOLUMEN INDIKATOREN ===
    # OBV
    obv = [0]
    for i in range(1, len(df)):
        if df['close'].iloc[i] > df['close'].iloc[i-1]:
            obv.append(obv[-1] + df['volume'].iloc[i])
        elif df['close'].iloc[i] < df['close'].iloc[i-1]:
            obv.append(obv[-1] - df['volume'].iloc[i])
        else:
            obv.append(obv[-1])
    features['obv'] = obv
    features['obv_ema'] = features['obv'].ewm(span=20).mean()
    
    # Volume Ratios
    features['volume_sma_20'] = df['volume'].rolling(20).mean()
    features['volume_ratio'] = df['volume'] / features['volume_sma_20']
    
    # === MOMENTUM ===
    for period in [5, 10, 20]:
        features[f'roc_{period}'] = df['close'].pct_change(periods=period) * 100
        features[f'momentum_{period}'] = df['close'] - df['close'].shift(period)
    
    # === VOLATILITÄT ===
    for period in [10, 20]:
        features[f'volatility_{period}'] = df['close'].pct_change().rolling(period).std() * 100
    
    # === PREIS PATTERN ===
    features['high_low_ratio'] = df['high'] / df['low']
    features['close_open_ratio'] = df['close'] / df['open']
    features['body_size'] = abs(df['close'] - df['open']) / df['open'] * 100
    
    # === TREND INDIKATOREN ===
    features['trend_strength_20'] = (df['close'] - features['sma_20']) / features['sma_20'] * 100
    features['trend_strength_50'] = (df['close'] - features['sma_50']) / features['sma_50'] * 100
    
    # === ZYKLISCHE FEATURES ===
    features['hour'] = df.index.hour
    features['day_of_week'] = df.index.dayofweek
    features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
    features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
    features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
    features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
    
    print(f"📈 {len(features.columns)} Ultimate Features erstellt")
    return features.dropna()

def create_sequences(data, target, look_back):
    """Robuste Sequenz-Erstellung"""
    X, y = [], []
    for i in range(look_back, len(data)):
        X.append(data[i-look_back:i])
        y.append(target[i])
    return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)

def build_ultimate_ensemble_models(input_shape):
    """Ultimate Ensemble-Modelle mit Legacy Optimizers"""
    models = {}
    
    # 1. Bidirectional LSTM
    model1 = Sequential([
        Bidirectional(LSTM(64, return_sequences=True, dropout=0.2), input_shape=input_shape),
        BatchNormalization(),
        LSTM(32, return_sequences=False, dropout=0.2),
        Dense(32, activation='relu'),
        Dropout(0.3),
        Dense(1)
    ])
    model1.compile(optimizer=Adam(learning_rate=0.001), loss='huber', metrics=['mae'])
    models['bidirectional'] = model1
    
    # 2. Deep LSTM
    model2 = Sequential([
        LSTM(96, return_sequences=True, dropout=0.2, input_shape=input_shape),
        LSTM(48, return_sequences=True, dropout=0.2),
        LSTM(24, return_sequences=False, dropout=0.2),
        Dense(32, activation='relu'),
        Dropout(0.3),
        Dense(1)
    ])
    model2.compile(optimizer=Adam(learning_rate=0.0005), loss='huber', metrics=['mae'])
    models['deep_lstm'] = model2
    
    # 3. GRU Model
    model3 = Sequential([
        GRU(64, return_sequences=True, dropout=0.2, input_shape=input_shape),
        GRU(32, return_sequences=False, dropout=0.2),
        Dense(32, activation='relu'),
        Dropout(0.3),
        Dense(1)
    ])
    model3.compile(optimizer=RMSprop(learning_rate=0.001), loss='huber', metrics=['mae'])
    models['gru'] = model3
    
    # 4. Wide LSTM
    model4 = Sequential([
        LSTM(128, return_sequences=False, dropout=0.3, input_shape=input_shape),
        Dense(64, activation='relu'),
        Dropout(0.4),
        Dense(32, activation='relu'),
        Dense(1)
    ])
    model4.compile(optimizer=Adam(learning_rate=0.001), loss='huber', metrics=['mae'])
    models['wide_lstm'] = model4
    
    # 5. Simple LSTM (für Stabilität)
    model5 = Sequential([
        LSTM(32, return_sequences=False, dropout=0.2, input_shape=input_shape),
        Dense(16, activation='relu'),
        Dense(1)
    ])
    model5.compile(optimizer=Adam(learning_rate=0.01), loss='mse', metrics=['mae'])
    models['simple_lstm'] = model5
    
    for name, model in models.items():
        print(f"✅ {name}: {model.count_params():,} Parameter")
    
    return models

def train_models_sequential(models, X_train, y_train, X_val, y_val):
    """Sequenzielles Training (robuster als parallel)"""
    print("🎯 Starte sequenzielles Training...")
    
    trained_models = {}
    performance = {}
    
    callbacks = [
        EarlyStopping(patience=CONFIG['patience'], restore_best_weights=True, verbose=0),
        ReduceLROnPlateau(factor=0.5, patience=10, min_lr=1e-7, verbose=0)
    ]
    
    for name, model in models.items():
        print(f"   Training {name}...")
        
        start_time = time.time()
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            callbacks=callbacks,
            verbose=0
        )
        
        training_time = time.time() - start_time
        val_loss = min(history.history['val_loss'])
        
        trained_models[name] = model
        performance[name] = val_loss
        
        print(f"   ✅ {name}: Val Loss = {val_loss:.6f}, Zeit = {training_time:.1f}s")
    
    # Sortiere nach Performance
    sorted_performance = sorted(performance.items(), key=lambda x: x[1])
    
    print(f"🏆 Modell-Ranking:")
    for i, (name, val_loss) in enumerate(sorted_performance):
        medal = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1}."
        print(f"   {medal} {name}: {val_loss:.6f}")
    
    return trained_models, performance

def create_ml_ensemble(X_train_flat, y_train, X_val_flat, y_val):
    """Traditionelle ML-Modelle"""
    print("🤖 Trainiere ML-Ensemble...")
    
    ml_models = {}
    
    # Random Forest
    print("   Training Random Forest...")
    rf = RandomForestRegressor(n_estimators=100, max_depth=15, n_jobs=-1, random_state=42)
    rf.fit(X_train_flat, y_train)
    ml_models['random_forest'] = rf
    
    # Gradient Boosting
    print("   Training Gradient Boosting...")
    gb = GradientBoostingRegressor(n_estimators=100, max_depth=8, random_state=42)
    gb.fit(X_train_flat, y_train)
    ml_models['gradient_boosting'] = gb
    
    # Ridge Regression
    ridge = Ridge(alpha=1.0)
    ridge.fit(X_train_flat, y_train)
    ml_models['ridge'] = ridge
    
    # Lasso Regression
    lasso = Lasso(alpha=0.1)
    lasso.fit(X_train_flat, y_train)
    ml_models['lasso'] = lasso
    
    # Evaluiere ML-Modelle
    ml_performance = {}
    for name, model in ml_models.items():
        pred = model.predict(X_val_flat)
        mse = mean_squared_error(y_val, pred)
        ml_performance[name] = mse
        print(f"   ✅ {name}: MSE = {mse:.6f}")
    
    return ml_models, ml_performance

def create_ultimate_ensemble(dl_models, ml_models, X_test, y_test, X_test_flat):
    """Ultimate Ensemble aus allen Modellen"""
    print("🏆 Erstelle Ultimate Ensemble...")

    # Deep Learning Predictions
    dl_predictions = []
    dl_weights = []

    for name, model in dl_models.items():
        pred = model.predict(X_test, verbose=0).flatten()
        mse = mean_squared_error(y_test, pred)
        r2 = r2_score(y_test, pred)
        weight = max(0.1, r2)  # Gewicht basierend auf R²
        dl_predictions.append(pred)
        dl_weights.append(weight)
        print(f"   DL {name}: R² = {r2:.4f}, Gewicht = {weight:.3f}")

    # ML Predictions
    ml_predictions = []
    ml_weights = []

    for name, model in ml_models.items():
        pred = model.predict(X_test_flat)
        mse = mean_squared_error(y_test, pred)
        r2 = r2_score(y_test, pred)
        weight = max(0.1, r2)
        ml_predictions.append(pred)
        ml_weights.append(weight)
        print(f"   ML {name}: R² = {r2:.4f}, Gewicht = {weight:.3f}")

    # Normalisiere Gewichte
    dl_weights = np.array(dl_weights) / np.sum(dl_weights)
    ml_weights = np.array(ml_weights) / np.sum(ml_weights)

    # Gewichtete Ensemble-Vorhersage
    dl_ensemble = np.average(dl_predictions, axis=0, weights=dl_weights)
    ml_ensemble = np.average(ml_predictions, axis=0, weights=ml_weights)

    # 75% Deep Learning, 25% Traditional ML (DL ist meist besser)
    ultimate_ensemble = 0.75 * dl_ensemble + 0.25 * ml_ensemble

    ensemble_r2 = r2_score(y_test, ultimate_ensemble)
    ensemble_rmse = np.sqrt(mean_squared_error(y_test, ultimate_ensemble))

    print(f"🎯 Ultimate Ensemble Performance:")
    print(f"   R²: {ensemble_r2:.4f} ({ensemble_r2*100:.1f}%)")
    print(f"   RMSE: {ensemble_rmse:.6f}")

    return ultimate_ensemble, dl_weights, ml_weights

def monte_carlo_ultimate_prediction(dl_models, ml_models, last_sequence, last_features, scaler, n_hours=24):
    """Ultimate Monte Carlo Zukunftsprognose"""
    print(f"🔮 Ultimate Monte Carlo Prognose für {n_hours}h...")
    print(f"   {CONFIG['monte_carlo_sims']} Simulationen mit {len(dl_models)} DL + {len(ml_models)} ML Modellen")

    all_predictions = []

    for sim in range(CONFIG['monte_carlo_sims']):
        if sim % 200 == 0:
            print(f"   Simulation {sim+1}/{CONFIG['monte_carlo_sims']}")

        current_seq = last_sequence.copy()
        current_features = last_features.copy()
        sim_predictions = []

        for hour in range(n_hours):
            # Deep Learning Ensemble Prediction
            dl_preds = []
            for model in dl_models.values():
                pred = model.predict(current_seq.reshape(1, *current_seq.shape), verbose=0)[0, 0]
                dl_preds.append(pred)

            # ML Ensemble Prediction
            ml_preds = []
            for model in ml_models.values():
                pred = model.predict(current_features.reshape(1, -1))[0]
                ml_preds.append(pred)

            # Ultimate Ensemble Prediction
            dl_mean = np.mean(dl_preds)
            ml_mean = np.mean(ml_preds)
            ensemble_pred = 0.75 * dl_mean + 0.25 * ml_mean

            # Adaptive Volatilität (steigt mit Zeit und Unsicherheit)
            base_volatility = 0.015
            time_factor = 1 + hour * 0.002
            uncertainty_factor = 1 + np.std(dl_preds + ml_preds) * 0.5
            volatility = base_volatility * time_factor * uncertainty_factor

            # Rauschen hinzufügen
            noise = np.random.normal(0, volatility)
            final_pred = ensemble_pred * (1 + noise)

            sim_predictions.append(final_pred)

            # Update für nächste Iteration
            new_features = current_features.copy()
            new_features[3] = final_pred  # close price

            # Realistische Feature-Updates
            for i in range(len(new_features)):
                if i != 3:  # Nicht close price
                    if i < 5:  # OHLCV
                        feature_volatility = volatility * 0.8
                    else:  # Technische Indikatoren
                        feature_volatility = volatility * 0.3

                    feature_noise = np.random.normal(0, feature_volatility)
                    new_features[i] = new_features[i] * (1 + feature_noise)

            # Update Sequenzen
            current_seq = np.vstack([current_seq[1:], new_features[:current_seq.shape[1]].reshape(1, -1)])
            current_features = new_features

        all_predictions.append(sim_predictions)

    # Umfassende Statistiken
    all_predictions = np.array(all_predictions)

    stats = {
        'mean': np.mean(all_predictions, axis=0),
        'median': np.median(all_predictions, axis=0),
        'std': np.std(all_predictions, axis=0),
        'q05': np.percentile(all_predictions, 5, axis=0),
        'q10': np.percentile(all_predictions, 10, axis=0),
        'q25': np.percentile(all_predictions, 25, axis=0),
        'q75': np.percentile(all_predictions, 75, axis=0),
        'q90': np.percentile(all_predictions, 90, axis=0),
        'q95': np.percentile(all_predictions, 95, axis=0),
        'min': np.min(all_predictions, axis=0),
        'max': np.max(all_predictions, axis=0)
    }

    # Skalierung rückgängig machen
    for key in stats:
        dummy_data = np.zeros((len(stats[key]), scaler.n_features_in_))
        dummy_data[:, 3] = stats[key]
        stats[key] = scaler.inverse_transform(dummy_data)[:, 3]

    return stats

def plot_ultimate_analysis(df, train_size, val_size, y_test, ensemble_pred, future_stats, all_performance):
    """Ultimate Analyse-Visualisierung"""
    plt.figure(figsize=(24, 18))

    # 1. Hauptpreis-Chart mit Ultimate Prognose
    plt.subplot(4, 3, (1, 3))
    dates = df.index

    plt.plot(dates[:train_size], df['close'].iloc[:train_size], 'b-', label='Training', alpha=0.7, linewidth=1)
    plt.plot(dates[train_size:train_size+val_size], df['close'].iloc[train_size:train_size+val_size],
             'orange', label='Validation', alpha=0.7, linewidth=1)

    test_start = train_size + val_size
    test_dates = dates[test_start:test_start+len(y_test)]
    plt.plot(test_dates, y_test, 'g-', label='Actual', linewidth=2)
    plt.plot(test_dates, ensemble_pred, 'r--', label='Ultimate Ensemble', linewidth=2)

    # Ultimate Zukunftsprognose
    future_dates = pd.date_range(start=dates[-1], periods=len(future_stats['mean'])+1, freq='1H')[1:]
    plt.plot(future_dates, future_stats['mean'], 'purple', linewidth=3, label='Ultimate Prediction')
    plt.fill_between(future_dates, future_stats['q05'], future_stats['q95'],
                     alpha=0.15, color='purple', label='90% Confidence')
    plt.fill_between(future_dates, future_stats['q25'], future_stats['q75'],
                     alpha=0.25, color='purple', label='50% Confidence')
    plt.fill_between(future_dates, future_stats['q10'], future_stats['q90'],
                     alpha=0.2, color='blue', label='80% Confidence')

    plt.title('ULTIMATE BITCOIN PREDICTION - FINAL MODEL', fontsize=18, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. Performance Vergleich
    plt.subplot(4, 3, 4)
    models = list(all_performance.keys())
    scores = list(all_performance.values())
    colors = plt.cm.viridis(np.linspace(0, 1, len(models)))

    bars = plt.barh(models, scores, color=colors)
    plt.title('Ultimate Model Performance')
    plt.xlabel('Validation Loss (Lower = Better)')

    # Beste 3 markieren
    sorted_indices = np.argsort(scores)
    for i, idx in enumerate(sorted_indices[:3]):
        bars[idx].set_color('gold' if i == 0 else 'silver' if i == 1 else '#CD7F32')

    plt.grid(True, alpha=0.3)

    # 3. Ultimate Zukunftsprognose Detail
    plt.subplot(4, 3, 5)
    hours = range(1, len(future_stats['mean']) + 1)
    plt.plot(hours, future_stats['mean'], 'purple', linewidth=3, label='Mean')
    plt.plot(hours, future_stats['median'], 'orange', linewidth=2, label='Median')
    plt.fill_between(hours, future_stats['q25'], future_stats['q75'], alpha=0.3, color='blue', label='IQR')
    plt.title('Ultimate Future Prediction')
    plt.xlabel('Hours Ahead')
    plt.ylabel('Price (USD)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 4. Volatilität und Unsicherheit
    plt.subplot(4, 3, 6)
    volatility = future_stats['std'] / future_stats['mean'] * 100
    uncertainty = (future_stats['q95'] - future_stats['q05']) / future_stats['mean'] * 100
    plt.plot(hours, volatility, 'red', linewidth=2, label='Volatility')
    plt.plot(hours, uncertainty, 'orange', linewidth=2, label='Uncertainty Range')
    plt.title('Risk Metrics')
    plt.xlabel('Hours Ahead')
    plt.ylabel('Percentage')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 5. Residuals
    plt.subplot(4, 3, 7)
    residuals = y_test - ensemble_pred
    plt.scatter(ensemble_pred, residuals, alpha=0.6, s=20, c='blue')
    plt.axhline(y=0, color='red', linestyle='--')
    plt.title('Ultimate Residuals')
    plt.xlabel('Predicted')
    plt.ylabel('Residuals')
    plt.grid(True, alpha=0.3)

    # 6. Error Distribution
    plt.subplot(4, 3, 8)
    residuals = y_test - ensemble_pred
    plt.hist(residuals, bins=25, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('Error Distribution')
    plt.xlabel('Error')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)

    # 7. Confidence Evolution
    plt.subplot(4, 3, 9)
    current_price = df['close'].iloc[-1]
    price_changes = (future_stats['mean'] / current_price - 1) * 100
    upper_change = (future_stats['q95'] / current_price - 1) * 100
    lower_change = (future_stats['q05'] / current_price - 1) * 100

    plt.plot(hours, price_changes, 'green', linewidth=2, label='Expected Change')
    plt.fill_between(hours, lower_change, upper_change, alpha=0.3, color='green')
    plt.title('Expected Price Change %')
    plt.xlabel('Hours Ahead')
    plt.ylabel('Change %')
    plt.axhline(y=0, color='red', linestyle='--')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 8-12. Weitere Analysen
    for i in range(10, 13):
        plt.subplot(4, 3, i)
        if i == 10:
            # Prediction Accuracy over Time
            accuracy = 100 - np.abs((y_test - ensemble_pred) / y_test) * 100
            plt.plot(accuracy, 'o-', alpha=0.7, markersize=3)
            plt.title('Prediction Accuracy %')
            plt.ylabel('Accuracy %')
        elif i == 11:
            # Model Ensemble Weights (Placeholder)
            weights = [0.3, 0.25, 0.2, 0.15, 0.1]
            labels = ['Best DL', '2nd DL', '3rd DL', 'Best ML', 'Others']
            plt.pie(weights, labels=labels, autopct='%1.1f%%')
            plt.title('Ensemble Weights')
        else:
            # Summary Statistics
            stats_text = f"""
            ULTIMATE MODEL SUMMARY

            Total Models: {len(all_performance)}
            Best R²: {max([r2_score(y_test, ensemble_pred)]):.4f}
            Features: {df.shape[1]-1}
            Monte Carlo Sims: {CONFIG['monte_carlo_sims']}

            Hardware: {os.cpu_count()} CPU cores
            GPU: {'Available' if gpus else 'Not Available'}
            """
            plt.text(0.1, 0.5, stats_text, fontsize=10, verticalalignment='center',
                     bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
            plt.axis('off')
            plt.title('Model Summary')

        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

def calculate_ultimate_metrics(y_true, y_pred):
    """Ultimate Metriken-Berechnung"""
    metrics = {
        'mse': mean_squared_error(y_true, y_pred),
        'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
        'mae': mean_absolute_error(y_true, y_pred),
        'r2': r2_score(y_true, y_pred),
        'mape': np.mean(np.abs((y_true - y_pred) / y_true)) * 100,
        'explained_variance': 1 - (np.var(y_true - y_pred) / np.var(y_true)),
        'max_error': np.max(np.abs(y_true - y_pred)),
        'mean_error': np.mean(y_true - y_pred),
        'median_error': np.median(np.abs(y_true - y_pred))
    }

    # Richtungsgenauigkeit
    if len(y_true) > 1:
        true_direction = np.diff(y_true) > 0
        pred_direction = np.diff(y_pred) > 0
        metrics['direction_accuracy'] = np.mean(true_direction == pred_direction) * 100
    else:
        metrics['direction_accuracy'] = 0

    # Zusätzliche Metriken
    metrics['accuracy_95'] = np.mean(np.abs((y_true - y_pred) / y_true) < 0.05) * 100  # 5% Genauigkeit
    metrics['accuracy_90'] = np.mean(np.abs((y_true - y_pred) / y_true) < 0.10) * 100  # 10% Genauigkeit

    return metrics

def main():
    """FINALE ULTIMATE HAUPTFUNKTION"""
    print("\n" + "="*80)
    print("🚀 FINAL ULTIMATE BITCOIN PREDICTION MODEL")
    print("="*80)
    print(f"🎯 Ziel: Maximale Genauigkeit mit robuster Architektur")
    print(f"💻 Hardware: {os.cpu_count()} CPU-Kerne + {'GPU' if gpus else 'CPU only'}")

    total_start = time.time()

    try:
        # 1. ULTIMATE Feature Engineering
        print("\n" + "="*60)
        print("PHASE 1: ULTIMATE FEATURE ENGINEERING")
        print("="*60)

        feature_start = time.time()
        df = load_and_create_ultimate_features()
        feature_time = time.time() - feature_start
        print(f"⚡ Feature Engineering: {feature_time:.1f}s")

        # 2. Robuste Datenaufbereitung
        print("\n" + "="*60)
        print("PHASE 2: ROBUST DATA PREPARATION")
        print("="*60)

        prep_start = time.time()

        X = df.drop('close', axis=1)
        y = df['close'].values

        # Robuste Skalierung
        feature_scaler = RobustScaler()
        target_scaler = StandardScaler()

        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()

        # Sequenzen erstellen
        X_seq, y_seq = create_sequences(X_scaled, y_scaled, CONFIG['look_back'])

        # Robuste Train-Val-Test Aufteilung
        train_size = int(len(X_seq) * CONFIG['train_split'])
        val_size = int(len(X_seq) * 0.15)

        X_train = X_seq[:train_size]
        y_train = y_seq[:train_size]
        X_val = X_seq[train_size:train_size+val_size]
        y_val = y_seq[train_size:train_size+val_size]
        X_test = X_seq[train_size+val_size:]
        y_test = y_seq[train_size+val_size:]

        print(f"📊 Datenaufteilung: Train {len(X_train)}, Val {len(X_val)}, Test {len(X_test)}")
        print(f"📊 Features: {X_scaled.shape[1]}")
        print(f"📊 Sequenzlänge: {CONFIG['look_back']}")

        prep_time = time.time() - prep_start
        print(f"⚡ Data Preparation: {prep_time:.1f}s")

        # 3. ULTIMATE Deep Learning Ensemble
        print("\n" + "="*60)
        print("PHASE 3: ULTIMATE DEEP LEARNING ENSEMBLE")
        print("="*60)

        dl_start = time.time()

        input_shape = (X_train.shape[1], X_train.shape[2])
        dl_models = build_ultimate_ensemble_models(input_shape)
        trained_dl_models, dl_performance = train_models_sequential(dl_models, X_train, y_train, X_val, y_val)

        dl_time = time.time() - dl_start
        print(f"⚡ Deep Learning Training: {dl_time:.1f}s")

        # 4. Traditional ML Ensemble
        print("\n" + "="*60)
        print("PHASE 4: TRADITIONAL ML ENSEMBLE")
        print("="*60)

        ml_start = time.time()

        X_train_flat = X_train.reshape(X_train.shape[0], -1)
        X_val_flat = X_val.reshape(X_val.shape[0], -1)
        X_test_flat = X_test.reshape(X_test.shape[0], -1)

        ml_models, ml_performance = create_ml_ensemble(X_train_flat, y_train, X_val_flat, y_val)

        ml_time = time.time() - ml_start
        print(f"⚡ ML Training: {ml_time:.1f}s")

        # 5. ULTIMATE Ensemble Creation
        print("\n" + "="*60)
        print("PHASE 5: ULTIMATE ENSEMBLE CREATION")
        print("="*60)

        ensemble_start = time.time()

        ultimate_ensemble_pred, dl_weights, ml_weights = create_ultimate_ensemble(
            trained_dl_models, ml_models, X_test, y_test, X_test_flat
        )

        ensemble_time = time.time() - ensemble_start
        print(f"⚡ Ultimate Ensemble: {ensemble_time:.1f}s")

        # 6. Skalierung rückgängig machen
        y_test_orig = target_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        ensemble_pred_orig = target_scaler.inverse_transform(ultimate_ensemble_pred.reshape(-1, 1)).flatten()

        # 7. Ultimate Metriken
        metrics = calculate_ultimate_metrics(y_test_orig, ensemble_pred_orig)

        print(f"\n📊 ULTIMATE ENSEMBLE PERFORMANCE:")
        print(f"R²: {metrics['r2']:.4f} ({metrics['r2']*100:.1f}%)")
        print(f"RMSE: ${metrics['rmse']:.2f}")
        print(f"MAE: ${metrics['mae']:.2f}")
        print(f"MAPE: {metrics['mape']:.2f}%")
        print(f"Direction Accuracy: {metrics['direction_accuracy']:.1f}%")
        print(f"95% Accuracy: {metrics['accuracy_95']:.1f}%")
        print(f"90% Accuracy: {metrics['accuracy_90']:.1f}%")

        # 8. ULTIMATE Future Predictions
        print("\n" + "="*60)
        print("PHASE 6: ULTIMATE FUTURE PREDICTIONS")
        print("="*60)

        future_start = time.time()

        last_sequence = X_test[-1]
        last_features = X_test_flat[-1]

        future_predictions = {}
        for hours in CONFIG['future_hours']:
            print(f"\n🔮 {hours}h Ultimate Prognose...")
            future_stats = monte_carlo_ultimate_prediction(
                trained_dl_models, ml_models, last_sequence, last_features, target_scaler, hours
            )
            future_predictions[hours] = future_stats

        future_time = time.time() - future_start
        print(f"⚡ Future Predictions: {future_time:.1f}s")

        # 9. ULTIMATE Visualisierung
        print("\n" + "="*60)
        print("PHASE 7: ULTIMATE VISUALIZATION")
        print("="*60)

        viz_start = time.time()

        all_performance = {**dl_performance, **ml_performance}
        plot_ultimate_analysis(df, train_size, val_size, y_test_orig, ensemble_pred_orig,
                              future_predictions[24], all_performance)

        viz_time = time.time() - viz_start
        print(f"⚡ Visualization: {viz_time:.1f}s")

        # 10. FINALE ULTIMATE PROGNOSE
        print("\n" + "="*80)
        print("🎯 FINALE ULTIMATE BITCOIN PROGNOSE")
        print("="*80)

        current_price = df['close'].iloc[-1]

        for hours in CONFIG['future_hours']:
            stats = future_predictions[hours]

            mean_price = stats['mean'][-1]
            median_price = stats['median'][-1]
            q25_price = stats['q25'][-1]
            q75_price = stats['q75'][-1]
            q05_price = stats['q05'][-1]
            q95_price = stats['q95'][-1]
            min_price = stats['min'][-1]
            max_price = stats['max'][-1]

            mean_change = (mean_price / current_price - 1) * 100

            print(f"\n📅 ULTIMATE {hours}h PROGNOSE:")
            print(f"   💰 Erwarteter Preis: ${mean_price:.2f} ({mean_change:+.2f}%)")
            print(f"   📊 Median: ${median_price:.2f}")
            print(f"   📈 50% Konfidenz: ${q25_price:.2f} - ${q75_price:.2f}")
            print(f"   📈 90% Konfidenz: ${q05_price:.2f} - ${q95_price:.2f}")
            print(f"   📈 Extremwerte: ${min_price:.2f} - ${max_price:.2f}")

            # Erweiterte Trend-Bewertung
            volatility = (q95_price - q05_price) / mean_price * 100

            if mean_change > 10:
                trend = "🚀 EXTREM BULLISH"
                risk = "🔥 HOCH"
            elif mean_change > 5:
                trend = "🚀 STARK BULLISH"
                risk = "⚠️  MITTEL-HOCH"
            elif mean_change > 2:
                trend = "📈 BULLISH"
                risk = "✅ MITTEL"
            elif mean_change > -2:
                trend = "➡️  SEITWÄRTS"
                risk = "✅ NIEDRIG"
            elif mean_change > -5:
                trend = "📉 BEARISH"
                risk = "⚠️  MITTEL"
            elif mean_change > -10:
                trend = "📉 STARK BEARISH"
                risk = "⚠️  MITTEL-HOCH"
            else:
                trend = "💥 EXTREM BEARISH"
                risk = "🔥 HOCH"

            print(f"   🎯 Trend: {trend}")
            print(f"   ⚠️  Risiko: {risk} (Volatilität: {volatility:.1f}%)")

        # FINALE STATISTIKEN
        total_time = time.time() - total_start

        print(f"\n" + "="*80)
        print("✅ FINAL ULTIMATE MODEL ABGESCHLOSSEN!")
        print("="*80)
        print(f"⚡ Gesamtzeit: {total_time:.1f} Sekunden")
        print(f"🎯 Finale Genauigkeit: {metrics['r2']*100:.1f}%")
        print(f"📊 Features: {X_scaled.shape[1]}")
        print(f"🤖 DL-Modelle: {len(trained_dl_models)}")
        print(f"🔧 ML-Modelle: {len(ml_models)}")
        print(f"🔮 Monte Carlo Sims: {CONFIG['monte_carlo_sims']}")
        print(f"💻 Hardware: {os.cpu_count()} CPU-Kerne + {'GPU' if gpus else 'CPU only'}")
        print(f"📈 Testsamples: {len(y_test_orig)}")

        # ULTIMATE Performance-Ranking
        print(f"\n🏆 ULTIMATE TOP 5 MODELLE:")
        sorted_performance = sorted(all_performance.items(), key=lambda x: x[1])
        for i, (name, score) in enumerate(sorted_performance[:5]):
            medal = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1}."
            print(f"   {medal} {name}: {score:.6f}")

        # ERFOLGS-BEWERTUNG
        if metrics['r2'] >= 0.95:
            print(f"\n🎉🎉🎉 ULTIMATE SUCCESS! 🎉🎉🎉")
            print(f"Genauigkeit: {metrics['r2']*100:.1f}% - EXZELLENT!")
        elif metrics['r2'] >= 0.90:
            print(f"\n🔥🔥🔥 OUTSTANDING! 🔥🔥🔥")
            print(f"Genauigkeit: {metrics['r2']*100:.1f}% - SEHR GUT!")
        elif metrics['r2'] >= 0.80:
            print(f"\n🎯🎯 ZIEL ERREICHT! 🎯🎯")
            print(f"Genauigkeit: {metrics['r2']*100:.1f}% ≥ 80%")
        elif metrics['r2'] >= 0.70:
            print(f"\n💪 SEHR GUTE LEISTUNG!")
            print(f"Genauigkeit: {metrics['r2']*100:.1f}%")
        else:
            print(f"\n✅ SOLIDE PERFORMANCE!")
            print(f"Genauigkeit: {metrics['r2']*100:.1f}%")

        print(f"\n🚀 FINAL ULTIMATE BITCOIN PREDICTION MODEL - MISSION ACCOMPLISHED! 🚀")

    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
