#!/usr/bin/env python3
"""
ULTIMATE HYPERPARAMETER TUNING - Bitcoin Prediction
Systematische Optimierung aller Parameter für maximale Genauigkeit
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, BatchNormalization, Bidirectional
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers.legacy import Adam, RMSprop, AdamW
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from itertools import product
import time
import warnings
warnings.filterwarnings('ignore')

print("🔥 ULTIMATE HYPERPARAMETER TUNING - Bitcoin Prediction")
print("=" * 70)

# HYPERPARAMETER GRID
HYPERPARAMETER_GRID = {
    'look_back': [12, 24, 48, 72],
    'lstm_units': [32, 64, 128, 256],
    'lstm_layers': [1, 2, 3],
    'dense_units': [16, 32, 64, 128],
    'dropout_rate': [0.1, 0.2, 0.3, 0.4],
    'learning_rate': [0.0001, 0.0005, 0.001, 0.005, 0.01],
    'batch_size': [16, 32, 64, 128],
    'optimizer': ['adam', 'rmsprop', 'adamw'],
    'scaler': ['minmax', 'standard', 'robust'],
    'activation': ['relu', 'tanh', 'elu'],
    'loss_function': ['mse', 'mae', 'huber'],
    'architecture': ['lstm', 'gru', 'bidirectional', 'hybrid']
}

class HyperparameterTuner:
    def __init__(self):
        self.results = []
        self.best_score = -np.inf
        self.best_params = None
        self.best_model = None
    
    def load_and_prepare_data(self):
        """Lade und bereite Daten vor"""
        print("📊 Lade Daten für Hyperparameter-Tuning...")
        
        df = pd.read_csv('crypto_data.csv')
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        # Erweiterte Features
        features = self.create_advanced_features(df)
        
        print(f"   ✅ {len(features)} Datenpunkte, {len(features.columns)} Features")
        return features
    
    def create_advanced_features(self, df):
        """Erstelle erweiterte Features"""
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # Moving Averages (verschiedene Perioden)
        for period in [5, 10, 14, 20, 21, 26, 50, 100]:
            if period <= len(df):
                features[f'sma_{period}'] = df['close'].rolling(period).mean()
                features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
        
        # MACD Familie
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        features['macd'] = ema_12 - ema_26
        features['macd_signal'] = features['macd'].ewm(span=9).mean()
        features['macd_histogram'] = features['macd'] - features['macd_signal']
        features['macd_slope'] = features['macd'].diff()
        
        # RSI Familie
        for period in [9, 14, 21, 25]:
            delta = df['close'].diff()
            gain = delta.where(delta > 0, 0).rolling(period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
            rs = gain / loss
            features[f'rsi_{period}'] = 100 - (100 / (1 + rs))
            features[f'rsi_{period}_slope'] = features[f'rsi_{period}'].diff()
        
        # Bollinger Bands
        for period in [20, 50]:
            sma = df['close'].rolling(period).mean()
            std = df['close'].rolling(period).std()
            features[f'bb_upper_{period}'] = sma + (std * 2)
            features[f'bb_lower_{period}'] = sma - (std * 2)
            features[f'bb_width_{period}'] = (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}']) / sma
            features[f'bb_position_{period}'] = (df['close'] - features[f'bb_lower_{period}']) / (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}'])
        
        # Stochastic Oscillator
        for period in [14, 21]:
            low_min = df['low'].rolling(period).min()
            high_max = df['high'].rolling(period).max()
            features[f'stoch_k_{period}'] = 100 * ((df['close'] - low_min) / (high_max - low_min))
            features[f'stoch_d_{period}'] = features[f'stoch_k_{period}'].rolling(3).mean()
        
        # Williams %R
        for period in [14, 21]:
            high_max = df['high'].rolling(period).max()
            low_min = df['low'].rolling(period).min()
            features[f'williams_r_{period}'] = -100 * ((high_max - df['close']) / (high_max - low_min))
        
        # ATR (Average True Range)
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        for period in [7, 14, 21]:
            features[f'atr_{period}'] = true_range.rolling(period).mean()
            features[f'atr_percent_{period}'] = features[f'atr_{period}'] / df['close'] * 100
        
        # Volume Indikatoren
        # OBV (On-Balance Volume)
        obv = [0]
        for i in range(1, len(df)):
            if df['close'].iloc[i] > df['close'].iloc[i-1]:
                obv.append(obv[-1] + df['volume'].iloc[i])
            elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                obv.append(obv[-1] - df['volume'].iloc[i])
            else:
                obv.append(obv[-1])
        features['obv'] = obv
        features['obv_ema'] = features['obv'].ewm(span=20).mean()
        
        # Volume Ratios
        for period in [10, 20, 50]:
            features[f'volume_sma_{period}'] = df['volume'].rolling(period).mean()
            features[f'volume_ratio_{period}'] = df['volume'] / features[f'volume_sma_{period}']
        
        # Momentum Indikatoren
        for period in [5, 10, 20, 50]:
            features[f'roc_{period}'] = df['close'].pct_change(periods=period) * 100
            features[f'momentum_{period}'] = df['close'] - df['close'].shift(period)
        
        # Volatilität
        for period in [5, 10, 20, 50]:
            features[f'volatility_{period}'] = df['close'].pct_change().rolling(period).std() * 100
        
        # Preis Pattern
        features['high_low_ratio'] = df['high'] / df['low']
        features['close_open_ratio'] = df['close'] / df['open']
        features['body_size'] = abs(df['close'] - df['open']) / df['open'] * 100
        
        # Trend Indikatoren
        for period in [20, 50, 100]:
            if period <= len(df):
                features[f'trend_strength_{period}'] = (df['close'] - features[f'sma_{period}']) / features[f'sma_{period}'] * 100
        
        # Zyklische Features
        features['hour'] = df.index.hour
        features['day_of_week'] = df.index.dayofweek
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
        features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
        
        return features.dropna()
    
    def create_sequences(self, data, target, look_back):
        """Erstelle Sequenzen"""
        X, y = [], []
        for i in range(look_back, len(data)):
            X.append(data[i-look_back:i])
            y.append(target[i])
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)
    
    def get_scaler(self, scaler_type):
        """Hole Scaler basierend auf Typ"""
        if scaler_type == 'minmax':
            return MinMaxScaler()
        elif scaler_type == 'standard':
            return StandardScaler()
        elif scaler_type == 'robust':
            return RobustScaler()
        else:
            return MinMaxScaler()
    
    def get_optimizer(self, optimizer_type, learning_rate):
        """Hole Optimizer basierend auf Typ"""
        if optimizer_type == 'adam':
            return Adam(learning_rate=learning_rate)
        elif optimizer_type == 'rmsprop':
            return RMSprop(learning_rate=learning_rate)
        elif optimizer_type == 'adamw':
            return AdamW(learning_rate=learning_rate)
        else:
            return Adam(learning_rate=learning_rate)
    
    def build_model(self, params, input_shape):
        """Baue Modell basierend auf Parametern"""
        model = Sequential()
        
        # Erste Schicht basierend auf Architektur
        if params['architecture'] == 'lstm':
            if params['lstm_layers'] == 1:
                model.add(LSTM(params['lstm_units'], return_sequences=False, 
                              dropout=params['dropout_rate'], input_shape=input_shape))
            else:
                model.add(LSTM(params['lstm_units'], return_sequences=True, 
                              dropout=params['dropout_rate'], input_shape=input_shape))
        
        elif params['architecture'] == 'gru':
            if params['lstm_layers'] == 1:
                model.add(GRU(params['lstm_units'], return_sequences=False, 
                             dropout=params['dropout_rate'], input_shape=input_shape))
            else:
                model.add(GRU(params['lstm_units'], return_sequences=True, 
                             dropout=params['dropout_rate'], input_shape=input_shape))
        
        elif params['architecture'] == 'bidirectional':
            if params['lstm_layers'] == 1:
                model.add(Bidirectional(LSTM(params['lstm_units'], return_sequences=False, 
                                           dropout=params['dropout_rate']), input_shape=input_shape))
            else:
                model.add(Bidirectional(LSTM(params['lstm_units'], return_sequences=True, 
                                           dropout=params['dropout_rate']), input_shape=input_shape))
        
        elif params['architecture'] == 'hybrid':
            model.add(LSTM(params['lstm_units'], return_sequences=True, 
                          dropout=params['dropout_rate'], input_shape=input_shape))
            model.add(GRU(params['lstm_units']//2, return_sequences=False, 
                         dropout=params['dropout_rate']))
        
        # Batch Normalization nach erster Schicht
        if params['lstm_layers'] > 1:
            model.add(BatchNormalization())
        
        # Zusätzliche LSTM/GRU Schichten
        for i in range(1, params['lstm_layers']):
            units = params['lstm_units'] // (2 ** i)  # Reduziere Units pro Schicht
            units = max(units, 16)  # Minimum 16 Units
            
            if i == params['lstm_layers'] - 1:  # Letzte RNN-Schicht
                return_sequences = False
            else:
                return_sequences = True
            
            if params['architecture'] in ['lstm', 'hybrid']:
                model.add(LSTM(units, return_sequences=return_sequences, 
                              dropout=params['dropout_rate']))
            elif params['architecture'] == 'gru':
                model.add(GRU(units, return_sequences=return_sequences, 
                             dropout=params['dropout_rate']))
            elif params['architecture'] == 'bidirectional':
                model.add(Bidirectional(LSTM(units, return_sequences=return_sequences, 
                                           dropout=params['dropout_rate'])))
            
            if return_sequences:
                model.add(BatchNormalization())
        
        # Dense Schichten
        model.add(Dense(params['dense_units'], activation=params['activation']))
        model.add(Dropout(params['dropout_rate']))
        
        # Zusätzliche Dense Schicht für komplexere Modelle
        if params['dense_units'] > 32:
            model.add(Dense(params['dense_units']//2, activation=params['activation']))
            model.add(Dropout(params['dropout_rate']/2))
        
        # Output Schicht
        model.add(Dense(1))
        
        # Kompiliere Modell
        optimizer = self.get_optimizer(params['optimizer'], params['learning_rate'])
        model.compile(optimizer=optimizer, loss=params['loss_function'], metrics=['mae'])
        
        return model
    
    def evaluate_params(self, params, features):
        """Evaluiere Parameter-Kombination"""
        try:
            # Daten vorbereiten
            X = features.drop('close', axis=1)
            y = features['close'].values
            
            # Skalierung
            feature_scaler = self.get_scaler(params['scaler'])
            target_scaler = self.get_scaler(params['scaler'])
            
            X_scaled = feature_scaler.fit_transform(X)
            y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
            
            # Sequenzen erstellen
            X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, params['look_back'])
            
            if len(X_seq) < 50:  # Zu wenig Daten
                return None
            
            # Train-Validation-Test Split
            train_size = int(len(X_seq) * 0.7)
            val_size = int(len(X_seq) * 0.15)
            
            X_train = X_seq[:train_size]
            y_train = y_seq[:train_size]
            X_val = X_seq[train_size:train_size+val_size]
            y_val = y_seq[train_size:train_size+val_size]
            X_test = X_seq[train_size+val_size:]
            y_test = y_seq[train_size+val_size:]
            
            # Modell erstellen
            model = self.build_model(params, (X_train.shape[1], X_train.shape[2]))
            
            # Callbacks
            callbacks = [
                EarlyStopping(patience=15, restore_best_weights=True, verbose=0),
                ReduceLROnPlateau(factor=0.5, patience=8, min_lr=1e-7, verbose=0)
            ]
            
            # Training
            start_time = time.time()
            history = model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=100,
                batch_size=params['batch_size'],
                callbacks=callbacks,
                verbose=0
            )
            training_time = time.time() - start_time
            
            # Evaluation
            y_pred = model.predict(X_test, verbose=0)
            
            # Skalierung rückgängig machen
            y_test_orig = target_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
            y_pred_orig = target_scaler.inverse_transform(y_pred).flatten()
            
            # Metriken berechnen
            r2 = r2_score(y_test_orig, y_pred_orig)
            rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
            mae = mean_absolute_error(y_test_orig, y_pred_orig)
            mape = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig)) * 100
            
            # Richtungsgenauigkeit
            if len(y_test_orig) > 1:
                true_direction = np.diff(y_test_orig) > 0
                pred_direction = np.diff(y_pred_orig) > 0
                direction_acc = np.mean(true_direction == pred_direction) * 100
            else:
                direction_acc = 0
            
            # Kombinierter Score (gewichtete Kombination verschiedener Metriken)
            combined_score = (
                r2 * 0.4 +                           # R² (40%)
                (1 - mape/100) * 0.3 +               # MAPE (30%)
                (direction_acc/100) * 0.2 +          # Direction Accuracy (20%)
                (1 - min(training_time/300, 1)) * 0.1  # Training Time (10%)
            )
            
            result = {
                'params': params.copy(),
                'r2': r2,
                'rmse': rmse,
                'mae': mae,
                'mape': mape,
                'direction_accuracy': direction_acc,
                'combined_score': combined_score,
                'training_time': training_time,
                'model_params': model.count_params(),
                'epochs_trained': len(history.history['loss']),
                'val_loss': min(history.history['val_loss'])
            }
            
            # Speichere bestes Modell
            if combined_score > self.best_score:
                self.best_score = combined_score
                self.best_params = params.copy()
                self.best_model = model
            
            return result
            
        except Exception as e:
            return None
    
    def random_search(self, features, n_trials=50):
        """Random Search für Hyperparameter"""
        print(f"\n🎯 Starte Random Search mit {n_trials} Versuchen...")
        
        for trial in range(n_trials):
            # Zufällige Parameter-Kombination
            params = {}
            for param, values in HYPERPARAMETER_GRID.items():
                params[param] = np.random.choice(values)
            
            print(f"\n   Trial {trial+1}/{n_trials}:")
            print(f"   Params: {params}")
            
            start_time = time.time()
            result = self.evaluate_params(params, features)
            eval_time = time.time() - start_time
            
            if result:
                self.results.append(result)
                print(f"   ✅ Score: {result['combined_score']:.4f}, R²: {result['r2']:.4f}")
                print(f"      RMSE: ${result['rmse']:.2f}, Zeit: {eval_time:.1f}s")
            else:
                print(f"   ❌ Fehlgeschlagen nach {eval_time:.1f}s")
            
            # Memory cleanup
            tf.keras.backend.clear_session()
        
        print(f"\n✅ Random Search abgeschlossen!")
        print(f"   {len(self.results)} erfolgreiche Versuche")
    
    def analyze_results(self):
        """Analysiere Hyperparameter-Tuning Ergebnisse"""
        if not self.results:
            print("❌ Keine Ergebnisse zum Analysieren!")
            return
        
        print("\n" + "=" * 70)
        print("📊 HYPERPARAMETER-TUNING ANALYSE")
        print("=" * 70)
        
        # Sortiere nach Combined Score
        sorted_results = sorted(self.results, key=lambda x: x['combined_score'], reverse=True)
        
        print(f"\n🏆 TOP 10 PARAMETER-KOMBINATIONEN:")
        for i, result in enumerate(sorted_results[:10]):
            print(f"\n   {i+1}. Combined Score: {result['combined_score']:.4f}")
            print(f"      R²: {result['r2']:.4f} ({result['r2']*100:.1f}%)")
            print(f"      RMSE: ${result['rmse']:.2f}")
            print(f"      MAPE: {result['mape']:.2f}%")
            print(f"      Direction Acc: {result['direction_accuracy']:.1f}%")
            print(f"      Training Zeit: {result['training_time']:.1f}s")
            print(f"      Parameter: {result['model_params']:,}")
            
            # Wichtigste Parameter anzeigen
            params = result['params']
            print(f"      Key Params: arch={params['architecture']}, units={params['lstm_units']}, ")
            print(f"                  lr={params['learning_rate']}, dropout={params['dropout_rate']}")
        
        # Beste Parameter
        best = sorted_results[0]
        print(f"\n🎯 BESTE PARAMETER-KOMBINATION:")
        for param, value in best['params'].items():
            print(f"   {param}: {value}")
        
        # Parameter-Analyse
        self.analyze_parameter_importance()
        
        # Visualisierung
        self.plot_tuning_results()
    
    def analyze_parameter_importance(self):
        """Analysiere Wichtigkeit einzelner Parameter"""
        print(f"\n📈 PARAMETER-WICHTIGKEIT ANALYSE:")
        
        # Für jeden Parameter: Durchschnittlicher Score pro Wert
        for param in HYPERPARAMETER_GRID.keys():
            param_scores = {}
            
            for result in self.results:
                value = result['params'][param]
                if value not in param_scores:
                    param_scores[value] = []
                param_scores[value].append(result['combined_score'])
            
            # Durchschnittliche Scores berechnen
            avg_scores = {value: np.mean(scores) for value, scores in param_scores.items()}
            
            # Sortiere nach Score
            sorted_values = sorted(avg_scores.items(), key=lambda x: x[1], reverse=True)
            
            print(f"\n   {param}:")
            for value, score in sorted_values[:3]:  # Top 3
                print(f"      {value}: {score:.4f}")
    
    def plot_tuning_results(self):
        """Visualisiere Tuning-Ergebnisse"""
        if len(self.results) < 5:
            return
        
        plt.figure(figsize=(20, 15))
        
        # 1. Score Distribution
        plt.subplot(3, 4, 1)
        scores = [r['combined_score'] for r in self.results]
        plt.hist(scores, bins=20, alpha=0.7, color='blue', edgecolor='black')
        plt.title('Combined Score Distribution')
        plt.xlabel('Combined Score')
        plt.ylabel('Frequency')
        
        # 2. R² vs Combined Score
        plt.subplot(3, 4, 2)
        r2_scores = [r['r2'] for r in self.results]
        plt.scatter(r2_scores, scores, alpha=0.6)
        plt.title('R² vs Combined Score')
        plt.xlabel('R² Score')
        plt.ylabel('Combined Score')
        
        # 3. Training Time vs Score
        plt.subplot(3, 4, 3)
        times = [r['training_time'] for r in self.results]
        plt.scatter(times, scores, alpha=0.6, c='red')
        plt.title('Training Time vs Score')
        plt.xlabel('Training Time (s)')
        plt.ylabel('Combined Score')
        
        # 4. Model Parameters vs Score
        plt.subplot(3, 4, 4)
        params = [r['model_params'] for r in self.results]
        plt.scatter(params, scores, alpha=0.6, c='green')
        plt.title('Model Parameters vs Score')
        plt.xlabel('Model Parameters')
        plt.ylabel('Combined Score')
        
        # 5-12. Parameter-spezifische Plots
        param_plots = ['lstm_units', 'learning_rate', 'dropout_rate', 'batch_size', 
                      'look_back', 'dense_units', 'lstm_layers', 'architecture']
        
        for i, param in enumerate(param_plots):
            plt.subplot(3, 4, i+5)
            
            if param in ['lstm_units', 'dense_units', 'batch_size', 'look_back', 'lstm_layers']:
                # Numerische Parameter
                param_values = [r['params'][param] for r in self.results]
                plt.scatter(param_values, scores, alpha=0.6)
                plt.xlabel(param)
                plt.ylabel('Combined Score')
            else:
                # Kategorische Parameter
                param_scores = {}
                for result in self.results:
                    value = result['params'][param]
                    if value not in param_scores:
                        param_scores[value] = []
                    param_scores[value].append(result['combined_score'])
                
                avg_scores = {value: np.mean(scores) for value, scores in param_scores.items()}
                
                values = list(avg_scores.keys())
                avg_vals = list(avg_scores.values())
                
                plt.bar(values, avg_vals, alpha=0.7)
                plt.xlabel(param)
                plt.ylabel('Avg Combined Score')
                plt.xticks(rotation=45)
            
            plt.title(f'{param} vs Score')
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

def main():
    """Hauptfunktion für Hyperparameter-Tuning"""
    print(f"💻 System: TensorFlow {tf.__version__}")
    
    tuner = HyperparameterTuner()
    
    # Daten laden
    features = tuner.load_and_prepare_data()
    
    # Random Search durchführen
    tuner.random_search(features, n_trials=30)  # Reduziert für Demo
    
    # Ergebnisse analysieren
    tuner.analyze_results()
    
    print(f"\n✅ HYPERPARAMETER-TUNING ABGESCHLOSSEN!")
    print(f"   Beste Combined Score: {tuner.best_score:.4f}")

if __name__ == "__main__":
    main()
