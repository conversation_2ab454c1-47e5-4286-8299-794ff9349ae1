#!/usr/bin/env python3
"""
🚀 CPU MAX SIMPLE 48H BITCOIN PREDICTION 🚀
============================================
Maximale CPU-Auslastung ohne externe Dependencies
"""

import os
import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge
import yfinance as yf
from multiprocessing import Pool, cpu_count
from concurrent.futures import ThreadPoolExecutor

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

# CPU MAX KONFIGURATION
MAX_CORES = cpu_count()
PARALLEL_JOBS = -1  # Alle verfügbaren Kerne
MONTE_CARLO_SIMS = 1000  # Optimiert für Geschwindigkeit

print("🚀 CPU MAX SIMPLE 48H BITCOIN PREDICTION")
print("=" * 44)
print(f"💻 CPU Kerne: {MAX_CORES}")
print(f"⚡ Parallel Jobs: MAXIMUM")
print(f"🔮 Monte Carlo: {MONTE_CARLO_SIMS} Simulationen")
print(f"🕐 Start: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def get_bitcoin_data_fast():
    """Schnelle Bitcoin-Datensammlung"""
    print("📊 Lade Bitcoin-Daten...")
    
    try:
        btc = yf.Ticker("BTC-USD")
        df = btc.history(period="3mo", interval="1h")
        
        if len(df) > 100:
            df.columns = [col.lower() for col in df.columns]
            print(f"✅ Echte Bitcoin-Daten: {len(df)} Stunden")
            print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
            return df, True
        else:
            raise Exception("Zu wenig Daten")
            
    except Exception as e:
        print(f"⚠️ API-Fehler, generiere Performance Daten...")
        
        # Schnelle Datengeneration
        end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        start_time = end_time - timedelta(days=90)
        dates = pd.date_range(start=start_time, end=end_time, freq='H')
        
        n_points = len(dates)
        np.random.seed(42)
        
        # Vectorized operations
        base_price = 67000
        trend = np.linspace(-5000, 8000, n_points)
        daily_vol = np.random.normal(0, 2000, n_points)
        weekly_cycle = 1500 * np.sin(2 * np.pi * np.arange(n_points) / (24 * 7))
        monthly_cycle = 3000 * np.sin(2 * np.pi * np.arange(n_points) / (24 * 30))
        
        prices = base_price + trend + daily_vol + weekly_cycle + monthly_cycle
        prices = np.maximum(prices, 30000)
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * np.random.uniform(1.001, 1.04, n_points),
            'low': prices * np.random.uniform(0.96, 0.999, n_points),
            'open': prices * np.random.uniform(0.99, 1.01, n_points),
            'volume': np.random.lognormal(15, 0.3, n_points)
        }, index=dates)
        
        print(f"✅ Performance Daten: {len(df)} Stunden")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
        return df, False

def create_cpu_max_features(df):
    """CPU-Max Feature Engineering"""
    print("🔧 Erstelle CPU-Max Features...")
    
    df = df.copy()
    
    # === TREND FEATURES ===
    print("   📈 Trend Features...")
    for window in [6, 12, 24, 48, 72]:
        df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
        df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
        df[f'price_vs_sma_{window}'] = df['close'] / df[f'sma_{window}'] - 1
    
    # === MOMENTUM FEATURES ===
    print("   ⚡ Momentum Features...")
    
    # RSI
    for period in [14, 24, 48]:
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(window=period).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
        rs = gain / loss
        df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
    
    # MACD
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    df['macd'] = ema_12 - ema_26
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    df['macd_histogram'] = df['macd'] - df['macd_signal']
    
    # === VOLATILITY FEATURES ===
    print("   📊 Volatility Features...")
    
    # Bollinger Bands
    for window in [20, 24, 48]:
        bb_middle = df['close'].rolling(window=window).mean()
        bb_std = df['close'].rolling(window=window).std()
        df[f'bb_upper_{window}'] = bb_middle + 2 * bb_std
        df[f'bb_lower_{window}'] = bb_middle - 2 * bb_std
        df[f'bb_position_{window}'] = (df['close'] - df[f'bb_lower_{window}']) / (df[f'bb_upper_{window}'] - df[f'bb_lower_{window}'])
    
    # Volatilität
    for window in [12, 24, 48]:
        df[f'volatility_{window}'] = df['close'].rolling(window=window).std()
        df[f'volatility_ratio_{window}'] = df[f'volatility_{window}'] / df['close']
    
    # === PRICE ACTION FEATURES ===
    print("   💰 Price Action Features...")
    
    # Price Changes
    for period in [1, 3, 6, 12, 24, 48]:
        df[f'price_change_{period}'] = df['close'].pct_change(periods=period)
        df[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
    
    # High-Low Features
    if 'high' in df.columns and 'low' in df.columns:
        df['hl_ratio'] = df['high'] / df['low']
        df['price_range'] = df['high'] - df['low']
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        # True Range
        df['tr'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                np.abs(df['high'] - df['close'].shift()),
                np.abs(df['low'] - df['close'].shift())
            )
        )
        df['atr_14'] = df['tr'].rolling(window=14).mean()
    
    # === VOLUME FEATURES ===
    print("   📦 Volume Features...")
    if 'volume' in df.columns:
        for window in [12, 24, 48]:
            df[f'volume_sma_{window}'] = df['volume'].rolling(window=window).mean()
            df[f'volume_ratio_{window}'] = df['volume'] / df[f'volume_sma_{window}']
    
    # === LAG FEATURES ===
    print("   🔄 Lag Features...")
    for lag in [1, 2, 3, 6, 12, 24]:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
        if 'rsi_14' in df.columns:
            df[f'rsi_lag_{lag}'] = df['rsi_14'].shift(lag)
    
    # === TIME FEATURES ===
    print("   🕐 Time Features...")
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    df['is_trading_hours'] = ((df.index.hour >= 9) & (df.index.hour <= 16)).astype(int)
    
    # Cyclical encoding
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    
    # === MARKET REGIME FEATURES ===
    print("   🏛️ Market Regime Features...")
    
    # Trend Strength
    def calc_trend_strength(prices):
        if len(prices) == 24:
            return np.abs(np.polyfit(range(24), prices, 1)[0])
        return 0
    
    df['trend_strength'] = df['close'].rolling(window=24).apply(calc_trend_strength)
    
    # Market Regime
    short_ma = df['close'].rolling(window=12).mean()
    long_ma = df['close'].rolling(window=48).mean()
    df['market_regime'] = np.where(short_ma > long_ma * 1.02, 1,
                                  np.where(short_ma < long_ma * 0.98, -1, 0))
    
    print(f"✅ CPU-Max Features erstellt: {df.shape[1]} Spalten")
    return df.dropna()

def prepare_cpu_max_data(df, sequence_length=60):
    """CPU-Max Datenvorbereitung"""
    print(f"🔄 Bereite CPU-Max Daten vor...")
    
    feature_cols = [col for col in df.columns if col != 'close']
    features = df[feature_cols].values
    target = df['close'].values
    
    # Skalierung
    feature_scaler = RobustScaler()
    target_scaler = MinMaxScaler()
    
    features_scaled = feature_scaler.fit_transform(features)
    target_scaled = target_scaler.fit_transform(target.reshape(-1, 1)).flatten()
    
    # Sequenzen erstellen
    X, y = [], []
    for i in range(sequence_length, len(features_scaled)):
        X.append(features_scaled[i-sequence_length:i])
        y.append(target_scaled[i])
    
    X, y = np.array(X), np.array(y)
    
    # Train/Test Split
    train_size = int(len(X) * 0.85)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]
    
    last_sequence = features_scaled[-sequence_length:]
    
    print(f"✅ CPU-Max Daten vorbereitet:")
    print(f"   Train: {X_train.shape}")
    print(f"   Test: {X_test.shape}")
    
    return (X_train, y_train), (X_test, y_test), last_sequence, (feature_scaler, target_scaler)

def train_cpu_max_models(train_data, test_data):
    """CPU-Max Model Training mit ALLEN Kernen"""
    print(f"\n🚀 Trainiere CPU-Max Modelle mit ALLEN {MAX_CORES} Kernen...")
    
    X_train, y_train = train_data
    X_test, y_test = test_data
    
    X_train_flat = X_train.reshape(X_train.shape[0], -1)
    X_test_flat = X_test.reshape(X_test.shape[0], -1)
    
    # CPU-Max Modelle
    models = {
        'ExtraTrees_CPU_MAX': ExtraTreesRegressor(
            n_estimators=300,
            max_depth=20,
            min_samples_split=2,
            min_samples_leaf=1,
            n_jobs=PARALLEL_JOBS,  # ALLE Kerne
            random_state=42,
            bootstrap=True
        ),
        'RandomForest_CPU_MAX': RandomForestRegressor(
            n_estimators=200,
            max_depth=18,
            min_samples_split=3,
            min_samples_leaf=1,
            n_jobs=PARALLEL_JOBS,  # ALLE Kerne
            random_state=42,
            bootstrap=True
        ),
        'GradientBoosting_CPU_MAX': GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=8,
            subsample=0.9,
            random_state=42
        )
    }
    
    results = {}
    
    for model_name, model in models.items():
        print(f"\n🤖 Trainiere {model_name} mit maximaler CPU-Power...")
        
        start_time = time.time()
        model.fit(X_train_flat, y_train)
        training_time = time.time() - start_time
        
        # Vorhersagen
        y_pred = model.predict(X_test_flat)
        
        # Metriken
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        results[model_name] = {
            'model': model,
            'training_time': training_time,
            'mse': mse,
            'mae': mae,
            'rmse': np.sqrt(mse),
            'r2': r2,
            'y_pred': y_pred,
            'y_test': y_test
        }
        
        print(f"✅ {model_name}: R²={r2:.4f}, RMSE={np.sqrt(mse):.4f}, Zeit={training_time:.1f}s")
    
    return results

def run_monte_carlo_simulation(args):
    """Monte Carlo Simulation für Parallelisierung"""
    model, last_sequence, target_hour, n_simulations = args

    predictions = []

    for sim in range(n_simulations):
        # Noise für Unsicherheit
        noise_level = 0.005 * (target_hour / 48)
        noisy_sequence = last_sequence + np.random.normal(0, noise_level, last_sequence.shape)
        current_sequence = noisy_sequence.copy()

        # Iterative Vorhersage (optimiert)
        step_size = max(1, target_hour // 6)

        for step in range(0, target_hour, step_size):
            pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]

            # Realistische Constraints
            if step > 0:
                prev_price = current_sequence[-1, 0]
                max_change = 0.02 * step_size
                pred_scaled = np.clip(pred_scaled,
                                    prev_price * (1 - max_change),
                                    prev_price * (1 + max_change))

            # Sequence aktualisieren
            new_row = current_sequence[-1].copy()
            new_row[0] = pred_scaled
            current_sequence = np.vstack([current_sequence[1:], new_row])

        # Finale Vorhersage
        final_pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]
        predictions.append(final_pred_scaled)

    return predictions

def predict_cpu_max_48h(best_models, last_sequence, target_scaler, current_time, current_price):
    """CPU-Max 48h Vorhersage mit parallelen Monte Carlo Simulationen"""
    print(f"🔮 Erstelle CPU-Max 48h Vorhersage mit {MAX_CORES} Kernen...")
    print(f"   Monte Carlo Simulationen: {MONTE_CARLO_SIMS}")

    key_hours = [1, 3, 6, 12, 18, 24, 30, 36, 42, 48]
    predictions = {}

    for hour in key_hours:
        print(f"📈 Berechne +{hour}h mit {MONTE_CARLO_SIMS} Simulationen...")

        all_model_predictions = []

        # Für jedes Modell
        for model_name, model_data in best_models.items():
            model = model_data['model']

            # Parallele Monte Carlo Simulation
            batch_size = MONTE_CARLO_SIMS // MAX_CORES

            # Argumente für parallele Verarbeitung
            args_list = []
            for batch in range(MAX_CORES):
                start_sim = batch * batch_size
                end_sim = (batch + 1) * batch_size if batch < MAX_CORES - 1 else MONTE_CARLO_SIMS
                n_sims_batch = end_sim - start_sim

                args_list.append((model, last_sequence, hour, n_sims_batch))

            # Parallele Ausführung
            with Pool(processes=MAX_CORES) as pool:
                batch_results = pool.map(run_monte_carlo_simulation, args_list)

            # Ergebnisse sammeln
            model_predictions = []
            for batch_result in batch_results:
                model_predictions.extend(batch_result)

            all_model_predictions.extend(model_predictions)

        # Zurück transformieren
        all_predictions = np.array(all_model_predictions)
        all_predictions_orig = target_scaler.inverse_transform(all_predictions.reshape(-1, 1)).flatten()

        # Wahrscheinlichkeitsberechnung
        predictions[hour] = {
            'datetime': current_time + timedelta(hours=hour),
            'mean': np.mean(all_predictions_orig),
            'median': np.median(all_predictions_orig),
            'std': np.std(all_predictions_orig),
            'min': np.min(all_predictions_orig),
            'max': np.max(all_predictions_orig),
            'q05': np.percentile(all_predictions_orig, 5),
            'q10': np.percentile(all_predictions_orig, 10),
            'q25': np.percentile(all_predictions_orig, 25),
            'q75': np.percentile(all_predictions_orig, 75),
            'q90': np.percentile(all_predictions_orig, 90),
            'q95': np.percentile(all_predictions_orig, 95),

            # Wahrscheinlichkeiten
            'prob_above_current': np.mean(all_predictions_orig > current_price) * 100,
            'prob_above_1pct': np.mean(all_predictions_orig > current_price * 1.01) * 100,
            'prob_above_2pct': np.mean(all_predictions_orig > current_price * 1.02) * 100,
            'prob_above_5pct': np.mean(all_predictions_orig > current_price * 1.05) * 100,
            'prob_above_10pct': np.mean(all_predictions_orig > current_price * 1.10) * 100,
            'prob_below_current': np.mean(all_predictions_orig < current_price) * 100,
            'prob_below_1pct': np.mean(all_predictions_orig < current_price * 0.99) * 100,
            'prob_below_2pct': np.mean(all_predictions_orig < current_price * 0.98) * 100,
            'prob_below_5pct': np.mean(all_predictions_orig < current_price * 0.95) * 100,
            'prob_below_10pct': np.mean(all_predictions_orig < current_price * 0.90) * 100,

            # Änderungen
            'change_pct': ((np.mean(all_predictions_orig) / current_price) - 1) * 100,
            'change_abs': np.mean(all_predictions_orig) - current_price,

            'all_predictions': all_predictions_orig
        }

    return predictions

def create_cpu_max_visualization(df, results, predictions, current_time, current_price, is_real_data):
    """CPU-Max Visualisierung"""
    print("📊 Erstelle CPU-Max Visualisierung...")

    fig = plt.figure(figsize=(24, 16))
    fig.patch.set_facecolor('#0a0a0a')

    title = "🚀 CPU-MAX 48H BITCOIN PREDICTION"
    subtitle = f"📅 Ab: {current_time.strftime('%Y-%m-%d %H:%M')} | 💰 Aktuell: ${current_price:,.0f} | 🖥️ {MAX_CORES} CPU Kerne | 🔮 {MONTE_CARLO_SIMS} Simulationen"
    fig.suptitle(f'{title}\n{subtitle}',
                 fontsize=18, color='white', fontweight='bold', y=0.95)

    # === 1. HAUPTCHART ===
    ax1 = plt.subplot2grid((4, 6), (0, 0), colspan=4, rowspan=2)

    # Historische Daten
    recent_data = df.tail(168)  # Letzte 7 Tage
    ax1.plot(recent_data.index, recent_data['close'],
             color='#00D4FF', linewidth=3, label='Historischer Preis', alpha=0.9)

    # Jetzt markieren
    ax1.axvline(x=current_time, color='#FF6B6B', linestyle='-', linewidth=4,
                label='JETZT', alpha=0.9)

    # Zukunftsprognose
    future_times = [pred['datetime'] for pred in predictions.values()]
    future_means = [pred['mean'] for pred in predictions.values()]
    future_q05 = [pred['q05'] for pred in predictions.values()]
    future_q95 = [pred['q95'] for pred in predictions.values()]
    future_q25 = [pred['q25'] for pred in predictions.values()]
    future_q75 = [pred['q75'] for pred in predictions.values()]

    # Prognose-Linie
    ax1.plot(future_times, future_means, color='#FFD700', linewidth=4,
             label='48h CPU-Max Prognose', alpha=0.9, marker='o', markersize=6)

    # Konfidenzintervalle
    ax1.fill_between(future_times, future_q05, future_q95,
                     color='#FFD700', alpha=0.15, label='90% Konfidenz')
    ax1.fill_between(future_times, future_q25, future_q75,
                     color='#FFD700', alpha=0.25, label='50% Konfidenz')

    # Wichtige Zeitpunkte
    for hour in [6, 12, 24, 36, 48]:
        if hour in predictions:
            pred = predictions[hour]
            ax1.axvline(x=pred['datetime'], color='#FF9500', linestyle='--',
                       alpha=0.7, linewidth=2)
            ax1.text(pred['datetime'], ax1.get_ylim()[1]*0.98, f'+{hour}h',
                    rotation=90, color='#FF9500', fontsize=11, ha='center', fontweight='bold')

    ax1.set_title('Bitcoin: Vergangenheit → CPU-MAX ZUKUNFT',
                  fontsize=15, color='white', fontweight='bold')
    ax1.set_ylabel('Preis (USD)', color='white', fontsize=12)
    ax1.legend(loc='upper left', fontsize=10)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(colors='white', labelsize=10)

    # X-Achse formatieren
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax1.xaxis.set_major_locator(mdates.HourLocator(interval=12))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

    # === 2. WAHRSCHEINLICHKEITS-TABELLE ===
    ax2 = plt.subplot2grid((4, 6), (0, 4), colspan=2, rowspan=2)
    ax2.axis('off')

    table_data = []
    headers = ['Zeit', 'Preis', 'Änderung', 'Wahrsch. ↑', 'Wahrsch. +5%']

    key_hours = [6, 12, 24, 36, 48]
    for hour in key_hours:
        if hour in predictions:
            pred = predictions[hour]

            table_data.append([
                f"+{hour}h",
                f"${pred['mean']:,.0f}",
                f"{pred['change_pct']:+.1f}%",
                f"{pred['prob_above_current']:.0f}%",
                f"{pred['prob_above_5pct']:.0f}%"
            ])

    table = ax2.table(cellText=table_data, colLabels=headers,
                     cellLoc='center', loc='center',
                     colWidths=[0.15, 0.22, 0.18, 0.22, 0.23])

    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1, 2.2)

    # Tabellen-Styling
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#333333')
        table[(0, i)].set_text_props(weight='bold', color='white')

    for i in range(1, len(table_data) + 1):
        for j in range(len(headers)):
            table[(i, j)].set_facecolor('#1a1a1a')
            table[(i, j)].set_text_props(color='white')

            # Farbkodierung
            if j == 2:  # Änderung %
                change_val = float(table_data[i-1][j].replace('%', '').replace('+', ''))
                if change_val > 2:
                    table[(i, j)].set_facecolor('#2d5a2d')
                elif change_val > 0:
                    table[(i, j)].set_facecolor('#3d4a2d')
                elif change_val > -2:
                    table[(i, j)].set_facecolor('#4a3d2d')
                else:
                    table[(i, j)].set_facecolor('#5a2d2d')
            elif j in [3, 4]:  # Wahrscheinlichkeiten
                prob_val = float(table_data[i-1][j].replace('%', ''))
                if prob_val > 65:
                    table[(i, j)].set_facecolor('#2d5a2d')
                elif prob_val > 50:
                    table[(i, j)].set_facecolor('#3d4a2d')
                elif prob_val > 35:
                    table[(i, j)].set_facecolor('#4a4a2d')
                else:
                    table[(i, j)].set_facecolor('#5a3d2d')

    ax2.set_title('CPU-Max Wahrscheinlichkeits-Analyse', color='white', fontweight='bold', fontsize=13)

    # === 3. ZUSAMMENFASSUNG ===
    ax3 = plt.subplot2grid((4, 6), (2, 0), colspan=6)
    ax3.axis('off')

    best_model = max(results.keys(), key=lambda x: results[x]['r2'])
    pred_48h = predictions[48]

    # Trading-Empfehlung
    prob_up = pred_48h['prob_above_current']
    change_48h = pred_48h['change_pct']

    if prob_up > 70 and change_48h > 3:
        recommendation = "STARKER KAUF 🚀🚀"
        rec_color = "#00FF00"
    elif prob_up > 60 and change_48h > 1:
        recommendation = "KAUF 📈"
        rec_color = "#90EE90"
    elif prob_up > 40:
        recommendation = "HALTEN ⚖️"
        rec_color = "#FFD700"
    elif prob_up < 30 and change_48h < -3:
        recommendation = "STARKER VERKAUF 🔻"
        rec_color = "#FF6B6B"
    else:
        recommendation = "VERKAUF 📉"
        rec_color = "#FFA500"

    summary_text = f"""
🚀 CPU-MAX 48H BITCOIN PREDICTION ZUSAMMENFASSUNG

📊 MODELL: {best_model.replace('_CPU_MAX', '')} (R²: {results[best_model]['r2']:.3f})  |  📅 PROGNOSE AB: {current_time.strftime('%Y-%m-%d %H:%M')}

💰 AKTUELL: ${current_price:,.0f}  →  🔮 48H ERWARTUNG: ${pred_48h['mean']:,.0f} ({change_48h:+.1f}%)

📈 WAHRSCHEINLICHKEITEN:
   Preis steigt: {pred_48h['prob_above_current']:.0f}%  |  Preis fällt: {pred_48h['prob_below_current']:.0f}%
   Gewinn >1%: {pred_48h['prob_above_1pct']:.0f}%  |  Gewinn >5%: {pred_48h['prob_above_5pct']:.0f}%  |  Gewinn >10%: {pred_48h['prob_above_10pct']:.0f}%

🎯 KONFIDENZ-BEREICH (90%): ${pred_48h['q05']:,.0f} - ${pred_48h['q95']:,.0f}  |  💻 CPU KERNE: {MAX_CORES}  |  🔮 SIMULATIONEN: {MONTE_CARLO_SIMS}
    """

    # Haupttext
    ax3.text(0.02, 0.8, summary_text, transform=ax3.transAxes,
             fontsize=11, color='white', verticalalignment='top',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='#1a1a1a', alpha=0.9))

    # Trading-Empfehlung hervorheben
    ax3.text(0.75, 0.5, recommendation, transform=ax3.transAxes,
             fontsize=16, color=rec_color, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.5', facecolor=rec_color, alpha=0.2))

    # Wahrscheinlichkeit hervorheben
    ax3.text(0.75, 0.2, f"{prob_up:.0f}% AUFWÄRTS", transform=ax3.transAxes,
             fontsize=13, color='#00FF00' if prob_up > 50 else '#FF6B6B', fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.3',
                      facecolor='#00FF00' if prob_up > 50 else '#FF6B6B', alpha=0.2))

    plt.tight_layout()
    plt.subplots_adjust(wspace=0.3, hspace=0.4)

    return fig

def main():
    """CPU-Max Hauptfunktion"""
    print("\n🚀" * 22)
    print("CPU-MAX 48H BITCOIN PREDICTION")
    print("🚀" * 22)

    start_time = time.time()

    try:
        # 1. Daten laden
        print("\n" + "="*55)
        print("PHASE 1: CPU-MAX DATENSAMMLUNG")
        print("="*55)
        df, is_real_data = get_bitcoin_data_fast()
        current_time = df.index[-1]
        current_price = df['close'].iloc[-1]

        # 2. Features
        print("\n" + "="*55)
        print("PHASE 2: CPU-MAX FEATURE ENGINEERING")
        print("="*55)
        df_features = create_cpu_max_features(df)

        # 3. Daten vorbereiten
        print("\n" + "="*55)
        print("PHASE 3: CPU-MAX DATENAUFBEREITUNG")
        print("="*55)
        train_data, test_data, last_sequence, scalers = prepare_cpu_max_data(df_features)
        feature_scaler, target_scaler = scalers

        # 4. Modelle trainieren
        print("\n" + "="*55)
        print("PHASE 4: CPU-MAX MODEL TRAINING")
        print("="*55)
        results = train_cpu_max_models(train_data, test_data)

        # 5. Beste Modelle
        sorted_results = sorted(results.items(), key=lambda x: x[1]['r2'], reverse=True)
        best_models = dict(sorted_results[:2])  # Top 2 für Geschwindigkeit

        print(f"\n🏆 Top Modelle für CPU-Max Vorhersage:")
        for name, result in best_models.items():
            print(f"   {name}: R²={result['r2']:.4f}")

        # 6. CPU-Max 48h Vorhersage
        print("\n" + "="*55)
        print("PHASE 5: CPU-MAX 48H VORHERSAGE")
        print("="*55)

        predictions = predict_cpu_max_48h(
            best_models, last_sequence, target_scaler, current_time, current_price
        )

        # 7. Visualisierung
        print("\n" + "="*55)
        print("PHASE 6: CPU-MAX VISUALISIERUNG")
        print("="*55)

        fig = create_cpu_max_visualization(
            df_features, results, predictions, current_time, current_price, is_real_data
        )

        # 8. Zusammenfassung
        total_time = time.time() - start_time
        print_cpu_max_summary(results, predictions, current_time, current_price, total_time, is_real_data)

        # Speichern
        os.makedirs('ultimate_plots', exist_ok=True)
        filename = 'cpu_max_48h_prediction.png'
        plt.savefig(f'ultimate_plots/{filename}',
                    facecolor='#0a0a0a', dpi=300, bbox_inches='tight')

        print(f"✅ CPU-Max Visualisierung: ultimate_plots/{filename}")
        plt.show()

        print(f"\n🎉 CPU-MAX 48H ANALYSE ABGESCHLOSSEN in {total_time:.1f}s! 🎉")

        return {
            'results': results,
            'predictions': predictions,
            'current_time': current_time,
            'current_price': current_price,
            'total_time': total_time,
            'cpu_cores_used': MAX_CORES,
            'monte_carlo_sims': MONTE_CARLO_SIMS
        }

    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()
        return None

def print_cpu_max_summary(results, predictions, current_time, current_price, total_time, is_real_data):
    """CPU-Max Zusammenfassung"""
    print("\n" + "="*75)
    print("🚀 CPU-MAX 48H BITCOIN PREDICTION RESULTS 🚀")
    print("="*75)

    data_type = "ECHTE LIVE-DATEN" if is_real_data else "CPU-MAX SIMULIERTE DATEN"
    print(f"\n📊 DATENQUELLE: {data_type}")
    print(f"📅 PROGNOSE AB: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💰 AKTUELLER PREIS: ${current_price:,.2f}")
    print(f"💻 CPU KERNE GENUTZT: {MAX_CORES}")
    print(f"🔮 MONTE CARLO SIMS: {MONTE_CARLO_SIMS}")

    # Beste Modelle
    best_model = max(results.keys(), key=lambda x: results[x]['r2'])
    print(f"\n🏆 BESTES MODELL: {best_model}")
    print(f"   R² Score: {results[best_model]['r2']:.4f} ({results[best_model]['r2']*100:.1f}%)")
    print(f"   RMSE: {results[best_model]['rmse']:.4f}")

    # 48h Vorhersagen
    print(f"\n🔮 CPU-MAX 48H VORHERSAGEN:")
    print(f"{'Zeit':<6} | {'Preis':<12} | {'Änderung':<10} | {'Wahrsch. ↑':<12} | {'Wahrsch. +5%':<12}")
    print("-" * 70)

    for hour in [6, 12, 24, 36, 48]:
        if hour in predictions:
            pred = predictions[hour]
            print(f"{hour:>4}h | ${pred['mean']:>10,.0f} | {pred['change_pct']:>+7.1f}% | "
                  f"{pred['prob_above_current']:>10.0f}% | {pred['prob_above_5pct']:>10.0f}%")

    # 48h Spezial-Analyse
    if 48 in predictions:
        pred_48h = predictions[48]

        # Trading-Empfehlung
        prob_up = pred_48h['prob_above_current']
        change_48h = pred_48h['change_pct']

        if prob_up > 70 and change_48h > 3:
            recommendation = "STARKER KAUF 🚀🚀"
        elif prob_up > 60 and change_48h > 1:
            recommendation = "KAUF 📈"
        elif prob_up > 40:
            recommendation = "HALTEN ⚖️"
        else:
            recommendation = "VERKAUF 📉"

        print(f"\n💡 CPU-MAX TRADING-EMPFEHLUNG: {recommendation}")
        print(f"   (Basierend auf {prob_up:.0f}% Aufwärts-Wahrscheinlichkeit)")

        print(f"\n🎯 48H DETAILS:")
        print(f"   Erwarteter Preis: ${pred_48h['mean']:,.0f}")
        print(f"   Konfidenz (90%): ${pred_48h['q05']:,.0f} - ${pred_48h['q95']:,.0f}")
        print(f"   Wahrsch. Gewinn >1%: {pred_48h['prob_above_1pct']:.0f}%")
        print(f"   Wahrsch. Gewinn >10%: {pred_48h['prob_above_10pct']:.0f}%")

    print(f"\n⚡ CPU-MAX PERFORMANCE: {total_time:.1f}s mit {MAX_CORES} Kernen")
    print("="*75)

if __name__ == "__main__":
    main()
