#!/usr/bin/env python3
"""
🚀 ULTIMATE 48H BITCOIN PREDICTION - FAST PRECISION VERSION 🚀
==============================================================
Optimiert für schnelle, präzise 48h Vorhersagen mit detaillierter Visualisierung
"""

import os
import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

print("🚀 ULTIMATE 48H BITCOIN PREDICTION - FAST PRECISION VERSION")
print("=" * 65)

def load_and_prepare_data():
    """Daten laden"""
    try:
        df = pd.read_csv('crypto_data.csv', index_col=0, parse_dates=True)
        print(f"✅ Daten geladen: {len(df)} Datenpunkte")
    except:
        print("🔄 Generiere optimierte Beispieldaten...")
        dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='H')
        np.random.seed(42)
        
        # Realistischere Bitcoin-Simulation
        base_price = 45000
        trend = np.linspace(0, 25000, len(dates))
        daily_volatility = 1500 * np.random.normal(0, 1, len(dates))
        weekly_cycle = 3000 * np.sin(2 * np.pi * np.arange(len(dates)) / (24 * 7))
        monthly_cycle = 5000 * np.sin(2 * np.pi * np.arange(len(dates)) / (24 * 30))
        
        prices = base_price + trend + daily_volatility + weekly_cycle + monthly_cycle
        prices = np.maximum(prices, 15000)
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * np.random.uniform(1.001, 1.05, len(dates)),
            'low': prices * np.random.uniform(0.95, 0.999, len(dates)),
            'open': prices * np.random.uniform(0.98, 1.02, len(dates)),
            'volume': np.random.lognormal(15, 0.5, len(dates))
        }, index=dates)
        
        print(f"✅ Beispieldaten generiert: {len(df)} Datenpunkte")
    
    return df

def create_fast_features(df):
    """Schnelle, effektive Features für 48h Vorhersagen"""
    print("🔧 Erstelle Fast Features...")
    
    df = df.copy()
    
    # === ESSENTIAL FEATURES ===
    # Moving Averages
    for window in [6, 12, 24, 48]:
        df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
        df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
        df[f'price_vs_sma_{window}'] = df['close'] / df[f'sma_{window}'] - 1
    
    # RSI
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0).rolling(window=14).mean()
    loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # MACD
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    df['macd'] = ema_12 - ema_26
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    df['macd_histogram'] = df['macd'] - df['macd_signal']
    
    # Volatilität
    for window in [12, 24, 48]:
        df[f'volatility_{window}'] = df['close'].rolling(window=window).std()
    
    # Bollinger Bands
    bb_middle = df['close'].rolling(window=20).mean()
    bb_std = df['close'].rolling(window=20).std()
    df['bb_upper'] = bb_middle + 2 * bb_std
    df['bb_lower'] = bb_middle - 2 * bb_std
    df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    
    # Price Changes
    for period in [1, 3, 6, 12, 24, 48]:
        df[f'price_change_{period}'] = df['close'].pct_change(periods=period)
    
    # Volume Features
    if 'volume' in df.columns:
        df['volume_sma'] = df['volume'].rolling(window=24).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
    
    # Lag Features (wichtigste)
    for lag in [1, 2, 3, 6, 12, 24]:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
    
    # Time Features
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    
    # Cyclical encoding
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    
    print(f"✅ Fast Features erstellt: {df.shape[1]} Spalten")
    return df.dropna()

def prepare_fast_data(df, sequence_length=48):
    """Schnelle Datenvorbereitung"""
    print(f"🔄 Bereite Fast Daten vor...")
    
    feature_cols = [col for col in df.columns if col != 'close']
    features = df[feature_cols].values
    target = df['close'].values
    
    # Skalierung
    feature_scaler = RobustScaler()
    target_scaler = MinMaxScaler()
    
    features_scaled = feature_scaler.fit_transform(features)
    target_scaled = target_scaler.fit_transform(target.reshape(-1, 1)).flatten()
    
    # Sequenzen erstellen
    X, y = [], []
    for i in range(sequence_length, len(features_scaled)):
        X.append(features_scaled[i-sequence_length:i])
        y.append(target_scaled[i])
    
    X, y = np.array(X), np.array(y)
    
    # Train/Test Split
    train_size = int(len(X) * 0.8)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]
    
    print(f"✅ Daten vorbereitet: Train={X_train.shape}, Test={X_test.shape}")
    
    return (X_train, y_train), (X_test, y_test), (feature_scaler, target_scaler)

def train_fast_models(train_data, test_data):
    """Schnelle, effektive Modelle trainieren"""
    print("\n🚀 Starte Fast Model Training...")
    
    X_train, y_train = train_data
    X_test, y_test = test_data
    
    # Flatten für ML Modelle
    X_train_flat = X_train.reshape(X_train.shape[0], -1)
    X_test_flat = X_test.reshape(X_test.shape[0], -1)
    
    models = {
        'Gradient_Boosting': GradientBoostingRegressor(
            n_estimators=100, learning_rate=0.1, max_depth=6, random_state=42
        ),
        'Random_Forest': RandomForestRegressor(
            n_estimators=100, max_depth=10, random_state=42, n_jobs=-1
        ),
        'Ridge_Optimized': Ridge(alpha=1.0)
    }
    
    results = {}
    
    for name, model in models.items():
        print(f"\n🤖 Trainiere {name}...")
        
        start_time = time.time()
        model.fit(X_train_flat, y_train)
        training_time = time.time() - start_time
        
        # Vorhersagen
        y_pred = model.predict(X_test_flat)
        
        # Metriken
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        results[name] = {
            'model': model,
            'training_time': training_time,
            'mse': mse,
            'mae': mae,
            'rmse': np.sqrt(mse),
            'r2': r2,
            'y_pred': y_pred,
            'y_test': y_test
        }
        
        print(f"✅ {name}: R²={r2:.4f}, RMSE={np.sqrt(mse):.4f}, Zeit={training_time:.1f}s")
    
    return results

def predict_48h_fast(best_model, last_sequence, target_scaler):
    """Schnelle 48h Vorhersage"""
    print("🔮 Erstelle 48h Vorhersage...")
    
    detailed_intervals = [1, 3, 6, 12, 18, 24, 30, 36, 42, 48]
    predictions = {}
    
    for interval in detailed_intervals:
        # Monte Carlo mit weniger Simulationen für Geschwindigkeit
        interval_predictions = []
        
        for sim in range(100):  # Reduziert für Geschwindigkeit
            noisy_sequence = last_sequence + np.random.normal(0, 0.005, last_sequence.shape)
            current_sequence = noisy_sequence.copy()
            
            for step in range(interval):
                pred_scaled = best_model.predict(current_sequence.reshape(1, -1))[0]
                
                # Sequence aktualisieren
                new_row = current_sequence[-1].copy()
                new_row[0] = pred_scaled
                current_sequence = np.vstack([current_sequence[1:], new_row])
            
            final_pred_scaled = best_model.predict(current_sequence.reshape(1, -1))[0]
            interval_predictions.append(final_pred_scaled)
        
        # Zurück transformieren
        interval_predictions = np.array(interval_predictions)
        interval_predictions_orig = target_scaler.inverse_transform(interval_predictions.reshape(-1, 1)).flatten()
        
        predictions[interval] = {
            'mean': np.mean(interval_predictions_orig),
            'median': np.median(interval_predictions_orig),
            'std': np.std(interval_predictions_orig),
            'min': np.min(interval_predictions_orig),
            'max': np.max(interval_predictions_orig),
            'q05': np.percentile(interval_predictions_orig, 5),
            'q25': np.percentile(interval_predictions_orig, 25),
            'q75': np.percentile(interval_predictions_orig, 75),
            'q95': np.percentile(interval_predictions_orig, 95)
        }
    
    return predictions

def create_fast_48h_visualization(df, results, predictions, target_scaler):
    """Schnelle, detaillierte 48h Visualisierung"""
    print("📊 Erstelle Fast 48h Visualisierung...")
    
    fig = plt.figure(figsize=(24, 16))
    fig.patch.set_facecolor('#0a0a0a')
    fig.suptitle('🚀 ULTIMATE 48H BITCOIN PREDICTION - FAST PRECISION', 
                 fontsize=20, color='white', fontweight='bold', y=0.98)
    
    # === 1. HAUPTCHART: 48h Vorhersage ===
    ax1 = plt.subplot2grid((4, 4), (0, 0), colspan=3, rowspan=2)
    
    # Historische Daten (letzte 5 Tage)
    recent_data = df.tail(120)
    ax1.plot(recent_data.index, recent_data['close'], 
             color='#00D4FF', linewidth=3, label='Historischer Preis', alpha=0.9)
    
    # Aktuelle Zeit
    current_time = recent_data.index[-1]
    current_price = recent_data['close'].iloc[-1]
    ax1.axvline(x=current_time, color='#FF6B6B', linestyle='-', linewidth=2, 
                label='Jetzt', alpha=0.8)
    
    # 48h Vorhersage
    future_times = pd.date_range(start=current_time, periods=49, freq='H')[1:]
    intervals = list(predictions.keys())
    mean_predictions = [predictions[h]['mean'] for h in intervals]
    q05_predictions = [predictions[h]['q05'] for h in intervals]
    q95_predictions = [predictions[h]['q95'] for h in intervals]
    
    # Interpolation für glatte Linie
    future_hours = np.arange(1, 49)
    interp_mean = np.interp(future_hours, intervals, mean_predictions)
    interp_q05 = np.interp(future_hours, intervals, q05_predictions)
    interp_q95 = np.interp(future_hours, intervals, q95_predictions)
    
    ax1.plot(future_times, interp_mean, color='#FFD700', linewidth=4, 
             label='48h Vorhersage', alpha=0.9)
    ax1.fill_between(future_times, interp_q05, interp_q95, 
                     color='#FFD700', alpha=0.2, label='90% Konfidenzintervall')
    
    # Wichtige Zeitpunkte markieren
    for hour in [6, 12, 24, 36, 48]:
        if hour <= len(future_times):
            ax1.axvline(x=future_times[hour-1], color='#FF9500', linestyle='--', 
                       alpha=0.5, linewidth=1)
            ax1.text(future_times[hour-1], ax1.get_ylim()[1]*0.95, f'{hour}h', 
                    rotation=90, color='#FF9500', fontsize=10)
    
    ax1.set_title('48-Stunden Bitcoin Preisprognose', fontsize=16, color='white', fontweight='bold')
    ax1.set_ylabel('Preis (USD)', color='white', fontsize=12)
    ax1.legend(loc='upper left', fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(colors='white', labelsize=10)
    
    # X-Achse formatieren
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax1.xaxis.set_major_locator(mdates.HourLocator(interval=6))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    
    # === 2. MODELL PERFORMANCE ===
    ax2 = plt.subplot2grid((4, 4), (0, 3))

    model_names = list(results.keys())
    r2_scores = [results[name]['r2'] for name in model_names]
    colors = ['#00FF00' if r2 > 0.8 else '#FFD700' if r2 > 0.5 else '#FF6B6B' for r2 in r2_scores]

    bars = ax2.barh(range(len(model_names)), r2_scores, color=colors, alpha=0.8)
    ax2.set_yticks(range(len(model_names)))
    ax2.set_yticklabels([name.replace('_', ' ') for name in model_names], fontsize=9)
    ax2.set_xlabel('R² Score', color='white', fontsize=10)
    ax2.set_title('Modell Performance', color='white', fontweight='bold', fontsize=12)
    ax2.tick_params(colors='white', labelsize=9)
    ax2.grid(True, alpha=0.3, axis='x')

    for i, (bar, score) in enumerate(zip(bars, r2_scores)):
        ax2.text(score + 0.01, bar.get_y() + bar.get_height()/2,
                f'{score:.3f}', va='center', color='white', fontsize=8)

    # === 3. DETAILLIERTE PROGNOSE TABELLE ===
    ax3 = plt.subplot2grid((4, 4), (1, 3), rowspan=2)
    ax3.axis('off')

    # Tabellen-Daten
    table_data = []
    headers = ['Zeit', 'Prognose', 'Änderung', 'Konfidenz']

    for hour in [1, 6, 12, 24, 36, 48]:
        if hour in predictions:
            pred = predictions[hour]
            change_pct = ((pred['mean'] / current_price) - 1) * 100

            table_data.append([
                f"{hour}h",
                f"${pred['mean']:,.0f}",
                f"{change_pct:+.1f}%",
                f"${pred['q05']:,.0f}-${pred['q95']:,.0f}"
            ])

    # Tabelle erstellen
    table = ax3.table(cellText=table_data, colLabels=headers,
                     cellLoc='center', loc='center',
                     colWidths=[0.2, 0.3, 0.2, 0.3])

    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1, 1.8)

    # Tabellen-Styling
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#333333')
        table[(0, i)].set_text_props(weight='bold', color='white')

    for i in range(1, len(table_data) + 1):
        for j in range(len(headers)):
            table[(i, j)].set_facecolor('#1a1a1a')
            table[(i, j)].set_text_props(color='white')

            # Farbkodierung für Änderung
            if j == 2:  # Änderung % Spalte
                change_val = float(table_data[i-1][j].replace('%', '').replace('+', ''))
                if change_val > 0:
                    table[(i, j)].set_facecolor('#2d5a2d')
                else:
                    table[(i, j)].set_facecolor('#5a2d2d')

    ax3.set_title('48h Prognose Details', color='white', fontweight='bold', fontsize=12)

    # === 4. UNSICHERHEITS-ANALYSE ===
    ax4 = plt.subplot2grid((4, 4), (2, 0), colspan=2)

    hours = list(predictions.keys())
    uncertainties = [predictions[h]['std'] for h in hours]

    ax4.plot(hours, uncertainties, 'o-', color='#FF6B6B', linewidth=3, markersize=6)
    ax4.fill_between(hours, uncertainties, alpha=0.3, color='#FF6B6B')

    ax4.set_title('Vorhersage-Unsicherheit', color='white', fontweight='bold', fontsize=12)
    ax4.set_xlabel('Stunden', color='white', fontsize=10)
    ax4.set_ylabel('Std. Abweichung (USD)', color='white', fontsize=10)
    ax4.grid(True, alpha=0.3)
    ax4.tick_params(colors='white', labelsize=9)

    # === 5. PREIS-VERTEILUNGEN ===
    ax5 = plt.subplot2grid((4, 4), (2, 2), colspan=2)

    if 24 in predictions and 48 in predictions:
        # Vereinfachte Verteilungen (Normal-Approximation)
        pred_24h_mean = predictions[24]['mean']
        pred_24h_std = predictions[24]['std']
        pred_48h_mean = predictions[48]['mean']
        pred_48h_std = predictions[48]['std']

        x_24h = np.linspace(pred_24h_mean - 3*pred_24h_std, pred_24h_mean + 3*pred_24h_std, 100)
        y_24h = (1/(pred_24h_std * np.sqrt(2*np.pi))) * np.exp(-0.5*((x_24h - pred_24h_mean)/pred_24h_std)**2)

        x_48h = np.linspace(pred_48h_mean - 3*pred_48h_std, pred_48h_mean + 3*pred_48h_std, 100)
        y_48h = (1/(pred_48h_std * np.sqrt(2*np.pi))) * np.exp(-0.5*((x_48h - pred_48h_mean)/pred_48h_std)**2)

        ax5.plot(x_24h, y_24h, color='#00D4FF', linewidth=2, label='24h Prognose')
        ax5.fill_between(x_24h, y_24h, alpha=0.3, color='#00D4FF')
        ax5.plot(x_48h, y_48h, color='#FFD700', linewidth=2, label='48h Prognose')
        ax5.fill_between(x_48h, y_48h, alpha=0.3, color='#FFD700')

        ax5.set_title('Prognose-Verteilungen', color='white', fontweight='bold', fontsize=12)
        ax5.set_xlabel('Preis (USD)', color='white', fontsize=10)
        ax5.set_ylabel('Wahrscheinlichkeit', color='white', fontsize=10)
        ax5.legend(fontsize=9)
        ax5.grid(True, alpha=0.3)
        ax5.tick_params(colors='white', labelsize=9)

    # === 6. ZUSAMMENFASSUNG ===
    ax6 = plt.subplot2grid((4, 4), (3, 0), colspan=4)
    ax6.axis('off')

    # Beste Modell-Info
    best_model_name = max(results.keys(), key=lambda x: results[x]['r2'])
    best_r2 = results[best_model_name]['r2']

    # 48h Prognose-Info
    pred_48h_mean = predictions[48]['mean']
    change_48h = ((pred_48h_mean / current_price) - 1) * 100
    confidence_range = predictions[48]['q95'] - predictions[48]['q05']

    # Risiko-Bewertung
    risk_level = "NIEDRIG" if confidence_range < 2000 else "MITTEL" if confidence_range < 5000 else "HOCH"
    risk_color = "#00FF00" if risk_level == "NIEDRIG" else "#FFD700" if risk_level == "MITTEL" else "#FF6B6B"

    # Trading-Empfehlung
    if change_48h > 3 and risk_level == "NIEDRIG":
        recommendation = "STARKER KAUF 🚀"
        rec_color = "#00FF00"
    elif change_48h > 1:
        recommendation = "KAUF 📈"
        rec_color = "#90EE90"
    elif change_48h > -1:
        recommendation = "HALTEN ⚖️"
        rec_color = "#FFD700"
    elif change_48h > -3:
        recommendation = "VERKAUF 📉"
        rec_color = "#FFA500"
    else:
        recommendation = "STARKER VERKAUF 🔻"
        rec_color = "#FF6B6B"

    summary_text = f"""
🚀 ULTIMATE 48H PROGNOSE ZUSAMMENFASSUNG

📊 BESTES MODELL: {best_model_name.replace('_', ' ')} (R²: {best_r2:.3f})

💰 AKTUELL: ${current_price:,.0f}    🔮 48H PROGNOSE: ${pred_48h_mean:,.0f} ({change_48h:+.1f}%)

📈 TREND: {"BULLISH 📈" if change_48h > 0 else "BEARISH 📉"}    ⚠️ RISIKO: {risk_level}

🎯 KONFIDENZ (90%): ${predictions[48]['q05']:,.0f} - ${predictions[48]['q95']:,.0f}
    """

    # Haupttext
    ax6.text(0.02, 0.8, summary_text, transform=ax6.transAxes,
             fontsize=12, color='white', verticalalignment='top',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='#1a1a1a', alpha=0.9))

    # Trading-Empfehlung hervorheben
    ax6.text(0.7, 0.5, recommendation, transform=ax6.transAxes,
             fontsize=16, color=rec_color, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.5', facecolor=rec_color, alpha=0.2))

    # Risiko-Level hervorheben
    ax6.text(0.7, 0.2, f"RISIKO: {risk_level}", transform=ax6.transAxes,
             fontsize=14, color=risk_color, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.3', facecolor=risk_color, alpha=0.2))

    plt.tight_layout()
    plt.subplots_adjust(wspace=0.3, hspace=0.4)

    # Speichern
    os.makedirs('ultimate_plots', exist_ok=True)
    plt.savefig('ultimate_plots/fast_48h_precision_analysis.png',
                facecolor='#0a0a0a', dpi=300, bbox_inches='tight')

    print("✅ Fast 48h Visualisierung gespeichert: ultimate_plots/fast_48h_precision_analysis.png")
    plt.show()

    return fig

def main():
    """Hauptfunktion für Fast 48h Prediction"""
    print("\n🚀" * 20)
    print("ULTIMATE 48H BITCOIN PREDICTION - FAST VERSION")
    print("🚀" * 20)

    start_time = time.time()

    try:
        # 1. Daten laden
        print("\n" + "="*50)
        print("PHASE 1: DATENSAMMLUNG")
        print("="*50)
        df = load_and_prepare_data()

        # 2. Feature Engineering
        print("\n" + "="*50)
        print("PHASE 2: FAST FEATURE ENGINEERING")
        print("="*50)
        df_features = create_fast_features(df)

        # 3. Daten vorbereiten
        print("\n" + "="*50)
        print("PHASE 3: DATENAUFBEREITUNG")
        print("="*50)
        train_data, test_data, scalers = prepare_fast_data(df_features)
        feature_scaler, target_scaler = scalers

        # 4. Modelle trainieren
        print("\n" + "="*50)
        print("PHASE 4: FAST MODEL TRAINING")
        print("="*50)
        results = train_fast_models(train_data, test_data)

        # 5. Bestes Modell auswählen
        best_model_name = max(results.keys(), key=lambda x: results[x]['r2'])
        best_model = results[best_model_name]['model']

        print(f"\n🏆 Bestes Modell: {best_model_name} (R²: {results[best_model_name]['r2']:.4f})")

        # 6. 48h Vorhersage
        print("\n" + "="*50)
        print("PHASE 5: 48H VORHERSAGE")
        print("="*50)
        X_test, y_test = test_data
        last_sequence = X_test[-1]

        predictions = predict_48h_fast(best_model, last_sequence, target_scaler)

        # 7. Visualisierung
        print("\n" + "="*50)
        print("PHASE 6: FAST 48H VISUALISIERUNG")
        print("="*50)
        fig = create_fast_48h_visualization(df_features, results, predictions, target_scaler)

        # 8. Zusammenfassung
        total_time = time.time() - start_time
        print_fast_summary(results, predictions, df_features, total_time)

        print(f"\n🎉 FAST 48H ANALYSE ABGESCHLOSSEN in {total_time:.1f}s! 🎉")

        return {
            'results': results,
            'predictions': predictions,
            'data': df_features,
            'total_time': total_time
        }

    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()
        return None

def print_fast_summary(results, predictions, df, total_time):
    """Fast Summary ausgeben"""
    print("\n" + "="*60)
    print("🚀 FAST 48H BITCOIN PREDICTION RESULTS 🚀")
    print("="*60)

    # Beste Modelle
    best_model = max(results.keys(), key=lambda x: results[x]['r2'])
    print(f"\n🏆 BESTES MODELL: {best_model}")
    print(f"   R² Score: {results[best_model]['r2']:.4f}")
    print(f"   RMSE: {results[best_model]['rmse']:.4f}")
    print(f"   Training Zeit: {results[best_model]['training_time']:.1f}s")

    # 48h Vorhersagen
    current_price = df['close'].iloc[-1]
    print(f"\n🔮 48H VORHERSAGEN:")
    print(f"   Aktueller Preis: ${current_price:,.2f}")

    for hour in [1, 6, 12, 24, 36, 48]:
        if hour in predictions:
            pred = predictions[hour]
            change_pct = ((pred['mean'] / current_price) - 1) * 100
            direction = "📈" if change_pct > 0 else "📉"
            print(f"   {hour:2}h: ${pred['mean']:8,.0f} ({change_pct:+6.1f}%) {direction}")

    # Trading-Empfehlung
    change_48h = ((predictions[48]['mean'] / current_price) - 1) * 100
    if change_48h > 2:
        recommendation = "KAUF 📈"
    elif change_48h > -2:
        recommendation = "HALTEN ⚖️"
    else:
        recommendation = "VERKAUF 📉"

    print(f"\n💡 EMPFEHLUNG: {recommendation}")
    print(f"⚡ Gesamtzeit: {total_time:.1f}s")
    print("💾 Visualisierung: ultimate_plots/fast_48h_precision_analysis.png")
    print("="*60)

if __name__ == "__main__":
    main()
