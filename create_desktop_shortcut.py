#!/usr/bin/env python3
"""
🖥️ DESKTOP-VERKNÜPFUNG ERSTELLER 🖥️
==================================
🏆 AUTOMATISCHE DESKTOP-VERKNÜPFUNG FÜR BITCOIN LAUNCHER 🏆
✅ Erstellt automatisch eine Desktop-Verknüpfung
✅ Windows-kompatibel mit schönem Icon
✅ Ein-Klick-Start für Bitcoin Trading Launcher
✅ Professionelle Verknüpfung mit Beschreibung

💡 EINFACH AUSFÜHREN UND DESKTOP-VERKNÜPFUNG IST FERTIG!
"""

import os
import sys
import winshell
from win32com.client import Dispatch

def create_desktop_shortcut():
    """Erstelle Desktop-Verknüpfung für Bitcoin Trading Launcher"""
    
    print("🖥️ Erstelle Desktop-Verknüpfung...")
    
    try:
        # Pfade ermitteln
        current_dir = os.getcwd()
        launcher_path = os.path.join(current_dir, "bitcoin_launcher.py")
        python_path = sys.executable
        
        # Desktop-Pfad ermitteln
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "Bitcoin Trading Launcher.lnk")
        
        # Prüfe ob Launcher existiert
        if not os.path.exists(launcher_path):
            print(f"❌ FEHLER: bitcoin_launcher.py nicht gefunden!")
            print(f"💡 Stellen Sie sicher, dass der Launcher im aktuellen Ordner ist.")
            return False
        
        # Erstelle Verknüpfung
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        
        # Verknüpfungs-Eigenschaften
        shortcut.Targetpath = python_path
        shortcut.Arguments = f'"{launcher_path}"'
        shortcut.WorkingDirectory = current_dir
        shortcut.Description = "Bitcoin Trading Launcher - Einfacher Zugang zu allen Bitcoin Trading Systemen"
        shortcut.IconLocation = python_path + ",0"  # Python-Icon verwenden
        
        # Speichere Verknüpfung
        shortcut.save()
        
        print(f"✅ Desktop-Verknüpfung erfolgreich erstellt!")
        print(f"📍 Speicherort: {shortcut_path}")
        print(f"🎯 Ziel: {launcher_path}")
        print(f"💡 Sie können jetzt den Bitcoin Trading Launcher vom Desktop starten!")
        
        return True
        
    except ImportError as e:
        print(f"❌ FEHLER: Benötigte Module nicht gefunden!")
        print(f"💡 Installieren Sie: pip install pywin32 winshell")
        print(f"🔧 Fehler-Details: {e}")
        return False
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Verknüpfung: {e}")
        return False

def create_batch_launcher():
    """Erstelle alternative Batch-Datei als Backup"""
    
    print("🔧 Erstelle Batch-Launcher als Alternative...")
    
    try:
        current_dir = os.getcwd()
        launcher_path = os.path.join(current_dir, "bitcoin_launcher.py")
        batch_path = os.path.join(current_dir, "Bitcoin Trading Launcher.bat")
        
        # Batch-Inhalt
        batch_content = f'''@echo off
title Bitcoin Trading Launcher
cd /d "{current_dir}"
python "{launcher_path}"
pause
'''
        
        # Schreibe Batch-Datei
        with open(batch_path, 'w') as f:
            f.write(batch_content)
        
        print(f"✅ Batch-Launcher erstellt: {batch_path}")
        print(f"💡 Sie können diese .bat-Datei auch direkt ausführen!")
        
        return True
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Batch-Datei: {e}")
        return False

def main():
    """Hauptfunktion"""
    
    print("🖥️ DESKTOP-VERKNÜPFUNG ERSTELLER")
    print("=" * 50)
    print("🚀 Erstelle Desktop-Verknüpfung für Bitcoin Trading Launcher...")
    print("")
    
    # Versuche Desktop-Verknüpfung zu erstellen
    shortcut_success = create_desktop_shortcut()
    
    # Erstelle Batch-Launcher als Alternative
    batch_success = create_batch_launcher()
    
    print("\n" + "=" * 50)
    
    if shortcut_success:
        print("🎉 ERFOLGREICH!")
        print("✅ Desktop-Verknüpfung wurde erstellt")
        print("🖱️ Doppelklicken Sie auf 'Bitcoin Trading Launcher' auf Ihrem Desktop")
    elif batch_success:
        print("🎉 ALTERNATIVE ERSTELLT!")
        print("✅ Batch-Launcher wurde erstellt")
        print("🖱️ Doppelklicken Sie auf 'Bitcoin Trading Launcher.bat'")
    else:
        print("❌ FEHLER!")
        print("💡 Starten Sie den Launcher manuell mit: python bitcoin_launcher.py")
    
    print("\n💡 VERWENDUNG:")
    print("1. Doppelklick auf Desktop-Verknüpfung")
    print("2. Wählen Sie Ihr gewünschtes Bitcoin Trading System (1-3)")
    print("3. Lehnen Sie sich zurück und lassen Sie das System arbeiten!")
    
    print("\n👋 Setup abgeschlossen!")

if __name__ == "__main__":
    main()
