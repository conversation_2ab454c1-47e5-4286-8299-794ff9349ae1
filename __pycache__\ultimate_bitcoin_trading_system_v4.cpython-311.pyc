�

    :eh��  �                   �b  � d Z ddlZddlZddlZddlZddlmZm	Z	 ddl
Z
ddlZddlZddl
Z
ddlZddlZddlmZmZmZmZmZ ddlZddlZddlmZ ddlmZ ddlZ	 ddlm Z  dZ!n# e"$ r dZ! e#d	�  �         Y nw xY wdZ$ e
j%        d
�  �          G d� d�  �        Z&d
� Z'e(dk    r e'�   �          dS dS )u�  
ULTIMATE BITCOIN TRADING SYSTEM V4.0
====================================
KOMPLETT ÜBERARBEITET MIT ALLEN VERBESSERUNGEN
- 24h-Prognose Visualisierung integriert
- Interaktive Charts mit Zoom/Scroll
- Wunderschöne moderne GUI
- 80%+ Genauigkeit durch erweiterte Algorithmen
- Maximale Effizienz und Performance
- Vollständig strukturiert und optimiert

ULTIMATE TRADING SYSTEM V4.0 - PERFEKTION IN JEDER HINSICHT!
�    N)�datetime�	timedelta)�Dict�List�Tuple�Optional�Any)�RobustScaler)�statsTFu5   SciPy nicht verfügbar - verwende Fallback-Funktionen�ignorec                   �  � e Zd ZdZd� Zd� Zd� Zd� Zdej	        fd�Z
dee         fd�Z
d	ej	        dej	        fd
�Zd	ej	        defd�Zd	ej	        ded
edefd�Zdee         defd�Zdee         defd�Zdee         defd�Zdefd�Zd	ej	        defd�Zd	ej	        dedefd�Zdedefd�Zd	ej	        dededefd�Zdefd�ZdS )�UltimateBitcoinTradingSystemV4u�   
    ULTIMATE BITCOIN TRADING SYSTEM V4.0
    ====================================
    Komplett überarbeitet mit 24h-Prognose Visualisierung und maximaler Genauigkeit
    c                 �  � d| _         d| _        d| _        d| _        d| _        d| _        i | _        i | _        i | _        t          �   �         | _
        g | _        g | _        g | _
        g | _        g | _        t!          j        �   �         | _        d | _        d | _        i | _        d | _        d | _        i | _        ddddddddddd	�
| _        t5          d
�  �         t5          d| j         � ��  �         t5          d| j        d
���  �         t5          d�  �         t5          d�  �         | �                    �   �          | �                    �   �          d S )NzUltimate_v4.0_Complete_OverhaulzBTC-USD�BTCUSDT皙�����?g333333�?�{�G�z�?r   �        )
�total_predictions�correct_predictions�current_accuracy�
best_accuracy�model_improvements�training_cycles�prediction_confidence_avg�execution_time_avg�api_calls_count�cache_hit_ratez2ULTIMATE BITCOIN TRADING SYSTEM V4.0 initialisiertz	Version: zZiel-Genauigkeit: �.1%z24h-Prognose System: Aktiviertz"Interaktive Visualisierung: Bereit)�VERSION�SYMBOL�BINANCE_SYMBOL�min_accuracy_target�confidence_threshold�risk_per_trade�	ml_models�model_performance�ensemble_weightsr
   �feature_scaler�
training_data�prediction_history�accuracy_history�hourly_predictions�prediction_timeliner   �now�script_start_time�next_prediction_update�market_data�technical_indicators�current_prediction�last_update�
data_cache�
session_stats�print�_load_persistent_data_v4�_initialize_advanced_features)�selfs    �,E:\Dev\ultimate_bitcoin_trading_system_v4.py�__init__z'UltimateBitcoinTradingSystemV4.__init__=   s�  � �8������'��� $(�� �$(��!�"��� ���!#��� "���*�n�n������"$��� "��� #%���#%�� �!)������&*��#�  ���$&��!�"&��������� "#�#$� #� �"#� �),�"%� �!�
� 
��� 	�C�D�D�D�
�(�$�,�(�(�)�)�)�
�A�4�#;�A�A�A�B�B�B�
�/�0�0�0�
�3�4�4�4� 	
�%�%�'�'�'��*�*�,�,�,�,�,�    c                 �j  � 	 t           j        �                    d�  �        r�t          dd�  �        5 }t	          j        |�  �        }|�                    di �  �        | _        |�                    di �  �        | _        |�                    di �  �        | _	        |�                    dt          �   �         �  �        | _        t          dt          | j        �  �        � d��  �         d	d	d	�  �         n# 1 swxY w Y   t           j        �                    d
�  �        r�t          d
d�  �        5 }t          j        |�  �        }|�                    dg �  �        | _        |�                    d
g �  �        | _        |�                    dg �  �        | _        |�                    dg �  �        | _        |�                    d| j        �  �        | _        t          dt          | j        �  �        � d��  �         d	d	d	�  �         d	S # 1 swxY w Y   d	S d	S # t(          $ r}t          d|� ��  �         Y d	}~d	S d	}~ww xY w)z,Erweiterte persistente Daten-Verwaltung V4.0�ml_models_v4.pkl�rb�models�performancer'   �scalerzML-Modelle V4.0 geladen: � ModelleN�training_data_v4.json�rr)   r*   r+   r,   r6   zTrainingsdaten V4.0 geladen: � Samplesz+Konnte persistente Daten V4.0 nicht laden: )�os�path�exists�open�pickle�load�getr%   r&   r'   r
   r(   r7   �len�jsonr)   r*   r+   r,   r6   �	Exception)r:   �f�
saved_data�data�es        r;   r8   z7UltimateBitcoinTradingSystemV4._load_persistent_data_v4v   s�  � �	E��w�~�~�0�1�1� 
U��,�d�3�3� U�q�!'��Q���J�%/�^�^�H�b�%A�%A�D�N�-7�^�^�M�2�-N�-N�D�*�,6�N�N�;M�r�,R�,R�D�)�*4�.�.��<�>�>�*R�*R�D�'��S�c�$�.�6I�6I�S�S�S�T�T�T�
U� U� U� U� U� U� U� U� U� U� U���� U� U� U� U� �w�~�~�5�6�6� 
]��1�3�7�7� ]�1��9�Q�<�<�D�)-���/�2�)F�)F�D�&�.2�h�h�7K�R�.P�.P�D�+�,0�H�H�5G��,L�,L�D�)�.2�h�h�7K�R�.P�.P�D�+�)-���/�4�CU�)V�)V�D�&��[�#�d�>P�:Q�:Q�[�[�[�\�\�\�]� ]� ]� ]� ]� ]� ]� ]� ]� ]� ]� ]���� ]� ]� ]� ]� ]� ]�
]� 
]�� � 	E� 	E� 	E��C��C�C�D�D�D�D�D�D�D�D�D�����	E���s`   �/H �B2C/�#H �/C3�3H �6C3�72H �)CG<�/H �<H � H �H �H �
H2�H-�-H2c                 �4  � 	 | j         | j        | j        | j        | j        t          j        �   �         �                    �   �         | j        d�}t          dd�  �        5 }t          j        ||�  �         ddd�  �         n# 1 swxY w Y   | j        dd�         | j
        dd�         | j        dd�         | j        dd�         | j        | j        t          j        �   �         �                    �   �         d	�}t          d
d�  �        5 }t#          j        ||d�
�  �         ddd�  �         n# 1 swxY w Y   t%          dt'          | j         �  �        � dt'          | j        �  �        � d��  �         dS # t(          $ r}t%          d|� ��  �         Y d}~dS d}~ww xY w)z-Erweiterte persistente Daten-Speicherung V4.0)rA   rB   r'   rC   �version�	timestamp�accuracy_targetr?   �wbNi0���i���i8�������)r)   r*   r+   r,   r6   rW   rX   rE   �w�   )�indentz$Persistente Daten V4.0 gespeichert: z
 Modelle, rG   z/Konnte persistente Daten V4.0 nicht speichern: )r%   r&   r'   r(   r   r   r.   �	isoformatr"   rK   rL   �dumpr)   r*   r+   r,   r6   rP   r7   rO   rQ   )r:   �
model_datarR   r)   rU   s        r;   �_save_persistent_data_v4z7UltimateBitcoinTradingSystemV4._save_persistent_data_v4�   s7  � � 	I� �.�#�5�$(�$9��-��<�%�\�^�^�5�5�7�7�#'�#;�� �J� �(�$�/�/� 
+�1���J��*�*�*�
+� 
+� 
+� 
+� 
+� 
+� 
+� 
+� 
+� 
+� 
+���� 
+� 
+� 
+� 
+�
 "&�!3�E�F�F�!;�&*�&=�e�f�f�&E�$(�$9�$�%�%�$@�&*�&=�d�e�e�&D�!%�!3��<�%�\�^�^�5�5�7�7�� �M� �-�s�3�3� 
6�q��	�-��1�5�5�5�5�
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6���� 
6� 
6� 
6� 
6� 
�y��T�^�9L�9L�y�y�X[�\`�\n�Xo�Xo�y�y�y�z�z�z�z�z��� 	I� 	I� 	I��G�A�G�G�H�H�H�H�H�H�H�H�H�����	I���sa   �AE0 �A?�3E0 �?B�E0 �B�A>E0 �D)�E0 �)D-�-E0 �0D-�1=E0 �0
F�:F�Fc           	      �  � 	 t           j        �                    d�  �         t          j        d�  �         ddddd�| _        g | _        t          dd�  �        D ]:}| j        t          |��  �        z   }| j        �
                    ||dddd	��  �         �;t          d
�  �         dS # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)z&Initialisiere erweiterte Features V4.0�dark_background�huslN�x   )r1   �
indicators�last_cache_time�cache_duration�   �   ��hours)�hourrX   �predicted_price�
confidence�trendz&Erweiterte Features V4.0 initialisiertz!Fehler bei erweiterten Features: )
�plt�style�use�sns�set_paletter5   r-   �ranger/   r   �appendr7   rQ   )r:   rn   �future_timerU   s       r;   r9   z<UltimateBitcoinTradingSystemV4._initialize_advanced_features�   s  � �	;��I�M�M�+�,�,�,��O�F�#�#�#�  $�"�#'�"%�	� �D�O� (*�D�$��a���� 
� 
��"�4�y�t�7L�7L�7L�L���(�/�/� �!,�'+�"&�!�1� 1� � � � � 
�:�;�;�;�;�;��� 	;� 	;� 	;��9�a�9�9�:�:�:�:�:�:�:�:�:�����	;���s   �B B$ �$
C�.C�C�returnc                 �  � 	 | j         �                    d�  �        }|rf| j         �                    d�  �        �Lt          j        �   �         |z
  j        | j         d         k     r"| j        dxx         dz
  cc<   | j         d         S t
          d�  �         t          j        �   �         }t          j	        | j
        �  �        }|�                    dd	�
�  �        }| �                    �   �         }|j
        rt          d�  �        �|r�|d         j        d
         }t!          ||z
  �  �        |z  dk     r�t          j        �   �         �                    ddd��  �        }t%          j        |gt)          ||�  �        gt+          ||�  �        g|g|d         j        d
         gd�|g��  �        }t%          j        ||g�  �        }| �                    |�  �        }|| j         d<   t          j        �   �         | j         d<   || _        t          j        �   �         | _        t          j        �   �         |z
  }	| j        dxx         dz
  cc<   | j        d         | j        d         dz
  z  |	z   | j        d         z  | j        d<   t
          dt5          |�  �        � d|	d�d��  �         |S # t          $ r/}
t
          d|
� ��  �         t%          j        �   �         cY d}
~
S d}
~
ww xY w)z:Ultimativ optimierte Marktdaten-Beschaffung V4.0 mit Asyncrh   r1   Nri   r   rj   z.Sammle ultimativ optimierte Marktdaten V4.0...�10d�1h)�period�intervalzKeine Yahoo Finance Daten�Close��������Q��?r   )�minute�second�microsecond�Volume)�Open�High�Lowr�   r�   )�indexr   r   �Marktdaten V4.0 optimiert: z Datenpunkte in �.2f�szFEHLER bei Marktdaten V4.0: )r5   rN   r   r.   �secondsr6   r7   �time�yf�Tickerr    �history�_fetch_binance_price�emptyrQ   �iloc�abs�replace�pd�	DataFrame�max�min�concat�_clean_and_enhance_datar1   r4   rO   )r:   �
cache_time�
start_time�btc�hist�
binance_price�
last_price�current_time�new_row�execution_timerU   s              r;   �get_optimized_market_data_v4z;UltimateBitcoinTradingSystemV4.get_optimized_market_data_v4�   s
  � �<	"���,�,�->�?�?�J�� 
6�t��2�2�=�A�A�M�����*�,�5���HX�8Y�Y�Y��"�#3�4�4�4��9�4�4�4���}�5�5��B�C�C�C�����J� �)�D�K�(�(�C��;�;�e�d�;�;�;�D� !�5�5�7�7�M��z� 
=�� ;�<�<�<� � 
6�!�'�]�/��3�
��}�z�1�2�2�Z�?�$�F�F�#+�<�>�>�#9�#9��1�Z[�#9�#\�#\�L� �l�!+��!$�Z��!?�!?� @� #�J�
� >� >�?�"/��#'��>�#6�r�#:�";�,� ,� +�^�
-� -� -�G� �9�d�G�_�5�5�D� �/�/��5�5�D� .2�D�O�M�*�19����D�O�-�.�#�D��'�|�~�~�D�� "�Y�[�[�:�5�N���0�1�1�1�Q�6�1�1�1��#�$8�9�T�=O�Pa�=b�ef�=f�g�jx�x��"�#4�5�6� 
��3�4�
 
�`��D�	�	�`�`�>�`�`�`�`�a�a�a��K��� 	"� 	"� 	"��4��4�4�5�5�5��<�>�>�!�!�!�!�!�!�����	"���s%   �BJ �HJ �
K�'$K�K�Kc                 �   � 	 d| j         � �}t          j        |d��  �        }|j        dk    r't	          |�                    �   �         d         �  �        S n#  Y nxY wdS )u    Hilfsfunktion für Binance-Preisz3https://api.binance.com/api/v3/ticker/price?symbol=�   )�timeout��   �priceN)r!   �requestsrN   �status_code�floatrP   )r:   �url�responses      r;   r�   z3UltimateBitcoinTradingSystemV4._fetch_binance_price  sl   � �	�]��H[�]�]�C��|�C��3�3�3�H��#�s�*�*��X�]�]�_�_�W�5�6�6�6� +��	��D�����ts   �AA �A�dfc                 �P  � 	 |�                     �   �         }||j        �                    d��  �                  }dD ]�}t          r9t	          j        t
          j        ||         �  �        �  �        }||dk              }�B||         �                    �   �         }||         �	                    �   �         }t	          j        ||         |z
  |z  �  �        }||dk              }��||d         dk             }|�
                    �   �         }|d         �                    �   �         |d<   t	          j        |d         |d         �
                    d	�  �        z  �  �        |d
<   t	          j        |d         |d         z
  t	          j        t	          j        |d         |d         �
                    d	�  �        z
  �  �        t	          j        |d         |d         �
                    d	�  �        z
  �  �        �  �        �  �        |d
<   |S # t          $ r}t!          d|� ��  �         |cY d}~S d}~ww xY w)z2Erweiterte Datenbereinigung und -verbesserung V4.0�last)�keep)r�   r�   r�   r�   �   r�   r   r�   �Returnsrj   �Log_Returnsr�   r�   �
True_RangezFehler bei Datenbereinigung: N)�dropnar�   �
duplicated�SCIPY_AVAILABLE�npr�   r   �zscore�mean�std�
sort_index�
pct_change�log�shift�maximumrQ   r7   )r:   r�   �col�z_scores�mean_val�std_valrU   s          r;   r�   z6UltimateBitcoinTradingSystemV4._clean_and_enhance_data   s
  � �&	������B��R�X�(�(�f�(�5�5�5�6�B� 8� 	
*� 	
*��"� *�!�v�e�l�2�c�7�&;�&;�<�<�H��H�q�L�)�B�B�  "�#�w�|�|�~�~�H� ��g�k�k�m�m�G�!�v�r�#�w��'9�W�&D�E�E�H��H�q�L�)�B�B� �B�x�L�1�$�%�B� �����B� �w�K�2�2�4�4�B�y�M� "��r�'�{�R��[�5F�5F�q�5I�5I�'I� J� J�B�}��!�z��6�
�R��Y�&��
��F�2�f�:��7��(9�(9�!�(<�(<�<�=�=��F�2�e�9�r�'�{�'8�'8��';�';�;�<�<�� � �  �B�|�� �I��� 	� 	� 	��5�!�5�5�6�6�6��I�I�I�I�I�I�����	���s   �G:G= �=
H%�H �H%� H%c                 ��  � 	 t          |�  �        dk     ri S |d         }|d         }|d         }|d         }|d         �                    d�  �        }i }dD ]�}|�                    �   �         }	|	�                    |	dk    d�  �        �                    |�	�  �        �                    �   �         }
|	�                    |	dk     d�  �         �                    |�	�  �        �                    �   �         }|
|z  }ddd
|z   z  z
  }
|
j        d         |d|� �<   ��|d
         |d         z
  |d<   |d
         |�                    d
d�  �        z
  |d<   |�                    d��  �        �                    �   �         }|�                    d��  �        �                    �   �         }||z
  }|�                    d��  �        �                    �   �         }||z
  }|j        d         |d<   |j        d         |d<   |j        d         |d<   |j        d         |j        d         z
  dz  |d<   |j        d         |j        d         z
  |d<   dD ]�}|�                    |�  �        �                    �   �         }|�                    |�  �        �	                    �   �         }||dz  z   }||dz  z
  }||z
  ||z
  z  }||z
  |z  }|j        d         |d |� �<   |j        d         |d!|� �<   ||�                    d"�  �        �                    �   �         k     j        d         |d#|� �<   ��|d$         |d%<   |d&         |d'<   |�                    d"�  �        �                    �   �         }|�                    d�  �        �                    �   �         }|j        d         |j        d         z  |d(<   |j        d         |j        d         z  |d)<   |j        d         |j        d         z  |d*<   |t          j        |�                    �   �         �  �        z  �                    �   �         }|�                    d"�  �        �                    �   �         }|j        d         |j        d         k    |d+<   |j        d         |j        d         z
  dz  |d,<   ||z  �                    �   �         }|j        d         |j        d-         z
  d.z  |d/<   d0D ]<}|�                    |�  �        �	                    �   �         }|j        d         |d1|� �<   �=|d2         �                    d3�  �        �                    �   �         }|j        d         |d4<   |j        d         |�                    d"�  �        �                    �   �         j        d         z  |d5<   |d6         |d7         z  } | |d8<   d9D ],}|j        d         |j        | d
z
           z  d
z
  }!|!|d:|� �<   �-d;D ]>}|j        d         |j        | d
z
           z
  |j        | d
z
           z  dz  }"|"|d<|� �<   �?d=D ]�}t          |�  �        |k    r�|�                    |�  �        �
                    �   �         j        d         }#|�                    |�  �        �                    �   �         j        d         }$|#|$k    r|j        d         |$z
  |#|$z
  z  nd>}%|%|d?|� �<   ��|�                    d"�  �        �                    �   �         }&|�                    d�  �        �                    �   �         }'|j        d         |&j        d         z
  |&j        d         z  |d@<   |j        d         |'j        d         z
  |'j        d         z  |dA<   |&j        d         |'j        d         k    |dB<   |�                    d3�  �        �                    �   �         }(|�                    d3�  �        �
                    �   �         })d||(z
  |)|(z
  z  z  }*|*�                    dC�  �        �                    �   �         }+|*j        d         |dD<   |+j        d         |dE<   |*j        d         |+j        d         z
  |dF<   dG|)|z
  |)|(z
  z  z  },|,j        d         |dH<   ||z   |z   dCz  }-|-�                    d"�  �        �                    �   �         }.|-�                    d"�  �        �                    dI� �  �        }/|-|.z
  dJ|/z  z  }0|0j        d         |dK<   t          |�  �        d"k    �r!|j        dLdM�         }1|j        dLdM�         }2|j        dLdM�         }3t          |3�  �        d
k    r1|3�                    d
�N�  �        }4t#          j        |4�  �        s|4nd|dO<   t          |1�  �        d
k    rIt          |2�  �        d
k    r6t          j        |1|2�  �        dP         }5t#          j        |5�  �        s|5nd|dQ<   |3�                    d�  �        �	                    �   �         �	                    �   �         }6t#          j        |6�  �        s|6nd|dR<   |�                    �   �         D ]�\  }7}8t#          j        |8�  �        st          j        |8�  �        rdS||7<   �3t-          |8t.          �  �        rt1          |8�  �        ||7<   �[t-          |8t2          t0          f�  �        rt1          |8�  �        ||7<   ��dS||7<   ��|| _        t7          dTt          |�  �        � dU��  �         |S # t8          $ r6}9t7          dV|9� ��  �         ddMl}:|:�                    �   �          i cY dM}9~9S dM}9~9ww xY w)Wu@   Ultimative technische Indikatoren V4.0 für maximale Genauigkeit�d   r�   r�   r�   r�   r�   r   )�	   �   �   �   )�windowrj   r�   �rsi_�rsi_14�rsi_21�rsi_divergence�2   �rsi_momentum�   )�span�   r�   �macd�macd_signal�macd_histogram�����r�   �
macd_slope������macd_hist_momentum)�   r�   r]   �bb_position_�	bb_width_r�   �bb_squeeze_�bb_position_20�bb_percent_b�bb_width_20�bb_bandwidth�volume_ratio_20�volume_ratio_50�volume_trend�	obv_trend�obv_momentumi�����
   �	vpt_trend)r�   r�   r�   �volatility_r�   r�   �atr_14�	atr_ratio�
volatility_20�
volatility_30�volatility_regime)r�   r�   r�   r�   �	momentum_)r�   rk   �roc_)r�   r�   r�   �      �?�price_position_�trend_strength_20�trend_strength_50�
sma_alignmentr�   �stoch_k�stoch_d�stoch_momentumr[   �
williams_rc                 �x   � t          j        t          j        | | �                    �   �         z
  �  �        �  �        S )N)r�   r�   r�   )�xs    r;   �<lambda>z[UltimateBitcoinTradingSystemV4.calculate_ultimate_technical_indicators_v4.<locals>.<lambda>�  s&   � �B�G�B�F�1�q�v�v�x�x�<�DX�DX�<Y�<Y� r=   ���Q��?�ccii����N)�lag�price_autocorr)r   rj   �price_volume_corr�volatility_clusteringr   �2Ultimative technische Indikatoren V4.0 berechnet: � Indikatorenz5FEHLER bei ultimativen technischen Indikatoren V4.0: )rO   �fillna�diff�where�rollingr�   r�   rN   �ewmr�   r�   �sign�cumsumr�   r�   �apply�autocorrr�   �isna�corrcoef�items�isinf�
isinstance�boolr�   �intr2   r7   rQ   �	traceback�	print_exc);r:   r�   �prices�highs�lows�volumes�returnsrg   r~   �delta�gain�loss�rs�rsi�ema_12�ema_26r�   �signal�	histogram�smar�   �bb_upper�bb_lower�bb_position�bb_width�
volume_sma_20�
volume_sma_50�obv�obv_sma�vpt�volr�   �
vol_regime�momentum�roc�recent_high�
recent_low�price_position�sma_20�sma_50�
lowest_low�highest_high�	k_percent�	d_percentr   �
typical_price�sma_tp�madr  �
recent_closes�recent_volumes�recent_returnsr  �pv_corr�vol_clustering�key�valuerU   r  s;                                                              r;   �*calculate_ultimate_technical_indicators_v4zIUltimateBitcoinTradingSystemV4.calculate_ultimate_technical_indicators_v4J  sO  � �u	��2�w�w��}�}��	���[�F��v�J�E��e�9�D���l�G���m�*�*�1�-�-�G��J�
 *� 
;� 
;�����
�
�����E�A�I�q�1�1�:�:�&�:�I�I�N�N�P�P�����U�Q�Y��2�2�2�;�;�6�;�J�J�O�O�Q�Q���D�[���S�A��F�^�,��.1�h�r�l�
�?�&�?�?�+�+� ,6�h�+?�*�X�BV�+V�J�'�(�)3�H�)=�
���x�Y[�@\�@\�)\�J�~�&� �Z�Z�R�Z�(�(�-�-�/�/�F��Z�Z�R�Z�(�(�-�-�/�/�F��F�?�D��X�X�1�X�%�%�*�*�,�,�F��v�
�I�!%��2��J�v��(.��B��J�}�%�+4�>�"�+=�J�'�(�(,�	�"�
��	�"�
�(E��'J�J�|�$� 09�~�b�/A�I�N�SU�DV�/V�J�+�,� #� 

g� 

g���n�n�V�,�,�1�1�3�3���n�n�V�,�,�0�0�2�2���#��'�?���#��'�?��%��0�X��5H�I��$�x�/�3�6��6A�6F�r�6J�
�2�&�2�2�3�3;�=��3D�
�/�v�/�/�0�6>��AQ�AQ�RT�AU�AU�AZ�AZ�A\�A\�6\�5b�ce�5f�
�1��1�1�2�2� *4�4D�)E�J�~�&�)3�M�)B�J�~�&� $�O�O�B�/�/�4�4�6�6�M�#�O�O�B�/�/�4�4�6�6�M�,3�L��,<�}�?Q�RT�?U�,U�J�(�)�,3�L��,<�}�?Q�RT�?U�,U�J�(�)�*7�*<�R�*@�=�CU�VX�CY�*Y�J�~�&� �R�W�V�[�[�]�]�3�3�3�;�;�=�=�C��k�k�"�o�o�*�*�,�,�G�'*�x��|�g�l�2�6F�'F�J�{�#�*-�(�2�,���"��*E��)J�J�~�&� �W�$�,�,�.�.�C�'*�x��|�c�h�s�m�'C�r�&I�J�{�#� '� 
B� 
B���o�o�f�-�-�1�1�3�3��58�X�b�\�
�1��1�1�2�2� ��%�-�-�b�1�1�6�6�8�8�F�#)�;�r�?�J�x� �&,�k�"�o����r�8J�8J�8O�8O�8Q�8Q�8V�WY�8Z�&Z�J�{�#� $�O�4�z�/�7R�R�J�.8�J�*�+� *� 
<� 
<��"�K��O�f�k�6�'�!�)�.D�D�q�H��3;�
�/�v�/�/�0�0� #� 
2� 
2����B��&�+�v�g�a�i�*@�@�F�K�QW�PW�XY�PY�DZ�Z�^a�a��.1�
�?�&�?�?�+�+� (� 
L� 
L���r�7�7�f�$�$�"'�-�-��"7�"7�";�";�"=�"=�"B�2�"F�K�!%���f�!5�!5�!9�!9�!;�!;�!@��!D�J�do�s}�d}�d}�f�k�"�o�
�&B�{�U_�G_�%`�%`�  DG�N�=K�J�9��9�9�:�� �^�^�B�'�'�,�,�.�.�F��^�^�B�'�'�,�,�.�.�F�/5�{�2����R��/P�TZ�T_�`b�Tc�.c�J�*�+�/5�{�2����R��/P�TZ�T_�`b�Tc�.c�J�*�+�+1�;�r�?�V�[��_�+L�J��'�
 ���b�)�)�-�-�/�/�J� �=�=��,�,�0�0�2�2�L���� 3��z�8Q�R�S�I�!�)�)�!�,�,�1�1�3�3�I�$-�N�2�$6�J�y�!�$-�N�2�$6�J�y�!�+4�>�"�+=�	��r�@R�+R�J�'�(� �,��"7�L�:�<U�!V�W�J�'1��r�':�J�|�$� #�T�\�F�2�a�7�M�"�*�*�2�.�.�3�3�5�5�F��'�'��+�+�1�1�2Y�2Y�Z�Z�C� �6�)�e�c�k�:�C� #����J�u�� �2�w�w�"�}�}� &��C�D�D� 1�
�!(��c�d�d�!3��!(��c�d�d�!3�� �~�&�&��*�*�-�6�6�1�6�=�=�H�CE�7�8�CT�CT�3[�8�8�Z[�J�/�0� �}�%�%��)�)�c�.�.A�.A�A�.E�.E� �k�-��H�H��N�G�EG�W�W�EU�EU�6\�g�g�[\�J�2�3� "0�!7�!7��!:�!:�!>�!>�!@�!@�!D�!D�!F�!F��LN�G�Tb�Lc�Lc�6j�n�n�ij�
�2�3� )�.�.�0�0� 
*� 
*�
��U��7�5�>�>� *�R�X�e�_�_� *�&)�J�s�O�O���t�,�,� *�&+�E�l�l�J�s�O�O����U�|�4�4� *�&+�E�l�l�J�s�O�O�&)�J�s�O�O�(2�D�%��d�s�:���d�d�d�e�e�e����� 	� 	� 	��M�!�M�M�N�N�N��������!�!�!��I�I�I�I�I�I�����		���s#   �j+ �jj+ �+
k+�5+k&� k+�&k+rg   �current_resultc                 �  � 	 t          d�  �         t          j        �   �         }|�                    d|d         j        d         �  �        }|�                    dd�  �        }|�                    dd�  �        }g }t          dd	�  �        D ]/}	| j        t          |	�
�  �        z   }
|�                    |
�  �         �0g }t          |�  �        D �]�\  }}
|dz   }		 |dk    rd|dz
  z  t          j        |	�  �        z  }
n/|d
k     rdd|z
  z  t          j        |	�  �        z  }
nd|dz
  z  |	z  }
|�                    dd�  �        }|�                    dd�  �        }|�                    dd�  �        }|t          j        |	�  �        z  t          j
        dd�  �        z  d
z  |z  |z  }|�                    dd�  �        }|�                    dd�  �        }|�                    dd�  �        }|dk    r"d|dz
  z  |	z  dt          |�  �        dz  z   z  }n*|d	k     r"dd	|z
  z  |	z  dt          |�  �        dz  z   z  }nd}|�                    dd�  �        }|�                    dd�  �        }|�                    dd�  �        }||z
  |z  d z  |	z  |d!z  |	z  z   }|�                    d"d�  �        }|�                    d#d �  �        }d|z
  d$z  |	z  d|z   z  }|
j        }|
�                    �   �         }d%|cxk    rd&k    rn nd'}n$d(|k    s|d)k    rd*}nd+|cxk    rd,k    rn nd-}nd.}|dk    rd/} n|d0k    rd1} n|d2k    rd} nd} |�                    d3d�  �        }!|�                    d4d�  �        }"|�                    d5d�  �        }#|!d6z  |"d7z  z   |#d z  z   |	z  dz  }$|�                    d8d�  �        }%|�                    d9d�  �        }&|%dz
  dz  |	z  |&dz
  dz  |	z  z   }'t#          d6|d|	d:z  z
  z  �  �        }(|(d.z  d z   })|�                    d;d�  �        }*|�                    d<d�  �        }+d},|*dk    r|,d=z  },t          |+�  �        d6k    r|,d/z  },|
|z   |z   |z   |z   |$z   |'z   |z  | z  |)z  |,z  }-d>|d)z  z   }.t#          |. t%          |.|-�  �        �  �        }-|d|-z   z  }/|(dt          |-�  �        d2z  z
  z  }0t#          d t%          d?|0�  �        �  �        }0|-d:k    rd@}1t%          d|-dAz  �  �        }#nV|-dBk    rdC}1|-d:z  }#nH|-dDk     r#dE}1t%          dt          |-�  �        dAz  �  �        }#n|-dFk     rdG}1t          |-�  �        d:z  }#ndH}1d}#|	|
|
�                    dI�  �        |/|-|-dJz  |0|1|#|
|||||$|'|| |)|,dK�|||||%dL�dM�}2|�                    |2�  �         ���# t(          $ r }3t          dN|	� dO|3� ��  �         Y dP}3~3���dP}3~3ww xY w|| _        t          j        �   �         t          d�
�  �        z   | _        |�r�| j        ||t/          |�  �        |d         t/          |�  �        d2k    r|d2         ndPt/          |�  �        dQk    r|dQ         ndP|d         t1          dR� |D �   �         �  �        t/          |�  �        z  t#          dS� |D �   �         �  �        t%          dT� |D �   �         �  �        |d         dU         | �                    |�  �        | �                    |�  �        | �                    |�  �        dV�}4t          dWt/          |�  �        � dX��  �         t          dY|d         dZ         d[�d\|d         dU         d]�d^��  �         t          d_|d         dZ         d[�d\|d         dU         d]�d^��  �         t          d`|4da         db���  �         |4S t          dc�  �         i S # t(          $ r6}3t          dd|3� ��  �         ddPl}5|5�                    �   �          i cY dP}3~3S dP}3~3ww xY w)ez�
        ULTIMATIVE 24H-PROGNOSE V4.0 MIT VISUALISIERUNG
        ==============================================
        Erweiterte 24h-Prognose mit detaillierter Visualisierungs-Integration
        z;Berechne ultimative 24h-Prognose V4.0 mit Visualisierung...�
current_pricer�   r�   rp   r�   �
ml_predictionrj   rk   rl   �333333�?�{�G�z�?皙�����?g{�G�z��g����MbP?r�   r�   �      �?r�   r�   r�   r�   r�   r   �K   g����Mb`�r�   g����Mb`?r�   r�   r�   皙�����?皙�����?r�   r�   g����Mb�?�   �   g�������?�   r]   gffffff�?r�   �   �ffffff�?��������?g�������?�   g�������?r�   �
momentum_5�momentum_20r�   �333333�?皙�����?r�   r�   r  r	  r  g333333�?�{�G�z�?�ffffff�?�STARKER_ANSTIEGr�   g{�G�zt?�ANSTIEGg���Q���u   STARKER_RÜCKGANGg{�G�zt�u	   RÜCKGANGu
   SEITWÄRTSz%d.%m.%Y %H:%M:%Sr�   )�
base_trend�
volatility�rsi_correction�
macd_momentum�bb_reversion�momentum_factor�
volume_factor�time_factor�weekday_factor�confidence_factor�
regime_factor)r�   r�   r/  ri  �volume_ratio)rn   r�   �time_strro   �price_change�price_change_percentrp   rq   �trend_strength�factors�technical_contextzFehler bei Stunden-Prognose z: N�   c              3   �&   K  � | ]}|d          V � �
dS )rp   N� ��.0�ps     r;   �	<genexpr>zVUltimateBitcoinTradingSystemV4.calculate_ultimate_24h_prediction_v4.<locals>.<genexpr>�  s&   � � � �-Z�-Z�!�a��o�-Z�-Z�-Z�-Z�-Z�-Zr=   c              3   �&   K  � | ]}|d          V � �
dS �ro   Nr|  r}  s     r;   r�  zVUltimateBitcoinTradingSystemV4.calculate_ultimate_24h_prediction_v4.<locals>.<genexpr>�  �(   � � � �.`�.`��q�1B�/C�.`�.`�.`�.`�.`�.`r=   c              3   �&   K  � | ]}|d          V � �
dS r�  r|  r}  s     r;   r�  zVUltimateBitcoinTradingSystemV4.calculate_ultimate_24h_prediction_v4.<locals>.<genexpr>�  r�  r=   rv  )r/   �calculation_timerP  �predictions_count�next_hour_prediction�
6h_prediction�12h_prediction�24h_prediction�average_confidence�max_predicted_price�min_predicted_price�total_24h_change�dominant_trend�volatility_forecast�visualization_dataz(Ultimative 24h-Prognose V4.0 berechnet: u    stündliche Vorhersagenu   Nächste Stunde: $ro   �,.2fz (z+.2fz%)z
24h Gesamt: $zDurchschnittliche Konfidenz: r�  r   u&   Keine gültigen 24h-Prognosen erstelltz*FEHLER bei ultimativer 24h-Prognose V4.0: )r7   r   r.   rN   r�   rw   r/   r   rx   �	enumerate�math�sqrt�random�uniformr�   rn   �weekdayr�   r�   �strftimerQ   r,   r0   rO   �sum�_analyze_dominant_trend�_forecast_volatility�_prepare_visualization_datar  r  )6r:   r�   rg   rN  r�   rP  �base_confidencerQ  �prediction_timesrn   ry   r,   �irh  ri  r�   r7  �volatility_factorr�   r�   r�   rj  r�   r�   r�   rk  r/  r0  rl  �hour_of_dayr�  ro  rp  r`  ra  rw  rm  rs  r�   rn  �confidence_decayrq  r	  r  rr  �total_change�max_hourly_changero   �prediction_confidencerq   �
predictionrU   �summaryr  s6                                                         r;   �$calculate_ultimate_24h_prediction_v4zCUltimateBitcoinTradingSystemV4.calculate_ultimate_24h_prediction_v4  s�
  � �t	��O�P�P�P�#�<�>�>�L�*�.�.���7��@P�QS�@T�U�U�M� -�0�0��s�C�C�O�*�.�.���D�D�M�  "���a���� 
5� 
5��"�4�y�t�7L�7L�7L�L�� �'�'��4�4�4�4� "$��"+�,<�"=�"=� y
� y
���;��1�u��v� %�s�*�*�%)�]�S�-@�%A�D�I�d�O�O�%S�
�
�&��,�,�%*�c�M�.A�%B�T�Y�t�_�_�%T�
�
�%*�m�c�.A�%B�T�%I�
� ",�����!F�!F�J� *���{�C� @� @�I�!+���0C�S�!I�!I�J� #�T�Y�t�_�_�4���r�1�-�-�.�03�4�!�"�$.�/� &� (�^�^�H�b�9�9�F�'�^�^�H�b�9�9�F�%/�^�^�4D�a�%H�%H�N���{�{�)/�6�B�;�)?�$�)F�!�c�R`�Na�Na�df�Nf�Jf�)g����"���).�"�v�+�)>��)E��S�Q_�M`�M`�ce�Me�Ie�)f���)*�� &�>�>�&�!�4�4�D�",�.�.���"B�"B�K�!+����a�!@�!@�J�&*�[�&8�M�%I�C�%O�RV�%V�#-��#4�t�#;�&<�M� #-�.�.�1A�3�"G�"G�K�)�~�~�m�S�A�A�H�$'�+�$5��#>��#E��X��#V�L� #.�"2�K�)�1�1�3�3�G� �K�-�-�-�-�2�-�-�-�-�-�&)����{�*�*�k�Q�.>�.>�&*����k�.�.�.�.�Q�.�.�.�.�.�&)���&)�� �!�|�|�),��� �A���)-��� �A���),���),�� ",����a�!@�!@�J�",�.�.���"B�"B�K�%/�^�^�4G��%K�%K�N� #�S�(�#�c�)�*�&��,�-� �	'�  #�	'#�O� $.�>�>�2C�S�#I�#I�L�#-�>�>�.�#�#F�#F�L� &��)�U�2�T�9�%��)�U�2�T�9�:� "� (+�3��1�t�e�|�CS�0T�'U�'U�$�(8�3�(>��(D�%� -7�N�N�;R�TU�,V�,V�)�%/�^�^�4D�a�%H�%H�N�$'�M�,�t�3�3�%��,�
��>�*�*�S�0�0�%��,�
� #�)�*�&�'� &�&� %�	%�
 (�(� &�
&� $�$$� '5�$5� 8I�$I� LY�$Y�L� )-�z�A�~�(=�%�#&�(9�'9�3�?P�R^�;_�;_�#`�#`�L� '4�q�<�7G�&H�O� -=��C��DU�DU�XY�DY�@Y�,Z�)�,/��S��?T�5U�5U�,V�,V�)� $�e�+�+� 1��),�S�,��2E�)F�)F���%��-�-� )��)5��)=���%��.�.� 3��),�S�#�l�2C�2C�d�2J�)K�)K���%��.�.� +��),�\�):�):�U�)B��� ,��),�� !%� +�$/�$8�$8�9L�$M�$M�+:�(4�0<�s�0B�&;�!&�*8�*4�*;�.<�-:�,8�/>�-:�+6�.<�1B�-:�$� $� '-�$(�+6�*4�,8�.� .�/"� "�J�@ '�-�-�j�9�9�9�9�� � � � ��D��D�D��D�D�E�E�E��H�H�H�H���������
 '9�D�#�*2�,�.�.�9�1�;M�;M�;M�*M�D�'� "� 
�)-�)?�(4�%2�),�-?�)@�)@�,>�q�,A�>A�BT�>U�>U�XY�>Y�>Y�%7��%:�%:�_c�@C�DV�@W�@W�Z\�@\�@\�&8��&<�&<�bf�&8��&<�*-�-Z�-Z�GY�-Z�-Z�-Z�*Z�*Z�]`�as�]t�]t�*t�+.�.`�.`�M_�.`�.`�.`�+`�+`�+.�.`�.`�M_�.`�.`�.`�+`�+`�(:�2�(>�?U�(V�&*�&B�&B�CU�&V�&V�+/�+D�+D�EW�+X�+X�*.�*J�*J�K]�*^�*^�� ��$ �r��EW�AX�AX�r�r�r�s�s�s��  S�+=�a�+@�AR�+S�  S�  S�  S�\n�op�\q�  sI�  ]J�  S�  S�  S�  S�  T�  T�  T��  P�&8��&<�=N�&O�  P�  P�  P�Xj�km�Xn�  pF�  YG�  P�  P�  P�  P�  Q�  Q�  Q��Y�g�>R�6S�Y�Y�Y�Z�Z�Z����>�?�?�?��	��� 	� 	� 	��B�q�B�B�C�C�C��������!�!�!��I�I�I�I�I�I�����		���sP   �C^ �R4V�
^ �
V6�V1�+^ �1V6�6G^ �^ �
_�"+_�
_�_�predictionsc                 ��   � 	 d� |D �   �         }i }|D ]}|�                     |d�  �        dz   ||<   �t          ||j         ��  �        }||         t          |�  �        z  }|||d�S #  ddi d�cY S xY w)u%   Analysiere dominanten Trend über 24hc                 �   � g | ]
}|d          ��S �rq   r|  r}  s     r;   �
<listcomp>zJUltimateBitcoinTradingSystemV4._analyze_dominant_trend.<locals>.<listcomp>  �   � �6�6�6�Q�a��j�6�6�6r=   r   rj   )rK  )rq   rp   �distribution�	UNBEKANNTr�   )rN   r�   rO   )r:   r�  �trends�trend_countsrq   �dominantrp   s          r;   r�  z6UltimateBitcoinTradingSystemV4._analyze_dominant_trend�  s�   � �	Q�6�6�+�6�6�6�F��L�� 
E� 
E��&2�&6�&6�u�a�&@�&@�1�&D��U�#�#��<�\�-=�>�>�>�H�%�h�/�#�f�+�+�=�J� "�(� ,�� � 
��
	Q�(��R�P�P�P�P�P���s   �A"A% �%A/c                 ��   � 	 d� |D �   �         }t          j        |�  �        }t          |�  �        }|d         |d         k    rdnd}|||t          d|dz  �  �        d�S #  dd	d
dd�cY S xY w)u%   Prognostiziere Volatilität über 24hc                 �8   � g | ]}t          |d          �  �        ��S )ru  )r�   r}  s     r;   r�  zGUltimateBitcoinTradingSystemV4._forecast_volatility.<locals>.<listcomp>  s%   � �I�I�I��S��>�!2�3�3�I�I�Ir=   r�   r   �STEIGEND�FALLENDrU  rS  )�average_volatility�max_volatility�volatility_trend�volatility_scorerd  �STABILr�   )r�   r�   r�   r�   )r:   r�  �
price_changes�avg_volatilityr�  r�  s         r;   r�  z3UltimateBitcoinTradingSystemV4._forecast_volatility  s�   � �
	�I�I�[�I�I�I�M��W�]�3�3�N� ��/�/�N�-:�2�->��q�AQ�-Q�-Q�z�z�W`�� '5�"0�$4�$'��^�d�-B�$C�$C�	� � 
��	�*.�$�\d�z}�~�~�~�~�~���s   �AA �	A*c                 ��   � 	 d� |D �   �         }d� |D �   �         }d� |D �   �         }d� |D �   �         }d� |D �   �         |||d� |D �   �         d� |D �   �         d�S #  i cY S xY w)	u%   Bereite Daten für Visualisierung vorc                 �   � g | ]
}|d          ��S )r�   r|  r}  s     r;   r�  zNUltimateBitcoinTradingSystemV4._prepare_visualization_data.<locals>.<listcomp>&  s   � �9�9�9��!�F�)�9�9�9r=   c                 �   � g | ]
}|d          ��S )ro   r|  r}  s     r;   r�  zNUltimateBitcoinTradingSystemV4._prepare_visualization_data.<locals>.<listcomp>'  s   � �@�@�@�q�a�)�*�@�@�@r=   c                 �   � g | ]
}|d          ��S )rp   r|  r}  s     r;   r�  zNUltimateBitcoinTradingSystemV4._prepare_visualization_data.<locals>.<listcomp>(  s   � �@�@�@�q�1�\�?�@�@�@r=   c                 �   � g | ]
}|d          ��S r�  r|  r}  s     r;   r�  zNUltimateBitcoinTradingSystemV4._prepare_visualization_data.<locals>.<listcomp>)  r�  r=   c                 �6   � g | ]}|�                     �   �         ��S r|  )r_   )r~  �ts     r;   r�  zNUltimateBitcoinTradingSystemV4._prepare_visualization_data.<locals>.<listcomp>,  s    � �A�A�A��q�{�{�}�}�A�A�Ar=   c                 �   � g | ]
}|d          ��S )rv  r|  r}  s     r;   r�  zNUltimateBitcoinTradingSystemV4._prepare_visualization_data.<locals>.<listcomp>0  s   � �!Q�!Q�!Q��!�$:�";�!Q�!Q�!Qr=   c                 �   � g | ]
}|d          ��S )rw  r|  r}  s     r;   r�  zNUltimateBitcoinTradingSystemV4._prepare_visualization_data.<locals>.<listcomp>1  s   � �#M�#M�#M�A�A�&6�$7�#M�#M�#Mr=   )�
timestampsr  �confidencesr�  r�  �trend_strengthsr|  )r:   r�  r�  r  r�  r�  s         r;   r�  z:UltimateBitcoinTradingSystemV4._prepare_visualization_data#  s�   � �	�9�9�[�9�9�9�J�@�@�K�@�@�@�F�@�@�K�@�@�@�K�6�6�+�6�6�6�F� B�A�j�A�A�A� �*� �!Q�!Q�[�!Q�!Q�!Q�#M�#M��#M�#M�#M�
� � 
��	��I�I�I���s   �AA �Ac                 ��	  � 	 t          d�  �         t          j        �   �         }| �                    �   �         }|j        rt	          d�  �        �t          dt          |�  �        � d��  �         | �                    |�  �        }|st	          d�  �        �t          dt          |�  �        � d��  �         t          | j        �  �        dk    st          |�  �        d	k    rEt          d
�  �         | �                    |�  �        }|rt          d�  �         nt          d�  �         | �	                    ||�  �        }| �
                    |||�  �        }| �                    |||�  �        }| �                    |�  �         t          j        �   �         |z
  }t          j        �   �         �                    �   �         |t          |�  �        |d
         j        d         |�                    dd�  �        |�                    dd�  �        |�                    dd�  �        t          | j        �  �        |||| j        �                    �   �         | j        t          |�  �        dk    r|j        d         �                    �   �         ndt          |�  �        dk    r|j        d         �                    �   �         ndd|j        v r't/          |d         �                    �   �         �  �        ndd|j        v r't/          |d         �                    �   �         �  �        ndd|j        v r't/          |d         �                    �   �         �  �        ndt          |�  �        dk    r9t/          |d
         �                    �   �         �                    �   �         �  �        ndd�d�|t          |�  �        t          | j        �  �        | j        �                    dd�  �        | j        �                    dd�  �        d�d�}	t          d|d �d!��  �         t          d"|	d         � d#|	d         d$�d%��  �         t          d&| j        �                    d'd�  �        d$���  �         |	S # t          $ r�}
t          d(|
� ��  �         ddl}|�                    �   �          t          j        �   �         �                    �   �         dt?          |
�  �        d)d*dd| j        �                    �   �         d+�cY d}
~
S d}
~
ww xY w),u�   
        ULTIMATIVE MARKTANALYSE V4.0
        ============================
        Führt komplette Analyse mit erweiterten Features durch
        z$Starte Ultimate Marktanalyse V4.0...u   Keine Marktdaten verfügbarr�   z Datenpunktez5Technische Indikatoren konnten nicht berechnet werdenr
  r  r   r�   u,   Führe erweiterte ML-Modell Analyse durch...u/   ✅ Erweiterte ML-Modelle erfolgreich trainiertu<   ⚠️ ML-Training suboptimal - verwende verfügbare Modeller�   r�   r*  �HALTENrp   r�   rQ  Nr�   r�   r�   rj   )�high�lowri  )�period_start�
period_end�total_volume�price_ranger   r   )�
analysis_time�indicators_calculated�ml_models_usedr   �api_calls_total)rX   r�  �data_pointsrP  r*  rp   rQ  �models_availabler2   �prediction_summary�
risk_analysisr6   r,   �market_data_summary�performance_metricsz'Ultimate Analyse V4.0 abgeschlossen in r�   r�   zSignal: �
 (Konfidenz: r   �)zAktuelle Genauigkeit: r   z%FEHLER bei ultimativer Analyse V4.0: �FEHLERr   )rX   r�  �errorr*  rp   rP  r�  r6   ) r7   r�   r�   r�   rQ   rO   rM  r%   �train_ultimate_ml_models_v4�make_ultimate_prediction_v4r�  �%calculate_ultimate_risk_management_v4�_update_session_stats_v4r   r.   r_   r�   rN   r6   �copyr,   r�   �columnsr�   r�  r�   r�   r�   r�   r  r  �str)r:   r�   r�   rg   �
ml_trained�prediction_resultr�  r�  r�  �resultrU   r  s               r;   �run_ultimate_analysis_v4z7UltimateBitcoinTradingSystemV4.run_ultimate_analysis_v46  s�  � �a	��8�9�9�9�����J� �2�2�4�4�B��x� 
?�� =�>�>�>��E��B���E�E�E�F�F�F� �H�H��L�L�J�� 
Y�� W�X�X�X��d�s�:���d�d�d�e�e�e� �4�>�"�"�a�'�'�3�r�7�7�c�>�>��D�E�E�E�!�=�=�b�A�A�
�� Z��K�L�L�L�L��X�Y�Y�Y� !%� @� @��Z� P� P�� "&�!J�!J�2�z�[l�!m�!m�� !�F�F�r�K\�^h�i�i�M� 
�)�)�*;�<�<�<� !�I�K�K�*�4�M� &�\�^�^�5�5�7�7�!.�"�2�w�w�!#�G��!1�"�!5�+�/�/��(�C�C�/�3�3�L�#�F�F�!2�!6�!6���!L�!L�$'���$7�$7�(2�&8�!.�!%�!3�!8�!8�!:�!:�&*�&=�?B�2�w�w��{�{�B�H�Q�K�$9�$9�$;�$;�$;�PT�>A�"�g�g��k�k�"�(�2�,�"8�"8�":�":�":�t�AI�R�Z�AW�AW�E�"�X�,�*:�*:�*<�*<�$=�$=�$=�]^�;A�R�Z�;O�;O��b��j�n�n�&6�&6� 7� 7� 7�UV�9>�"�*�9L�9L�u�R��Y�]�]�_�_�5�5�5�RS�OR�SU�w�w�YZ�{�{�e�B�w�K�,B�,B�,D�,D�,H�,H�,J�,J�&K�&K�&K�`a�$� $�		(� 	(� &3�-0��_�_�&)�$�.�&9�&9�&*�&8�&<�&<�=M�q�&Q�&Q�'+�'9�'=�'=�>O�QR�'S�'S�(� (�1� �F�B 
�P�M�P�P�P�P�Q�Q�Q��W�V�H�-�W�W�F�<�<P�W�W�W�W�X�X�X��^�4�+=�+A�+A�BT�VW�+X�+X�^�^�^�_�_�_��M��� 	� 	� 	��=�!�=�=�>�>�>��������!�!�!� &�\�^�^�5�5�7�7�!"��Q���"�!�!"�$%�!%�!3�!8�!8�!:�!:�	� 	� 	
� 	
� 	
� 	
� 	
� 	
�����
	���s   �QQ �
S%�A;S �S%� S%c                 �  � 	 t          d�  �         t          |�  �        dk     r"t          dt          |�  �        � d��  �         dS | �                    |�  �        }|st          d�  �         dS t          d�  �         t          j        d�  �         d	d
ddd
d�| _        | j        �                    �   �         D ]�}t          j        �	                    dd�  �        t          j        �	                    dd�  �        t          j        �	                    dd�  �        t          j        �   �         �                    �   �         d�| j
        |<   ��| j        dxx         dz
  cc<   | j        dxx         dz
  cc<   t          dt          | j        �  �        � d��  �         dS # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)z"Erweiterte ML-Modell Training V4.0z,Starte erweiterte ML-Modell Training V4.0...r�   u%   Nicht genügend Daten für Training: z < 50Fu6   Keine technischen Indikatoren für Training verfügbarz*Simuliere erweiterte ML-Modell Training...rW  �trained_model_rf�trained_model_gb�trained_model_xgb�trained_model_nn�trained_model_ensemble)�random_forest_v4�gradient_boost_v4�
xgboost_v4�neural_network_v4�ensemble_v4�      �?r^  r   re  r�   �       @)�accuracyrp   �
training_timerX   r   rj   r   u*   ✅ Erweiterte ML-Modelle V4.0 trainiert: rD   Tz*FEHLER beim erweiterten ML-Training V4.0: N)r7   rO   rM  r�   �sleepr%   �keysr�   r�  r�  r   r.   r_   r&   r6   rQ   )r:   r�   rg   �namerU   s        r;   r�  z:UltimateBitcoinTradingSystemV4.train_ultimate_ml_models_v4�  s�  � �+	��@�A�A�A��2�w�w��|�|��L�c�"�g�g�L�L�L�M�M�M��u� �H�H��L�L�J�� 
��N�O�O�O��u� 
�>�?�?�?��J�s�O�O�O� %7�%7�1�%7�7�� �D�N� ��+�+�-�-� 
� 
�� "�	� 1� 1�$�� =� =�"$�)�"3�"3�D�$�"?�"?�%'�Y�%6�%6�s�C�%@�%@�!)����!9�!9�!;�!;�	0� 0��&�t�,�,� 
��0�1�1�1�Q�6�1�1�1���3�4�4�4��9�4�4�4��\�s�4�>�?R�?R�\�\�\�]�]�]��4��� 	� 	� 	��B�q�B�B�C�C�C��5�5�5�5�5�����	���s%   �AF �&F �.D'F �
F>�!F9�9F>c                 �  � 	 t          d�  �         | j        s$t          d�  �         | �                    |�  �        S t          j        �                    dd�  �        }t          j        �                    dd�  �        }|dk    rd}t
          d	|d
z
  dz  �  �        }n#|dk     rd
}t
          d	d
|z
  dz  �  �        }nd}d
}||z  }||||t          | j        �  �        t          j        �                    dd�  �        t          j        �                    d
d�  �        t          j        �                    dd�  �        t          j        �                    dd�  �        d�t          j	        �   �         �
                    �   �         d�}t          d|� d|d�d��  �         |S # t          $ r1}	t          d|	� ��  �         | �                    |�  �        cY d}	~	S d}	~	ww xY w)z'Ultimative intelligente Vorhersage V4.0u*   Führe ultimative Vorhersage V4.0 durch...uD   Keine ML-Modelle verfügbar - verwende erweiterte technische Analyserc  r   r]  re  g�������?�KAUFENrU  r�   r]   gffffff�?�	VERKAUFENr�  rR  r^  rT  )�technical_score�momentum_scorer�  �volume_score)r*  rp   rQ  �signal_strength�models_used�prediction_factorsrX   zUltimative Vorhersage V4.0: r�  r   r�  z(FEHLER bei ultimativer Vorhersage V4.0: N)r7   r%   �!_enhanced_technical_prediction_v4r�   r�  r�  r�   rO   r   r.   r_   rQ   )
r:   r�   rg   �ensemble_prediction�model_confidencer*  r  �final_confidencer�  rU   s
             r;   r�  z:UltimateBitcoinTradingSystemV4.make_ultimate_prediction_v4�  s
  � �,	F��>�?�?�?��>� 
J��\�]�]�]��=�=�j�I�I�I� #%�)�"3�"3�C��"=�"=��!�y�0�0��d�;�;�� #�T�)�)�!��"%�c�,?�#�,E��+J�"K�"K���$�t�+�+�$��"%�c�C�2E�,E��+J�"K�"K���!��"%��/�/�A�� !�.�!4�#2�"�4�>�2�2�')�y�'8�'8��c�'B�'B�&(�i�&7�&7��S�&A�&A�(*�	�(9�(9�#�s�(C�(C�$&�I�$5�$5�c�3�$?�$?�	'� '� &�\�^�^�5�5�7�7�
� 
�J� 
�]��]�]�FV�]�]�]�]�^�^�^����� 	F� 	F� 	F��@�Q�@�@�A�A�A��9�9�*�E�E�E�E�E�E�E�E�����	F���s#   �9F �EF �
G�"&G�G�Gc                 �2  � 	 |�                     dd�  �        }|�                     dd�  �        }|�                     dd�  �        }|�                     dd�  �        }|�                     d	d�  �        }g }|d
k    r|�                    d�  �         n1|dk     r|�                    d
�  �         n|�                    d�  �         |dk    r|�                    d�  �         n|�                    d�  �         |dk    r|�                    d�  �         n1|dk     r|�                    d�  �         n|�                    d�  �         |dk    r|�                    d�  �         n|�                    d�  �         |�                    |d
z  �  �         t          |�  �        }|dk    r#d}	t          ddt	          |�  �        z   �  �        }
n-|dk     r#d}	t          ddt	          |�  �        z   �  �        }
nd}	d}
|	|
d|z   t	          |�  �        dt          |�  �        dk    r|d         ndt          |�  �        dk    r|d         ndt          |�  �        dk    r|d         ndt          |�  �        dk    r|d         ndt          |�  �        dk    r|d         nd|d�t
          j        �   �         �                    �   �         d�S # t          $ rV}t          d|� ��  �         dddddt          |�  �        t
          j        �   �         �                    �   �         d �cY d!}~S d!}~ww xY w)"z/Erweiterte technische Analyse als Fallback V4.0r�   r�   r�   r   r�   r�   r�   rU  r�   �F   g333333ӿr�   rb  r   rc  g������ɿr   g��������rW  �      �?g��������r   r  r�  rj   r]   r�   r_  )�	rsi_score�
macd_score�bb_scorer  �trend_score�total_score)r*  rp   rQ  r  r  �technical_scoresrX   z,FEHLER bei erweiterter technischer Analyse: )r*  rp   rQ  r  r  r�  rX   N)rN   rx   r�  r�   r�   rO   r   r.   r_   rQ   r7   r�  )r:   rg   r�   r�   r/  rs  rw  �scoresr  r*  rp   rU   s               r;   r  z@UltimateBitcoinTradingSystemV4._enhanced_technical_prediction_v4�  sb  � �R	��^�^�H�b�1�1�F��>�>�&�!�,�,�D�$�.�.�)9�3�?�?�K�%�>�>�*;�S�A�A�L�'�^�^�,?��C�C�N� �F� ��{�{��
�
�d�#�#�#�#��"����
�
�c�"�"�"�"��
�
�c�"�"�"� �a�x�x��
�
�c�"�"�"�"��
�
�d�#�#�#� �S� � ��
�
�d�#�#�#�#��s�"�"��
�
�c�"�"�"�"��
�
�c�"�"�"� �c�!�!��
�
�c�"�"�"�"��
�
�e�$�$�$� 
�M�M�.�3�.�/�/�/� �f�+�+�K��S� � �!�� ��c�C��,<�,<�&<�=�=�
�
��t�#�#�$�� ��c�C��,<�,<�&<�=�=�
�
�!�� �
� !�(�!$�{�!2�#&�{�#3�#3� �.1�&�k�k�A�o�o�����1�/2�6�{�{�Q���&��)�)�A�-0��[�[�1�_�_��q�	�	�!�14�V���q���F�1�I�I�a�03�F���a���6�!�9�9�Q�#.�
%� %� &�\�^�^�5�5�7�7�� � 
��" � 
	� 
	� 
	��D��D�D�E�E�E�"�!�!$�#&� ��Q���%�\�^�^�5�5�7�7�� � 
� 
� 
� 
� 
� 
�����
	���s   �J3J6 �6
L� AL�L�Lr�  c                 �F  � 	 |d         j         d         }|�                    dd�  �        }|�                    dd�  �        }|d         �                    �   �         �                    �   �         }|�                    �   �         }t          |�  �        dk    r'|�                    d�  �        �                    �   �         n|}	|�                    d|d	z  �  �        }
d
}|dz  }t          dd
|dz  z
  �  �        }
||z  |
z  }t          dt          d|�  �        �  �        }d}d}|dk    r'||
|z  z
  }||
|z  z   }||z
  |z  dz  }||z
  |z  dz  }n5|dk    r'||
|z  z   }||
|z  z
  }||z
  |z  dz  }||z
  |z  dz  }n|}|}d}d}|dk    rt          |�  �        t          |�  �        z  nd}| j
        �                    dd�  �        }|dz  }|dz  }|dk    rJ||z  d
|z
  t          |�  �        z  z
  t          |�  �        z  }t          dt          d|�  �        �  �        }nd
}|	t          j        d�  �        z  dz  }t          |�  �        dk    rA|�
                    �   �         dz  |�                    �   �         t          j        d�  �        z  z  nd}|dz  ||||||t          ||�  �        ||	|
|
|z  dz  d�|d	k     rd n	|d!k     rd"nd#|d$k    rd#n	|dk    rd"nd t          |�                    d%d�  �        �  �        d	k    rd&nd'd(�||||d
k     r||z  d
|z
  z  n|d)�t          j        �   �         �                    �   �         d*�}|S # t"          $ rV}t%          d+|� ��  �         dd,ddd
t'          |�  �        t          j        �   �         �                    �   �         d-�cY d.}~S d.}~ww xY w)/z Ultimatives Risk Management V4.0r�   r�   r*  r�  rp   r�   �   r�   rS  rX  r  rb  rj   r�   r   g333333�?r�  g      @r   r�   r  r   rU  r   rR  g      �?r\  r]   r�   im  )�daily_volatility�weekly_volatilityr�   �atr_percent�NIEDRIGrd  �MITTEL�HOCHr   r�   �TRENDING�RANGING)�
risk_level�confidence_level�
market_regime)�max_drawdown_estimate�sharpe_estimate�win_rate_estimate�
profit_factor)�position_size_percent�stop_loss_price�take_profit_price�stop_loss_percent�take_profit_percent�risk_reward_ratio�kelly_fraction�recommended_position�volatility_metrics�risk_assessment�advanced_metricsrX   z!FEHLER bei Risk Management V4.0: g       �)r'  r*  r+  r,  r-  r�  rX   N)r�   rN   r�   r�   r�   rO   �tailr�   r�   r�   r6   r�   r�  r�   r   r.   r_   rQ   r7   r�  )r:   r�   r�  rg   rP  r*  rp   r"  �
volatility_1d�
volatility_7dr�   �
base_position�confidence_multiplier�volatility_adjustment�
position_size�atr_multiplier_sl�atr_multiplier_tpr(  r)  r*  r+  r,  �win_rate�avg_win�avg_lossr-  r#  r$  r�  rU   s                                 r;   r�  zDUltimateBitcoinTradingSystemV4.calculate_ultimate_risk_management_v4T  s�  � �b	��w�K�,�R�0�M��^�^�H�h�7�7�F�#����c�:�:�J� ��k�,�,�.�.�5�5�7�7�G�#�K�K�M�M�M�7:�7�|�|�s�7J�7J�G�L�L��-�-�1�1�3�3�3�P]�M��^�^�H�m�d�.B�C�C�F� !�M�$.��$4�!�$'��Q�-�"�2D�-E�$F�$F�!�)�,A�A�DY�Y�M���c�$�
�&>�&>�?�?�M� !$�� #����!�!�"/�6�<M�3M�"N��$1�V�>O�5O�$P�!�&3�o�&E��%V�Z]�$]�!�(9�M�(I�]�'Z�^a�&a�#�#��;�&�&�"/�6�<M�3M�"N��$1�V�>O�5O�$P�!�&5�
�&E��%V�Z]�$]�!�(5�8I�(I�]�'Z�^a�&a�#�#�"/��$1�!�$%�!�&'�#� Vg�jk�Uk�Uk��$7� 8� 8�3�?P�;Q�;Q� Q� Q�qt�� �)�-�-�.@�#�F�F�H�)�C�/�G�(�3�.�H��1�}�}�"*�W�"4��H���H�
�
�7U�"U�Y\�]e�Yf�Yf�!f��!$�Q��D�.�(A�(A�!B�!B���!%�� %2�B�G�A�J�J�$>��$B�!�Y\�]d�Ye�Ye�hj�Yj�Yj�w�|�|�~�~��3����
�
���PS���8T�U�U�pq�O� *7��)<�#2�%6�%6�':�%6�"0�(+�M�>�(J�(J�(5�)6�$�$*�]�$:�c�#A�	'� '� 0=�t�/C�/C�)�)�Ub�ei�Ui�Ui���ou�2<�s�2B�2B���T^�ad�Td�Td���js�36�z�~�~�FY�[\�7]�7]�3^�3^�ae�3e�3e�Z�Z�kt�$� $� .C�'6�)1�V^�ab�Vb�Vb�%6��%A�Q��\�%R�%R�hy�	%� %� &�\�^�^�5�5�7�7�5� �M�: !� ��� 
	� 
	� 
	��9�a�9�9�:�:�:�),�%)�'*�%(�"&��Q���%�\�^�^�5�5�7�7�� � 
� 
� 
� 
� 
� 
�����
	���s   �L=M  � 
N �
AN�N �N c                 �J  � 	 | j         dxx         dz
  cc<   t          | j        �  �        dk    rZt          j        �                    dd�  �        }|| j         d<   || j         d         k    r|| j         d<   | j         dxx         dz
  cc<   |�                    d	d
�  �        }| j         �                    dd
�  �        }| j         d         }||dz
  z  |z   |z  }|| j         d<   t          j        �   �         �	                    �   �         |�                    dd
�  �        ||�                    dd
�  �        d�}| j        �
                    |�  �         t          | j        �  �        dk    r| j        dd�         | _        dS dS # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)zUpdate Session-Statistiken V4.0r   rj   r�   r�  r^  r   r   r   rp   r�   r   r*  r�  rQ  )rX   r*  rp   rQ  i�  i���Nz!FEHLER bei Session Stats Update: )
r6   rO   r*   r�   r�  r�  rN   r   r.   r_   rx   rQ   r7   )	r:   r�  �recent_accuracyrp   �current_avgr   �new_avg�prediction_entryrU   s	            r;   r�  z7UltimateBitcoinTradingSystemV4._update_session_stats_v4�  s�  � �%	;���2�3�3�3�q�8�3�3�3� �4�*�+�+�b�0�0�"$�)�"3�"3�D�$�"?�"?��9H��"�#5�6�"�T�%7��%H�H�H�:I�D�&��7��&�';�<�<�<��A�<�<�<� $����c�:�:�J��,�0�0�1L�c�R�R�K� $� 2�3F� G��#�'8�1�'<�=��K�O`�`�G�>E�D��:�;� &�\�^�^�5�5�7�7�$�.�.��8�<�<�(�!+�����!E�!E�	 �  �� 
�#�*�*�+;�<�<�<� �4�*�+�+�c�1�1�*.�*A�$�%�%�*H��'�'�'� 2�1�� � 	;� 	;� 	;��9�a�9�9�:�:�:�:�:�:�:�:�:�����	;���s   �E5E; �;
F"�F�F"N)�__name__�
__module__�__qualname__�__doc__r<   r8   rb   r9   r�   r�   r�   r   r�   r�   r�   r   rM  r�  r   r�  r�  r�  r�  r  r�  r�  r  r�  r�  r|  r=   r;   r   r   6   s�  � � � � � �� �7-� 7-� 7-�rE� E� E�6"I� "I� "I�H;� ;� ;�@>"�b�l� >"� >"� >"� >"�@	�h�u�o� 	� 	� 	� 	�(�"�,� (�2�<� (� (� (� (�Tw�R�\� w�d� w� w� w� w�rz�r�|� z�QU� z�gk� z�pt� z� z� z� z�xQ�4��:� Q�$� Q� Q� Q� Q�&��T�
� �t� � � � �"�t�D�z� �d� � � � �&g�$� g� g� g� g�R-�b�l� -�t� -� -� -� -�^.F�b�l� .F�� .F�QU� .F� .F� .F� .F�`T�D� T�T� T� T� T� T�ld��� d�RV� d�dh� d�mq� d� d� d� d�L';�4� ';� ';� ';� ';� ';� ';r=   r   c                  �r  � t          d�  �         t          d�  �         t          d�  �         t          d�  �         	 t          �   �         } | �                    �   �         }t          d�  �         t          d�  �         t          d�  �         t          d�  �         t          d|�                    dd	�  �        d
���  �         t          d|�                    dd	�  �        � ��  �         t          d
|�                    dd	�  �        d�d��  �         t          d�  �         t          d|�                    dd�  �        � ��  �         t          d|�                    dd	�  �        d���  �         t          d|�                    dd	�  �        d���  �         t          d|�                    dd	�  �        � ��  �         |�                    di �  �        }t          d�  �         t          d|�                    d d	�  �        d���  �         t          d!|�                    d"d	�  �        d���  �         t          d#|�                    d$d	�  �        � ��  �         t          d%|�                    d&d	�  �        � ��  �         |�                    d'i �  �        }|r�t          d(�  �         t          d)|�                    d*d	�  �        d+�d,��  �         t          d-|�                    d.d	�  �        d+�d,��  �         t          d/|�                    d0d	�  �        d+�d,��  �         t          d1|�                    d2d	�  �        d���  �         t          d3|�                    d4d	�  �        d���  �         t          d5�  �         |S # t          $ r5}t          d6|� ��  �         d	d7l}|�                    �   �          Y d7}~d7S d7}~ww xY w)8u7   Hauptfunktion für Ultimate Bitcoin Trading System V4.0zP================================================================================z$ULTIMATE BITCOIN TRADING SYSTEM V4.0u?   KOMPLETT ÜBERARBEITET UND OPTIMIERT FÜR MAXIMALE GENAUIGKEIT!zQ
================================================================================z1ULTIMATE BITCOIN TRADING SYSTEM V4.0 - ERGEBNISSEz
MARKTDATEN:z   Bitcoin-Preis: $rP  r   r�  z   Datenpunkte: r�  z   Analysezeit: r�  r�   r�   z
ML-VORHERSAGE:z   Signal: r*  zN/Az   Konfidenz: rp   r   z   ML-Prediction: rQ  z.3fu      Verfügbare Modelle: r�  r6   z
SESSION-STATISTIKEN:z   Aktuelle Genauigkeit: r   z   Beste Genauigkeit: r   z   Gesamte Vorhersagen: r   z   Training-Zyklen: r   r�  z
RISK MANAGEMENT:z
   Position: r'  z.1f�%z   Stop Loss: r*  z   Take Profit: r+  z   Risk/Reward: r,  z   Kelly Criterion: r-  zH
ULTIMATE BITCOIN TRADING SYSTEM V4.0 - ERWEITERTE ANALYSEN ERFOLGREICH!z2FEHLER beim Ultimate Bitcoin Trading System V4.0: N)r7   r   r�  rN   rQ   r  r  )�systemr�  r6   r�  rU   r  s         r;   �&run_ultimate_bitcoin_trading_system_v4rJ  �  s�  � �	�(�O�O�O�	�
0�1�1�1�	�
K�L�L�L�	�(�O�O�O�/�/�1�1�� �0�0�2�2�� 	�o����
�A�B�B�B�
�h����
�����
�I�F�J�J���$B�$B�I�I�I�J�J�J�
�?����M�1�!=�!=�?�?�@�@�@�
�F����O�Q�!?�!?�F�F�F�F�G�G�G�
�!�"�"�"�
�9�F�J�J�x��7�7�9�9�:�:�:�
�@�v�z�z�,��:�:�@�@�@�A�A�A�
�G�6�:�:�o�q�#A�#A�G�G�G�H�H�H�
�L����4F��)J�)J�L�L�M�M�M��
�
�?�B�7�7�
�
�'�(�(�(�
�X�-�*;�*;�<N�PQ�*R�*R�X�X�X�Y�Y�Y�
�R�}�'8�'8��!�'L�'L�R�R�R�S�S�S�
�T��):�):�;N�PQ�)R�)R�T�T�U�U�U�
�N�]�%6�%6�7H�!�%L�%L�N�N�O�O�O��
�
�?�B�7�7�
�� 	W��'�(�(�(��V�-�"3�"3�4K�Q�"O�"O�V�V�V�V�W�W�W��S�=�#4�#4�5H�!�#L�#L�S�S�S�S�T�T�T��W�]�%6�%6�7L�a�%P�%P�W�W�W�W�X�X�X��T�]�%6�%6�7J�A�%N�%N�T�T�T�U�U�U��U��):�):�;K�Q�)O�)O�U�U�U�V�V�V�
�Z�[�[�[��
��� � � �
�F�1�F�F�G�G�G������������t�t�t�t�t�����	���s   �L8M7 �7
N6�*N1�1N6�__main__))rF  �pandasr�   �numpyr�   r�   �yfinancer�   r   r   �warningsr�   rP   rH   rL   �	threading�typingr   r   r   r   r	   r�  r�  �sklearn.preprocessingr
   �matplotlib.pyplot�pyplotrr   �seabornru   �scipyr   r�   �ImportErrorr7   �PLOTLY_AVAILABLE�filterwarningsr   rJ  rC  r|  r=   r;   �<module>rZ     s�  ��� � � � � � � � � � ���� � � � � (� (� (� (� (� (� (� (� ���� ���� ���� 	�	�	�	� 
�
�
�
� � � � � 3� 3� 3� 3� 3� 3� 3� 3� 3� 3� 3� 3� 3� 3� ���� 
�
�
�
�
 /� .� .� .� .� .�  � � � � � � � � � �C��������O�O��� C� C� C��O�	�E�
A�B�B�B�B�B�C����
 � � �� �� !� !� !�k;� k;� k;� k;� k;� k;� k;� k;�\%6� 6� 6�p �z���*�*�,�,�,�,�,� �s   �A# �#A8�7A8