�

    ��eh-�  �                   ��  � d Z ddlZddlmZmZ ddlmZ ddlm	Z	 ddl
mZ ddlm
Z ddlZddlZddlZddlmZmZ ddlZddlZddlZddlZddlZddlZddlZddlZddlm Z  ddl!Z!	 ddl"m#Z# d	Z$n# e%$ r d
Z$ e&d�  �         Y nw xY wej'        �(                    d�  �          ej)        d
�  �          e!j*        e!j+        d��  �          G d� d�  �        Z,d� Z-e.dk    r e-�   �          dS dS )u�  
ULTIMATE BITCOIN TRADING GUI V7.0 - SUPER OPTIMIZED
==================================================

VOLLSTÄNDIG OPTIMIERTE VERSION MIT:
- Entfernte doppelte Funktionen
- Verbesserte ML-Model-Optimierung
- Anschaulichere und bedienungsfreundlichere Visualisierung
- Optimiertes Training und Prognostizieren
- Modulare Architektur

Version: V7.0 SUPER OPTIMIZED
Erstellt: 2025-07-02
�    N)�ttk�
messagebox)�FigureCanvasTkAgg)�
FuncAnimation)�datetime�	timedelta)�deque)�UltimateBitcoinTradingSystemV6TFu+   WARNUNG: Trading System V6 nicht verfügbar�dark_background�viridis�)%(asctime)s - %(levelname)s - %(message)s)�level�formatc                   �  � e Zd ZdZdZd� Zd� Zd� Zd� Zd� Z	d� Z
d	� Zd
� Zd� Z
d� Zd
� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Z d� Z!d � Z"d!� Z#d"� Z$d#� Z%d$� Z&d%� Z'd&� Z(d'� Z)d(� Z*d)� Z+d*� Z,d+� Z-d,S )-�UltimateBitcoinGUIV7Optimizedu�   
    ULTIMATE BITCOIN TRADING GUI V7.0 - SUPER OPTIMIZED
    
    Vollständig optimierte Version mit:
    - Entfernte Duplikate
    - Verbesserte ML-Modelle
    - Anschaulichere Visualisierung
    - Optimiertes Training/Prognose
    zV7.0_SUPER_OPTIMIZEDc                 �  � t          d| j        � ��  �         t          d�  �         t          rt          �   �         nd| _        d| _        d| _        d| _        dddd�| _        dddd�| _	        t          j        �   �         | _        ddddddd	�| _
        t          d
��  �        | _        t          d��  �        | _        t          d
��  �        | _        i | _        i | _        i | _        dddddd�| _        | �                    �   �          | �                    �   �          t          d�  �         t1          j        d| j        � d��  �         dS )z(Initialisierung der optimierten GUI V7.0zULTIMATE BITCOIN TRADING GUI �<============================================================NF)�analysis�training�
monitoring)�analysis_running�training_running�monitoring_running�        �HOLD)�
current_price�prediction_24h�signal�
confidence�data_quality�model_accuracy�d   )�maxlen�2   �   �   �   ��  ��   i,  )�update_interval�training_interval�chart_update_rate�max_data_points�auto_save_intervalz"Optimierte GUI V7.0 initialisiert!zUltimateBitcoinGUIV7Optimized �
 gestartet)�print�VERSION�TRADING_SYSTEM_AVAILABLEr
   �trading_system�root�notebook�shutdown_requested�threads�
thread_states�	threading�Lock�thread_lock�datar	   �performance_history�scan_history�training_history�widgets�charts�
animations�config�_setup_logging�_register_shutdown_handlers�logging�info��selfs    �+E:\Dev\ultimate_bitcoin_gui_v7_OPTIMIZED.py�__init__z&UltimateBitcoinGUIV7Optimized.__init__B   s�  � �
�<�d�l�<�<�=�=�=�
�h���� C[�d�<�>�>�>�`d��� ��	���
�"'��� ���
� 
��� !&� %�"'�
� 
���
 %�>�+�+��� !�!����!�

� 
��	� $)��#4�#4�#4�� �!��,�,�,��� %�R� 0� 0� 0��� ���������  "�!$�!%�"�"%�
� 
��� 	
�������(�(�*�*�*�
�2�3�3�3���N�d�l�N�N�N�O�O�O�O�O�    c                 �   � dt          j        �   �         �                    d�  �        � d�}t          j        |t          j        d��  �         dS )zSetup optimiertes Logging�bitcoin_gui_v7_z%Y%m%dz.logr
   )�filenamer   r   N)r   �now�strftimerF   �basicConfig�INFO)rI   �log_files     rJ   rD   z,UltimateBitcoinGUIV7Optimized._setup_logging�   sU   � �L�X�\�^�^�%<�%<�X�%F�%F�L�L�L������,�>�	
� 	
� 	
� 	
� 	
� 	
rL   c                 �  � 	 t          j        | j        �  �         t          t          d�  �        r$t	          j        t          j        | j        �  �         t          t          d�  �        r&t	          j        t          j        | j        �  �         dS dS # t          $ r"}t          j
        d|� ��  �         Y d}~dS d}~ww xY w)zRegistriere Shutdown-Handler�SIGINT�SIGTERMzFehler bei Shutdown-Handler: N)�atexit�register�_cleanup_on_exit�hasattrr   rV   �_signal_handlerrW   �	ExceptionrF   �error�rI   �es     rJ   rE   z9UltimateBitcoinGUIV7Optimized._register_shutdown_handlers�   s�   � �	?��O�D�1�2�2�2��v�x�(�(� 
C��
�f�m�T�-A�B�B�B��v�y�)�)� 
D��
�f�n�d�.B�C�C�C�C�C�
D� 
D��� 	?� 	?� 	?��M�=�!�=�=�>�>�>�>�>�>�>�>�>�����	?���s   �BB �
B=�B8�8B=c                 �^   � t          j        d|� d��  �         | �                    �   �          dS )zSignal HandlerzSignal z empfangen - initiiere ShutdownN)rF   rG   �shutdown)rI   �signum�frames      rJ   r\   z-UltimateBitcoinGUIV7Optimized._signal_handler�   s/   � ���F�v�F�F�F�G�G�G��
�
�����rL   c                 �d   � t          j        d�  �         d| _        | �                    �   �          dS )zCleanup beim BeendenzCleanup beim Beenden...TN)rF   rG   r6   �_stop_all_threadsrH   s    rJ   rZ   z.UltimateBitcoinGUIV7Optimized._cleanup_on_exit�   s3   � ���.�/�/�/�"&������ � � � � rL   c                 ��  � 	 | j         5  dD ]m\  }}d| j        |<   | j        �                    |�  �        }|rB|�                    �   �         r.t          j        d|� d��  �         |�                    d��  �         �n| j        �                    �   �          ddd�  �         n# 1 swxY w Y   t          j        d�  �         dS # t          $ r"}t          j
        d	|� ��  �         Y d}~dS d}~ww xY w)
zStoppe alle Threads - Optimiert))r   r   )r   r   )r   r   FzStoppe z
 Thread...g       @)�timeoutNzAlle Threads gestopptz!Fehler beim Stoppen der Threads: )r;   r8   r7   �get�is_aliverF   rG   �join�clearr]   r^   )rI   �name�	state_key�threadr`   s        rJ   rf   z/UltimateBitcoinGUIV7Optimized._stop_all_threads�   se  � �	C��!� 

%� 

%�(L� 1� 1�O�D�)� 5:�D�&�y�1�!�\�-�-�d�3�3�F�� 1�&�/�/�"3�"3� 1���%?�t�%?�%?�%?�@�@�@����C��0�0�0����"�"�$�$�$�

%� 

%� 

%� 

%� 

%� 

%� 

%� 

%� 

%� 

%� 

%���� 

%� 

%� 

%� 

%� 
�L�0�1�1�1�1�1��� 	C� 	C� 	C��M�A�a�A�A�B�B�B�B�B�B�B�B�B�����	C���s;   �C  �B
B�C  �B#�#C  �&B#�'C  � 
C,�
C'�'C,c                 �.  � 	 t          j        d�  �         | �                    �   �          | j        r;	 | j        �                    �   �          | j        �                    �   �          n#  Y nxY wt          j        d�  �         n.# t          $ r!}t          j        d|� ��  �         Y d}~nd}~ww xY w	 t          j	        d�  �         dS #  t          j        d�  �         Y dS xY w# 	 t          j	        d�  �         w #  t          j        d�  �         Y w xY wxY w)zSauberes Beenden der Anwendungz+Beende Ultimate Bitcoin Trading GUI V7.0...zAnwendung beendetzFehler beim Beenden: Nr   )rF   rG   rf   r4   �quit�destroyr]   r^   �sys�exit�os�_exitr_   s     rJ   rb   z&UltimateBitcoinGUIV7Optimized.shutdown�   s/  � �	��L�F�G�G�G��"�"�$�$�$��y� 
���I�N�N�$�$�$��I�%�%�'�'�'�'����D�����L�,�-�-�-�-��� 	7� 	7� 	7��M�5�!�5�5�6�6�6�6�6�6�6�6�����	7����
����������
��������������
���������
��������������sj   �/B �2A% �$B �%A)�'B � C" �
B,�B'�"C" �'B,�,C" �0C �C�"D�$C9�8D�9D�Dc                 ��  � 	 | j         sdS t          j        d�  �         | �                    �   �         }|�|j        rdS | �                    |�  �        }| �                    |�  �        }| �                    ||�  �        }|rA| �                    �   �         | j	        d<   t          j        d| j	        d         d���  �         dS dS # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)	u-   Optimiere ML-Modelle für bessere PerformanceFzStarte ML-Model-Optimierung...Nr!   z$ML-Modelle optimiert - Genauigkeit: �.1%Tz!Fehler bei ML-Model-Optimierung: )r3   rF   rG   � _collect_optimized_training_data�empty�_advanced_feature_engineering�_optimize_hyperparameters�_train_optimized_models�_evaluate_model_performancer<   r]   r^   )rI   �
training_data�features�best_params�successr`   s         rJ   �optimize_ml_modelsz0UltimateBitcoinGUIV7Optimized.optimize_ml_models�   s#  � �	��&� 
��u��L�9�:�:�:� !�A�A�C�C�M��$�
�(;�$��u� �9�9�-�H�H�H� �8�8��B�B�K� �2�2�8�[�I�I�G�� 
�.2�.N�.N�.P�.P��	�*�+���e�D�I�N^�D_�e�e�e�f�f�f��t��5��� 	� 	� 	��M�A�a�A�A�B�B�B��5�5�5�5�5�����	���s"   �C �1C �BC �
C/�
C*�*C/c                 �>  � 	 | j         �                    �   �         }|j        rdS | �                    |�  �        }|dk     rt	          j        d|d���  �         dS | �                    |�  �        }|S # t          $ r"}t	          j        d|� ��  �         Y d}~dS d}~ww xY w)z Sammle optimierte TrainingsdatenNgffffff�?u   Datenqualität zu niedrig: rx   z#Fehler bei Trainingsdatensammlung: )	r3   �get_precise_market_data_v6rz   �_assess_data_qualityrF   �warning�_clean_training_datar]   r^   )rI   �df�
quality_scorer`   s       rJ   ry   z>UltimateBitcoinGUIV7Optimized._collect_optimized_training_data�   s�   � �	��$�?�?�A�A�B��x� 
��t� !�5�5�b�9�9�M��s�"�"��� Q�m� Q� Q� Q�R�R�R��t� �*�*�2�.�.�B��I��� 	� 	� 	��M�C��C�C�D�D�D��4�4�4�4�4�����	���s"   � A0 �3A0 �A0 �0
B�:B�Bc                 �  � 	 |�                     �   �         }d|j        vra|�                    t          j        g��  �        j        }t          |�  �        dk    r||d                  |d<   nt
          j        d�  �         |S |d         �                    �   �         |d<   t          j	        |d         |d         �
                    d�  �        z  �  �        |d<   |d         �                    d�  �        �                    �   �         |d	<   | �
                    |�  �        }| �                    |�  �        }| �                    |�  �        }| �                    |�  �        }|S # t"          $ r#}t
          j        d
|� ��  �         |cY d}~S d}~ww xY w)zErweiterte Feature-Engineering�close��includer   u;   Keine numerischen Spalten für Feature-Engineering gefunden�returns�   �log_returnsr%   �
volatilityz Fehler bei Feature-Engineering: N)�copy�columns�
select_dtypes�np�number�lenrF   r�   �
pct_change�log�shift�rolling�std�_add_technical_indicators�_add_temporal_features�_add_microstructure_features�_select_best_featuresr]   r^   )rI   r�   r�   �numeric_colsr`   s        rJ   r{   z;UltimateBitcoinGUIV7Optimized._advanced_feature_engineering  s�  � �"	��w�w�y�y�H� �h�.�.�.�'�5�5�r�y�k�5�J�J�R���|�$�$�q�(�(�(0��a��(A�H�W�%�%��O�$a�b�b�b�#�O� #+�7�"3�">�">�"@�"@�H�Y��&(�f�X�g�->��'�AR�AX�AX�YZ�A[�A[�-[�&\�&\�H�]�#�%-�i�%8�%@�%@��%D�%D�%H�%H�%J�%J�H�\�"� �5�5�h�?�?�H� �2�2�8�<�<�H� �8�8��B�B�H� �1�1�(�;�;�H��O��� 	� 	� 	��M�@�Q�@�@�A�A�A��I�I�I�I�I�I�����	���s%   �A=E � CE �
F	�&F�>F	�F	c                 �  � 	 |d         �                     �   �         }|�                    |dk    d�  �        �                    d��  �        �                    �   �         }|�                    |dk     d�  �         �                    d��  �        �                    �   �         }||z  }ddd|z   z  z
  |d<   |d         �                    d�	�  �        �                    �   �         }|d         �                    d
�	�  �        �                    �   �         }||z
  |d<   |d         �                    d�	�  �        �                    �   �         |d
<   |d         |d
         z
  |d<   |d         �                    d�  �        �                    �   �         }|d         �                    d�  �        �                    �   �         }	||	dz  z   |d<   ||	dz  z
  |d<   |d         |d         z
  |d         |d         z
  z  |d<   |d         �                    d�  �        �                    �   �         }
|d         �                    d�  �        �                    �   �         }d|d         |
z
  z  ||
z
  z  |d<   |d         �                    d�  �        �                    �   �         |d<   d||d         z
  z  ||
z
  z  |d<   |S # t          $ r#}t          j
        d|� ��  �         |cY d}~S d}~ww xY w)u-   Füge optimierte technische Indikatoren hinzur�   r   �   )�windowr"   r�   �rsi�   )�span�   �macd�	   �macd_signal�macd_histogramr%   �   �bb_upper�bb_lower�bb_position�low�high�stoch_k�   �stoch_di�����
williams_rz$Fehler bei technischen Indikatoren: N)�diff�wherer�   �mean�ewmr�   �min�maxr]   rF   r^   )
rI   r�   �delta�gain�loss�rs�ema_12�ema_26�sma_20�std_20�low_14�high_14r`   s
                rJ   r�   z7UltimateBitcoinGUIV7Optimized._add_technical_indicators*  s�  � �#	��w�K�$�$�&�&�E��K�K���	�1�-�-�6�6�b�6�A�A�F�F�H�H�D��[�[����A�.�.�.�7�7�r�7�B�B�G�G�I�I�D����B��s�a�"�f�~�.�B�u�I� ��[�_�_�"�_�-�-�2�2�4�4�F���[�_�_�"�_�-�-�2�2�4�4�F��&��B�v�J� "�6�
���A�� 6� 6� ;� ;� =� =�B�}��#%�f�:��=�0A�#A�B�� � ��[�(�(��,�,�1�1�3�3�F���[�(�(��,�,�0�0�2�2�F�#�v��z�2�B�z�N�#�v��z�2�B�z�N�!#�G��r�*�~�!=�"�Z�.�SU�V`�Sa�Ba� b�B�}�� ��Y�&�&�r�*�*�.�.�0�0�F���j�(�(��,�,�0�0�2�2�G��2�g�;��#7�8�G�f�<L�M�B�y�M��y�M�1�1�!�4�4�9�9�;�;�B�y�M�  $�w��G��'<�=��6�AQ�R�B�|���I��� 	� 	� 	��M�D��D�D�E�E�E��I�I�I�I�I�I�����	���s   �J&J) �)
K�3K�K�Kc                 �`  � 	 |j         j        |d<   |j         j        |d<   |j         j        |d<   |j         j        |d<   t          j        dt
          j        z  |d         z  dz  �  �        |d<   t          j        dt
          j        z  |d         z  dz  �  �        |d<   t          j        dt
          j        z  |d         z  d	z  �  �        |d
<   t          j        dt
          j        z  |d         z  d	z  �  �        |d<   |S # t          $ r#}t          j        d|� ��  �         |cY d
}~S d
}~ww xY w)u!   Füge zeitbasierte Features hinzu�hour�day_of_week�month�quarterr�   �   �hour_sin�hour_cos�   �dow_sin�dow_cosz#Fehler bei zeitbasierten Features: N)�indexr�   �	dayofweekr�   r�   r�   �sin�pi�cosr]   rF   r^   �rI   r�   r`   s      rJ   r�   z4UltimateBitcoinGUIV7Optimized._add_temporal_featuresQ  s*  � �	����B�v�J� "�� 2�B�}���(�.�B�w�K��H�,�B�y�M�  �V�A���I��6�
�$:�R�$?�@�@�B�z�N��V�A���I��6�
�$:�R�$?�@�@�B�z�N��F�1�r�u�9�r�-�/@�#@�1�#D�E�E�B�y�M��F�1�r�u�9�r�-�/@�#@�1�#D�E�E�B�y�M��I��� 	� 	� 	��M�C��C�C�D�D�D��I�I�I�I�I�I�����	���s   �C=D  � 
D-�
D(�"D-�(D-c                 �  � 	 |d         |d         z
  |d         z  |d<   |d         |d         �                     �   �         z  �                    �   �         |d<   |d         t          j        |d         �                    �   �         �  �        z  �                    �   �         |d<   |d         |d         z  �                    �   �         |d         �                    �   �         z  |d<   |d         |d         z  |d	<   |d         �                    d
�  �        �                    |d         �  �        |d<   |S # t          $ r#}t          j	        d|� ��  �         |cY d
}~S d
}~ww xY w)u(   Füge Markt-Mikrostruktur Features hinzur�   r�   r�   �spread_proxy�volume�vpt�obv�vwap�
vwap_ratior%   �pv_corrz#Fehler bei Mikrostruktur-Features: N)
r�   �cumsumr�   �signr�   r�   �corrr]   rF   r^   r�   s      rJ   r�   z:UltimateBitcoinGUIV7Optimized._add_microstructure_featurese  s\  � �	�"$�V�*�r�%�y�"8�B�w�K�!G�B�~�� �H���7��(>�(>�(@�(@�@�H�H�J�J�B�u�I� �H�����7��0@�0@�0B�0B�(C�(C�C�K�K�M�M�B�u�I� �X�,��G��4�<�<�>�>��H��AT�AT�AV�AV�V�B�v�J�!�'�{�R��Z�7�B�|�� �w�K�/�/��3�3�8�8��H��F�F�B�y�M��I��� 	� 	� 	��M�C��C�C�D�D�D��I�I�I�I�I�I�����	���s   �D,D/ �/
E�9E�E�Ec                 �N  � 	 |�                     d��  �        �                     d��  �        �                     d�  �        }|�                    t          j        g��  �        j        }||         �                    �   �         }||dk             j        }t          |�  �        | j        d         k    rA|�	                    d�	�  �        �
                    | j        d         �  �        j        }||         S ||         S # t          $ r#}t          j
        d
|� ��  �         |cY d}~S d}~ww xY w)u   Wähle die besten Features aus�ffill��method�bfillr   r�   g�����ư>r-   F)�	ascendingzFehler bei Feature-Selektion: N)�fillnar�   r�   r�   r�   �varr�   r�   rC   �sort_values�headr]   rF   r^   )rI   r�   �df_cleanr�   �	variances�
good_variance�top_featuresr`   s           rJ   r�   z3UltimateBitcoinGUIV7Optimized._select_best_features~  s+  � �	��y�y��y�0�0�7�7�w�7�G�G�N�N�q�Q�Q�H� $�1�1�2�9�+�1�F�F�N�L� !��.�2�2�4�4�I�%�i�$�&6�7�=�M� �=�!�!�D�K�0A�$B�B�B�(�4�4�u�4�E�E�J�J�4�;�Wh�Ki�j�j�p����-�-��M�*�*��� 	� 	� 	��M�>�1�>�>�?�?�?��I�I�I�I�I�I�����	���s$   �C,C7 �/C7 �7
D$�D�D$�D$c                 �   � 	 ddddd�}t          j        d�  �         |S # t          $ r#}t          j        d|� ��  �         i cY d}~S d}~ww xY w)	zOptimiere Hyperparameterr"   �
   g�������?�*   )�n_estimators�	max_depth�
learning_rate�random_statezHyperparameter optimiertz'Fehler bei Hyperparameter-Optimierung: N)rF   rG   r]   r^   )rI   r�   r�   r`   s       rJ   r|   z7UltimateBitcoinGUIV7Optimized._optimize_hyperparameters�  s�   � �	� !$��!$� "�	� �K� 
�L�3�4�4�4����� 	� 	� 	��M�G�A�G�G�H�H�H��I�I�I�I�I�I�����	���s   � �
A�A�A�Ac                 ��   � 	 | j         sdS | j         �                    |i �  �        }|rt          j        d�  �         dS dS # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)zTrainiere optimierte ModelleFz(Optimierte Modelle erfolgreich trainiertTz*Fehler beim Training optimierter Modelle: N)r3   �train_precise_ml_model_v6rF   rG   r]   r^   )rI   r�   �paramsr�   r`   s        rJ   r}   z5UltimateBitcoinGUIV7Optimized._train_optimized_models�  s�   � �	��&� 
��u� �)�C�C�H�b�Q�Q�G�� 
���G�H�H�H��t��5��� 	� 	� 	��M�J�q�J�J�K�K�K��5�5�5�5�5�����	���s   �A  �1A  � 
A,�
A'�'A,c                 �f   � 	 dS # t           $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)zBewerte Modell-Performanceg333333�?zFehler bei Modell-Bewertung: Nr   )r]   rF   r^   r_   s     rJ   r~   z9UltimateBitcoinGUIV7Optimized._evaluate_model_performance�  sQ   � �	��4��� 	� 	� 	��M�=�!�=�=�>�>�>��3�3�3�3�3�����	���s   �
0�+�0c                 ��  � 	 |j         rdS d|�                    �   �         �                    �   �         �                    �   �         t          |�  �        t          |j        �  �        z  z  z
  }|�                    t          j        g��  �        j        }t          |�  �        dk    rj||         �                    �   �         �	                    �   �         ||         �	                    �   �         �	                    �   �         z  }t          d|�  �        }nd}||z   dz  }t          dt          d|�  �        �  �        S # t          $ r"}t          j        d|� ��  �         Y d}~d	S d}~ww xY w)
u   Bewerte Datenqualitätr   r�   r�   r   g      �?r�   u%   Fehler bei Datenqualitätsbewertung: N�      �?)rz   �isnull�sumr�   r�   r�   r�   r�   r�   r�   r�   r�   r]   rF   r^   )rI   r�   �completenessr�   �variability�qualityr`   s          rJ   r�   z2UltimateBitcoinGUIV7Optimized._assess_data_quality�  sT  � �	��x� 
��s� ��	�	����� 1� 1� 5� 5� 7� 7�3�r�7�7�S���_�_�;T� U�V�L� �+�+�R�Y�K�+�@�@�H�L��<� � �1�$�$� ��.�2�2�4�4�9�9�;�;�b��>N�>S�>S�>U�>U�>Z�>Z�>\�>\�\��!�#�{�3�3���!�� $�k�1�Q�6�G��s�C��W�-�-�.�.�.��� 	� 	� 	��M�E�!�E�E�F�F�F��3�3�3�3�3�����	���s   �D6 �D*D6 �6
E"� E�E"c                 �  � 	 |�                     �   �         }|�                    t          j        g��  �        j        }|D ]l}||         �                    d�  �        }||         �                    d�  �        }||z
  }|d|z  z
  }|d|z  z   }	|||         |k    ||         |	k    z           }�m|�                    d��  �        �                    d��  �        �                    d�  �        }t          j        d	t          |�  �        � d
t          |�  �        � d��  �         |S # t          $ r#}
t          j        d|
� ��  �         |cY d
}
~
S d
}
~
ww xY w)zBereinige Trainingsdatenr�   g      �?g      �?g      �?r�   r�   r�   r   zDaten bereinigt: z -> z ZeilenzFehler bei Datenbereinigung: N)�drop_duplicatesr�   r�   r�   r�   �quantiler�   rF   rG   r�   r]   r^   )rI   r�   r�   r�   �col�Q1�Q3�IQR�lower_bound�upper_boundr`   s              rJ   r�   z2UltimateBitcoinGUIV7Optimized._clean_training_data�  sn  � �	��)�)�+�+�H� $�1�1�2�9�+�1�F�F�N�L�#� 
e� 
e���c�]�+�+�D�1�1���c�]�+�+�D�1�1���2�g�� �3��9�n�� �3��9�n��#�X�c�]�k�%A�h�s�m�Wb�Fb�$c�d���  ���g��6�6�=�=�W�=�M�M�T�T�UV�W�W�H��L�P�S��W�W�P�P�#�h�-�-�P�P�P�Q�Q�Q��O��� 	� 	� 	��M�=�!�=�=�>�>�>��I�I�I�I�I�I�����	���s   �DD �
E�)E�E�Ec                 �t  � 	 t          j        �   �         | _        | j        �                    d| j        � ��  �         | j        �                    d�  �         | j        �                    d��  �         | �                    �   �          | �                    �   �          | �	                    �   �          | �
                    �   �          | j        �                    d| j        �  �         | �
                    �   �          t          j        d�  �         dS # t           $ r"}t          j        d|� ��  �         Y d	}~d
S d	}~ww xY w)z/Erstelle optimierte und benutzerfreundliche GUI�Ultimate Bitcoin Trading GUI �1400x900�#1a1a1a��bg�WM_DELETE_WINDOWzOptimierte GUI erstelltTzFehler bei GUI-Erstellung: NF)�tk�Tkr4   �titler1   �geometry�	configure�_create_modern_header�_create_control_dashboard�_create_advanced_charts�_create_simple_status�protocolrb   �_start_time_updaterF   rG   r]   r^   r_   s     rJ   �create_optimized_guiz2UltimateBitcoinGUIV7Optimized.create_optimized_gui�  s8  � �	�����D�I��I�O�O�J�D�L�J�J�K�K�K��I���z�*�*�*��I���9��-�-�-� 
�&�&�(�(�(��*�*�,�,�,��(�(�*�*�*��&�&�(�(�(� 
�I���1�4�=�A�A�A� 
�#�#�%�%�%��L�2�3�3�3��4��� 	� 	� 	��M�;��;�;�<�<�<��5�5�5�5�5�����	���s   �DD �
D7�D2�2D7c                 �  � 	 t          j        | j        dd��  �        }|�                    ddd��  �         |�                    d�  �         t          j        |dd	d
d��  �        }|�                    dd
d
��  �         t          j        |d��  �        }|�                    dd
d��  �         t          j        |dddd��  �        | j        d<   | j        d         �                    �   �          t          j        |dddd��  �        | j        d<   | j        d         �                    �   �          dS # t          $ r"}t          j	        d|� ��  �         Y d}~dS d}~ww xY w)zErstelle modernen Header�#2d2d2d�P   �r  �height�x�   ��fill�padx�padyFzULTIMATE BITCOIN TRADING)�Arial�   �bold�#00ff88��text�font�fgr  �leftr%   ��sider)  r*  r  �rightr�   z$0.00)r+  �   r-  �#ffffff�price_labelr   )r+  r�   r-  �#ffaa00�signal_labelzFehler bei Header-Erstellung: N)
r  �Framer4   �pack�pack_propagate�Labelr@   r]   rF   r^   )rI   �header�title_label�
live_framer`   s        rJ   r  z3UltimateBitcoinGUIV7Optimized._create_modern_header  s�  � �(	@��X�d�i�I�b�A�A�A�F��K�K�S�q�q�K�1�1�1��!�!�%�(�(�(� �(��/�*���� � �K� 
���&�r���;�;�;� ��&�Y�7�7�7�J��O�O��r��O�;�;�;� +-�(���*���+� +� +�D�L��'� 
�L��'�,�,�.�.�.� ,.�8���*���,� ,� ,�D�L��(� 
�L��(�-�-�/�/�/�/�/��� 	@� 	@� 	@��M�>�1�>�>�?�?�?�?�?�?�?�?�?�����	@���s   �D(D, �,
E�6E�Ec           
      �  � 	 t          j        | j        dd��  �        }|�                    ddd��  �         |�                    d�  �         t          j        |d��  �        }|�                    d	d
dd�
�  �         d| j        dddfd| j        dddfd| j        dddfd| j        dddfd| j	        dddfd| j
        dddfd| j        dddfd| j        dddfg}i | j
        d <   |D ]P\  }}}}}t          j        ||||d!d"ddd#d�$�
  �
        }	|	�                    ||ddd%�&�  �         |	| j
        d          |<   �Qt!          d'�  �        D ]}
|�                    |
d�(�  �         �d*S # t$          $ r"}t'          j        d)|� ��  �         Y d*}~d*S d*}~ww xY w)+z&Erstelle optimiertes Control Dashboardr!  �x   r#  r%  r&  r'  Fr  T�bothr%   r�   )�expandr(  r)  r*  �ANALYSE STARTENz#0088ffr   �ANALYSE STOPPEN�#ff4444r�   �TRAINING STARTENz#8800ffr�   �TRAINING STOPPENr�   �KONTINUIERLICH ANz#00aa44�KONTINUIERLICH AUSzMODELL OPTIMIEREN�#ff8800zDATEN EXPORTIERENz#aa44aa�buttons�white)r+  r�   r-  �raised)	r0  �commandr  r2  r1  r)  r*  �relief�bd�ew)�row�columnr)  r*  �sticky�   )�weightzFehler bei Control Dashboard: N)r  r<  r4   r=  r>  �_start_analysis�_stop_analysis�_start_training�_stop_training�_start_continuous�_stop_continuousr�   �_export_datar@   �Button�grid�range�grid_columnconfigurer]   rF   r^   )rI   �
control_frame�button_gridrO  r0  rR  �colorrV  r  �btn�ir`   s               rJ   r  z7UltimateBitcoinGUIV7Optimized._create_control_dashboardD  s.  � �+	@��H�T�Y�9�S�I�I�I�M����C�a�a��8�8�8��(�(��/�/�/� �(�=�Y�?�?�?�K����D�v�B�R��H�H�H� #�D�$8�)�Q��J�"�D�$7��A�q�I�#�T�%9�9�a��K�#�T�%8�)�Q��J�$�d�&<�i��A�N�%�t�'<�i��A�N�$�d�&=�y�!�Q�O�$�d�&7��A�q�I�	�G� ')�D�L��#�29� 
4� 
4�.��g�u�c�3��i���#���.���#��� � �� ���S��1�1�T��J�J�J�03���Y�'��-�-� �1�X�X� 
>� 
>���0�0��1�0�=�=�=�=�
>� 
>�� � 	@� 	@� 	@��M�>�1�>�>�?�?�?�?�?�?�?�?�?�����	@���s   �EE �
F�&F�Fc                 �`  � 	 t          j        | j        d��  �        }|�                    dddd��  �         t	          j        |�  �        | _        | j        �                    dd��  �         | �                    �   �          d	S # t          $ r"}t          j
        d|� ��  �         Y d	}~d	S d	}~ww xY w)
z(Erstelle erweiterte Chart-Visualisierungr  r  rE  Tr&  )r(  rF  r)  r*  �r(  rF  zFehler bei Chart-Erstellung: N)r  r<  r4   r=  r   �Notebookr5   �_create_price_chart_tabr]   rF   r^   )rI   �chart_framer`   s      rJ   r  z5UltimateBitcoinGUIV7Optimized._create_advanced_chartss  s�   � �
	?��(�4�9��;�;�;�K����&��A�A��F�F�F�  �L��5�5�D�M��M���F�4��8�8�8� 
�(�(�*�*�*�*�*�� � 	?� 	?� 	?��M�=�!�=�=�>�>�>�>�>�>�>�>�>�����	?���s   �A=B �
B-�B(�(B-c           	      �j  � 	 t          j        | j        �  �        }| j        �                    |d��  �         t	          j        dddddddgi�	�  �        \  }\  }}||fD ]H}|�                    d�  �         |�                    d
d��  �         |�                    d
dddd��  �         �It          ||�  �        }|�
                    �   �         �                    dd
��  �         |||g|d�| j        d<   | �
                    �   �          dS # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)zErstelle Preis-Chart TabzPREIS & PROGNOSE�r0  r�   r�   )r�   �   r  �
height_ratiosr�   )�figsize�	facecolor�gridspec_kwrP  r�   ��colors�	labelsizeT�333333�?�-r�   �#444444��alpha�	linestyle�	linewidthrh  rE  rl  )�fig�axes�canvas�pricezFehler bei Preis-Chart: N)r   r<  r5   �add�plt�subplots�
set_facecolor�tick_paramsrc  r   �
get_tk_widgetr=  rA   �_update_price_chartr]   rF   r^   )rI   �tabr�  �ax1�ax2�axr�  r`   s           rJ   rn  z5UltimateBitcoinGUIV7Optimized._create_price_chart_tab�  s�  � �	:��)�D�M�*�*�C��M���c�(:��;�;�;� "�l�1�a��3<�6E��1�v�5N�P� P� P�O�C��#�s�
 �C�j� 
X� 
X��� � ��+�+�+����g���;�;�;�����C�3�#�Y��W�W�W�W� '�s�C�0�0�F�� � �"�"�'�'�V�D�'�A�A�A� ,/��c�
�f�#U�#U�D�K�� � 
�$�$�&�&�&�&�&��� 	:� 	:� 	:��M�8�Q�8�8�9�9�9�9�9�9�9�9�9�����	:���s   �DD �
D2�D-�-D2c                 �P  � 	 t          j        | j        dd��  �        }|�                    ddd��  �         |�                    d�  �         t          j        |dd	d
d��  �        | j        d<   | j        d         �                    d
dd��  �         t          j        |t          j        �   �         �	                    d�  �        d	dd��  �        | j        d<   | j        d         �                    ddd��  �         dS # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)z Erstelle einfache Status-Anzeiger!  �(   r#  r%  r&  r'  Fz
System bereit)r+  r�   r.  r/  �status_labelr3  r%   r�   r4  �%H:%M:%Sr8  �
time_labelr6  zFehler bei Status-Erstellung: N)
r  r<  r4   r=  r>  r?  r@   r   rP   rQ   r]   rF   r^   )rI   �status_framer`   s      rJ   r  z3UltimateBitcoinGUIV7Optimized._create_simple_status�  s[  � �	@��8�D�I�)�B�G�G�G�L����3�Q�Q��7�7�7��'�'��.�.�.� ,.�8��$�"���,� ,� ,�D�L��(� 
�L��(�-�-�6���-�L�L�L� *,����\�^�^�,�,�Z�8�8�"���*� *� *�D�L��&� 
�L��&�+�+��r��+�K�K�K�K�K��� 	@� 	@� 	@��M�>�1�>�>�?�?�?�?�?�?�?�?�?�����	@���s   �C5C9 �9
D%�D � D%c                 �z  � 	 | j         �                    d�  �        rn| j        si| j         d         �                    t	          j        �   �         �                    d�  �        ��  �         | j        �                    d| j	        �  �         dS dS dS # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)zStarte Zeit-Updater�  r�  rq  r(   zFehler bei Zeit-Update: N)
r@   ri   r6   rC   r   rP   rQ   r4   �afterr  r]   rF   r^   r_   s     rJ   r  z0UltimateBitcoinGUIV7Optimized._start_time_update�  s�   � �	:��|����-�-� 
?�d�6M� 
?���\�*�1�1�x�|�~�~�7N�7N�z�7Z�7Z�1�[�[�[��	����d�&=�>�>�>�>�>�
?� 
?� 
?� 
?�� � 	:� 	:� 	:��M�8�Q�8�8�9�9�9�9�9�9�9�9�9�����	:���s   �BB �
B:�B5�5B:c                 �  � � 	 � j         d         r� �                    d�  �         dS d� j         d<   � �                    �   �          � fd�}t          j        |d��  �        � j        d<   � j        d         �                    �   �          � �                    d�  �         dS # t          $ r"}t          j	        d	|� ��  �         Y d}~dS d}~ww xY w)
zStarte optimierte Analyser   zAnalyse bereits aktivNTc                  �  �� 	 �j         d         r��j        s���                    �   �         } | r)��                    | �  �         ��                    �   �          t          �j        d         �  �        D ],}�j         d         r�j        r nt          j        d�  �         �-�j         d         r�j        ��n.# t          $ r!}t          j        d|� ��  �         Y d }~nd }~ww xY wd�j         d<   �j        r"�j        �
                    d�j        �  �         d S d S # d�j         d<   �j        r!�j        �
                    d�j        �  �         w w xY w)Nr   r*   r�   zFehler im Analyse-Thread: Fr   )r8   r6   �_perform_optimized_analysis�_update_data_from_analysis�_update_gui_displaysrd  rC   �time�sleepr]   rF   r^   r4   r�  �_update_button_states)�result�_r`   rI   s      �rJ   �analysis_workerzFUltimateBitcoinGUIV7Optimized._start_analysis.<locals>.analysis_worker�  s�  �� �G��,�-?�@� *��I`� *�!%�!A�!A�!C�!C��!� 8� �;�;�F�C�C�C� �5�5�7�7�7� "'�t�{�3D�'E�!F�!F� *� *�A�#'�#5�6H�#I� &�T�Md� &� %�� �J�q�M�M�M�M� �,�-?�@� *��I`� *��� !� D� D� D��M�"B�q�"B�"B�C�C�C�C�C�C�C�C�����D���� >C�D�&�'9�:��y� G��	����4�+E�F�F�F�F�F�G� G�� >C�D�&�'9�:��y� G��	����4�+E�F�F�F�F�G���s0   �B.B2 �1D �2
C�<C�D �C�D �4E	��target�daemonr   zOptimierte Analyse gestartetzFehler beim Start der Analyse: �
r8   �_log_messager�  r9   �Threadr7   �startr]   rF   r^   )rI   r�  r`   s   `  rJ   r[  z-UltimateBitcoinGUIV7Optimized._start_analysis�  s  �� �%	A��!�"4�5� 
��!�!�"9�:�:�:���59�D��1�2��&�&�(�(�(�
G� 
G� 
G� 
G� 
G�. (1�'7��W[�'\�'\�'\�D�L��$��L��$�*�*�,�,�,����<�=�=�=�=�=��� 	A� 	A� 	A��M�?�A�?�?�@�@�@�@�@�@�@�@�@�����	A����   �"B �A5B �
C
�(C�C
c                 ��   � 	 d| j         d<   | �                    �   �          | �                    d�  �         dS # t          $ r"}t	          j        d|� ��  �         Y d}~dS d}~ww xY w)zStoppe AnalyseFr   zAnalyse gestopptz!Fehler beim Stoppen der Analyse: N�r8   r�  r�  r]   rF   r^   r_   s     rJ   r\  z,UltimateBitcoinGUIV7Optimized._stop_analysis�  s�   � �	C�5:�D��1�2��&�&�(�(�(����0�1�1�1�1�1��� 	C� 	C� 	C��M�A�a�A�A�B�B�B�B�B�B�B�B�B�����	C����   �37 �
A#�A�A#c                 �  � � 	 � j         d         r� �                    d�  �         dS d� j         d<   � �                    �   �          � fd�}t          j        |d��  �        � j        d<   � j        d         �                    �   �          � �                    d�  �         dS # t          $ r"}t          j	        d	|� ��  �         Y d}~dS d}~ww xY w)
zStarte optimiertes Trainingr   zTraining bereits aktivNTc                  �  �� 	 t          j         �   �         } ��                    �   �         }t          j         �   �         | z
  }�j        �                    t	          j        �   �         ||�j        d         d��  �         |r)��                    d|d�d�j        d         d���  �         n��                    d�  �         n.# t          $ r!}t          j
        d|� ��  �         Y d }~nd }~ww xY wd	�j        d
<   �j        r"�j        �
                    d�j        �  �         d S d S # d	�j        d
<   �j        r!�j        �
                    d�j        �  �         w w xY w)Nr!   )�	timestamp�durationr�   �accuracyzTraining erfolgreich in z.1fzs - Genauigkeit: rx   zTraining fehlgeschlagenzFehler im Training-Thread: Fr   r   )r�  r�   r?   �appendr   rP   r<   r�  r]   rF   r^   r8   r4   r�  r�  )�training_startr�   �
training_timer`   rI   s       �rJ   �training_workerzFUltimateBitcoinGUIV7Optimized._start_training.<locals>.training_worker	  s�  �� �G�%)�Y�[�[�N�"�5�5�7�7�G�$(�I�K�K�.�$@�M� �)�0�0�%-�\�^�^�$1�#*�$(�I�.>�$?�	2� 2� � � � � E��)�)�  +K�]�  +K�  +K�  +K�im�ir�  tD�  jE�  +K�  +K�  +K�  L�  L�  L�  L��)�)�*C�D�D�D��� � E� E� E��M�"C��"C�"C�D�D�D�D�D�D�D�D�����E���� >C�D�&�'9�:��y� G��	����4�+E�F�F�F�F�F�G� G�� >C�D�&�'9�:��y� G��	����4�+E�F�F�F�F�G���s0   �B8B< �;D �<
C'�C"�D �"C'�'D �4Er�  r   zOptimiertes Training gestartetz!Fehler beim Start des Trainings: r�  )rI   r�  r`   s   `  rJ   r]  z-UltimateBitcoinGUIV7Optimized._start_training�  s  �� �(	C��!�"4�5� 
��!�!�":�;�;�;���59�D��1�2��&�&�(�(�(�
G� 
G� 
G� 
G� 
G�4 (1�'7��W[�'\�'\�'\�D�L��$��L��$�*�*�,�,�,����>�?�?�?�?�?��� 	C� 	C� 	C��M�A�a�A�A�B�B�B�B�B�B�B�B�B�����	C���r�  c                 ��   � 	 d| j         d<   | �                    �   �          | �                    d�  �         dS # t          $ r"}t	          j        d|� ��  �         Y d}~dS d}~ww xY w)zStoppe TrainingFr   zTraining gestopptz#Fehler beim Stoppen des Trainings: Nr�  r_   s     rJ   r^  z,UltimateBitcoinGUIV7Optimized._stop_training+  s�   � �	E�5:�D��1�2��&�&�(�(�(����1�2�2�2�2�2��� 	E� 	E� 	E��M�C��C�C�D�D�D�D�D�D�D�D�D�����	E���r�  c                 �  � � 	 � j         d         r� �                    d�  �         dS d� j         d<   � �                    �   �          � fd�}t          j        |d��  �        � j        d<   � j        d         �                    �   �          � �                    d�  �         dS # t          $ r"}t          j	        d	|� ��  �         Y d}~dS d}~ww xY w)
u#   Starte kontinuierliche Überwachungr   u*   Kontinuierliche Überwachung bereits aktivNTc                  �H  �� 	 �j         d         r��j        s�j         d         s��                    �   �          �j         d         stt          �j        �  �        dk    sHt          j        �   �         �j        d         d         z
  �                    �   �         �j        d         k    r��	                    �   �          t          d�  �        D ],} �j         d         r�j        r nt          j        d	�  �         �-�j         d         r�j        ��n.# t          $ r!}t          j        d
|� ��  �         Y d }~nd }~ww xY wd�j         d<   �j        r"�j        �                    d�j        �  �         d S d S # d�j         d<   �j        r!�j        �                    d�j        �  �         w w xY w)Nr   r   r   r   �����r�  r+   �   r�   zFehler im Monitoring-Thread: F)r8   r6   r[  r�   r?   r   rP   �
total_secondsrC   r]  rd  r�  r�  r]   rF   r^   r4   r�  r�  )r�  r`   rI   s     �rJ   �monitoring_workerzJUltimateBitcoinGUIV7Optimized._start_continuous.<locals>.monitoring_worker>  s  �� �G��,�-A�B� *�4�Kb� *�#�1�2D�E� 3� �0�0�2�2�2�  $�1�2D�E� 7�"�4�#8�9�9�Q�>�>� (�����1F�r�1J�;�1W� W�f�f�h�h�ko�kv�  xK�  lL�   L�   L� $� 4� 4� 6� 6� 6� "'�r��� *� *�A�#'�#5�6J�#K� &�t�Of� &� %�� �J�q�M�M�M�M�! �,�-A�B� *�4�Kb� *���$ !� G� G� G��M�"E�!�"E�"E�F�F�F�F�F�F�F�F�����G���� @E�D�&�';�<��y� G��	����4�+E�F�F�F�F�F�G� G�� @E�D�&�';�<��y� G��	����4�+E�F�F�F�F�G���s0   �DD
 �	E- �

D5�D0�+E- �0D5�5E- �-4F!r�  r   u&   Kontinuierliche Überwachung gestartetu5   Fehler beim Start der kontinuierlichen Überwachung: r�  )rI   r�  r`   s   `  rJ   r_  z/UltimateBitcoinGUIV7Optimized._start_continuous4  s  �� �)	W��!�"6�7� 
��!�!�"N�O�O�O���7;�D��3�4��&�&�(�(�(�
G� 
G� 
G� 
G� 
G�6 *3�)9�AR�[_�)`�)`�)`�D�L��&��L��&�,�,�.�.�.����F�G�G�G�G�G��� 	W� 	W� 	W��M�U�RS�U�U�V�V�V�V�V�V�V�V�V�����	W���r�  c                 �  � 	 d| j         d<   | �                    �   �          | �                    �   �          | �                    �   �          | �                    d�  �         dS # t
          $ r"}t
          j        d|� ��  �         Y d}~dS d}~ww xY w)u#   Stoppe kontinuierliche ÜberwachungFr   u%   Kontinuierliche Überwachung gestopptu7   Fehler beim Stoppen der kontinuierlichen Überwachung: N)r8   r\  r^  r�  r�  r]   rF   r^   r_   s     rJ   r`  z.UltimateBitcoinGUIV7Optimized._stop_continuousa  s�   � �	Y�7<�D��3�4����!�!�!����!�!�!��&�&�(�(�(����E�F�F�F�F�F��� 	Y� 	Y� 	Y��M�W�TU�W�W�X�X�X�X�X�X�X�X�X�����	Y���s   �AA �
B�)B�Bc                 �&  � 	 | j         sdS | j         �                    �   �         }|r<t          j        �   �         |d<   | j        |d<   | j        �                    |�  �         |S dS # t          $ r"}t          j	        d|� ��  �         Y d}~dS d}~ww xY w)u   Führe optimierte Analyse durchNr�  �analysis_versionz Fehler bei optimierter Analyse: )
r3   �run_prediction_scan_v6r   rP   r1   r>   r�  r]   rF   r^   )rI   r�  r`   s      rJ   r�  z9UltimateBitcoinGUIV7Optimized._perform_optimized_analysisl  s�   � �	��&� 
��t� �(�?�?�A�A�F�� 
�&.�l�n�n��{�#�-1�\��)�*� �!�(�(��0�0�0��
��4��� 	� 	� 	��M�@�Q�@�@�A�A�A��4�4�4�4�4�����	���s   �A$ �AA$ �$
B�.B�Bc                 ��  � 	 |�                     dd�  �        | j        d<   |�                     di �  �        }|�                     dd�  �        | j        d<   |�                     dd�  �        | j        d<   |�                     di �  �        }|�                     di �  �        }|�                     d	d�  �        | j        d
<   |�                     dd�  �        | j        d<   | j        �                    t	          j        �   �         | j        d         | j        d         | j        d         | j        d
         d��  �         dS # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)z'Aktualisiere Daten aus Analyse-Ergebnisr   r   �
predictionr   r   r   �hourly_forecast�
target_24hr�  r   �live_data_qualityr    r!   )r�  r�  r   r   r�  zFehler bei Daten-Update: N)	ri   r<   r=   r�  r   rP   r]   rF   r^   )rI   r�  r�  �forecastr�  r`   s         rJ   r�  z8UltimateBitcoinGUIV7Optimized._update_data_from_analysis�  st  � �	;�)/���O�Q�)G�)G�D�I�o�&����L�"�5�5�J�",�.�.��6�"B�"B�D�I�h��&0�n�n�\�1�&E�&E�D�I�l�#��z�z�"3�R�8�8�H�!���l�B�7�7�J�*4�.�.��!�*D�*D�D�I�&�'�(.�
�
�3F��(J�(J�D�I�n�%� 
�$�+�+�%�\�^�^���?�3��)�H�-�"�i��5� �I�&6�7�-� -� 
� 
� 
� 
� 
�� � 	;� 	;� 	;��M�9�a�9�9�:�:�:�:�:�:�:�:�:�����	;���s   �D5D9 �9
E%�E � E%c                 ��  � 	 | j         �                    d�  �        sdS | j         d         }d|v r+|d         �                    | j        d         rdnd��  �         d|v r+|d         �                    | j        d         rdnd��  �         d	|v r+|d	         �                    | j        d
         rdnd��  �         d|v r+|d         �                    | j        d
         rdnd��  �         d|v r+|d         �                    | j        d
         rdnd��  �         d|v r-|d         �                    | j        d
         rdnd��  �         dS dS # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)u   Aktualisiere Button-ZuständerO  NrG  r   �disabled�normal)�staterH  rJ  r   rK  rL  r   rM  zFehler bei Button-Update: )r@   ri   rC   r8   r]   rF   r^   )rI   rO  r`   s      rJ   r�  z3UltimateBitcoinGUIV7Optimized._update_button_states�  s2  � �%	<��<�#�#�I�.�.� 
����l�9�-�G� !�G�+�+��)�*�1�1�(,�(:�;M�(N�\�*�*�T\� 2� � � � !�G�+�+��)�*�1�1�&*�&8�9K�&L�\�(�(�R\� 2� � � �
 "�W�,�,��*�+�2�2�(,�(:�;M�(N�\�*�*�T\� 3� � � � "�W�,�,��*�+�2�2�&*�&8�9K�&L�\�(�(�R\� 3� � � �
 #�g�-�-��+�,�3�3�(,�(:�;O�(P�^�*�*�V^� 4� � � � $�w�.�.��,�-�4�4�&*�&8�9M�&N�^�(�(�T^� 5� � � � � � /�.��
 � 	<� 	<� 	<��M�:�q�:�:�;�;�;�;�;�;�;�;�;�����	<���s   �E	 �D'E	 �	
E5�E0�0E5c                 �   � 	 | j         r"| j         �                    d| j        �  �         dS dS # t          $ r"}t	          j        d|� ��  �         Y d}~dS d}~ww xY w)zAktualisiere GUI-Anzeigenr   zFehler bei GUI-Display-Update: N)r4   r�  �_update_displays_main_threadr]   rF   r^   r_   s     rJ   r�  z2UltimateBitcoinGUIV7Optimized._update_gui_displays�  s�   � �	A��y� 
F��	����4�#D�E�E�E�E�E�
F� 
F��� 	A� 	A� 	A��M�?�A�?�?�@�@�@�@�@�@�@�@�@�����	A���s   �'- �
A�A�Ac                 ��  � 	 d| j         v r0| j         d         �                    d| j        d         d����  �         d| j         v rfddd	d
�}|�                    | j        d         d�  �        }| j         d         �                    | j        d         � d
| j        d         d�d�|��  �         | �                    �   �          dS # t
          $ r"}t
          j        d|� ��  �         Y d}~dS d}~ww xY w)z$Aktualisiere Displays im Main Threadr9  �$r   z,.2frq  r;  r.  rI  r:  )�BUY�SELLr   r   r8  z (r   rx   �))r0  r2  z*Fehler bei Display-Update im Main Thread: N)r@   rC   r<   ri   r�  r]   rF   r^   )rI   �
signal_colorsrh  r`   s       rJ   r�  z:UltimateBitcoinGUIV7Optimized._update_displays_main_thread�  s<  � �	L����,�,���]�+�2�2�>�T�Y��7�>�>�>� 3� � � � ���-�-�(1�9�i� X� X�
�%�)�)�$�)�H�*=�y�I�I����^�,�3�3� �I�h�/�Q�Q�4�9�\�3J�Q�Q�Q�Q�� 4� � � � 
�$�$�&�&�&�&�&��� 	L� 	L� 	L��M�J�q�J�J�K�K�K�K�K�K�K�K�K�����	L���s   �B<C  � 
C,�
C'�'C,c           
      �  � 	 d| j         vrdS | j         d         }|d         \  }}|�                    �   �          |�                    �   �          ||fD ]H}|�                    d�  �         |�                    dd��  �         |�                    dd	d
dd�
�  �         �It          | j        �  �        dk    �rd� | j        D �   �         }d� | j        D �   �         }d� | j        D �   �         }|�                    ||ddd��  �         |�                    ddd��  �         |�	                    dd��  �         |�
                    �   �          |�                    ||ddd��  �         |�                    ddd��  �         |�	                    dd��  �         |�                    dd��  �         |�
                    �   �          |�                    d d!�  �         nD|�
                    ddd"|j        d#d#dd$�%�  �         |�
                    ddd"|j        d#d#dd�%�  �         |d&         �                    �   �          dS # t           $ r"}t#          j        d'|� ��  �         Y d}~dS d}~ww xY w)(zAktualisiere Preis-Chartr�  Nr�  r  rP  r�   rw  Trz  r{  r�   r|  r}  r�   c                 �   � g | ]
}|d          ��S )r�  � ��.0�ps     rJ   �
<listcomp>zEUltimateBitcoinGUIV7Optimized._update_price_chart.<locals>.<listcomp>  s   � �J�J�J�A��;��J�J�JrL   c                 �   � g | ]
}|d          ��S )r�  r�  r�  s     rJ   r�  zEUltimateBitcoinGUIV7Optimized._update_price_chart.<locals>.<listcomp>  s   � �G�G�G��!�G�*�G�G�GrL   c                 �$   � g | ]
}|d          dz  ��S )r   r"   r�  r�  s     rJ   r�  zEUltimateBitcoinGUIV7Optimized._update_price_chart.<locals>.<listcomp>  s!   � �W�W�W��q����4�W�W�WrL   r.  r�   z
Bitcoin Preis)rh  r�  �labelzBitcoin Preis-Verlaufr�   )rh  �fontsizezPreis (USD))rh  rN  zVorhersage-Konfidenzr�   z
Konfidenz (%)�Zeitr   r"   zSammle Daten...�centerr�   )�	transform�ha�varh  r�  r�  zFehler bei Preis-Chart-Update: )rA   rl   r�  r�  rc  r�   r=   �plot�	set_title�
set_ylabel�legend�
set_xlabel�set_ylimr0  �	transAxes�drawr]   rF   r^   )	rI   �chartr�  r�  r�  �times�prices�confidencesr`   s	            rJ   r�  z1UltimateBitcoinGUIV7Optimized._update_price_chart�  s�  � �/	A��d�k�)�)����K��(�E��V�}�H�C�� 
�I�I�K�K�K��I�I�K�K�K� �C�j� 
X� 
X��� � ��+�+�+����g���;�;�;�����C�3�#�Y��W�W�W�W� �4�+�,�,�q�0�0�J�J��1I�J�J�J��G�G�d�.F�G�G�G��W�W�d�>V�W�W�W�� �����i�1�O��\�\�\��
�
�5�W�r�
�R�R�R����}�G��<�<�<��
�
���� �����9��Qg��h�h�h��
�
�4�G�b�
�Q�Q�Q�����g��>�>�>����v�W��5�5�5��
�
�������Q��$�$�$�$� ����c�#4��
�#���"� � N� N� N�����c�#4��
�#���"� � N� N� N� 
�(�O� � �"�"�"�"�"��� 	A� 	A� 	A��M�?�A�?�?�@�@�@�@�@�@�@�@�@�����	A���s   �	H. �HH. �.
I�8I�Ic                 �   � t          j        �   �         �                    d�  �        }d|� d|� �}t          |�  �         t	          j        |�  �         dS )zLog-Nachricht ausgebenr�  �[z] N)r   rP   rQ   r0   rF   rG   )rI   �messager�  �log_msgs       rJ   r�  z*UltimateBitcoinGUIV7Optimized._log_message  sQ   � ��L�N�N�+�+�J�7�7�	�,�i�,�,�7�,�,��
�g������W�����rL   c                 �  � 	 t          j        �   �         �                    d�  �        }| j        rAt	          j        t
          | j        �  �        �  �        }|�                    d|� d�d��  �         | j        rAt	          j        t
          | j        �  �        �  �        }|�                    d|� d�d��  �         | �	                    d|� d��  �         d	S # t          $ r"}t          j        d|� ��  �         Y d	}~d	S d	}~ww xY w)
zExportiere Datenz
%Y%m%d_%H%M%S�bitcoin_performance_z.csvF)r�   �bitcoin_training_zDaten exportiert: bitcoin_*_zFehler beim Datenexport: N)
r   rP   rQ   r=   �pd�	DataFrame�list�to_csvr?   r�  r]   rF   r^   )rI   r�  �df_perf�df_trainr`   s        rJ   ra  z*UltimateBitcoinGUIV7Optimized._export_data&  s$  � �	;� ����/�/��@�@�I� �'� 
T��,�t�D�,D�'E�'E�F�F�����E�i�E�E�E�U��S�S�S� �$� 
R��<��T�-B�(C�(C�D�D����� C�I� C� C� C�5��Q�Q�Q����L�Y�L�L�L�M�M�M�M�M��� 	;� 	;� 	;��M�9�a�9�9�:�:�:�:�:�:�:�:�:�����	;���s   �CC �
C?�C:�:C?c                 �  � 	 | �                     �   �         r8| �                    d| j        � d��  �         | j        �                    �   �          n| �                    d�  �         n.# t
          $ r!}t
          j        d|� ��  �         Y d}~nd}~ww xY w| �                    �   �          dS # | �                    �   �          w xY w)zStarte die optimierte GUIr  r/   zFehler beim Starten der GUIu    Fehler beim Ausführen der GUI: N)	r  r�  r1   r4   �mainloopr]   rF   r^   rb   r_   s     rJ   �runz!UltimateBitcoinGUIV7Optimized.run:  s�   � �		��(�(�*�*� 
A��!�!�"Z�$�,�"Z�"Z�"Z�[�[�[��	�"�"�$�$�$�$��!�!�"?�@�@�@���� 	B� 	B� 	B��M�@�Q�@�@�A�A�A�A�A�A�A�A�����	B���� 
�M�M�O�O�O�O�O��D�M�M�O�O�O�O���s0   �A!A$ �#B( �$
B�.B
�B( �
B�B( �(B>N).�__name__�
__module__�__qualname__�__doc__r1   rK   rD   rE   r\   rZ   rf   rb   r�   ry   r{   r�   r�   r�   r�   r|   r}   r~   r�   r�   r  r  r  r  rn  r  r  r[  r\  r]  r^  r_  r`  r�  r�  r�  r�  r�  r�  r�  ra  r�  r�  rL   rJ   r   r   5   s�  � � � � � �� � %�G�<P� <P� <P�|
� 
� 
�	?� 	?� 	?�� � �
!� !� !�C� C� C�$� � �4� � �B� � �0$� $� $�L%� %� %�N� � �(� � �2� � �2� � �$� � �&� � �� � �2� � �<� � �6*@� *@� *@�X-@� -@� -@�^?� ?� ?�":� :� :�<@� @� @�<:� :� :�'A� 'A� 'A�RC� C� C�*C� *C� *C�XE� E� E�+W� +W� +W�Z	Y� 	Y� 	Y�� � �2;� ;� ;�>'<� '<� '<�RA� A� A�L� L� L�.1A� 1A� 1A�f� � �;� ;� ;�(� � � � rL   r   c                  �J  � 	 t          d�  �         t          d�  �         t          d�  �         t          �   �          t          d�  �         t          d�  �         t          d�  �         t          d�  �         t          d�  �         t          d�  �         t          �   �          t          �   �         } | �                    �   �          dS # t          $ r t          d	�  �         Y dS t          $ r4}t          d
|� ��  �         t          j        d|� ��  �         Y d}~dS d}~ww xY w)
�
Hauptfunktionr   z3ULTIMATE BITCOIN TRADING GUI V7.0 - SUPER OPTIMIZEDz	Features:z- Entfernte doppelte Funktionenz- Optimierte ML-Modellez- Verbesserte Visualisierungz- Benutzerfreundliche Bedienungz!- Optimiertes Training & Prognosez
Beende Anwendung...zFehler: z
Hauptfehler: N)r0   r   r�  �KeyboardInterruptr]   rF   r^   )�appr`   s     rJ   �mainr  G  s;  � �+�
�h����
�C�D�D�D�
�h����
����
�k����
�/�0�0�0�
�'�(�(�(�
�,�-�-�-�
�/�0�0�0�
�1�2�2�2�
����+�-�-�����	�	�	�	�	��� '� '� '�
�%�&�&�&�&�&�&�� +� +� +�
�n��n�n�����
�)�a�)�)�*�*�*�*�*�*�*�*�*�����+���s   �CC	 �	D"�%	D"�.)D�D"�__main__)/r  �tkinterr  r   r   �matplotlib.pyplot�pyplotr�  �!matplotlib.backends.backend_tkaggr   �matplotlib.animationr   �matplotlib.dates�dates�mdates�seaborn�sns�pandasr�  �numpyr�   r   r   r9   r�  ru   rs   r   rX   �json�pickle�collectionsr	   rF   �"ultimate_bitcoin_trading_system_v6r
   r2   �ImportErrorr0   �style�use�set_paletterR   rS   r   r  r   r�  rL   rJ   �<module>r     s!  ��
� 
� � � � � #� #� #� #� #� #� #� #� � � � � � � ?� ?� ?� ?� ?� ?� .� .� .� .� .� .� !� !� !� !� !� !� � � � � � � � � � � � � (� (� (� (� (� (� (� (� � � � � ���� 	�	�	�	� 
�
�
�
� 
�
�
�
� 
�
�
�
� ���� 
�
�
�
� � � � � � � ����9�Q�Q�Q�Q�Q�Q�#����� 9� 9� 9�$��	�E�
7�8�8�8�8�8�9����
 �	�
�
��  �  �  � ���	� � � � �� �'�,�/Z� [� [� [� [�P� P� P� P� P� P� P� P�d +� +� +�0 �z����D�F�F�F�F�F� �s   �&A/ �/B�B