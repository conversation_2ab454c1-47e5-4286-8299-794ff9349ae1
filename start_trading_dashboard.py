#!/usr/bin/env python3
"""
🚀 TRADING DASHBOARD LAUNCHER
============================
Einfacher Launcher für das modulare Bitcoin Trading Dashboard
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """🔍 Abhängigkeiten prüfen"""
    required_modules = ['tkinter', 'numpy', 'requests']
    missing = []

    print("🔍 Prüfe Abhängigkeiten...")

    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} fehlt")
            missing.append(module)

    if missing:
        print(f"\n⚠️ Fehlende Module: {', '.join(missing)}")
        print("📦 Installiere mit: pip install numpy requests")
        return False

    print("✅ Alle Abhängigkeiten verfügbar")
    return True

def start_dashboard():
    """🚀 Dashboard starten"""
    dashboard_file = Path("modular_trading_dashboard.py")

    if not dashboard_file.exists():
        print("❌ Dashboard-Datei nicht gefunden!")
        return False

    print("🚀 Starte Modulares Bitcoin Trading Dashboard...")
    print("📊 3 Berechnungsmodelle verfügbar")
    print("🔧 Modulare Architektur - erweiterbar")
    print("💰 Live Bitcoin-Kurs mit Intervall-Updates")
    print("\n" + "="*50)

    try:
        subprocess.run([sys.executable, str(dashboard_file)], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Dashboard-Start fehlgeschlagen: {e}")
        return False
    except KeyboardInterrupt:
        print("\n🛑 Dashboard beendet")
        return True

def main():
    """🚀 Hauptfunktion"""
    print("🚀 MODULARES BITCOIN TRADING DASHBOARD")
    print("=" * 50)

    # Abhängigkeiten prüfen
    if not check_dependencies():
        input("\n⏸️ Drücke Enter zum Beenden...")
        return

    print("\n" + "="*50)

    # Dashboard starten
    start_dashboard()

    print("\n👋 Auf Wiedersehen!")

if __name__ == "__main__":
    main()