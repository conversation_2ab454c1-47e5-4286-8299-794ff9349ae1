#!/usr/bin/env python3
"""
Ultimate Bitcoin Prediction Model - Lokale Version
Verwendet lokale CSV-Daten für robuste Vorhersagen
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, Bidirectional, BatchNormalization
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
from tensorflow.keras.optimizers import Adam
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score, mean_absolute_percentage_error
from datetime import datetime, timedelta
import time
import os
import joblib
import warnings
warnings.filterwarnings('ignore')

# Schnelle Konfiguration für lokale Daten
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.8,
    'look_back': 24,        # <PERSON>uz<PERSON><PERSON> von 48
    'future_steps': 24,     # <PERSON>uz<PERSON><PERSON> von 72
    'batch_size': 128,      # <PERSON><PERSON><PERSON><PERSON>ht für schnelleres Training
    'epochs': 30,           # Reduziert von 100
    'patience': 8,          # Reduziert von 15
    'save_dir': 'saved_models',
    'monte_carlo_simulations': 50  # Reduziert von 200
}

class LocalDataProcessor:
    def __init__(self):
        self.feature_scaler = None
        self.target_scaler = None
        self.feature_columns = None
    
    def load_local_data(self):
        """Lade lokale CSV-Daten"""
        try:
            print(f"📊 Lade lokale Daten aus {CONFIG['data_file']}...")
            df = pd.read_csv(CONFIG['data_file'])
            
            # Zeitstempel konvertieren
            df['time'] = pd.to_datetime(df['time'])
            df.set_index('time', inplace=True)
            
            print(f"✅ Daten geladen: {len(df)} Datenpunkte")
            print(f"   Zeitraum: {df.index[0]} bis {df.index[-1]}")
            print(f"   Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
            
            return df
        except Exception as e:
            print(f"❌ Fehler beim Laden der Daten: {e}")
            return None
    
    def add_technical_indicators(self, df):
        """Erweiterte technische Indikatoren"""
        print("📈 Berechne technische Indikatoren...")
        
        # Moving Averages
        df['ema_9'] = df['close'].ewm(span=9).mean()
        df['ema_21'] = df['close'].ewm(span=21).mean()
        df['ema_50'] = df['close'].ewm(span=50).mean()
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        
        # MACD
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(window=14).mean()
        avg_loss = loss.rolling(window=14).mean()
        rs = avg_gain / avg_loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        df['bb_std'] = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # ATR
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        df['atr'] = true_range.rolling(14).mean()
        df['atr_percent'] = df['atr'] / df['close'] * 100
        
        # Stochastic
        low_14 = df['low'].rolling(14).min()
        high_14 = df['high'].rolling(14).max()
        df['stoch_k'] = 100 * ((df['close'] - low_14) / (high_14 - low_14))
        df['stoch_d'] = df['stoch_k'].rolling(3).mean()
        
        # Volume indicators
        df['volume_sma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # Price patterns
        df['price_roc'] = df['close'].pct_change(periods=12) * 100
        df['momentum'] = df['close'] - df['close'].shift(10)
        df['volatility'] = df['close'].pct_change().rolling(window=20).std() * 100
        
        # High/Low ratios
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        
        return df.dropna()
    
    def prepare_data(self):
        """Daten vorbereiten"""
        # Daten laden
        df = self.load_local_data()
        if df is None:
            raise ValueError("Keine Daten verfügbar!")
        
        # Technische Indikatoren hinzufügen
        df = self.add_technical_indicators(df)
        
        # Feature-Auswahl
        feature_columns = [
            'open', 'high', 'low', 'close', 'volume',
            'ema_9', 'ema_21', 'ema_50', 'sma_20', 'sma_50',
            'macd', 'macd_signal', 'macd_histogram',
            'rsi', 'stoch_k', 'stoch_d',
            'bb_upper', 'bb_lower', 'bb_width', 'bb_position',
            'atr', 'atr_percent', 'volume_sma', 'volume_ratio',
            'price_roc', 'momentum', 'volatility',
            'high_low_ratio', 'close_open_ratio'
        ]
        
        self.feature_columns = [col for col in feature_columns if col in df.columns]
        print(f"📊 Verwende {len(self.feature_columns)} Features")
        
        # Daten vorbereiten
        feature_data = df[self.feature_columns].values.astype('float32')
        target_data = df['close'].values.reshape(-1, 1).astype('float32')
        
        # Skalierung
        self.feature_scaler = RobustScaler()
        self.target_scaler = MinMaxScaler(feature_range=(0, 1))
        
        feature_scaled = self.feature_scaler.fit_transform(feature_data)
        target_scaled = self.target_scaler.fit_transform(target_data)
        
        # Train-Test Split
        train_size = int(len(feature_scaled) * CONFIG['train_split'])
        
        # Sequenzen erstellen
        X_train, y_train = self.create_sequences(
            feature_scaled[:train_size], target_scaled[:train_size]
        )
        X_test, y_test = self.create_sequences(
            feature_scaled[train_size:], target_scaled[train_size:]
        )
        
        print(f"✅ Daten vorbereitet: Train {X_train.shape}, Test {X_test.shape}")
        
        return X_train, y_train, X_test, y_test, df, train_size
    
    def create_sequences(self, features, targets):
        """Sequenzen für LSTM erstellen"""
        X, y = [], []
        for i in range(len(features) - CONFIG['look_back']):
            X.append(features[i:i + CONFIG['look_back']])
            y.append(targets[i + CONFIG['look_back']])
        return np.array(X), np.array(y)
    
    def inverse_transform_target(self, data):
        """Target-Skalierung rückgängig machen"""
        return self.target_scaler.inverse_transform(data)

class HybridModelBuilder:
    def build_model(self, input_shape):
        """Schnelles kompaktes Modell"""
        model = Sequential([
            # Kleinere LSTM-Schicht
            LSTM(64, return_sequences=True, dropout=0.2, input_shape=input_shape),

            # Zweite LSTM-Schicht
            LSTM(32, return_sequences=False, dropout=0.2),

            # Kompakte Dense Layers
            Dense(32, activation='relu'),
            Dropout(0.3),
            Dense(16, activation='relu'),
            Dense(1, activation='linear')
        ])

        optimizer = Adam(learning_rate=0.001)  # Höhere Learning Rate für schnelleres Training
        model.compile(optimizer=optimizer, loss='mse', metrics=['mae'])

        return model
    
    def get_callbacks(self):
        """Training Callbacks"""
        os.makedirs(CONFIG['save_dir'], exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_path = os.path.join(CONFIG['save_dir'], f"btc_local_{timestamp}.h5")
        
        callbacks = [
            EarlyStopping(monitor='val_loss', patience=CONFIG['patience'], 
                         restore_best_weights=True, verbose=1),
            ModelCheckpoint(model_path, monitor='val_loss', save_best_only=True, verbose=1),
            ReduceLROnPlateau(monitor='val_loss', factor=0.7, patience=8, 
                             min_lr=0.00001, verbose=1)
        ]
        
        return callbacks, model_path

class AdvancedPredictor:
    def __init__(self, model, processor):
        self.model = model
        self.processor = processor
    
    def predict_future(self, last_sequence):
        """Monte Carlo Zukunftsprognose"""
        print("🔮 Erstelle Zukunftsprognosen...")
        
        n_simulations = CONFIG['monte_carlo_simulations']
        future_steps = CONFIG['future_steps']
        all_predictions = []
        
        for sim in range(n_simulations):
            if sim % 50 == 0:
                print(f"   Simulation {sim+1}/{n_simulations}")
            
            current_seq = last_sequence.copy()
            sim_predictions = []
            
            for step in range(future_steps):
                pred = self.model.predict(current_seq.reshape(1, *current_seq.shape), verbose=0)[0, 0]
                sim_predictions.append(pred)
                
                # Nächste Sequenz mit Rauschen
                new_features = self.generate_next_features(current_seq, pred, step)
                current_seq = np.vstack([current_seq[1:], new_features.reshape(1, -1)])
            
            all_predictions.append(sim_predictions)
        
        # Statistiken
        all_predictions = np.array(all_predictions)
        mean_pred = np.mean(all_predictions, axis=0)
        upper_95 = np.percentile(all_predictions, 97.5, axis=0)
        lower_95 = np.percentile(all_predictions, 2.5, axis=0)
        upper_80 = np.percentile(all_predictions, 90, axis=0)
        lower_80 = np.percentile(all_predictions, 10, axis=0)
        
        # Skalierung rückgängig machen
        mean_rescaled = self.processor.inverse_transform_target(mean_pred.reshape(-1, 1)).flatten()
        upper_95_rescaled = self.processor.inverse_transform_target(upper_95.reshape(-1, 1)).flatten()
        lower_95_rescaled = self.processor.inverse_transform_target(lower_95.reshape(-1, 1)).flatten()
        upper_80_rescaled = self.processor.inverse_transform_target(upper_80.reshape(-1, 1)).flatten()
        lower_80_rescaled = self.processor.inverse_transform_target(lower_80.reshape(-1, 1)).flatten()
        
        return {
            'mean': mean_rescaled,
            'upper_95': upper_95_rescaled,
            'lower_95': lower_95_rescaled,
            'upper_80': upper_80_rescaled,
            'lower_80': lower_80_rescaled
        }
    
    def generate_next_features(self, current_seq, predicted_close, step):
        """Generiere nächste Features"""
        last_features = current_seq[-1].copy()
        volatility = 0.02 * (1 + step * 0.001)
        
        # Neue Features mit Rauschen
        new_features = last_features.copy()
        new_features[3] = predicted_close  # close
        
        # Andere Features mit Variation
        for i in range(len(new_features)):
            if i != 3:
                noise = np.random.normal(0, volatility * 0.5)
                new_features[i] = new_features[i] * (1 + noise)
        
        return new_features
    
    def evaluate_model(self, X_train, y_train, X_test, y_test):
        """Modell evaluieren"""
        train_pred = self.model.predict(X_train, verbose=0)
        test_pred = self.model.predict(X_test, verbose=0)
        
        y_train_orig = self.processor.inverse_transform_target(y_train)
        y_test_orig = self.processor.inverse_transform_target(y_test)
        train_pred_orig = self.processor.inverse_transform_target(train_pred)
        test_pred_orig = self.processor.inverse_transform_target(test_pred)
        
        metrics = {
            'train_rmse': np.sqrt(mean_squared_error(y_train_orig, train_pred_orig)),
            'test_rmse': np.sqrt(mean_squared_error(y_test_orig, test_pred_orig)),
            'train_mae': mean_absolute_error(y_train_orig, train_pred_orig),
            'test_mae': mean_absolute_error(y_test_orig, test_pred_orig),
            'train_r2': r2_score(y_train_orig, train_pred_orig),
            'test_r2': r2_score(y_test_orig, test_pred_orig),
            'train_mape': mean_absolute_percentage_error(y_train_orig, train_pred_orig),
            'test_mape': mean_absolute_percentage_error(y_test_orig, test_pred_orig)
        }
        
        return metrics, y_train_orig, y_test_orig, train_pred_orig, test_pred_orig

class Visualizer:
    def plot_results(self, df, train_size, metrics, predictions, future_pred, history):
        """Umfassende Visualisierung"""
        fig, axes = plt.subplots(2, 2, figsize=(20, 12))

        # 1. Hauptpreis-Chart
        ax1 = axes[0, 0]
        dates = df.index
        prices = df['close'].values

        train_dates = dates[:train_size]
        test_dates = dates[train_size:]
        train_prices = prices[:train_size]

        y_test_orig, test_pred_orig = predictions['test_actual'], predictions['test_pred']

        ax1.plot(train_dates, train_prices, label='Training', color='blue', alpha=0.7)
        ax1.plot(test_dates[-len(y_test_orig):], y_test_orig.flatten(),
                label='Test (Actual)', color='green', linewidth=2)
        ax1.plot(test_dates[-len(test_pred_orig):], test_pred_orig.flatten(),
                label='Test (Predicted)', color='red', linestyle='--', linewidth=2)

        # Zukunftsprognose
        if future_pred:
            future_dates = pd.date_range(
                start=dates[-1] + pd.Timedelta(hours=1),
                periods=len(future_pred['mean']),
                freq='1H'
            )

            ax1.plot(future_dates, future_pred['mean'],
                   label='Future Prediction', color='purple', linewidth=2)
            ax1.fill_between(future_dates, future_pred['lower_95'], future_pred['upper_95'],
                           color='purple', alpha=0.2, label='95% Confidence')
            ax1.fill_between(future_dates, future_pred['lower_80'], future_pred['upper_80'],
                           color='purple', alpha=0.3, label='80% Confidence')

        ax1.set_title('Bitcoin Price Prediction - Local Model', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Date')
        ax1.set_ylabel('Price (USD)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

        # 2. Training History
        ax2 = axes[0, 1]
        if history:
            epochs = range(1, len(history.history['loss']) + 1)
            ax2.plot(epochs, history.history['loss'], 'b-', label='Training Loss', linewidth=2)
            ax2.plot(epochs, history.history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
            ax2.set_title('Training Progress', fontweight='bold')
            ax2.set_xlabel('Epoch')
            ax2.set_ylabel('Loss')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

        # 3. Residuals
        ax3 = axes[1, 0]
        y_test = predictions['test_actual'].flatten()
        y_pred = predictions['test_pred'].flatten()
        residuals = y_test - y_pred

        ax3.scatter(y_pred, residuals, alpha=0.6, s=20)
        ax3.axhline(y=0, color='red', linestyle='--')
        ax3.set_title('Residuals Plot', fontweight='bold')
        ax3.set_xlabel('Predicted Values')
        ax3.set_ylabel('Residuals')
        ax3.grid(True, alpha=0.3)

        # 4. Metriken
        ax4 = axes[1, 1]
        ax4.axis('off')

        metrics_text = f"""
        MODEL PERFORMANCE METRICS

        Test RMSE: ${metrics.get('test_rmse', 0):.2f}
        Test MAE: ${metrics.get('test_mae', 0):.2f}
        Test R²: {metrics.get('test_r2', 0):.4f}
        Test MAPE: {metrics.get('test_mape', 0):.2f}%

        Train RMSE: ${metrics.get('train_rmse', 0):.2f}
        Train R²: {metrics.get('train_r2', 0):.4f}

        Data Points: {len(df)}
        Features: {len(df.columns)}
        """

        ax4.text(0.1, 0.9, metrics_text, transform=ax4.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

        plt.tight_layout()
        plt.show()

    def plot_future_detail(self, future_pred):
        """Detaillierte Zukunftsprognose"""
        plt.figure(figsize=(15, 8))

        hours = range(1, len(future_pred['mean']) + 1)

        plt.plot(hours, future_pred['mean'], 'purple', linewidth=3, label='Prediction')
        plt.fill_between(hours, future_pred['lower_95'], future_pred['upper_95'],
                        alpha=0.2, color='purple', label='95% Confidence Interval')
        plt.fill_between(hours, future_pred['lower_80'], future_pred['upper_80'],
                        alpha=0.3, color='purple', label='80% Confidence Interval')

        # Preisänderung berechnen
        initial_price = future_pred['mean'][0]
        final_price = future_pred['mean'][-1]
        change_pct = ((final_price / initial_price) - 1) * 100

        plt.title(f'72-Hour Bitcoin Price Prediction\nExpected Change: {change_pct:+.2f}%',
                 fontsize=16, fontweight='bold')
        plt.xlabel('Hours Ahead', fontsize=12)
        plt.ylabel('Price (USD)', fontsize=12)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)

        # Markierungen für 24h Intervalle
        for h in [24, 48, 72]:
            if h <= len(hours):
                plt.axvline(x=h, color='gray', linestyle='--', alpha=0.5)
                plt.text(h, plt.ylim()[1]*0.95, f'{h}h', ha='center', fontsize=10)

        plt.tight_layout()
        plt.show()

def main():
    """Hauptfunktion"""
    print("🚀 ULTIMATE BITCOIN PREDICTION MODEL - LOCAL VERSION")
    print("=" * 60)

    start_time = time.time()

    try:
        # 1. Daten vorbereiten
        processor = LocalDataProcessor()
        X_train, y_train, X_test, y_test, df, train_size = processor.prepare_data()

        # 2. Modell erstellen
        print("🏗️  Erstelle Hybrid-Modell...")
        builder = HybridModelBuilder()
        model = builder.build_model((X_train.shape[1], X_train.shape[2]))
        callbacks, model_path = builder.get_callbacks()

        print(f"📋 Modell-Parameter: {model.count_params():,}")

        # 3. Training
        print("🎯 Starte Training...")
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            callbacks=callbacks,
            verbose=1,
            shuffle=True
        )

        # 4. Evaluation
        predictor = AdvancedPredictor(model, processor)
        metrics, y_train_orig, y_test_orig, train_pred_orig, test_pred_orig = predictor.evaluate_model(
            X_train, y_train, X_test, y_test
        )

        print("\n📊 MODELL-PERFORMANCE:")
        print(f"Test RMSE: ${metrics['test_rmse']:.2f}")
        print(f"Test MAE: ${metrics['test_mae']:.2f}")
        print(f"Test R²: {metrics['test_r2']:.4f}")
        print(f"Test MAPE: {metrics['test_mape']:.2f}%")

        # 5. Zukunftsprognose
        print("\n🔮 Erstelle 72-Stunden Prognose...")
        last_sequence = X_test[-1]
        future_predictions = predictor.predict_future(last_sequence)

        # 6. Modell speichern
        print("💾 Speichere Modell...")
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        joblib.dump(processor.feature_scaler, f"{CONFIG['save_dir']}/feature_scaler_{timestamp}.pkl")
        joblib.dump(processor.target_scaler, f"{CONFIG['save_dir']}/target_scaler_{timestamp}.pkl")

        # 7. Visualisierung
        print("📈 Erstelle Visualisierungen...")
        visualizer = Visualizer()

        predictions_dict = {
            'test_actual': y_test_orig,
            'test_pred': test_pred_orig,
            'train_actual': y_train_orig,
            'train_pred': train_pred_orig
        }

        visualizer.plot_results(df, train_size, metrics, predictions_dict, future_predictions, history)
        visualizer.plot_future_detail(future_predictions)

        # 8. Prognose-Zusammenfassung
        print("\n🎯 72-STUNDEN PROGNOSE:")
        current_price = df['close'].iloc[-1]

        for hours in [24, 48, 72]:
            if hours <= len(future_predictions['mean']):
                pred_price = future_predictions['mean'][hours-1]
                change_pct = ((pred_price / current_price) - 1) * 100
                lower_95 = future_predictions['lower_95'][hours-1]
                upper_95 = future_predictions['upper_95'][hours-1]

                print(f"In {hours}h: ${pred_price:.2f} ({change_pct:+.2f}%) "
                      f"[95% CI: ${lower_95:.2f} - ${upper_95:.2f}]")

        total_time = time.time() - start_time
        print(f"\n✅ Fertig! Gesamtzeit: {total_time:.1f} Sekunden")
        print(f"📁 Modell gespeichert: {model_path}")

    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
