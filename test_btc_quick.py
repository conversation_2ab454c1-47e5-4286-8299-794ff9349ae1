#!/usr/bin/env python3
"""
SCHNELLTEST - Bitcoin Prediction Model
Testet Basis-Funktionalität in unter 2 Minuten
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers.legacy import Adam
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, r2_score
import time
import warnings
warnings.filterwarnings('ignore')

print("🧪 SCHNELLTEST - Bitcoin Prediction Model")
print("=" * 50)

def quick_test():
    """Schneller Funktionstest"""
    start_time = time.time()
    
    try:
        # 1. Daten laden
        print("📊 1. Lade Testdaten...")
        df = pd.read_csv('crypto_data.csv')
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        print(f"   ✅ {len(df)} Datenpunkte geladen")
        
        # 2. Einfache Features
        print("🔧 2. Erstelle Features...")
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # Nur wichtigste Features für Schnelltest
        features['sma_10'] = df['close'].rolling(10).mean()
        features['sma_20'] = df['close'].rolling(20).mean()
        features['rsi'] = calculate_rsi(df['close'], 14)
        features['volatility'] = df['close'].pct_change().rolling(5).std()
        
        features = features.dropna()
        print(f"   ✅ {len(features.columns)} Features erstellt")
        
        # 3. Daten vorbereiten
        print("🔄 3. Bereite Daten vor...")
        X = features.drop('close', axis=1)
        y = features['close'].values
        
        scaler_X = MinMaxScaler()
        scaler_y = MinMaxScaler()
        
        X_scaled = scaler_X.fit_transform(X)
        y_scaled = scaler_y.fit_transform(y.reshape(-1, 1)).flatten()
        
        # Einfache Sequenzen (kurz für Schnelltest)
        look_back = 12
        X_seq, y_seq = create_sequences(X_scaled, y_scaled, look_back)
        
        # Train-Test Split
        train_size = int(len(X_seq) * 0.8)
        X_train, X_test = X_seq[:train_size], X_seq[train_size:]
        y_train, y_test = y_seq[:train_size], y_seq[train_size:]
        
        print(f"   ✅ Train: {len(X_train)}, Test: {len(X_test)}")
        
        # 4. Schnelles Modell
        print("🤖 4. Trainiere Schnell-Modell...")
        model = Sequential([
            LSTM(32, return_sequences=False, input_shape=(X_train.shape[1], X_train.shape[2])),
            Dense(16, activation='relu'),
            Dense(1)
        ])
        
        model.compile(optimizer=Adam(0.01), loss='mse', metrics=['mae'])
        
        # Schnelles Training (nur 10 Epochen)
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=10,
            batch_size=32,
            verbose=0
        )
        
        print(f"   ✅ Training abgeschlossen")
        
        # 5. Evaluation
        print("📊 5. Evaluiere Modell...")
        y_pred = model.predict(X_test, verbose=0)
        
        # Skalierung rückgängig
        y_test_orig = scaler_y.inverse_transform(y_test.reshape(-1, 1)).flatten()
        y_pred_orig = scaler_y.inverse_transform(y_pred).flatten()
        
        # Metriken
        r2 = r2_score(y_test_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
        mae = np.mean(np.abs(y_test_orig - y_pred_orig))
        
        print(f"   📈 R²: {r2:.4f} ({r2*100:.1f}%)")
        print(f"   📈 RMSE: ${rmse:.2f}")
        print(f"   📈 MAE: ${mae:.2f}")
        
        # 6. Schnelle Visualisierung
        print("📈 6. Erstelle Visualisierung...")
        plt.figure(figsize=(12, 6))
        
        plt.subplot(1, 2, 1)
        plt.plot(y_test_orig[:50], 'g-', label='Actual', linewidth=2)
        plt.plot(y_pred_orig[:50], 'r--', label='Predicted', linewidth=2)
        plt.title('SCHNELLTEST - Vorhersage vs Realität')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.subplot(1, 2, 2)
        plt.scatter(y_test_orig, y_pred_orig, alpha=0.6)
        min_val, max_val = min(y_test_orig.min(), y_pred_orig.min()), max(y_test_orig.max(), y_pred_orig.max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
        plt.xlabel('Actual')
        plt.ylabel('Predicted')
        plt.title('Scatter Plot')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        # 7. Schnelle Zukunftsprognose
        print("🔮 7. Erstelle Zukunftsprognose...")
        last_sequence = X_test[-1].reshape(1, X_test.shape[1], X_test.shape[2])
        future_pred = model.predict(last_sequence, verbose=0)
        future_price = scaler_y.inverse_transform(future_pred)[0, 0]
        current_price = y_test_orig[-1]
        change_pct = (future_price / current_price - 1) * 100
        
        print(f"   💰 Aktueller Preis: ${current_price:.2f}")
        print(f"   🔮 Prognose: ${future_price:.2f} ({change_pct:+.2f}%)")
        
        # Gesamtzeit
        total_time = time.time() - start_time
        
        # ERGEBNIS
        print("\n" + "=" * 50)
        print("✅ SCHNELLTEST ABGESCHLOSSEN!")
        print("=" * 50)
        print(f"⚡ Gesamtzeit: {total_time:.1f} Sekunden")
        print(f"🎯 Genauigkeit: {r2*100:.1f}%")
        print(f"📊 Testsamples: {len(y_test_orig)}")
        
        # Bewertung
        if r2 >= 0.8:
            print("🎉 EXZELLENT! Modell funktioniert perfekt!")
        elif r2 >= 0.6:
            print("🔥 SEHR GUT! Modell zeigt starke Performance!")
        elif r2 >= 0.4:
            print("💪 GUT! Modell funktioniert ordentlich!")
        elif r2 >= 0.0:
            print("✅ OK! Modell ist besser als Zufall!")
        else:
            print("⚠️ VERBESSERUNG NÖTIG! Modell braucht Optimierung!")
        
        return True, {
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'time': total_time,
            'samples': len(y_test_orig)
        }
        
    except Exception as e:
        print(f"❌ FEHLER im Schnelltest: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def calculate_rsi(prices, period=14):
    """Berechne RSI"""
    delta = prices.diff()
    gain = delta.where(delta > 0, 0).rolling(period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def create_sequences(data, target, look_back):
    """Erstelle Sequenzen"""
    X, y = [], []
    for i in range(look_back, len(data)):
        X.append(data[i-look_back:i])
        y.append(target[i])
    return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)

if __name__ == "__main__":
    success, results = quick_test()
    
    if success:
        print(f"\n🎯 SCHNELLTEST ERFOLGREICH!")
        print(f"   Bereit für ausführliche Tests!")
    else:
        print(f"\n❌ SCHNELLTEST FEHLGESCHLAGEN!")
        print(f"   Bitte Probleme beheben vor weiteren Tests!")
