#!/usr/bin/env python3
"""
BITCOIN TRADING COMPLETE INSTALLER - BENUTZERFREUNDLICH
======================================================
VOLLSTÄNDIGE INSTALLATION DES BITCOIN TRADING SYSTEMS
- Desktop-Verknüpfung erstellen
- Alle Launcher installieren
- Visualisierungen einrichten
- Benutzerfreundliche Bedienung
- Ein-Klick-Installation

COMPLETE INSTALLER - ALLES IN EINEM!
"""

import os
import sys
import subprocess
import time
from datetime import datetime

class BitcoinTradingCompleteInstaller:
    """
    BITCOIN TRADING COMPLETE INSTALLER
    =================================
    Vollständige Installation des Bitcoin Trading Systems
    mit Desktop-Verknüpfung und Visualisierungen.
    """
    
    def __init__(self):
        self.script_dir = os.getcwd()
        self.desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        
        print("BITCOIN TRADING COMPLETE INSTALLER initialisiert")
        print(f"Installations-Verzeichnis: {self.script_dir}")
        print(f"Desktop-Pfad: {self.desktop_path}")
    
    def check_system_requirements(self):
        """Prüfe System-Voraussetzungen"""
        print("\nPrüfe System-Voraussetzungen...")
        print("="*50)
        
        requirements_met = True
        
        # Python-Version prüfen
        python_version = sys.version_info
        if python_version.major >= 3 and python_version.minor >= 7:
            print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro} - OK")
        else:
            print(f"❌ Python {python_version.major}.{python_version.minor} - Mindestens Python 3.7 erforderlich")
            requirements_met = False
        
        # Erforderliche Module prüfen
        required_modules = [
            ('tkinter', 'GUI-Framework'),
            ('pandas', 'Datenverarbeitung'),
            ('numpy', 'Numerische Berechnungen'),
            ('matplotlib', 'Visualisierungen')
        ]
        
        for module, description in required_modules:
            try:
                __import__(module)
                print(f"✅ {module} - {description} - OK")
            except ImportError:
                print(f"❌ {module} - {description} - FEHLT")
                requirements_met = False
        
        # Verfügbare Scripts prüfen
        required_scripts = [
            'bitcoin_trading_launcher_no_emoji.py',
            'ultimate_complete_bitcoin_trading_FAVORITE_NO_EMOJI.py',
            'btc_ultimate_optimized_complete_NO_EMOJI.py',
            'bitcoin_trading_simple_fixed_NO_EMOJI.py'
        ]
        
        print("\nPrüfe verfügbare Scripts...")
        for script in required_scripts:
            if os.path.exists(script):
                print(f"✅ {script} - OK")
            else:
                print(f"❌ {script} - FEHLT")
                requirements_met = False
        
        return requirements_met
    
    def install_desktop_shortcut(self):
        """Installiere Desktop-Verknüpfung"""
        print("\nInstalliere Desktop-Verknüpfung...")
        print("="*50)
        
        try:
            # Führe Desktop-Shortcut Creator aus
            result = subprocess.run([
                sys.executable, 
                'bitcoin_desktop_shortcut_creator.py'
            ], capture_output=True, text=True, input='\n')
            
            if result.returncode == 0:
                print("✅ Desktop-Verknüpfung erfolgreich installiert")
                return True
            else:
                print(f"❌ Fehler bei Desktop-Verknüpfung: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Fehler bei Desktop-Verknüpfung Installation: {e}")
            return False
    
    def test_launchers(self):
        """Teste alle Launcher"""
        print("\nTeste Launcher-Funktionalität...")
        print("="*50)
        
        launchers = [
            ('bitcoin_trading_launcher_no_emoji.py', 'Standard Launcher'),
            ('bitcoin_launcher_with_visualizations.py', 'Visualisierungs-Launcher')
        ]
        
        all_working = True
        
        for launcher, description in launchers:
            if os.path.exists(launcher):
                print(f"📋 Teste {description}...")
                try:
                    # Kurzer Test-Start (5 Sekunden)
                    process = subprocess.Popen([
                        sys.executable, launcher
                    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    
                    time.sleep(3)  # Kurz warten
                    process.terminate()
                    
                    try:
                        process.wait(timeout=2)
                        print(f"✅ {description} - Funktioniert")
                    except subprocess.TimeoutExpired:
                        process.kill()
                        print(f"✅ {description} - Läuft (beendet)")
                        
                except Exception as e:
                    print(f"❌ {description} - Fehler: {e}")
                    all_working = False
            else:
                print(f"❌ {description} - Datei nicht gefunden")
                all_working = False
        
        return all_working
    
    def test_trading_models(self):
        """Teste Trading-Modelle"""
        print("\nTeste Trading-Modelle...")
        print("="*50)
        
        models = [
            ('ultimate_complete_bitcoin_trading_FAVORITE_NO_EMOJI.py', 'FAVORIT-Modell'),
            ('btc_ultimate_optimized_complete_NO_EMOJI.py', 'OPTIMIERT-Modell'),
            ('bitcoin_trading_simple_fixed_NO_EMOJI.py', 'SIMPLE-Modell')
        ]
        
        all_working = True
        
        for model, description in models:
            if os.path.exists(model):
                print(f"📊 Teste {description}...")
                try:
                    # Schneller Test
                    result = subprocess.run([
                        sys.executable, model
                    ], capture_output=True, text=True, timeout=30)
                    
                    if result.returncode == 0:
                        print(f"✅ {description} - Funktioniert")
                        
                        # Suche nach wichtigen Ausgaben
                        if 'ERFOLGREICH' in result.stdout:
                            print(f"   📈 Analyse erfolgreich durchgeführt")
                        if 'Signal:' in result.stdout:
                            print(f"   📊 Trading-Signal generiert")
                    else:
                        print(f"❌ {description} - Fehler beim Ausführen")
                        all_working = False
                        
                except subprocess.TimeoutExpired:
                    print(f"⚠️ {description} - Timeout (läuft zu lange)")
                except Exception as e:
                    print(f"❌ {description} - Fehler: {e}")
                    all_working = False
            else:
                print(f"❌ {description} - Datei nicht gefunden")
                all_working = False
        
        return all_working
    
    def create_user_guide(self):
        """Erstelle Benutzerhandbuch"""
        print("\nErstelle Benutzerhandbuch...")
        print("="*50)
        
        guide_content = f"""
BITCOIN TRADING SYSTEM - BENUTZERHANDBUCH
========================================

INSTALLATION ABGESCHLOSSEN: {datetime.now().strftime('%d.%m.%Y %H:%M:%S')}

SCHNELLSTART:
------------

1. DESKTOP-VERKNÜPFUNG:
   ✅ Doppelklicken Sie auf "Bitcoin Trading Launcher" auf Ihrem Desktop
   ✅ Der Launcher startet automatisch

2. ALTERNATIVE STARTS:
   ✅ Bitcoin_Trading_Launcher.bat (Batch-Datei)
   ✅ Bitcoin_Trading_Launcher.ps1 (PowerShell)

VERFÜGBARE LAUNCHER:
-------------------

1. STANDARD LAUNCHER:
   📁 bitcoin_trading_launcher_no_emoji.py
   📋 Professionelle GUI mit 3 Trading-Modellen
   🎯 Emoji-frei für maximale Kompatibilität

2. VISUALISIERUNGS-LAUNCHER:
   📁 bitcoin_launcher_with_visualizations.py
   📊 Erweiterte GUI mit Live-Charts
   📈 Real-time Dashboard und Statistiken

TRADING-MODELLE:
---------------

1. FAVORIT-MODELL:
   📁 ultimate_complete_bitcoin_trading_FAVORITE_NO_EMOJI.py
   🏆 Das bewährte System mit kontinuierlichem Lernen
   ⭐ EMPFOHLEN für beste Ergebnisse

2. OPTIMIERT-MODELL:
   📁 btc_ultimate_optimized_complete_NO_EMOJI.py
   ⚡ Das schnelle System für Echtzeit-Analysen
   🚀 Optimiert für Geschwindigkeit

3. SIMPLE-MODELL:
   📁 bitcoin_trading_simple_fixed_NO_EMOJI.py
   🔒 Das zuverlässige System ohne externe Abhängigkeiten
   ✅ Maximale Kompatibilität

BEDIENUNG:
---------

1. Launcher starten (Desktop-Verknüpfung)
2. Trading-Modell auswählen
3. ">> STARTEN" klicken
4. Ergebnisse im Dashboard betrachten
5. Trading-Signale befolgen

ERGEBNISSE VERSTEHEN:
--------------------

📊 TRADING-SIGNALE:
   - KAUFEN: Bitcoin kaufen empfohlen
   - VERKAUFEN: Bitcoin verkaufen empfohlen  
   - HALTEN: Position halten empfohlen

📈 KONFIDENZ:
   - Prozentuale Sicherheit des Signals
   - Höhere Werte = zuverlässigere Signale

💰 RISK MANAGEMENT:
   - Position Size: Empfohlene Investitionsgröße
   - Stop Loss: Verlustbegrenzung
   - Take Profit: Gewinnmitnahme

SUPPORT:
-------

Bei Problemen:
1. Prüfen Sie die Log-Ausgaben im Launcher
2. Stellen Sie sicher, dass Python installiert ist
3. Alle Scripts sind emoji-frei und kompatibel
4. README_Bitcoin_Trading.txt für Details

DATEIEN ÜBERSICHT:
-----------------

LAUNCHER:
- bitcoin_trading_launcher_no_emoji.py (Standard)
- bitcoin_launcher_with_visualizations.py (Mit Charts)

MODELLE:
- ultimate_complete_bitcoin_trading_FAVORITE_NO_EMOJI.py
- btc_ultimate_optimized_complete_NO_EMOJI.py  
- bitcoin_trading_simple_fixed_NO_EMOJI.py

INSTALLATION:
- bitcoin_desktop_shortcut_creator.py
- install_bitcoin_trading_complete.py

START-DATEIEN:
- Bitcoin_Trading_Launcher.bat
- Bitcoin_Trading_Launcher.ps1

DOKUMENTATION:
- README_Bitcoin_Trading.txt
- Bitcoin_Trading_Benutzerhandbuch.txt (diese Datei)

Viel Erfolg beim Bitcoin Trading!

HAFTUNGSAUSSCHLUSS:
------------------
Dieses System dient nur zu Bildungszwecken.
Trading birgt Risiken. Investieren Sie nur Geld, 
das Sie sich leisten können zu verlieren.
"""
        
        try:
            guide_path = os.path.join(self.script_dir, "Bitcoin_Trading_Benutzerhandbuch.txt")
            with open(guide_path, 'w', encoding='utf-8') as f:
                f.write(guide_content)
            
            print(f"✅ Benutzerhandbuch erstellt: {guide_path}")
            return True
            
        except Exception as e:
            print(f"❌ Fehler beim Erstellen des Benutzerhandbuchs: {e}")
            return False
    
    def run_complete_installation(self):
        """Führe vollständige Installation durch"""
        print("BITCOIN TRADING SYSTEM - VOLLSTÄNDIGE INSTALLATION")
        print("="*70)
        print("Installiert Desktop-Verknüpfung, Launcher und Visualisierungen")
        print("="*70)
        
        try:
            # 1. System-Voraussetzungen prüfen
            if not self.check_system_requirements():
                print("\n❌ INSTALLATION ABGEBROCHEN - Voraussetzungen nicht erfüllt")
                return False
            
            # 2. Desktop-Verknüpfung installieren
            if not self.install_desktop_shortcut():
                print("\n⚠️ WARNUNG - Desktop-Verknüpfung konnte nicht erstellt werden")
            
            # 3. Launcher testen
            if not self.test_launchers():
                print("\n⚠️ WARNUNG - Einige Launcher funktionieren nicht korrekt")
            
            # 4. Trading-Modelle testen
            if not self.test_trading_models():
                print("\n⚠️ WARNUNG - Einige Trading-Modelle funktionieren nicht korrekt")
            
            # 5. Benutzerhandbuch erstellen
            self.create_user_guide()
            
            # 6. Erfolgs-Meldung
            print("\n" + "="*70)
            print("🎉 BITCOIN TRADING SYSTEM ERFOLGREICH INSTALLIERT!")
            print("="*70)
            print("✅ Desktop-Verknüpfung erstellt")
            print("✅ Launcher getestet")
            print("✅ Trading-Modelle verfügbar")
            print("✅ Visualisierungen eingerichtet")
            print("✅ Benutzerhandbuch erstellt")
            
            print("\n📋 NÄCHSTE SCHRITTE:")
            print("1. Doppelklicken Sie auf 'Bitcoin Trading Launcher' auf dem Desktop")
            print("2. Wählen Sie ein Trading-Modell aus")
            print("3. Klicken Sie '>> STARTEN' für Bitcoin-Analyse")
            print("4. Lesen Sie das Benutzerhandbuch für Details")
            
            print("\n🚀 VIEL ERFOLG BEIM BITCOIN TRADING!")
            
            return True
            
        except Exception as e:
            print(f"\n❌ FEHLER bei Installation: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Hauptfunktion"""
    try:
        installer = BitcoinTradingCompleteInstaller()
        success = installer.run_complete_installation()
        
        if success:
            print("\n✅ INSTALLATION ERFOLGREICH ABGESCHLOSSEN!")
        else:
            print("\n❌ INSTALLATION MIT FEHLERN ABGESCHLOSSEN!")
        
        print("\nDrücken Sie Enter um fortzufahren...")
        input()
        
    except Exception as e:
        print(f"❌ UNERWARTETER FEHLER: {e}")
        import traceback
        traceback.print_exc()
        print("\nDrücken Sie Enter um zu beenden...")
        input()

if __name__ == "__main__":
    main()
