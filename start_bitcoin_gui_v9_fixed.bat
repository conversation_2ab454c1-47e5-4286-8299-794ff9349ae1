@echo off
title Bitcoin Trading GUI V9.0 - FEHLERFREIE EDITION
color 0D

echo ================================================================================
echo BITCOIN TRADING GUI V9.0 - FEHLERFREIE EDITION STARTER
echo ================================================================================
echo.
echo Starte Bitcoin Trading GUI V9.0 FIXED...
echo.

cd /d "e:\Dev"

echo Pruefe Python Installation...
python --version
if errorlevel 1 (
    echo FEHLER: Python ist nicht installiert oder nicht im PATH!
    echo Bitte installieren Sie Python 3.8+ von https://python.org
    pause
    exit /b 1
)

echo.
echo Pruefe GUI-Pakete...
python -c "import tkinter, matplotlib, seaborn, xgboost, scipy" 2>nul
if errorlevel 1 (
    echo Installiere GUI-Pakete...
    pip install matplotlib seaborn xgboost scipy
)

echo.
echo ================================================================================
echo STARTE BITCOIN TRADING GUI V9.0 - FEHLERFREIE EDITION
echo ================================================================================
echo.

python ultimate_bitcoin_gui_v9_fixed.py

echo.
echo ================================================================================
echo Bitcoin Trading GUI V9.0 FIXED beendet.
echo ================================================================================
pause
