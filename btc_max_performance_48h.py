#!/usr/bin/env python3
"""
🚀 MAXIMUM PERFORMANCE 48H BITCOIN PREDICTION 🚀
================================================
Nutzt ALLE verfügbaren CPU-Kerne und maximale Rechenleistung
"""

import os
import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge
import yfinance as yf
from multiprocessing import Pool, cpu_count
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import joblib
from numba import jit, prange
import psutil

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

# MAXIMUM PERFORMANCE KONFIGURATION
MAX_CORES = cpu_count()
MAX_MEMORY = psutil.virtual_memory().total // (1024**3)  # GB
PARALLEL_JOBS = MAX_CORES
MONTE_CARLO_SIMS = 2000  # Erhöht für bessere Genauigkeit

print("🚀 MAXIMUM PERFORMANCE 48H BITCOIN PREDICTION")
print("=" * 50)
print(f"💻 CPU Kerne: {MAX_CORES}")
print(f"🧠 RAM: {MAX_MEMORY}GB")
print(f"⚡ Parallel Jobs: MAXIMUM")
print(f"🔮 Monte Carlo: {MONTE_CARLO_SIMS} Simulationen")
print(f"🕐 Start: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def get_bitcoin_data_parallel():
    """Parallele Datensammlung"""
    print("📊 Lade Bitcoin-Daten mit maximaler Performance...")
    
    try:
        # Parallele API-Calls
        with ThreadPoolExecutor(max_workers=4) as executor:
            btc = yf.Ticker("BTC-USD")
            future = executor.submit(btc.history, period="3mo", interval="1h")
            df = future.result(timeout=30)
        
        if len(df) > 100:
            df.columns = [col.lower() for col in df.columns]
            print(f"✅ Echte Bitcoin-Daten: {len(df)} Stunden")
            print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
            return df, True
        else:
            raise Exception("Zu wenig Daten")
            
    except Exception as e:
        print(f"⚠️ API-Fehler, generiere High-Performance Daten...")
        
        # Hochperformante Datengeneration
        end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        start_time = end_time - timedelta(days=90)
        dates = pd.date_range(start=start_time, end=end_time, freq='H')
        
        # Vectorized operations für Geschwindigkeit
        n_points = len(dates)
        np.random.seed(42)
        
        base_price = 67000
        trend = np.linspace(-5000, 8000, n_points)
        daily_vol = np.random.normal(0, 2000, n_points)
        weekly_cycle = 1500 * np.sin(2 * np.pi * np.arange(n_points) / (24 * 7))
        monthly_cycle = 3000 * np.sin(2 * np.pi * np.arange(n_points) / (24 * 30))
        
        # Market events (parallel generation)
        events = np.random.choice([0, 1], n_points, p=[0.995, 0.005])
        event_impact = events * np.random.normal(0, 10000, n_points)
        
        prices = base_price + trend + daily_vol + weekly_cycle + monthly_cycle + event_impact
        prices = np.maximum(prices, 25000)
        
        # Vectorized OHLCV generation
        high_mult = np.random.uniform(1.001, 1.05, n_points)
        low_mult = np.random.uniform(0.95, 0.999, n_points)
        open_mult = np.random.uniform(0.98, 1.02, n_points)
        volume_base = np.random.lognormal(15, 0.4, n_points)
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * high_mult,
            'low': prices * low_mult,
            'open': prices * open_mult,
            'volume': volume_base
        }, index=dates)
        
        print(f"✅ High-Performance Daten: {len(df)} Stunden")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
        return df, False

@jit(nopython=True, parallel=True)
def fast_rolling_calculations(prices, windows):
    """Ultra-schnelle Rolling-Berechnungen mit Numba"""
    n = len(prices)
    results = np.zeros((n, len(windows)))
    
    for i in prange(len(windows)):
        window = windows[i]
        for j in prange(window, n):
            results[j, i] = np.mean(prices[j-window:j])
    
    return results

def create_max_performance_features(df):
    """Maximum Performance Feature Engineering"""
    print("🔧 Erstelle Features mit maximaler CPU-Auslastung...")
    
    df = df.copy()
    
    # Parallele Feature-Berechnung
    with ProcessPoolExecutor(max_workers=MAX_CORES) as executor:
        
        # === TREND FEATURES (parallel) ===
        def calculate_trend_features(data):
            close_prices = data['close'].values
            windows = np.array([6, 12, 24, 48, 72, 168])
            
            # Ultra-schnelle Rolling-Berechnungen
            sma_results = fast_rolling_calculations(close_prices, windows)
            
            features = {}
            for i, window in enumerate(windows):
                features[f'sma_{window}'] = sma_results[:, i]
                features[f'ema_{window}'] = data['close'].ewm(span=window).mean().values
                features[f'price_vs_sma_{window}'] = close_prices / sma_results[:, i] - 1
            
            return features
        
        # === MOMENTUM FEATURES (parallel) ===
        def calculate_momentum_features(data):
            features = {}
            
            # RSI mit verschiedenen Perioden (vectorized)
            for period in [6, 14, 24, 48]:
                delta = data['close'].diff()
                gain = delta.where(delta > 0, 0).rolling(window=period).mean()
                loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
                rs = gain / loss
                features[f'rsi_{period}'] = 100 - (100 / (1 + rs))
            
            # MACD Familie
            ema_12 = data['close'].ewm(span=12).mean()
            ema_26 = data['close'].ewm(span=26).mean()
            ema_9 = data['close'].ewm(span=9).mean()
            
            features['macd'] = ema_12 - ema_26
            features['macd_signal'] = features['macd'].ewm(span=9).mean()
            features['macd_histogram'] = features['macd'] - features['macd_signal']
            features['macd_slope'] = features['macd'].diff()
            
            return features
        
        # === VOLATILITY FEATURES (parallel) ===
        def calculate_volatility_features(data):
            features = {}
            
            # Bollinger Bands (vectorized)
            for window in [12, 20, 24, 48]:
                bb_middle = data['close'].rolling(window=window).mean()
                bb_std = data['close'].rolling(window=window).std()
                features[f'bb_upper_{window}'] = bb_middle + 2 * bb_std
                features[f'bb_lower_{window}'] = bb_middle - 2 * bb_std
                features[f'bb_width_{window}'] = features[f'bb_upper_{window}'] - features[f'bb_lower_{window}']
                features[f'bb_position_{window}'] = (data['close'] - features[f'bb_lower_{window}']) / features[f'bb_width_{window}']
            
            # Volatilität Features
            for window in [6, 12, 24, 48, 72]:
                features[f'volatility_{window}'] = data['close'].rolling(window=window).std()
                features[f'volatility_ratio_{window}'] = features[f'volatility_{window}'] / data['close']
            
            return features
        
        # Parallele Ausführung
        futures = [
            executor.submit(calculate_trend_features, df),
            executor.submit(calculate_momentum_features, df),
            executor.submit(calculate_volatility_features, df)
        ]
        
        # Ergebnisse sammeln
        for future in futures:
            feature_dict = future.result()
            for name, values in feature_dict.items():
                df[name] = values
    
    # === PRICE ACTION FEATURES (vectorized) ===
    # Price Changes (alle auf einmal)
    periods = [1, 3, 6, 12, 24, 48]
    for period in periods:
        df[f'price_change_{period}'] = df['close'].pct_change(periods=period)
        df[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
    
    # === HIGH-LOW FEATURES ===
    if 'high' in df.columns and 'low' in df.columns:
        df['hl_ratio'] = df['high'] / df['low']
        df['price_range'] = df['high'] - df['low']
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        # True Range und ATR (vectorized)
        df['tr'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                np.abs(df['high'] - df['close'].shift()),
                np.abs(df['low'] - df['close'].shift())
            )
        )
        for window in [14, 24, 48]:
            df[f'atr_{window}'] = df['tr'].rolling(window=window).mean()
    
    # === VOLUME FEATURES ===
    if 'volume' in df.columns:
        for window in [12, 24, 48]:
            df[f'volume_sma_{window}'] = df['volume'].rolling(window=window).mean()
            df[f'volume_ratio_{window}'] = df['volume'] / df[f'volume_sma_{window}']
        
        # Volume-Price Trend (vectorized)
        df['vpt'] = (df['volume'] * df['close'].pct_change()).cumsum()
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()
    
    # === LAG FEATURES (vectorized) ===
    lag_periods = [1, 2, 3, 6, 12, 24, 48]
    for lag in lag_periods:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
        if 'volume' in df.columns:
            df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
        if 'rsi_14' in df.columns:
            df[f'rsi_lag_{lag}'] = df['rsi_14'].shift(lag)
    
    # === ROLLING STATISTICS (parallel) ===
    with ThreadPoolExecutor(max_workers=MAX_CORES) as executor:
        def calc_rolling_stats(window):
            stats = {}
            stats[f'close_mean_{window}'] = df['close'].rolling(window=window).mean()
            stats[f'close_std_{window}'] = df['close'].rolling(window=window).std()
            stats[f'close_min_{window}'] = df['close'].rolling(window=window).min()
            stats[f'close_max_{window}'] = df['close'].rolling(window=window).max()
            stats[f'close_skew_{window}'] = df['close'].rolling(window=window).skew()
            stats[f'close_kurt_{window}'] = df['close'].rolling(window=window).kurt()
            return stats
        
        futures = [executor.submit(calc_rolling_stats, w) for w in [6, 12, 24, 48, 72]]
        for future in futures:
            stats = future.result()
            for name, values in stats.items():
                df[name] = values
    
    # === TIME FEATURES (vectorized) ===
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['month'] = df.index.month
    df['quarter'] = df.index.quarter
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    df['is_trading_hours'] = ((df.index.hour >= 9) & (df.index.hour <= 16)).astype(int)
    
    # Cyclical encoding (vectorized)
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
    df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
    
    # === MARKET REGIME FEATURES ===
    # Trend Strength (parallel)
    def calc_trend_strength(prices, window=24):
        return np.abs(np.polyfit(range(window), prices, 1)[0]) if len(prices) == window else 0
    
    df['trend_strength'] = df['close'].rolling(window=24).apply(calc_trend_strength)
    
    # Market Regime
    short_ma = df['close'].rolling(window=12).mean()
    long_ma = df['close'].rolling(window=48).mean()
    df['market_regime'] = np.where(short_ma > long_ma * 1.02, 1,
                                  np.where(short_ma < long_ma * 0.98, -1, 0))
    
    # Volatility Regime
    df['vol_regime'] = df['volatility_24'].rolling(window=48).rank(pct=True)
    
    print(f"✅ Maximum Performance Features: {df.shape[1]} Spalten")
    return df.dropna()

def prepare_max_performance_data(df, sequence_length=96):  # Erhöht für bessere Genauigkeit
    """Maximum Performance Datenvorbereitung"""
    print(f"🔄 Bereite Daten mit maximaler Performance vor...")
    
    feature_cols = [col for col in df.columns if col != 'close']
    features = df[feature_cols].values
    target = df['close'].values
    
    # Parallele Skalierung
    with ThreadPoolExecutor(max_workers=2) as executor:
        feature_scaler = RobustScaler()
        target_scaler = MinMaxScaler()
        
        future_features = executor.submit(feature_scaler.fit_transform, features)
        future_target = executor.submit(target_scaler.fit_transform, target.reshape(-1, 1))
        
        features_scaled = future_features.result()
        target_scaled = future_target.result().flatten()
    
    # Parallele Sequenz-Erstellung
    def create_sequences_parallel(start_idx, end_idx):
        X_part, y_part = [], []
        for i in range(start_idx, end_idx):
            if i >= sequence_length:
                X_part.append(features_scaled[i-sequence_length:i])
                y_part.append(target_scaled[i])
        return np.array(X_part), np.array(y_part)
    
    # Aufteilen in Chunks für parallele Verarbeitung
    n_points = len(features_scaled)
    chunk_size = n_points // MAX_CORES
    
    with ProcessPoolExecutor(max_workers=MAX_CORES) as executor:
        futures = []
        for i in range(MAX_CORES):
            start_idx = i * chunk_size
            end_idx = (i + 1) * chunk_size if i < MAX_CORES - 1 else n_points
            futures.append(executor.submit(create_sequences_parallel, start_idx, end_idx))
        
        # Ergebnisse sammeln
        X_parts, y_parts = [], []
        for future in futures:
            X_part, y_part = future.result()
            if len(X_part) > 0:
                X_parts.append(X_part)
                y_parts.append(y_part)
    
    X = np.vstack(X_parts)
    y = np.hstack(y_parts)
    
    # Train/Test Split
    train_size = int(len(X) * 0.8)
    val_size = int(len(X) * 0.1)
    
    X_train = X[:train_size]
    y_train = y[:train_size]
    X_val = X[train_size:train_size+val_size]
    y_val = y[train_size:train_size+val_size]
    X_test = X[train_size+val_size:]
    y_test = y[train_size+val_size:]
    
    last_sequence = features_scaled[-sequence_length:]
    
    print(f"✅ Maximum Performance Daten:")
    print(f"   Train: {X_train.shape}")
    print(f"   Validation: {X_val.shape}")
    print(f"   Test: {X_test.shape}")
    print(f"   Features: {X_train.shape[2]}")
    
    return (X_train, y_train), (X_val, y_val), (X_test, y_test), last_sequence, (feature_scaler, target_scaler)

def train_max_performance_models(train_data, val_data, test_data):
    """Maximum Performance Model Training mit allen CPU-Kernen"""
    print(f"\n🚀 Trainiere Modelle mit MAXIMALER CPU-Auslastung ({MAX_CORES} Kerne)...")

    X_train, y_train = train_data
    X_val, y_val = val_data
    X_test, y_test = test_data

    # Flatten für ML Modelle
    X_train_flat = X_train.reshape(X_train.shape[0], -1)
    X_val_flat = X_val.reshape(X_val.shape[0], -1)
    X_test_flat = X_test.reshape(X_test.shape[0], -1)

    # Maximum Performance Modelle mit allen verfügbaren Kernen
    models = {
        'ExtraTrees_MAX': ExtraTreesRegressor(
            n_estimators=500,  # Erhöht für bessere Performance
            max_depth=20,
            min_samples_split=2,
            min_samples_leaf=1,
            n_jobs=PARALLEL_JOBS,  # ALLE Kerne
            random_state=42,
            bootstrap=True
        ),
        'RandomForest_MAX': RandomForestRegressor(
            n_estimators=300,
            max_depth=25,
            min_samples_split=3,
            min_samples_leaf=1,
            n_jobs=PARALLEL_JOBS,  # ALLE Kerne
            random_state=42,
            bootstrap=True
        ),
        'GradientBoosting_MAX': GradientBoostingRegressor(
            n_estimators=200,
            learning_rate=0.1,
            max_depth=8,
            subsample=0.9,
            random_state=42
        ),
        'Ridge_MAX': Ridge(
            alpha=0.1,
            solver='auto'
        )
    }

    results = {}

    # Paralleles Training mehrerer Modelle
    def train_single_model(model_name, model):
        print(f"🤖 Trainiere {model_name} mit maximaler Power...")

        start_time = time.time()

        # Training mit allen verfügbaren Ressourcen
        if hasattr(model, 'n_jobs'):
            model.n_jobs = PARALLEL_JOBS

        model.fit(X_train_flat, y_train)
        training_time = time.time() - start_time

        # Vorhersagen
        y_pred_val = model.predict(X_val_flat)
        y_pred_test = model.predict(X_test_flat)

        # Metriken
        mse_val = mean_squared_error(y_val, y_pred_val)
        mse_test = mean_squared_error(y_test, y_pred_test)
        mae_test = mean_absolute_error(y_test, y_pred_test)
        r2_test = r2_score(y_test, y_pred_test)

        return {
            'model': model,
            'training_time': training_time,
            'mse_val': mse_val,
            'mse_test': mse_test,
            'mae_test': mae_test,
            'rmse_test': np.sqrt(mse_test),
            'r2_test': r2_test,
            'y_pred_test': y_pred_test,
            'y_test': y_test
        }

    # Paralleles Training (nutzt alle CPU-Kerne optimal)
    with ProcessPoolExecutor(max_workers=min(4, MAX_CORES)) as executor:
        futures = {executor.submit(train_single_model, name, model): name
                  for name, model in models.items()}

        for future in futures:
            model_name = futures[future]
            try:
                result = future.result()
                results[model_name] = result

                print(f"✅ {model_name}: R²={result['r2_test']:.4f}, "
                      f"RMSE={result['rmse_test']:.4f}, Zeit={result['training_time']:.1f}s")

            except Exception as e:
                print(f"❌ {model_name} Fehler: {e}")

    return results

@jit(nopython=True, parallel=True)
def monte_carlo_simulation_parallel(base_sequence, n_simulations, n_steps, noise_level):
    """Ultra-schnelle parallele Monte Carlo Simulation mit Numba"""
    results = np.zeros(n_simulations)

    for sim in prange(n_simulations):
        # Noise für diese Simulation
        noise = np.random.normal(0, noise_level, base_sequence.shape)
        noisy_sequence = base_sequence + noise

        # Einfache Vorhersage-Logik (kann erweitert werden)
        trend = np.mean(noisy_sequence[-10:] - noisy_sequence[-20:-10])
        volatility = np.std(noisy_sequence[-24:])

        # Prognose basierend auf Trend und Volatilität
        prediction = noisy_sequence[-1] + trend * n_steps + np.random.normal(0, volatility)
        results[sim] = prediction

    return results

def predict_max_performance_48h(best_models, last_sequence, target_scaler, current_time, current_price):
    """Maximum Performance 48h Vorhersage mit parallelen Monte Carlo Simulationen"""
    print(f"🔮 Erstelle 48h Vorhersage mit MAXIMALER Performance...")
    print(f"   Monte Carlo Simulationen: {MONTE_CARLO_SIMS}")
    print(f"   Parallele Verarbeitung: {MAX_CORES} Kerne")

    # Detaillierte Zeitpunkte
    detailed_hours = list(range(1, 49))  # Alle Stunden von 1-48
    key_hours = [1, 3, 6, 12, 18, 24, 30, 36, 42, 48]  # Wichtige Zeitpunkte

    predictions = {}

    # Parallele Vorhersage für alle Zeitpunkte
    def predict_single_hour(hour):
        print(f"📈 Berechne +{hour}h mit {MONTE_CARLO_SIMS} Simulationen...")

        all_model_predictions = []

        # Für jedes Modell
        for model_name, model_data in best_models.items():
            model = model_data['model']

            # Monte Carlo Simulationen für dieses Modell
            model_predictions = []

            # Batch-Verarbeitung für Geschwindigkeit
            batch_size = MONTE_CARLO_SIMS // MAX_CORES

            with ProcessPoolExecutor(max_workers=MAX_CORES) as executor:
                futures = []

                for batch in range(MAX_CORES):
                    start_sim = batch * batch_size
                    end_sim = (batch + 1) * batch_size if batch < MAX_CORES - 1 else MONTE_CARLO_SIMS
                    n_sims_batch = end_sim - start_sim

                    future = executor.submit(run_simulation_batch,
                                           model, last_sequence, hour, n_sims_batch)
                    futures.append(future)

                # Ergebnisse sammeln
                for future in futures:
                    batch_predictions = future.result()
                    model_predictions.extend(batch_predictions)

            all_model_predictions.extend(model_predictions)

        # Zurück transformieren
        all_predictions = np.array(all_model_predictions)
        all_predictions_orig = target_scaler.inverse_transform(all_predictions.reshape(-1, 1)).flatten()

        # Wahrscheinlichkeitsberechnung
        return {
            'datetime': current_time + timedelta(hours=hour),
            'mean': np.mean(all_predictions_orig),
            'median': np.median(all_predictions_orig),
            'std': np.std(all_predictions_orig),
            'min': np.min(all_predictions_orig),
            'max': np.max(all_predictions_orig),
            'q01': np.percentile(all_predictions_orig, 1),
            'q05': np.percentile(all_predictions_orig, 5),
            'q10': np.percentile(all_predictions_orig, 10),
            'q25': np.percentile(all_predictions_orig, 25),
            'q75': np.percentile(all_predictions_orig, 75),
            'q90': np.percentile(all_predictions_orig, 90),
            'q95': np.percentile(all_predictions_orig, 95),
            'q99': np.percentile(all_predictions_orig, 99),

            # Erweiterte Wahrscheinlichkeiten
            'prob_above_current': np.mean(all_predictions_orig > current_price) * 100,
            'prob_above_1pct': np.mean(all_predictions_orig > current_price * 1.01) * 100,
            'prob_above_2pct': np.mean(all_predictions_orig > current_price * 1.02) * 100,
            'prob_above_5pct': np.mean(all_predictions_orig > current_price * 1.05) * 100,
            'prob_above_10pct': np.mean(all_predictions_orig > current_price * 1.10) * 100,
            'prob_below_current': np.mean(all_predictions_orig < current_price) * 100,
            'prob_below_1pct': np.mean(all_predictions_orig < current_price * 0.99) * 100,
            'prob_below_2pct': np.mean(all_predictions_orig < current_price * 0.98) * 100,
            'prob_below_5pct': np.mean(all_predictions_orig < current_price * 0.95) * 100,
            'prob_below_10pct': np.mean(all_predictions_orig < current_price * 0.90) * 100,

            # Änderungen
            'change_pct': ((np.mean(all_predictions_orig) / current_price) - 1) * 100,
            'change_abs': np.mean(all_predictions_orig) - current_price,

            # Risiko-Metriken
            'var_95': np.percentile(all_predictions_orig, 5) - current_price,  # Value at Risk
            'cvar_95': np.mean(all_predictions_orig[all_predictions_orig <= np.percentile(all_predictions_orig, 5)]) - current_price,  # Conditional VaR

            # Alle Vorhersagen für weitere Analyse
            'all_predictions': all_predictions_orig
        }

    # Parallele Berechnung für wichtige Zeitpunkte
    with ProcessPoolExecutor(max_workers=MAX_CORES) as executor:
        futures = {executor.submit(predict_single_hour, hour): hour for hour in key_hours}

        for future in futures:
            hour = futures[future]
            try:
                predictions[hour] = future.result()
            except Exception as e:
                print(f"❌ Fehler bei +{hour}h: {e}")

    return predictions

def run_simulation_batch(model, last_sequence, target_hour, n_simulations):
    """Batch-Simulation für ein Modell"""
    predictions = []

    for sim in range(n_simulations):
        # Noise basierend auf Zeitraum
        noise_level = 0.01 * (target_hour / 48)
        noisy_sequence = last_sequence + np.random.normal(0, noise_level, last_sequence.shape)
        current_sequence = noisy_sequence.copy()

        # Iterative Vorhersage (optimiert)
        step_size = max(1, target_hour // 12)  # Adaptive Schrittgröße

        for step in range(0, target_hour, step_size):
            pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]

            # Realistische Constraints
            if step > 0:
                prev_price = current_sequence[-1, 0]
                max_change = 0.03 * step_size  # Max 3% pro Schritt
                pred_scaled = np.clip(pred_scaled,
                                    prev_price * (1 - max_change),
                                    prev_price * (1 + max_change))

            # Sequence aktualisieren
            new_row = current_sequence[-1].copy()
            new_row[0] = pred_scaled
            current_sequence = np.vstack([current_sequence[1:], new_row])

        # Finale Vorhersage
        final_pred_scaled = model.predict(current_sequence.reshape(1, -1))[0]
        predictions.append(final_pred_scaled)

    return predictions

def create_max_performance_visualization(df, results, predictions, current_time, current_price, is_real_data):
    """Maximum Performance Visualisierung"""
    print("📊 Erstelle Maximum Performance Visualisierung...")

    fig = plt.figure(figsize=(32, 20))
    fig.patch.set_facecolor('#0a0a0a')

    title = "🚀 MAXIMUM PERFORMANCE 48H BITCOIN PREDICTION"
    subtitle = f"📅 Ab: {current_time.strftime('%Y-%m-%d %H:%M')} | 💰 Aktuell: ${current_price:,.0f} | 🖥️ {MAX_CORES} CPU Kerne | 🧠 {MAX_MEMORY}GB RAM"
    fig.suptitle(f'{title}\n{subtitle}',
                 fontsize=22, color='white', fontweight='bold', y=0.96)

    # === 1. HAUPTCHART: Historisch + Zukunft ===
    ax1 = plt.subplot2grid((5, 6), (0, 0), colspan=4, rowspan=2)

    # Historische Daten (letzte 10 Tage)
    recent_data = df.tail(240)
    ax1.plot(recent_data.index, recent_data['close'],
             color='#00D4FF', linewidth=3, label='Historischer Preis', alpha=0.9)

    # Jetzt markieren
    ax1.axvline(x=current_time, color='#FF6B6B', linestyle='-', linewidth=4,
                label='JETZT', alpha=0.9)

    # Zukunftsprognose mit allen Konfidenzintervallen
    future_times = [pred['datetime'] for pred in predictions.values()]
    future_means = [pred['mean'] for pred in predictions.values()]
    future_q01 = [pred['q01'] for pred in predictions.values()]
    future_q05 = [pred['q05'] for pred in predictions.values()]
    future_q25 = [pred['q25'] for pred in predictions.values()]
    future_q75 = [pred['q75'] for pred in predictions.values()]
    future_q95 = [pred['q95'] for pred in predictions.values()]
    future_q99 = [pred['q99'] for pred in predictions.values()]

    # Prognose-Linie
    ax1.plot(future_times, future_means, color='#FFD700', linewidth=5,
             label='48h Prognose (Mittelwert)', alpha=0.9, marker='o', markersize=8)

    # Mehrere Konfidenzintervalle
    ax1.fill_between(future_times, future_q01, future_q99,
                     color='#FFD700', alpha=0.1, label='98% Konfidenz')
    ax1.fill_between(future_times, future_q05, future_q95,
                     color='#FFD700', alpha=0.15, label='90% Konfidenz')
    ax1.fill_between(future_times, future_q25, future_q75,
                     color='#FFD700', alpha=0.25, label='50% Konfidenz')

    # Wichtige Zeitpunkte markieren
    for hour in [6, 12, 24, 36, 48]:
        if hour in predictions:
            pred = predictions[hour]
            ax1.axvline(x=pred['datetime'], color='#FF9500', linestyle='--',
                       alpha=0.7, linewidth=2)
            ax1.text(pred['datetime'], ax1.get_ylim()[1]*0.98, f'+{hour}h',
                    rotation=90, color='#FF9500', fontsize=12, ha='center', fontweight='bold')

    ax1.set_title('Bitcoin Preis: Vergangenheit → ZUKUNFT (Maximum Performance)',
                  fontsize=18, color='white', fontweight='bold')
    ax1.set_ylabel('Preis (USD)', color='white', fontsize=14)
    ax1.legend(loc='upper left', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(colors='white', labelsize=12)

    # X-Achse formatieren
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax1.xaxis.set_major_locator(mdates.HourLocator(interval=12))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

    # === 2. ERWEITERTE WAHRSCHEINLICHKEITS-TABELLE ===
    ax2 = plt.subplot2grid((5, 6), (0, 4), colspan=2, rowspan=2)
    ax2.axis('off')

    table_data = []
    headers = ['Zeit', 'Preis', 'Änderung', 'Wahrsch. ↑', 'Wahrsch. +5%', 'VaR 95%']

    key_hours = [6, 12, 24, 36, 48]
    for hour in key_hours:
        if hour in predictions:
            pred = predictions[hour]

            table_data.append([
                f"+{hour}h",
                f"${pred['mean']:,.0f}",
                f"{pred['change_pct']:+.1f}%",
                f"{pred['prob_above_current']:.0f}%",
                f"{pred['prob_above_5pct']:.0f}%",
                f"${pred['var_95']:,.0f}"
            ])

    table = ax2.table(cellText=table_data, colLabels=headers,
                     cellLoc='center', loc='center',
                     colWidths=[0.12, 0.18, 0.15, 0.15, 0.15, 0.15])

    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2.5)

    # Erweiterte Tabellen-Styling
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#333333')
        table[(0, i)].set_text_props(weight='bold', color='white')

    for i in range(1, len(table_data) + 1):
        for j in range(len(headers)):
            table[(i, j)].set_facecolor('#1a1a1a')
            table[(i, j)].set_text_props(color='white')

            # Erweiterte Farbkodierung
            if j == 2:  # Änderung %
                change_val = float(table_data[i-1][j].replace('%', '').replace('+', ''))
                if change_val > 3:
                    table[(i, j)].set_facecolor('#2d5a2d')
                elif change_val > 0:
                    table[(i, j)].set_facecolor('#3d4a2d')
                elif change_val > -3:
                    table[(i, j)].set_facecolor('#4a3d2d')
                else:
                    table[(i, j)].set_facecolor('#5a2d2d')
            elif j in [3, 4]:  # Wahrscheinlichkeiten
                prob_val = float(table_data[i-1][j].replace('%', ''))
                if prob_val > 70:
                    table[(i, j)].set_facecolor('#2d5a2d')
                elif prob_val > 50:
                    table[(i, j)].set_facecolor('#3d4a2d')
                elif prob_val > 30:
                    table[(i, j)].set_facecolor('#4a4a2d')
                else:
                    table[(i, j)].set_facecolor('#5a3d2d')

    ax2.set_title('Erweiterte Wahrscheinlichkeits-Analyse', color='white', fontweight='bold', fontsize=14)

    # === 3. PERFORMANCE DASHBOARD ===
    ax3 = plt.subplot2grid((5, 6), (2, 0), colspan=2)

    # Modell Performance Ranking
    model_names = list(results.keys())
    r2_scores = [results[name]['r2_test'] for name in model_names]
    training_times = [results[name]['training_time'] for name in model_names]

    # Bubble Chart: R² vs Training Time
    colors = ['#00FF00', '#90EE90', '#FFD700', '#FF9500']
    sizes = [r2 * 1000 for r2 in r2_scores]  # Größe basierend auf R²

    scatter = ax3.scatter(training_times, r2_scores, s=sizes, c=colors[:len(model_names)],
                         alpha=0.7, edgecolors='white', linewidth=2)

    # Labels für jeden Punkt
    for i, name in enumerate(model_names):
        ax3.annotate(name.replace('_MAX', ''),
                    (training_times[i], r2_scores[i]),
                    xytext=(5, 5), textcoords='offset points',
                    color='white', fontsize=10, fontweight='bold')

    ax3.set_title('Modell Performance Dashboard', color='white', fontweight='bold', fontsize=14)
    ax3.set_xlabel('Training Zeit (s)', color='white', fontsize=12)
    ax3.set_ylabel('R² Score', color='white', fontsize=12)
    ax3.grid(True, alpha=0.3)
    ax3.tick_params(colors='white', labelsize=10)

    return fig

def main():
    """Hauptfunktion - MAXIMUM PERFORMANCE"""
    print("\n🚀" * 30)
    print("MAXIMUM PERFORMANCE 48H BITCOIN PREDICTION")
    print("🚀" * 30)

    start_time = time.time()

    try:
        # 1. Daten laden (parallel)
        print("\n" + "="*70)
        print("PHASE 1: MAXIMUM PERFORMANCE DATENSAMMLUNG")
        print("="*70)
        df, is_real_data = get_bitcoin_data_parallel()
        current_time = df.index[-1]
        current_price = df['close'].iloc[-1]

        # 2. Features (parallel)
        print("\n" + "="*70)
        print("PHASE 2: MAXIMUM PERFORMANCE FEATURE ENGINEERING")
        print("="*70)
        df_features = create_max_performance_features(df)

        # 3. Daten vorbereiten (parallel)
        print("\n" + "="*70)
        print("PHASE 3: MAXIMUM PERFORMANCE DATENAUFBEREITUNG")
        print("="*70)
        train_data, val_data, test_data, last_sequence, scalers = prepare_max_performance_data(df_features)
        feature_scaler, target_scaler = scalers

        # 4. Modelle trainieren (parallel)
        print("\n" + "="*70)
        print("PHASE 4: MAXIMUM PERFORMANCE MODEL TRAINING")
        print("="*70)
        results = train_max_performance_models(train_data, val_data, test_data)

        if not results:
            print("❌ Keine Modelle erfolgreich trainiert!")
            return None

        # 5. Beste Modelle auswählen
        sorted_results = sorted(results.items(), key=lambda x: x[1]['r2_test'], reverse=True)
        best_models = dict(sorted_results[:3])  # Top 3 Modelle

        print(f"\n🏆 Top 3 Modelle für Maximum Performance Vorhersage:")
        for name, result in best_models.items():
            print(f"   {name}: R²={result['r2_test']:.4f}, Zeit={result['training_time']:.1f}s")

        # 6. MAXIMUM PERFORMANCE 48h Vorhersage
        print("\n" + "="*70)
        print("PHASE 5: MAXIMUM PERFORMANCE 48H VORHERSAGE")
        print("="*70)

        predictions = predict_max_performance_48h(
            best_models, last_sequence, target_scaler, current_time, current_price
        )

        # 7. Visualisierung
        print("\n" + "="*70)
        print("PHASE 6: MAXIMUM PERFORMANCE VISUALISIERUNG")
        print("="*70)

        fig = create_max_performance_visualization(
            df_features, results, predictions, current_time, current_price, is_real_data
        )

        # 8. Zusammenfassung
        total_time = time.time() - start_time
        print_max_performance_summary(results, predictions, current_time, current_price, total_time, is_real_data)

        # Speichern
        os.makedirs('ultimate_plots', exist_ok=True)
        filename = 'max_performance_48h_prediction.png'
        plt.savefig(f'ultimate_plots/{filename}',
                    facecolor='#0a0a0a', dpi=300, bbox_inches='tight')

        print(f"✅ Maximum Performance Visualisierung: ultimate_plots/{filename}")
        plt.show()

        print(f"\n🎉 MAXIMUM PERFORMANCE 48H ANALYSE ABGESCHLOSSEN in {total_time:.1f}s! 🎉")

        return {
            'results': results,
            'predictions': predictions,
            'current_time': current_time,
            'current_price': current_price,
            'total_time': total_time,
            'cpu_cores_used': MAX_CORES,
            'monte_carlo_sims': MONTE_CARLO_SIMS
        }

    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()
        return None

def print_max_performance_summary(results, predictions, current_time, current_price, total_time, is_real_data):
    """Maximum Performance Zusammenfassung"""
    print("\n" + "="*90)
    print("🚀 MAXIMUM PERFORMANCE 48H BITCOIN PREDICTION RESULTS 🚀")
    print("="*90)

    data_type = "ECHTE LIVE-DATEN" if is_real_data else "HIGH-PERFORMANCE SIMULIERTE DATEN"
    print(f"\n📊 DATENQUELLE: {data_type}")
    print(f"📅 PROGNOSE AB: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💰 AKTUELLER PREIS: ${current_price:,.2f}")
    print(f"💻 CPU KERNE GENUTZT: {MAX_CORES}")
    print(f"🧠 RAM VERFÜGBAR: {MAX_MEMORY}GB")
    print(f"🔮 MONTE CARLO SIMS: {MONTE_CARLO_SIMS}")

    # Performance Statistiken
    best_model = max(results.keys(), key=lambda x: results[x]['r2_test'])
    print(f"\n🏆 BESTES MODELL: {best_model}")
    print(f"   R² Score: {results[best_model]['r2_test']:.4f} ({results[best_model]['r2_test']*100:.1f}%)")
    print(f"   RMSE: {results[best_model]['rmse_test']:.4f}")
    print(f"   Training Zeit: {results[best_model]['training_time']:.1f}s")

    # Alle Modell-Performances
    print(f"\n📊 ALLE MODELL-PERFORMANCES:")
    print(f"{'Modell':<25} | {'R²':<8} | {'RMSE':<8} | {'Zeit':<8}")
    print("-" * 60)
    for model_name, result in results.items():
        print(f"{model_name:<25} | {result['r2_test']:<8.4f} | {result['rmse_test']:<8.4f} | {result['training_time']:<8.1f}s")

    # Erweiterte 48h Vorhersagen
    print(f"\n🔮 MAXIMUM PERFORMANCE 48H VORHERSAGEN:")
    print(f"{'Zeit':<6} | {'Preis':<12} | {'Änderung':<10} | {'Wahrsch. ↑':<12} | {'Wahrsch. +5%':<12} | {'VaR 95%':<12}")
    print("-" * 80)

    for hour in [6, 12, 24, 36, 48]:
        if hour in predictions:
            pred = predictions[hour]
            print(f"{hour:>4}h | ${pred['mean']:>10,.0f} | {pred['change_pct']:>+7.1f}% | "
                  f"{pred['prob_above_current']:>10.0f}% | {pred['prob_above_5pct']:>10.0f}% | "
                  f"${pred['var_95']:>10,.0f}")

    # 48h Spezial-Analyse
    if 48 in predictions:
        pred_48h = predictions[48]
        print(f"\n🎯 48H MAXIMUM PERFORMANCE ANALYSE:")
        print(f"   Erwarteter Preis: ${pred_48h['mean']:,.0f}")
        print(f"   Änderung: {pred_48h['change_pct']:+.1f}%")
        print(f"   Konfidenz (99%): ${pred_48h['q01']:,.0f} - ${pred_48h['q99']:,.0f}")
        print(f"   Konfidenz (90%): ${pred_48h['q05']:,.0f} - ${pred_48h['q95']:,.0f}")
        print(f"   Wahrsch. Gewinn >1%: {pred_48h['prob_above_1pct']:.0f}%")
        print(f"   Wahrsch. Gewinn >5%: {pred_48h['prob_above_5pct']:.0f}%")
        print(f"   Wahrsch. Gewinn >10%: {pred_48h['prob_above_10pct']:.0f}%")
        print(f"   Value at Risk (95%): ${pred_48h['var_95']:,.0f}")
        print(f"   Conditional VaR (95%): ${pred_48h['cvar_95']:,.0f}")

        # Trading-Empfehlung basierend auf erweiterten Metriken
        prob_up = pred_48h['prob_above_current']
        prob_5pct = pred_48h['prob_above_5pct']
        change_48h = pred_48h['change_pct']

        if prob_up > 75 and prob_5pct > 40 and change_48h > 3:
            recommendation = "STARKER KAUF 🚀🚀"
        elif prob_up > 65 and change_48h > 2:
            recommendation = "KAUF 📈"
        elif prob_up > 55:
            recommendation = "SCHWACHER KAUF 📈"
        elif prob_up > 45:
            recommendation = "HALTEN ⚖️"
        elif prob_up > 35:
            recommendation = "SCHWACHER VERKAUF 📉"
        elif prob_up > 25:
            recommendation = "VERKAUF 📉"
        else:
            recommendation = "STARKER VERKAUF 🔻🔻"

        print(f"\n💡 MAXIMUM PERFORMANCE TRADING-EMPFEHLUNG: {recommendation}")
        print(f"   (Basierend auf {prob_up:.0f}% Aufwärts-Wahrscheinlichkeit)")

    print(f"\n⚡ PERFORMANCE-STATISTIKEN:")
    print(f"   Gesamtzeit: {total_time:.1f}s")
    print(f"   CPU-Auslastung: MAXIMUM ({MAX_CORES} Kerne)")
    print(f"   Monte Carlo Simulationen: {MONTE_CARLO_SIMS:,}")
    print(f"   Parallele Verarbeitung: AKTIVIERT")

    print("="*90)

if __name__ == "__main__":
    main()
