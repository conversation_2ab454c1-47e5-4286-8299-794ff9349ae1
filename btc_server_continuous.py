#!/usr/bin/env python3
"""
BITCOIN PREDICTION SERVER - CONTINUOUS CALCULATION
Sammelt kontinuierlich Daten und berechnet neue Prognosen
Läuft 24/7 auf Server mit automatischen Updates
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers.legacy import Adam
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, r2_score
import sqlite3
import json
import time
import schedule
import threading
import requests
import os
from datetime import datetime, timedelta
import logging
import pickle
import warnings
warnings.filterwarnings('ignore')

# LOGGING SETUP
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('btc_server.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# SERVER KONFIGURATION
SERVER_CONFIG = {
    'update_interval_minutes': 15,      # Alle 15 Minuten neue Daten
    'retrain_interval_hours': 6,        # Alle 6 Stunden neu trainieren
    'backup_interval_hours': 24,        # Täglich Backup
    'max_data_points': 10000,          # Maximale Datenpunkte in DB
    'prediction_horizons': [1, 6, 12, 24, 48, 72],  # Stunden
    'model_ensemble_size': 5,           # Anzahl Modelle im Ensemble
    'api_endpoints': {
        'binance': 'https://api.binance.com/api/v3/klines',
        'coinbase': 'https://api.exchange.coinbase.com/products/BTC-USD/candles'
    },
    'database_file': 'btc_predictions.db',
    'models_dir': 'models',
    'data_dir': 'data',
    'results_dir': 'results'
}

class BitcoinDataCollector:
    """Kontinuierliche Datensammlung von verschiedenen APIs"""
    
    def __init__(self):
        self.ensure_directories()
        self.init_database()
    
    def ensure_directories(self):
        """Erstelle notwendige Verzeichnisse"""
        for dir_name in ['models', 'data', 'results', 'backups']:
            os.makedirs(dir_name, exist_ok=True)
    
    def init_database(self):
        """Initialisiere SQLite Datenbank"""
        conn = sqlite3.connect(SERVER_CONFIG['database_file'])
        cursor = conn.cursor()
        
        # Tabelle für Rohdaten
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS btc_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume REAL,
                source TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Tabelle für Prognosen
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                prediction_time DATETIME,
                target_time DATETIME,
                horizon_hours INTEGER,
                predicted_price REAL,
                confidence_lower REAL,
                confidence_upper REAL,
                model_version TEXT,
                accuracy_score REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Tabelle für Modell-Performance
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_name TEXT,
                training_time DATETIME,
                r2_score REAL,
                rmse REAL,
                mae REAL,
                validation_samples INTEGER,
                features_count INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("Datenbank initialisiert")
    
    def fetch_binance_data(self):
        """Hole aktuelle Daten von Binance"""
        try:
            url = SERVER_CONFIG['api_endpoints']['binance']
            params = {
                'symbol': 'BTCUSDT',
                'interval': '1h',
                'limit': 100
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            df_list = []
            
            for candle in data:
                df_list.append({
                    'timestamp': pd.to_datetime(int(candle[0]), unit='ms'),
                    'open': float(candle[1]),
                    'high': float(candle[2]),
                    'low': float(candle[3]),
                    'close': float(candle[4]),
                    'volume': float(candle[5]),
                    'source': 'binance'
                })
            
            df = pd.DataFrame(df_list)
            logger.info(f"Binance: {len(df)} Datenpunkte geholt")
            return df
            
        except Exception as e:
            logger.error(f"Fehler beim Holen von Binance-Daten: {e}")
            return pd.DataFrame()
    
    def fetch_coinbase_data(self):
        """Hole aktuelle Daten von Coinbase"""
        try:
            url = SERVER_CONFIG['api_endpoints']['coinbase']
            params = {
                'granularity': 3600,  # 1 Stunde
                'start': (datetime.now() - timedelta(days=4)).isoformat(),
                'end': datetime.now().isoformat()
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            df_list = []
            
            for candle in data:
                df_list.append({
                    'timestamp': pd.to_datetime(candle[0], unit='s'),
                    'low': float(candle[1]),
                    'high': float(candle[2]),
                    'open': float(candle[3]),
                    'close': float(candle[4]),
                    'volume': float(candle[5]),
                    'source': 'coinbase'
                })
            
            df = pd.DataFrame(df_list)
            logger.info(f"Coinbase: {len(df)} Datenpunkte geholt")
            return df
            
        except Exception as e:
            logger.error(f"Fehler beim Holen von Coinbase-Daten: {e}")
            return pd.DataFrame()
    
    def save_data_to_db(self, df):
        """Speichere Daten in Datenbank"""
        if df.empty:
            return
        
        conn = sqlite3.connect(SERVER_CONFIG['database_file'])
        
        for _, row in df.iterrows():
            # Prüfe ob Datenpunkt bereits existiert
            cursor = conn.cursor()
            cursor.execute('''
                SELECT COUNT(*) FROM btc_data 
                WHERE timestamp = ? AND source = ?
            ''', (row['timestamp'], row['source']))
            
            if cursor.fetchone()[0] == 0:
                cursor.execute('''
                    INSERT INTO btc_data (timestamp, open, high, low, close, volume, source)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (row['timestamp'], row['open'], row['high'], 
                      row['low'], row['close'], row['volume'], row['source']))
        
        conn.commit()
        conn.close()
        logger.info(f"{len(df)} Datenpunkte in DB gespeichert")
    
    def collect_all_data(self):
        """Sammle Daten von allen Quellen"""
        logger.info("Starte Datensammlung...")
        
        # Binance Daten
        binance_df = self.fetch_binance_data()
        if not binance_df.empty:
            self.save_data_to_db(binance_df)
        
        # Coinbase Daten
        coinbase_df = self.fetch_coinbase_data()
        if not coinbase_df.empty:
            self.save_data_to_db(coinbase_df)
        
        # Cleanup alte Daten
        self.cleanup_old_data()
        
        logger.info("Datensammlung abgeschlossen")
    
    def cleanup_old_data(self):
        """Entferne alte Daten um DB-Größe zu begrenzen"""
        conn = sqlite3.connect(SERVER_CONFIG['database_file'])
        cursor = conn.cursor()
        
        cursor.execute('''
            DELETE FROM btc_data 
            WHERE id NOT IN (
                SELECT id FROM btc_data 
                ORDER BY timestamp DESC 
                LIMIT ?
            )
        ''', (SERVER_CONFIG['max_data_points'],))
        
        deleted = cursor.rowcount
        conn.commit()
        conn.close()
        
        if deleted > 0:
            logger.info(f"{deleted} alte Datenpunkte entfernt")

class BitcoinPredictor:
    """Kontinuierliche Bitcoin-Prognose"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.last_training_time = None
        self.model_version = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    def load_data_from_db(self):
        """Lade Daten aus Datenbank"""
        conn = sqlite3.connect(SERVER_CONFIG['database_file'])
        
        # Aggregiere Daten von verschiedenen Quellen
        query = '''
            SELECT 
                timestamp,
                AVG(open) as open,
                AVG(high) as high,
                AVG(low) as low,
                AVG(close) as close,
                AVG(volume) as volume
            FROM btc_data 
            WHERE timestamp >= datetime('now', '-30 days')
            GROUP BY timestamp
            ORDER BY timestamp
        '''
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if not df.empty:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            logger.info(f"Daten geladen: {len(df)} Punkte")
        
        return df
    
    def create_features(self, df):
        """Erstelle Features für Modell"""
        features = df.copy()
        
        # Moving Averages
        for period in [5, 10, 20, 50]:
            features[f'sma_{period}'] = df['close'].rolling(period).mean()
            features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
        
        # MACD
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        features['macd'] = ema_12 - ema_26
        features['macd_signal'] = features['macd'].ewm(span=9).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        sma_20 = df['close'].rolling(20).mean()
        std_20 = df['close'].rolling(20).std()
        features['bb_width'] = (std_20 * 2) / sma_20
        
        # Volatilität
        features['volatility'] = df['close'].pct_change().rolling(10).std()
        
        # Volume Features
        features['volume_sma'] = df['volume'].rolling(20).mean()
        features['volume_ratio'] = df['volume'] / features['volume_sma']
        
        # Zeitbasierte Features
        features['hour'] = features.index.hour
        features['day_of_week'] = features.index.dayofweek
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        
        return features.dropna()
    
    def create_sequences(self, data, target, look_back=24):
        """Erstelle Sequenzen für LSTM"""
        X, y = [], []
        for i in range(look_back, len(data)):
            X.append(data[i-look_back:i])
            y.append(target[i])
        return np.array(X), np.array(y)
    
    def build_model(self, input_shape, model_id=0):
        """Erstelle LSTM-Modell"""
        model = Sequential([
            LSTM(64, return_sequences=True, dropout=0.2, input_shape=input_shape),
            LSTM(32, return_sequences=False, dropout=0.2),
            Dense(32, activation='relu'),
            Dropout(0.3),
            Dense(1)
        ])
        
        lr = 0.001 * (0.8 ** model_id)  # Verschiedene Learning Rates
        model.compile(optimizer=Adam(learning_rate=lr), loss='mse', metrics=['mae'])
        
        return model
    
    def train_models(self):
        """Trainiere Ensemble von Modellen"""
        logger.info("Starte Modell-Training...")
        
        # Daten laden
        df = self.load_data_from_db()
        if len(df) < 100:
            logger.warning("Zu wenig Daten für Training")
            return False
        
        # Features erstellen
        features_df = self.create_features(df)
        
        # Daten vorbereiten
        X = features_df.drop('close', axis=1)
        y = features_df['close'].values
        
        # Skalierung
        feature_scaler = MinMaxScaler()
        target_scaler = MinMaxScaler()
        
        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        
        # Sequenzen erstellen
        X_seq, y_seq = self.create_sequences(X_scaled, y_scaled)
        
        # Train-Test Split
        train_size = int(len(X_seq) * 0.8)
        X_train, X_test = X_seq[:train_size], X_seq[train_size:]
        y_train, y_test = y_seq[:train_size], y_seq[train_size:]
        
        # Trainiere Ensemble
        self.models = {}
        self.scalers = {'feature': feature_scaler, 'target': target_scaler}
        
        for i in range(SERVER_CONFIG['model_ensemble_size']):
            logger.info(f"Trainiere Modell {i+1}/{SERVER_CONFIG['model_ensemble_size']}")
            
            model = self.build_model((X_train.shape[1], X_train.shape[2]), i)
            
            history = model.fit(
                X_train, y_train,
                validation_data=(X_test, y_test),
                epochs=50,
                batch_size=32,
                verbose=0
            )
            
            # Performance bewerten
            y_pred = model.predict(X_test, verbose=0)
            y_test_orig = target_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
            y_pred_orig = target_scaler.inverse_transform(y_pred).flatten()
            
            r2 = r2_score(y_test_orig, y_pred_orig)
            rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
            
            self.models[f'model_{i}'] = model
            
            # Performance in DB speichern
            self.save_model_performance(f'model_{i}', r2, rmse, len(X_test), X.shape[1])
            
            logger.info(f"Modell {i+1}: R² = {r2:.4f}, RMSE = {rmse:.2f}")
        
        # Modelle speichern
        self.save_models()
        self.last_training_time = datetime.now()
        
        logger.info("Modell-Training abgeschlossen")
        return True
