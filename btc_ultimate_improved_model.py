#!/usr/bin/env python3
"""
🎯 ULTIMATE IMPROVED BITCOIN PREDICTION MODEL
Radikal verbessert für maximale Genauigkeit mit vorhandenen Daten
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers import <PERSON>
from sklearn.preprocessing import MinMaxScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor
import warnings
import time
warnings.filterwarnings('ignore')

print("🎯 ULTIMATE IMPROVED BITCOIN PREDICTION MODEL")
print("=" * 60)
print("🚀 Radikal verbessert für maximale Genauigkeit")
print("💡 Optimiert für vorhandene Daten ohne Overfitting")
print("=" * 60)

# RADIKAL VERBESSERTE KONFIGURATION
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.70,           # Mehr Test-Daten
    'validation_split': 0.20,      # Robuste Validation
    'test_split': 0.10,            # Ausreichend Test
    'look_back': 8,                # VIEL kürzer - weniger Overfitting
    'batch_size': 32,              # Kleinere Batches
    'epochs': 50,                  # Weniger Epochen
    'patience': 15,                # Früh stoppen
    'learning_rate': 0.01,         # HÖHERE Learning Rate
    'dropout_rate': 0.6,           # EXTREME Regularisierung
    'n_best_features': 6,          # NUR die allerbesten Features
    'target_accuracy': 0.80        # Realistisches Ziel
}

class ImprovedBitcoinPredictor:
    """Radikal verbesserter Predictor"""
    
    def __init__(self):
        self.scalers = {}
        self.selected_features = None
    
    def load_and_create_smart_features(self):
        """Nur die SMARTESTEN Features - Qualität über Quantität"""
        print("\n📊 SMART FEATURE ENGINEERING")
        print("-" * 40)
        
        df = pd.read_csv(CONFIG['data_file'])
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        print(f"📈 Daten: {len(df)} Punkte")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
        
        # Basis Features
        features = pd.DataFrame(index=df.index)
        features['close'] = df['close']
        features['volume'] = df['volume']
        features['high'] = df['high']
        features['low'] = df['low']
        
        # === NUR DIE BESTEN FEATURES ===
        print("   🎯 Erstelle nur BEWÄHRTE Features...")
        
        # 1. Returns (wichtigster Indikator)
        features['returns'] = df['close'].pct_change()
        
        # 2. Einfache Moving Averages (nur die wichtigsten)
        features['sma_5'] = df['close'].rolling(5).mean()
        features['sma_10'] = df['close'].rolling(10).mean()
        features['price_sma5_ratio'] = df['close'] / features['sma_5']
        
        # 3. RSI (Standard 14)
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # 4. Preis-Ratios
        features['hl_ratio'] = df['high'] / df['low']
        features['co_ratio'] = df['close'] / df['open']
        
        # 5. Volumen Ratio
        features['volume_sma'] = df['volume'].rolling(5).mean()
        features['volume_ratio'] = df['volume'] / features['volume_sma']
        
        # 6. Zeit-Features (nur Stunde)
        features['hour'] = df.index.hour
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        
        # Cleanup
        features = features.dropna()
        
        print(f"   ✅ {len(features.columns)} Smart Features erstellt")
        print(f"   📊 {len(features)} saubere Datenpunkte")
        
        return features
    
    def smart_feature_selection(self, features):
        """Ultra-intelligente Feature Selection"""
        print("\n🧠 SMART FEATURE SELECTION")
        print("-" * 30)
        
        X = features.drop('close', axis=1)
        y = features['close']
        
        print(f"📊 Vor Selektion: {X.shape[1]} Features")
        
        # Nur die allerbesten Features
        selector = SelectKBest(score_func=f_regression, k=CONFIG['n_best_features'])
        X_selected = selector.fit_transform(X, y)
        
        # Feature Namen
        selected_mask = selector.get_support()
        self.selected_features = X.columns[selected_mask].tolist()
        
        print(f"✅ Nach Selektion: {len(self.selected_features)} Features")
        
        # Top Features anzeigen
        feature_scores = selector.scores_[selected_mask]
        top_features = sorted(zip(self.selected_features, feature_scores), key=lambda x: x[1], reverse=True)
        
        print(f"\n🏆 TOP {len(self.selected_features)} SMART FEATURES:")
        for i, (feature, score) in enumerate(top_features):
            print(f"   {i+1}. {feature}: {score:.2f}")
        
        # Finales DataFrame
        selected_df = X[self.selected_features].copy()
        selected_df['close'] = features['close']
        
        return selected_df
    
    def create_sequences_smart(self, data, target, look_back):
        """Intelligente Sequenz-Erstellung"""
        X, y = [], []
        for i in range(look_back, len(data)):
            X.append(data[i-look_back:i])
            y.append(target[i])
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)
    
    def prepare_data_smart(self, features):
        """Smart Datenaufbereitung"""
        print("\n🔄 SMART DATENAUFBEREITUNG")
        print("-" * 25)
        
        # Feature Selection
        features_selected = self.smart_feature_selection(features)
        
        # Features und Target
        X = features_selected.drop('close', axis=1)
        y = features_selected['close'].values
        
        print(f"📊 Finale Features: {X.shape[1]}")
        print(f"📊 Samples: {len(y)}")
        
        # VERBESSERTE Skalierung - MinMaxScaler für Stabilität
        print("   🔧 Verbesserte Skalierung...")
        feature_scaler = MinMaxScaler(feature_range=(0, 1))
        target_scaler = MinMaxScaler(feature_range=(0, 1))
        
        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        
        # Sequenzen erstellen
        print(f"   📦 Erstelle Sequenzen (Look-back: {CONFIG['look_back']})...")
        X_seq, y_seq = self.create_sequences_smart(X_scaled, y_scaled, CONFIG['look_back'])
        
        print(f"   ✅ {len(X_seq)} Sequenzen erstellt")
        print(f"   📐 Sequenz-Shape: {X_seq.shape}")
        
        # Speichere Scaler
        self.scalers['feature'] = feature_scaler
        self.scalers['target'] = target_scaler
        
        return X_seq, y_seq
    
    def split_data_smart(self, X, y):
        """Intelligente Datenaufteilung"""
        print("\n✂️  SMART DATENAUFTEILUNG")
        print("-" * 20)
        
        total_size = len(X)
        train_size = int(total_size * CONFIG['train_split'])
        val_size = int(total_size * CONFIG['validation_split'])
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        X_val = X[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        X_test = X[train_size+val_size:]
        y_test = y[train_size+val_size:]
        
        print(f"📊 Training: {len(X_train)} ({len(X_train)/total_size*100:.1f}%)")
        print(f"📊 Validation: {len(X_val)} ({len(X_val)/total_size*100:.1f}%)")
        print(f"📊 Test: {len(X_test)} ({len(X_test)/total_size*100:.1f}%)")
        
        return (X_train, y_train), (X_val, y_val), (X_test, y_test)
    
    def build_improved_model(self, input_shape):
        """RADIKAL VERBESSERTES MODELL - Anti-Overfitting"""
        print("🎯 Baue RADIKAL VERBESSERTES Modell...")
        
        model = Sequential(name='ImprovedPredictor')
        
        # EINFACHE aber EFFEKTIVE Architektur
        model.add(LSTM(16, return_sequences=True, dropout=CONFIG['dropout_rate'], 
                      recurrent_dropout=CONFIG['dropout_rate'], input_shape=input_shape))
        model.add(BatchNormalization())
        
        model.add(LSTM(8, return_sequences=False, dropout=CONFIG['dropout_rate'], 
                      recurrent_dropout=CONFIG['dropout_rate']))
        model.add(BatchNormalization())
        
        # Minimale Dense Layers
        model.add(Dense(8, activation='relu'))
        model.add(Dropout(CONFIG['dropout_rate']))
        
        model.add(Dense(1, activation='sigmoid'))  # Sigmoid für 0-1 Range
        
        # VERBESSERTE Kompilierung
        optimizer = Adam(learning_rate=CONFIG['learning_rate'])
        model.compile(optimizer=optimizer, loss='mse', metrics=['mae'])  # MSE statt Huber
        
        print(f"   ✅ Improved Model: {model.count_params():,} Parameter")
        return model
    
    def train_improved_model(self, model, X_train, y_train, X_val, y_val):
        """Verbessertes Training"""
        print(f"\n🎯 IMPROVED TRAINING")
        print("-" * 20)
        
        # Verbesserte Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=CONFIG['patience'],
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.2,  # Aggressivere LR-Reduktion
                patience=CONFIG['patience']//3,
                min_lr=1e-6,
                verbose=1
            )
        ]
        
        start_time = time.time()
        
        # Training
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            callbacks=callbacks,
            verbose=1
        )
        
        training_time = time.time() - start_time
        
        print(f"   ✅ Training: {training_time:.1f}s")
        
        return model, history
    
    def evaluate_improved_model(self, model, X_test, y_test):
        """Verbesserte Evaluation"""
        print(f"\n📊 IMPROVED EVALUATION")
        print("-" * 25)
        
        # Vorhersagen
        y_pred = model.predict(X_test, verbose=0)
        
        # Skalierung rückgängig
        y_test_orig = self.scalers['target'].inverse_transform(y_test.reshape(-1, 1)).flatten()
        y_pred_orig = self.scalers['target'].inverse_transform(y_pred).flatten()
        
        # Verbesserte Metriken
        r2 = r2_score(y_test_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
        mae = mean_absolute_error(y_test_orig, y_pred_orig)
        mape = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig)) * 100
        
        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(y_pred_orig) > 0
            direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            direction_acc = 0
        
        # Accuracy Bands
        accuracy_1pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.01) * 100
        accuracy_5pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.05) * 100
        accuracy_10pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.10) * 100
        
        # Explained Variance
        explained_var = 1 - np.var(y_test_orig - y_pred_orig) / np.var(y_test_orig)
        
        results = {
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'mape': mape,
            'direction_accuracy': direction_acc,
            'accuracy_1pct': accuracy_1pct,
            'accuracy_5pct': accuracy_5pct,
            'accuracy_10pct': accuracy_10pct,
            'explained_variance': explained_var,
            'y_test_orig': y_test_orig,
            'y_pred_orig': y_pred_orig
        }
        
        print(f"   📈 R²: {r2:.4f} ({r2*100:.1f}%)")
        print(f"   💰 RMSE: ${rmse:.2f}")
        print(f"   📊 MAPE: {mape:.2f}%")
        print(f"   🎯 Direction: {direction_acc:.1f}%")
        print(f"   ✅ 1% Accuracy: {accuracy_1pct:.1f}%")
        print(f"   ✅ 5% Accuracy: {accuracy_5pct:.1f}%")
        print(f"   ✅ 10% Accuracy: {accuracy_10pct:.1f}%")
        print(f"   📊 Explained Var: {explained_var:.4f}")
        
        return results
