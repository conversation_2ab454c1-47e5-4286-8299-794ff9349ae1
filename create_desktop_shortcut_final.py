#!/usr/bin/env python3
"""
🖥️ DESKTOP-VERKNÜPFUNG FÜR FIXED FINAL LAUNCHER 🖥️
==================================================
🏆 AUTOMATISCHE DESKTOP-VERKNÜPFUNG ERSTELLER 🏆
✅ Erstellt echte Windows-Verknüpfung für Fixed Final Launcher
✅ Kopiert Batch-Datei als Backup auf Desktop
✅ Beide Optionen für maximale Kompatibilität
✅ Sofortiger Test der erstellten Verknüpfung

💡 EINFACH AUSFÜHREN UND DESKTOP-VERKNÜPFUNG IST FERTIG!
"""

import os
import sys
import shutil

def create_desktop_shortcut_final():
    """Erstelle Desktop-Verknüpfung für Fixed Final Launcher"""
    
    print("🖥️ Erstelle Desktop-Verknüpfung für Ultimate Launcher Fixed Final...")
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        # Pfade ermitteln
        current_dir = os.getcwd()
        launcher_path = os.path.join(current_dir, "ultimate_launcher_fixed_final.py")
        python_path = sys.executable
        
        # Desktop-Pfad ermitteln
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "Ultimate Bitcoin Trading Launcher FIXED FINAL.lnk")
        
        # Prüfe ob Fixed Final Launcher existiert
        if not os.path.exists(launcher_path):
            print(f"❌ FEHLER: ultimate_launcher_fixed_final.py nicht gefunden!")
            print(f"💡 Stellen Sie sicher, dass der Fixed Final Launcher im aktuellen Ordner ist.")
            return False
        
        # Erstelle Windows-Verknüpfung
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        
        # Verknüpfungs-Eigenschaften
        shortcut.Targetpath = python_path
        shortcut.Arguments = f'"{launcher_path}"'
        shortcut.WorkingDirectory = current_dir
        shortcut.Description = "Ultimate Bitcoin Trading Launcher FIXED FINAL - 100% funktionsfähig, alle 3 Modelle + Gesamtprognose"
        shortcut.IconLocation = python_path + ",0"  # Python-Icon verwenden
        
        # Speichere Verknüpfung
        shortcut.save()
        
        print(f"✅ Windows-Verknüpfung erfolgreich erstellt!")
        print(f"📍 Speicherort: {shortcut_path}")
        print(f"🎯 Ziel: {launcher_path}")
        
        return True
        
    except ImportError as e:
        print(f"💡 winshell/pywin32 nicht verfügbar - verwende Batch-Alternative")
        return False
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Windows-Verknüpfung: {e}")
        return False

def copy_batch_to_desktop():
    """Kopiere Batch-Datei auf Desktop"""
    
    print("🔧 Kopiere Batch-Datei auf Desktop...")
    
    try:
        current_dir = os.getcwd()
        
        # Desktop-Pfad ermitteln
        try:
            import winshell
            desktop = winshell.desktop()
        except:
            # Fallback für Desktop-Pfad
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        
        # Batch-Datei Pfade
        source_batch = os.path.join(current_dir, "Ultimate Bitcoin Trading Launcher FIXED FINAL.bat")
        desktop_batch = os.path.join(desktop, "Ultimate Bitcoin Trading Launcher FIXED FINAL.bat")
        
        # Prüfe ob Batch-Datei existiert
        if not os.path.exists(source_batch):
            print(f"⚠️ Batch-Datei nicht gefunden, erstelle neue...")
            create_new_batch_file(source_batch, current_dir)
        
        # Kopiere auf Desktop
        shutil.copy2(source_batch, desktop_batch)
        
        print(f"✅ Batch-Datei erfolgreich auf Desktop kopiert!")
        print(f"📍 Desktop-Speicherort: {desktop_batch}")
        print(f"🎯 Quelle: {source_batch}")
        
        return True
        
    except Exception as e:
        print(f"❌ FEHLER beim Kopieren der Batch-Datei: {e}")
        return False

def create_new_batch_file(batch_path, current_dir):
    """Erstelle neue Batch-Datei falls nicht vorhanden"""
    
    try:
        launcher_path = os.path.join(current_dir, "ultimate_launcher_fixed_final.py")
        
        # Batch-Inhalt
        batch_content = f'''@echo off
title Ultimate Bitcoin Trading Launcher FIXED FINAL - 100%% Funktionsfaehig
color 0A
echo.
echo ========================================
echo    ULTIMATE BITCOIN TRADING LAUNCHER
echo    FIXED FINAL - 100%% FUNKTIONSFAEHIG
echo ========================================
echo    Alle 3 Modelle + Gesamtprognose
echo    Robuster Konsolen-Launcher ohne GUI-Abhaengigkeiten
echo    Echtzeit-Fehlerbeschreibung + Live-Status
echo ========================================
echo.
cd /d "{current_dir}"
python "{launcher_path}"
if errorlevel 1 (
    echo.
    echo ❌ Fehler beim Starten des Fixed Final Launchers
    echo 💡 Stellen Sie sicher, dass Python installiert ist
    echo 📄 Launcher-Datei: {launcher_path}
    echo.
    pause
) else (
    echo.
    echo ✅ Fixed Final Launcher erfolgreich beendet
    echo 👋 Bis zum naechsten Mal!
    echo.
    timeout /t 3 >nul
)
'''
        
        # Schreibe Batch-Datei
        with open(batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"✅ Neue Batch-Datei erstellt: {batch_path}")
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Batch-Datei: {e}")

def check_launcher_files():
    """Prüfe verfügbare Launcher-Dateien"""
    
    print("📄 Prüfe verfügbare Launcher-Dateien...")
    
    current_dir = os.getcwd()
    launcher_files = [
        "ultimate_launcher_fixed_final.py",
        "Ultimate Bitcoin Trading Launcher FIXED FINAL.bat",
        "bitcoin_launcher_working.py",
        "ultimate_complete_bitcoin_trading_FAVORITE.py"
    ]
    
    available_files = []
    
    for file in launcher_files:
        file_path = os.path.join(current_dir, file)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            available_files.append((file, file_size))
            print(f"✅ {file} - {file_size:,} Bytes")
        else:
            print(f"❌ {file} - Nicht gefunden")
    
    if available_files:
        print(f"\n💡 {len(available_files)} Launcher-Dateien verfügbar")
        return True
    else:
        print(f"\n❌ Keine Launcher-Dateien gefunden!")
        return False

def test_desktop_shortcut():
    """Teste ob Desktop-Verknüpfung funktioniert"""
    
    print("🧪 Teste Desktop-Verknüpfung...")
    
    try:
        # Desktop-Pfad ermitteln
        try:
            import winshell
            desktop = winshell.desktop()
        except:
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        
        # Prüfe verfügbare Verknüpfungen
        shortcut_lnk = os.path.join(desktop, "Ultimate Bitcoin Trading Launcher FIXED FINAL.lnk")
        shortcut_bat = os.path.join(desktop, "Ultimate Bitcoin Trading Launcher FIXED FINAL.bat")
        
        found_shortcuts = []
        
        if os.path.exists(shortcut_lnk):
            found_shortcuts.append(("Windows-Verknüpfung (.lnk)", shortcut_lnk))
        
        if os.path.exists(shortcut_bat):
            found_shortcuts.append(("Batch-Datei (.bat)", shortcut_bat))
        
        if found_shortcuts:
            print(f"✅ {len(found_shortcuts)} Desktop-Verknüpfung(en) gefunden:")
            for shortcut_type, path in found_shortcuts:
                print(f"   📄 {shortcut_type}: {os.path.basename(path)}")
            return True
        else:
            print(f"❌ Keine Desktop-Verknüpfungen gefunden!")
            return False
            
    except Exception as e:
        print(f"❌ FEHLER beim Testen der Desktop-Verknüpfung: {e}")
        return False

def main():
    """Hauptfunktion"""
    
    print("🖥️ DESKTOP-VERKNÜPFUNG FÜR ULTIMATE LAUNCHER FIXED FINAL")
    print("=" * 80)
    print("🚀 Erstelle Desktop-Verknüpfung für ultimate_launcher_fixed_final.py...")
    print("")
    
    # Prüfe verfügbare Dateien
    files_available = check_launcher_files()
    
    if not files_available:
        print("❌ Keine Launcher-Dateien gefunden!")
        print("💡 Stellen Sie sicher, dass Sie im richtigen Verzeichnis sind.")
        return
    
    print("")
    
    # Versuche Windows-Verknüpfung zu erstellen
    shortcut_success = create_desktop_shortcut_final()
    
    # Kopiere Batch-Datei auf Desktop (als Backup)
    batch_success = copy_batch_to_desktop()
    
    print("\n" + "=" * 80)
    
    if shortcut_success or batch_success:
        print("🎉 ERFOLGREICH!")
        print("✅ Desktop-Verknüpfung wurde erstellt")
        print("🖱️ Schauen Sie auf Ihren Desktop nach:")
        if shortcut_success:
            print("   📄 'Ultimate Bitcoin Trading Launcher FIXED FINAL.lnk'")
        if batch_success:
            print("   📄 'Ultimate Bitcoin Trading Launcher FIXED FINAL.bat'")
    else:
        print("❌ FEHLER!")
        print("💡 Starten Sie den Launcher manuell mit:")
        print("   python ultimate_launcher_fixed_final.py")
    
    # Teste Desktop-Verknüpfung
    print("")
    test_success = test_desktop_shortcut()
    
    print("\n💡 ULTIMATE LAUNCHER FIXED FINAL FEATURES:")
    print("🏅 Alle 3 Bitcoin Trading Modelle:")
    print("   1. FAVORIT - Bewährt & Getestet (Session #15+)")
    print("   2. FINALE V3 - Optimiert & Effizient (300+ Features)")
    print("   3. KI-SYSTEM V3 - Innovativ & Selbstlernend (6 AI Capabilities)")
    print("🎯 Gesamtprognose-Funktionalität aus allen 3 Berechnungen")
    print("🔄 Kontinuierliche Berechnung für bessere Ergebnisse")
    print("📊 Echtzeit-Fehlerbeschreibung und Live-Status-Updates")
    print("🛑 Automatischer Script-Stop beim Beenden")
    print("🛡️ Robuster Konsolen-Launcher ohne GUI-Abhängigkeiten")
    
    print("\n🚀 VERWENDUNG:")
    print("1. Doppelklick auf Desktop-Verknüpfung")
    print("2. Wählen Sie 's1/s2/s3' für einzelne Modelle")
    print("3. Wählen Sie 'a' für alle 3 Modelle")
    print("4. Wählen Sie 'e' für Gesamtprognose")
    print("5. Wählen Sie 'c' für kontinuierliche Berechnung")
    print("6. Wählen Sie 'i' für detaillierten Status")
    print("7. Wählen Sie 'q' zum Beenden")
    
    print("\n👋 Desktop-Verknüpfung Setup abgeschlossen!")

if __name__ == "__main__":
    main()
