�

    �Veh��  �                   ��  � d Z ddlZddlZddlZddlZddlZddl	m	Z	m
Z
 ddlZddlZddl
Z
ddlZddlZddlmZmZmZmZ  e
j        d�  �         ddlmZmZ ddlmZmZ ddlmZmZmZ dd	l m!Z!m"Z" 	 ddl#Z$d
Z%n# e&$ r dZ% e'd�  �         Y nw xY w	 ddl(Z)dd
l*m+Z+ ddl,m-Z-m.Z.m/Z/ d
Z0n# e&$ r dZ0 e'd�  �         Y nw xY wddl1m2Z3 ddl4m5Z5 ddl6Z7	 ddl8m9Z: ddl;m<Z= ddl>m?Z? d
Z@n# e&$ r dZ@ e'd�  �         Y nw xY w	 ddlAZAd
ZBn# e&$ r dZB e'd�  �         Y nw xY w	 ddlCmDZDmEZE ddlFmGZG d
ZHn
# e&$ r dZHY nw xY w G d� d�  �        ZId� ZJeKdk    r eJ�   �          dS dS )u�  
ULTIMATE BITCOIN TRADING SYSTEM V7.0 - ULTIMATE EDITION
=======================================================
REVOLUTIONÄRES SYSTEM MIT MAXIMALEN OPTIMIERUNGEN
- 3D-Visualisierung und interaktive Charts
- Ensemble ML-Modelle (XGBoost + LSTM + Random Forest)
- Kontinuierliches Auto-Training System
- Sentiment-Analyse und On-Chain-Daten
- Quantitative Trading-Strategien
- Real-time Streaming und Multi-Timeframe

ULTIMATE TRADING SYSTEM V7.0 - ABSOLUTE PERFEKTION!
�    N)�datetime�	timedelta)�Dict�List�Tuple�Optional�ignore)�RobustScaler�StandardScaler)�RandomForestRegressor�GradientBoostingRegressor)�mean_squared_error�r2_score�mean_absolute_error)�TimeSeriesSplit�GridSearchCVTFuK   ⚠️ XGBoost nicht verfügbar - installieren Sie mit: pip install xgboost)�
Sequential)�LSTM�Dense�DropoutuQ   ⚠️ TensorFlow nicht verfügbar - installieren Sie mit: pip install tensorflow)�Axes3D)�
make_subplotsu9   ⚠️ Plotly nicht verfügbar - 3D-Charts mit MatplotlibuN   ⚠️ TA-Lib nicht verfügbar - erweiterte technische Indikatoren deaktiviert)�stats�optimize)�
find_peaksc            
       �:  � e Zd ZdZd� Zdefd�Zdefd�Zdefd�Zde	j
        dede	j
        fd	�Zde	j
        de
fd
�Zde	j
        de	j
        fd�Zde	j
        de	j        fd�Zd
ej        de	j        dedeej        ej        f         fd�Zde
fd�Zde
fd�Zdefd�ZdS )�UltimateBitcoinTradingSystemV7u�   
    ULTIMATE BITCOIN TRADING SYSTEM V7.0 - ULTIMATE EDITION
    =======================================================
    Revolutionäres System mit maximalen Optimierungen
    c                 �  � d| _         d| _        d| _        t          j        �   �         | _        dddddd	d
�| _        ddd
�| _        dddd�| _        t          j
        �   �         | _        i | _        i | _
        i | _        d | _        d| _        d d d d d�| _        ddddd�| _        t'          �   �         t)          �   �         d�| _        d| _        d| _        d | _        d | _        i | _        g | _        g | _        d| _        d | _        g | _        i | _         dg d�dg d�dg d�dg d�dg d�d�| _!        t          j
        �   �         | _"        i | _#        i | _$        i | _%        | j        �&                    �   �         dddddddddddddd�| _'        g g g g g d �| _(        tS          d!�  �         tS          d"| j         � ��  �         tS          d#| j        �*                    d$�  �        � ��  �         tS          d%tW          | j        �  �        � d&��  �         tS          d'tW          | j        �  �        � d(��  �         tS          d)�  �         tS          d*tW          | j!        �  �        � d+��  �         tS          d,�  �         tS          d-�  �         d S ).Nz%Ultimate_Trading_System_v7.0_UltimatezBTC-USD�BTCUSDTz:https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDTz7https://api.coinbase.com/v2/exchange-rates?currency=BTCzKhttps://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usdz2https://api.kraken.com/0/public/Ticker?pair=XBTUSDz.https://www.bitstamp.net/api/v2/ticker/btcusd/z*https://api.gemini.com/v1/pubticker/btcusd)�binance�coinbase�	coingecko�kraken�bitstamp�geminiz>https://newsapi.org/v2/everything?q=bitcoin&sortBy=publishedAtzKhttps://cryptonews-api.com/api/v1/category?section=general&token=YOUR_TOKEN)�newsapi�
cryptonewsz!https://api.blockchain.info/statsz;https://api.glassnode.com/v1/metrics/market/price_usd_closez6https://api.coinmetrics.io/v4/timeseries/asset-metrics)�blockchain_info�	glassnode�coinmetrics�   )�
random_forest�xgboost�lstm�gradient_boostg      �?gffffff�?g333333�?)�robust�standardF�  r   T)�active�signals)�mean_reversion�momentum�breakout�	arbitrage�ml_ensemble�        )�script_start_time�total_scans�successful_scans�ensemble_accuracy�best_model_accuracy�api_calls_count�live_data_quality�sentiment_score�onchain_strength�auto_training_cycles�total_analysis_time�prediction_hit_rate�sharpe_ratio�max_drawdown)�model_accuracy_trend�prediction_errors�execution_times�memory_usage�api_response_timesz2Ultimate Bitcoin Trading System V7.0 initialisiertz	Version: zStart-Zeit: z%d.%m.%Y %H:%M:%SzEnhanced APIs: z Live-Daten QuellenzEnsemble Models: z ML-Algorithmenz*Auto-Training: Kontinuierliches LernsystemzQuantitative Strategies: z Strategienz:Advanced Analytics: 3D-Visualisierung, Sentiment, On-Chainu.   Performance Monitoring: Real-time Überwachung),�VERSION�SYMBOL�BINANCE_SYMBOLr   �nowr;   �
api_endpoints�news_endpoints�onchain_endpoints�pd�	DataFrame�market_data�live_price_cache�sentiment_cache�
onchain_cache�last_cache_time�cache_duration�ensemble_models�
model_weightsr
   r   �scalers�auto_training_active�training_interval�last_training_time�training_thread�model_versions�performance_history�scan_results�scan_counter�last_scan_result�prediction_visualizations�multi_timeframe_data�trading_strategies�correlation_matrix�volatility_surface�risk_metrics�portfolio_optimization�	isoformat�
session_stats�performance_monitor�print�strftime�len)�selfs    �,E:\Dev\ultimate_bitcoin_trading_system_v7.py�__init__z'UltimateBitcoinTradingSystemV7.__init__[   s<  � �>������'��� "*������ T�Q�f�J�H�B�

� 
��� X�g�
� 
���  C�V�S�"
� "
��� �<�>�>��� "���!������#��� ��� "���"�	 
�  
��� "���"�	
� 
��� #�n�n�&�(�(�
� 
��� %*��!�!%���"&���#��� ���#%�� � ������ $���)+��&�$&��!� *.�"�=�=�#'�B�7�7�#'�B�7�7�$(�R�8�8�&*�r�:�:�#
� #
��� #%�,�.�.���"$������&(��#� "&�!7�!A�!A�!C�!C�� !�!$�#&� �!$�"� #�$%�#&�#&���
� 
���& %'�!#�!��"$�$
� $
�� � 	�C�D�D�D�
�(�$�,�(�(�)�)�)�
�S�T�3�<�<�=P�Q�Q�S�S�T�T�T�
�L��D�$6� 7� 7�L�L�L�M�M�M�
�L�#�d�&:�";�";�L�L�L�M�M�M�
�;�<�<�<�
�S�#�d�.E�*F�*F�S�S�S�T�T�T�
�K�L�L�L�
�?�@�@�@�@�@�    �returnc                 �0  ���� 	 t          d�  �         t          j        �   �         }i }i }d}	 t          j        �   �         }t          j        | j        d         d��  �        }t          j        �   �         |z
  }|j        dk    rT|�                    �   �         }t          |d         �  �        |d<   ||d<   |dz
  }t          d	|d         d
�d|d�d
��  �         n)# t          $ r}	t          d|	� ��  �         Y d}	~	nd}	~	ww xY w	 t          j        �   �         }t          j        | j        d         d��  �        }t          j        �   �         |z
  }|j        dk    r`|�                    �   �         }t          |d         d         d         �  �        |d<   ||d<   |dz
  }t          d|d         d
�d|d�d
��  �         n)# t          $ r}	t          d|	� ��  �         Y d}	~	nd}	~	ww xY w	 t          j        �   �         }t          j        | j        d         d��  �        }t          j        �   �         |z
  }|j        dk    rZ|�                    �   �         }t          |d         d         �  �        |d<   ||d<   |dz
  }t          d|d         d
�d|d�d
��  �         n)# t          $ r}	t          d|	� ��  �         Y d}	~	nd}	~	ww xY w	 t          j        �   �         }t          j        | j        d         d��  �        }t          j        �   �         |z
  }|j        dk    r�|�                    �   �         }t          |d         �
                    �   �         �  �        d         }
t          |
d         d         �  �        |d<   ||d<   |dz
  }t          d|d         d
�d|d�d
��  �         n)# t          $ r}	t          d|	� ��  �         Y d}	~	nd}	~	ww xY w	 t          j        �   �         }t          j        | j        d          d��  �        }t          j        �   �         |z
  }|j        dk    rT|�                    �   �         }t          |d!         �  �        |d <   ||d <   |dz
  }t          d"|d          d
�d|d�d
��  �         n)# t          $ r}	t          d#|	� ��  �         Y d}	~	nd}	~	ww xY w	 t          j        �   �         }t          j        | j        d$         d��  �        }t          j        �   �         |z
  }|j        dk    rT|�                    �   �         }t          |d!         �  �        |d$<   ||d$<   |dz
  }t          d%|d$         d
�d|d�d
��  �         n)# t          $ r}	t          d&|	� ��  �         Y d}	~	nd}	~	ww xY w|�r^t          |�
                    �   �         �  �        }t          j        |�  �        }t          j
        |�  �        }
t          j        |�  �        }t          |�  �        t!          |�  �        z
  }t          j        |�  �        }t          j        |d'�  �        �t          j        |d(�  �        ���z
  }d)|z  ����fd*�|D �   �         }|rt          j        |�  �        }n|}|t'          | j        �  �        z  }|dk    rt          d+d,||z  z
  �  �        nd+}t          d+d,t          j
        t          |�
                    �   �         �  �        �  �        d-z  z
  �  �        }|d.z  |d/z  z   |d0z  z   }t          j        �   �         |z
  }|||
||t'          | j        �  �        |||��|t'          |�  �        t'          |�  �        z
  d1�||||d2�||t)          j        �   �         �                    �   �         d3�}| j        d4         �                    t          |�
                    �   �         �  �        �  �         t'          | j        d4         �  �        d5k    r| j        d4         d6d�         | j        d4<   | j        d7xx         |z
  cc<   || j        d8<   t          d9|d
���  �         t          d:|d;�d|� d<t'          | j        �  �        � d=��  �         t          d>|d;���  �         t          d?t          j
        t          |�
                    �   �         �  �        �  �        d�d@��  �         t          dAt'          |�  �        t'          |�  �        z
  � ��  �         |S t          dB�  �        �# t          $ r1}	t          dC|	� ��  �         | �                    |	�  �        cY d}	~	S d}	~	ww xY w)Dz�
        ENHANCED LIVE-DATEN V7.0
        ========================
        Sammelt Live-Daten von 6 APIs mit erweiterten Metriken
        z-Sammle Enhanced Live-Daten V7.0 von 6 APIs...r   r    �   ��timeout��   �price�   u   ✅ Binance: $�,.2fz (�.3fzs)u   ❌ Binance API Fehler: Nr!   �data�rates�USDu   ✅ Coinbase: $u   ❌ Coinbase API Fehler: r"   �bitcoin�usdu   ✅ CoinGecko: $u   ❌ CoinGecko API Fehler: r#   �result�cu
   ✅ Kraken: $u   ❌ Kraken API Fehler: r$   �lastu   ✅ Bitstamp: $u   ❌ Bitstamp API Fehler: r%   u
   ✅ Gemini: $u   ❌ Gemini API Fehler: �   �K   g      �?c                 �@   �� g | ]}��z
  |cxk    r	��z   k    �n n|��S � r�   )�.0�p�outlier_threshold�q1�q3s     ���rw   �
<listcomp>zLUltimateBitcoinTradingSystemV7.get_enhanced_live_data_v7.<locals>.<listcomp>S  sH   �� �p�p�p�a�"�?P�:P�TU�:o�:o�:o�:o�Y[�^o�Yo�:o�:o�:o�:o�:o��:o�:o�:ory   r:   �      �?g       @�      �?�333333�?皙�����?)�std�variance�ranger�   r�   �iqr�outliers_removed)�overall_quality�api_success_rate�price_consistency�response_time_quality)�consensus_price�
raw_consensus�
mean_price�individual_prices�successful_apis�
total_apis�price_statistics�quality_metricsrM   �
fetch_time�	timestamprM   �d   i����r@   rA   u   ✅ Enhanced Live-Preis: $u   📊 Datenqualität: �.1%�/z APIs)u   📈 Preis-Konsistenz: u    ⚡ Durchschnittliche API-Zeit: �su   🔍 Ausreißer entfernt: z,Keine Live-Preise von Enhanced APIs erhaltenu$   ❌ FEHLER bei Enhanced Live-Daten: )rs   �time�requests�getrR   �status_code�json�float�	Exception�list�values�np�median�meanr�   �max�min�var�
percentileru   r   rQ   rp   rr   �extendrq   �_get_enhanced_fallback_data_v7)rv   �
start_time�live_pricesrM   r�   �	api_start�response�api_timer�   �e�ticker_data�prices_listr�   r�   �	price_std�price_range�price_variancer�   �clean_prices�clean_consensusr�   r�   r�   r�   r�   r�   r�   r�   r�   s                             @@@rw   �get_enhanced_live_data_v7z8UltimateBitcoinTradingSystemV7.get_enhanced_live_data_v7�   sO
  ���� �w	:��A�B�B�B�����J��K�!#���O�
6� �I�K�K�	�#�<��(:�9�(E�q�Q�Q�Q���9�;�;��2���'�3�.�.�#�=�=�?�?�D�-2�4��=�-A�-A�K�	�*�4<�&�y�1�#�q�(�O��Z�;�y�+A�Z�Z�Z�(�Z�Z�Z�Z�[�[�[���� 
6� 
6� 
6��4��4�4�5�5�5�5�5�5�5�5�����
6����
7� �I�K�K�	�#�<��(:�:�(F�PQ�R�R�R���9�;�;��2���'�3�.�.�#�=�=�?�?�D�.3�D��L��4I�%�4P�.Q�.Q�K�
�+�5=�&�z�2�#�q�(�O��\�K�
�,C�\�\�\�H�\�\�\�\�]�]�]���� 
7� 
7� 
7��5�!�5�5�6�6�6�6�6�6�6�6�����
7����
8� �I�K�K�	�#�<��(:�;�(G�QR�S�S�S���9�;�;��2���'�3�.�.�#�=�=�?�?�D�/4�T�)�_�U�5K�/L�/L�K��,�6>�&�{�3�#�q�(�O��^�[��-E�^�^�^�h�^�^�^�^�_�_�_���� 
8� 
8� 
8��6�1�6�6�7�7�7�7�7�7�7�7�����
8����

5� �I�K�K�	�#�<��(:�8�(D�a�P�P�P���9�;�;��2���'�3�.�.�#�=�=�?�?�D�"&�t�H�~�'<�'<�'>�'>�"?�"?��"B�K�,1�+�c�2B�1�2E�,F�,F�K��)�3;�&�x�0�#�q�(�O��X�+�h�*?�X�X�X��X�X�X�X�Y�Y�Y���� 
5� 
5� 
5��3��3�3�4�4�4�4�4�4�4�4�����
5����
7� �I�K�K�	�#�<��(:�:�(F�PQ�R�R�R���9�;�;��2���'�3�.�.�#�=�=�?�?�D�.3�D��L�.A�.A�K�
�+�5=�&�z�2�#�q�(�O��\�K�
�,C�\�\�\�H�\�\�\�\�]�]�]���� 
7� 
7� 
7��5�!�5�5�6�6�6�6�6�6�6�6�����
7����
5� �I�K�K�	�#�<��(:�8�(D�a�P�P�P���9�;�;��2���'�3�.�.�#�=�=�?�?�D�,1�$�v�,�,?�,?�K��)�3;�&�x�0�#�q�(�O��X�+�h�*?�X�X�X��X�X�X�X�Y�Y�Y���� 
5� 
5� 
5��3��3�3�4�4�4�4�4�4�4�4�����
5���� � M
P�"�;�#5�#5�#7�#7�8�8�� #%�)�K�"8�"8���W�[�1�1�
��F�;�/�/�	�!�+�.�.��[�1A�1A�A��!#���!4�!4�� �]�;��3�3���]�;��3�3���2�g��$'�#�I�!�  q�p�p�p�p�p�;�p�p�p��� 6�&(�i��&=�&=�O�O�&5�O� $3�S��9K�5L�5L�#L� �Ud�gh�Uh�Uh�C��S�I��4O�-P�$Q�$Q�$Q�nq�!�(+�C�����EW�E^�E^�E`�E`�@a�@a�8b�8b�eh�8h�1i�(j�(j�%�#3�c�#9�<M�PS�<S�#S�Vk�nq�Vq�#q��!�Y�[�[�:�5�
� (7�%4�",�)4�'6�"%�d�&8�"9�"9�(�$2�!,� � �"�,/��,<�,<�s�<�?P�?P�,P�)� )� ,;�,<�->�1F�	(� (� +=�",�!)����!9�!9�!;�!;�1� ��8 �(�)=�>�E�E�d�K]�Kd�Kd�Kf�Kf�Fg�Fg�h�h�h��t�/�0D�E�F�F��L�L�EI�E]�^r�Es�tx�ty�ty�Ez�D�,�-A�B� �"�#4�5�5�5��H�5�5�5�:I��"�#6�7��I�?�I�I�I�J�J�J��v�o�v�v�v�_�v�v�WZ�[_�[m�Wn�Wn�v�v�v�w�w�w��G�0A�G�G�G�H�H�H��j����FX�F_�F_�Fa�Fa�Ab�Ab�9c�9c�j�j�j�j�k�k�k��Y�3�{�3C�3C�c�,�FW�FW�3W�Y�Y�Z�Z�Z��
�  � N�O�O�O��� 	:� 	:� 	:��<��<�<�=�=�=� �6�6�q�9�9�9�9�9�9�9�9�����		:���s%  �(c �B)C �c �
C>�"C9�4c �9C>�>c �B5F8 �7c �8
G�G�c �G�c �"B/J �c �
J8�J3�.c �3J8�8c �<CN �c �
N?�#N:�5c �:N?�?c �B)Q- �,c �-
R�7R�	c �R�c �B)U � c �
U'�U"�c �"U'�'M#c �c �
d�$&d�
d�dc                 �  � 	 t          d�  �         	 t          j        | j        �  �        }|j        }|�                    dd�  �        }|dk    rTt          d|d���  �         ||d|idddd	id
t
          j        �   �         �                    �   �         dt          |�  �        d�
S n)# t          $ r}t          d|� ��  �         Y d
}~nd
}~ww xY w| j        j        s�| j        d         j
        d         }t          j        �                    dd�  �        }|d|z   z  }t          d|d�d��  �         ||d|idt#          | j        �  �        ddidt
          j        �   �         �                    �   �         dt          |�  �        d�
S dt          j        �                    dd�  �        z   }	t          d|	d���  �         |	|	d|	idt#          | j        �  �        ddidt
          j        �   �         �                    �   �         dt          |�  �        d�
S # t          $ rH}
t          d|
� ��  �         dd|
� �t
          j        �   �         �                    �   �         d�cY d
}
~
S d
}
~
ww xY w) zEnhanced Fallback System V7.0z*Aktiviere Enhanced Fallback System V7.0...�regularMarketPricer   u   ✅ Yahoo Finance Fallback: $r�   �
yahoo_financer�   r�   gffffff�?r�   )
r�   r�   r�   r�   r�   r�   r�   r�   �fallback�original_erroru#   ❌ Yahoo Finance Fallback Fehler: N�Close�����g{�G�zt?u   ✅ Intelligenter Fallback: $z (basierend auf letztem Preis)�intelligent_fallbackr�   皙�����?�intelligenti� i�  u   ⚠️ Letzter Fallback: $�emergency_fallbackr�   �	emergencyu*   ❌ KRITISCHER FEHLER im Fallback System: zFallback failed: )r�   �errorr�   )rs   �yf�TickerrO   �infor�   r   rQ   rp   �strr�   rW   �empty�ilocr�   �random�normalru   rR   )rv   r�   �btcr�   �
current_price�yf_error�
last_price�realistic_change�fallback_price�realistic_price�fallback_errors              rw   r�   z=UltimateBitcoinTradingSystemV7._get_enhanced_fallback_data_v7�  s�  � �F	��>�?�?�?�
H��i���,�,���x�� $���)=�q� A� A�
� �1�$�$��N�-�N�N�N�O�O�O�+8�)6�.=�}�-M�+,�&'�,=�s�+C�&)�%-�\�^�^�%=�%=�%?�%?�$3�*-�e�*�*�� � � %�� � 
H� 
H� 
H��F�H�F�F�G�G�G�G�G�G�G�G�����
H���� �#�)� 
�!�-�g�6�;�B�?�
�#%�9�#3�#3�A�u�#=�#=� �!+�q�3C�/C�!D���i�n�i�i�i�i�j�j�j�'5�%3�*@�.�)Q�'(�"%�d�&8�"9�"9�(9�3�'?�"%�!)����!9�!9�!;�!;� -�&)�%�j�j�� � � %�r�y�'7�'7��4�'@�'@�@�O��E��E�E�E�F�F�F� $3�!0�&:�O�%L�#$�!�$�"4�5�5�$5�s�#;�!�%�\�^�^�5�5�7�7�'�"%�e�*�*�� � 
�� � 	� 	� 	��O�~�O�O�P�P�P�#)�=�^�=�=�%�\�^�^�5�5�7�7�� � 
� 
� 
� 
� 
� 
�����	���sO   �H �BB# �"H �#
C	�-C�?H �C	�	B5H �?BH �
I�=I�I�Ic                 �L  � 	 t          d�  �         t          j        �   �         }ddd�ddd�ddd�d	d
d�d�}i }|�                    �   �         D �]�\  }}	 t          j        | j        �  �        }|�                    |d         |d
         ��  �        }|j        �s]| �                    �   �         }|d         }	|	dk    r�t          j
        �   �         }
|d         j        d         }|	|z
  |z  }|t          |	|dt          |�  �        dz  z   z  �  �        t          |	|dt          |�  �        dz  z
  z  �  �        |	|d         j        dd�         �                    �   �         d�}
t!          j        |
�  �        }|
�                    �   �         D ]\  }}||j        ||f<   �| �                    ||�  �        }||	t)          |�  �        ||d         |d
         d�||<   t          d|� dt)          |�  �        � d��  �         ���# t*          $ r4}t          d|� d|� ��  �         dt-          |�  �        i||<   Y d}~���d}~ww xY wt          j        �   �         |z
  }t          dt)          |�  �        � d|d �d!��  �         || _        |S # t*          $ r}t          d"|� ��  �         i cY d}~S d}~ww xY w)#u�   
        MULTI-TIMEFRAME DATEN V7.0
        ==========================
        Sammelt Daten für verschiedene Zeitrahmen
        z$Sammle Multi-Timeframe Daten V7.0...�7d�1h)�period�interval�30d�4h�1y�1d�5y�1wk)r�   r�   r�   �1wr�   r�   r�   r   r�   r�   r�   r�   �Volumei����N)�Open�High�Lowr�   r�   )r�   r�   �data_points�	timeframer�   r�   u   ✅ �: � Datenpunkteu   ❌ Fehler bei Timeframe r�   zMulti-Timeframe Daten V7.0: z Zeitrahmen in �.2fr�   u&   ❌ FEHLER bei Multi-Timeframe Daten: )rs   r�   �itemsr�   r�   rO   �historyr�   r�   r   rQ   r�   r�   �absr�   r�   rU   �	Timestamp�loc�_calculate_enhanced_metrics_v7ru   r�   r�   rj   )rv   r�   �
timeframes�
multi_data�tf_name�	tf_configr�   �hist�	live_datar�   �current_time�
last_close�price_change�new_row�	new_index�col�valuer�   r�   s                      rw   �get_multi_timeframe_data_v7z:UltimateBitcoinTradingSystemV7.get_multi_timeframe_data_v7�  sB  � �D	��8�9�9�9�����J� "&�4�8�8�!&�D�9�9�!%�4�8�8�!%�5�9�9�	� �J� �J�&0�&6�&6�&8�&8� -
<� -
<�"���,<��)�D�K�0�0�C��;�;�i��.A�I�V`�La�;�b�b�D��:� $I�$(�$B�$B�$D�$D�	�(1�2C�(D�
� )�1�,�,�+3�<�>�>�L�)-�g��);�B�)?�J� -:�J�,F�*�+T�L�(2�(+�M�:��S�Q]�M^�M^�ad�Md�Id�;e�(f�(f�'*�=�*��C�P\�L]�L]�`c�Lc�Hc�:d�'e�'e�)6�*.�x�.�*=�c�d�d�*C�*H�*H�*J�*J�'� '�G� )+��\�(B�(B�I�.5�m�m�o�o� A� A�
��U�;@����C�� 8� 8�  $�B�B�4��Q�Q�� %)�-:�+.�t�9�9�)0�&/��&9�(1�*�(=�
/� /�
�7�+� �G�W�G�G��D�	�	�G�G�G�H�H�H��� � <� <� <��D�g�D�D��D�D�E�E�E�+2�C��F�F�*;�J�w�'�'�'�'�'�'�����<���� ����z�1�J��b��Z���b�b�Q[�b�b�b�b�c�c�c�(2�D�%����� 	� 	� 	��>�1�>�>�?�?�?��I�I�I�I�I�I�����	���sJ   �AI; �F!G7�5I; �7
H5�)H0�*I; �0H5�5AI; �;
J#�J�J#�J#�dfr�   c                 �l
  � 	 |j         r|S |d         �                    �   �         |d<   t          j        |d         |d         �                    d�  �        z  �  �        |d<   |d         �                    d�  �        �                    �   �         |d<   t          �r�	 t          j	        |d         d��  �        |d<   t          j
        |d         d��  �        |d	<   t          j        |d         d��  �        |d
<   t          j        |d         d��  �        |d<   t          j
        |d         �  �        \  |d
<   |d<   |d<   t          j        |d         |d         |d         �  �        \  |d<   |d<   t          j        |d         �  �        \  |d<   |d<   |d<   t          j        |d         |d         |d         �  �        |d<   d|j        v rVt          j        |d         |d         �  �        |d<   t          j        |d         |d         |d         |d         �  �        |d<   t)          d|� d��  �         �nZ# t*          $ r }t)          d|� d|� ��  �         Y d}~�n5d}~ww xY w|d         �                    d�  �        �                    �   �         |d<   |d         �                    d� �  �        �                    �   �         |d	<   |d         �                    �   �         }|�                    |d!k    d!�  �        �                    d�"�  �        �                    �   �         }|�                    |d!k     d!�  �         �                    d�"�  �        �                    �   �         }||z  }d#d#d|z   z  z
  |d<   |d         �                    d$� �  �        �                    �   �         }|d         �                    d%� �  �        �                    �   �         }	||	z
  |d
<   |d
         �                    d&� �  �        �                    �   �         |d<   |d
         |d         z
  |d<   |d         �                    d�  �        �                    �   �         }
|d         �                    d�  �        �                    �   �         }|
|d'z  z   |d<   |
|d<   |
|d'z  z
  |d<   |d         �                    d#�  �        �                    d(�)�  �        |d*<   d|j        v r/|d         �                    d#�  �        �                    d(�)�  �        nd+|d,<   t7          |d         |d         z
  �  �        |d         z  |d-<   t8          �rZ	 |d         j        }|d         j        }
t=          |d.�/�  �        \  }}t=          |
 d.�/�  �        \  }}t          j        |d0<   t          j        |d1<   tA          |�  �        d!k    r5|j!        |         d         |j!        ||j        �"                    d0�  �        f<   tA          |�  �        d!k    r5|j!        |         d         |j!        ||j        �"                    d1�  �        f<   |d0         �#                    d2�3�  �        |d0<   |d1         �#                    d2�3�  �        |d1<   n)# t*          $ r}t)          d4|� ��  �         Y d}~nd}~ww xY w|S # t*          $ r}t)          d5|� ��  �         |cY d}~S d}~ww xY w)6z!Berechne erweiterte Metriken V7.0r�   �Returnsr�   �Log_Returns�   �
Volatility)�
timeperiod�SMA_20�EMA_20�WMA_20�   �RSI�MACD�MACD_Signal�	MACD_Histr�   r�   �Stoch_K�Stoch_D�BB_Upper�	BB_Middle�BB_Lower�ATRr�   �OBV�ADu   ✅ TA-Lib Indikatoren für z
 berechnetu   ⚠️ TA-Lib Fehler für r   N)�spanr   )�windowr�   �   �   �	   �   T��pct�Price_Percentiler�   �Volume_Percentile�Trend_Strength�
   )�distance�Resistance_Level�
Support_Level�ffill)�methodu-   ⚠️ Support/Resistance Berechnung Fehler: u%   ❌ FEHLER bei erweiterten Metriken: )$r�   �
pct_changer�   �log�shift�rollingr�   �TALIB_AVAILABLE�talib�SMA�EMA�WMAr"  r#  �STOCH�BBANDSr+  �columnsr,  r-  rs   r�   r�   �ewm�diff�where�rankr  �SCIPY_AVAILABLEr�   r   �nanru   r�   �get_loc�fillna)rv   r  r�   r�   �delta�gain�loss�rs�ema_12�ema_26�	bb_middle�bb_std�highs�lows�resistance_peaks�_�
support_peakss                    rw   r  z=UltimateBitcoinTradingSystemV7._calculate_enhanced_metrics_v7-  s�  � �d	��x� 
��	� �w�K�2�2�4�4�B�y�M� "��r�'�{�R��[�5F�5F�q�5I�5I�'I� J� J�B�}��!�)�}�4�4�R�8�8�<�<�>�>�B�|�� � 3
:�I�#(�9�R��[�R�#H�#H�#H�B�x�L�#(�9�R��[�R�#H�#H�#H�B�x�L�#(�9�R��[�R�#H�#H�#H�B�x�L� !&�	�"�W�+�"� E� E� E�B�u�I�EJ�Z�PR�SZ�P[�E\�E\�B�B�v�J��=� 1�2�k�?�38�;�r�&�z�2�e�9�VX�Y`�Va�3b�3b�0�B�y�M�2�i�=� GL�l�SU�V]�S^�F_�F_�C�B�z�N�B�{�O�R�
�^� %�	�"�V�*�b��i��G�� M� M�B�u�I�  �2�:�-�-�$)�I�b��k�2�h�<�$H�$H��5�	�#(�8�B�v�J��5�	�2�g�;�PR�S[�P\�#]�#]��4���N��N�N�N�O�O�O�O�� � I� I� I��G�y�G�G�A�G�G�H�H�H�H�H�H�H�H�����I����  "�'�{�2�2�2�6�6�;�;�=�=��8��!�'�{���B��7�7�<�<�>�>��8�� �7��(�(�*�*�����E�A�I�q�1�1�:�:�"�:�E�E�J�J�L�L�����U�Q�Y��2�2�2�;�;�2�;�F�F�K�K�M�M���D�[���3�!�b�&�>�2��5�	� �G����b��1�1�6�6�8�8���G����b��1�1�6�6�8�8��#�f�_��6�
�$&�v�J�N�N��N�$:�$:�$?�$?�$A�$A��=�!�"$�V�*�r�-�/@�"@��;�� �w�K�/�/��3�3�8�8�:�:�	��G��,�,�R�0�0�4�4�6�6��!*�f�q�j�!9��:��"+��;��!*�f�q�j�!9��:�� &(��[�%8�%8��%=�%=�%B�%B�t�%B�%L�%L�B�!�"�RZ�^`�^h�Rh�Rh�b��l�&:�&:�3�&?�&?�&D�&D��&D�&N�&N�&N�nq�B�"�#� $'�r�'�{�R��\�'A�#B�#B�R��\�#Q�B�� � � 
O�O��v�J�-�E��e�9�+�D�*4�U�R�*H�*H�*H�'�$�a�'1�4�%�"�'E�'E�'E�$�M�1�-/�V�B�)�*�*,�&�B��'��+�,�,�q�0�0�\^�\c�dt�\u�v|�\}��� 0�"�*�2D�2D�EW�2X�2X� X�Y��=�)�)�A�-�-�VX�V]�^k�Vl�mr�Vs���
�r�z�/A�/A�/�/R�/R� R�S� .0�0B�-C�-J�-J�RY�-J�-Z�-Z�B�)�*�*,�_�*=�*D�*D�G�*D�*T�*T�B��'�'�� � O� O� O��M�!�M�M�N�N�N�N�N�N�N�N�����O���� �I��� 	� 	� 	��=�!�=�=�>�>�>��I�I�I�I�I�I�����	���sz   �Z �BZ �FH4 �2Z �4
I�>I�Z �I�KZ �0D/Y  �Z � 
Z�*Z�<Z �Z�Z �
Z3�Z.�(Z3�.Z3c           
      �$  � 	 t          d�  �         t          j        �   �         }t          |�  �        dk     r"t          dt          |�  �        � d��  �         dS | �                    |�  �        }|j        rt          d�  �         dS | �                    |�  �        }|j        rt          d�  �         dS t
          t          |�  �        t          |�  �        �  �        }|j        d|�         }|j        d|�         }t          |�  �        d	k     rt          d
�  �         dS t          d��  �        }| j	        d
         �
                    |�  �        }| j	        d         �
                    |�  �        }i }		 t          d�  �         t          dddddd��  �        }
g }|�                    |�  �        D ]\  }}
||         ||
         }}|j        |         |j        |
         }}|
�
                    ||�  �         |
�                    |�  �        }t          ||�  �        }|�                    |�  �         ��|
�
                    ||�  �         |
| j        d<   |t%          j        |�  �        t%          j        |�  �        |
j        d�}||	d<   t          dt%          j        |�  �        d�dt%          j        |�  �        d���  �         n)# t,          $ r}t          d|� ��  �         Y d}~nd}~ww xY wt.          �r�	 t          d�  �         t1          j        ddddddd� �  �        }g }|�                    |�  �        D ]�\  }}
||         ||
         }}|j        |         |j        |
         }}|�
                    ||||fgd�!�  �         |�                    |�  �        }t          ||�  �        }|�                    |�  �         ��|�
                    ||�  �         || j        d"<   |t%          j        |�  �        t%          j        |�  �        |j        d�}||	d"<   t          d#t%          j        |�  �        d�dt%          j        |�  �        d���  �         n)# t,          $ r}t          d$|� ��  �         Y d}~nd}~ww xY wt4          �r	 t          d%�  �         d}| �                    |||�  �        \  }}t          |�  �        d&k    �r�t9          t;          d	d'||j        d(         f�)�  �        t?          d*�  �        t;          d	d�+�  �        t?          d*�  �        tA          d,�  �        tA          d(�  �        g�  �        }|�!                    d-d.d/g�0�  �         tD          j#        j$        �%                    d1dd'�2�  �        }tM          t          |�  �        dz  �  �        }|d|�         ||d�         } }|d|�         ||d�         }"}!|�
                    ||!| |"fd	d3|gd4�5�  �        }#|| j        d6<   |�                    | d4�7�  �        }$t          |"|$�  �        }%|%|#j'        d8         d         |#j'        d1         d         t          |#j'        d8         �  �        d9�}&|&|	d6<   t          d:|%d���  �         n)# t,          $ r}t          d;|� ��  �         Y d}~nd}~ww xY w	 t          d<�  �         tQ          dd=ddd�>�  �        }'g }(|�                    |�  �        D ]\  }}
||         ||
         }}|j        |         |j        |
         }}|'�
                    ||�  �         |'�                    |�  �        }t          ||�  �        }|(�                    |�  �         ��|'�
                    ||�  �         |'| j        d?<   |(t%          j        |(�  �        t%          j        |(�  �        |'j        d�})|)|	d?<   t          d@t%          j        |(�  �        d�dt%          j        |(�  �        d���  �         n)# t,          $ r}t          dA|� ��  �         Y d}~nd}~ww xY w|	| _)        g }*|	�*                    �   �         D ]D\  }+},dB|,v r|*�                    |,dB         �  �         �%dC|,v r|*�                    |,dC         �  �         �E|*rht%          j        |*�  �        }-|-| j+        dD<   tY          |*�  �        | j+        dE<   t          dF|-dG���  �         t          dHtY          |*�  �        dG���  �         t          j        �   �         |z
  }.t          dI|.dJ�dK��  �         | j-        dL         �                    |.�  �         t          | j-        dL         �  �        d	k    r| j-        dL         dMd�         | j-        dL<   d'S # t,          $ r5}t          dN|� ��  �         d4dl.}/|/�/                    �   �          Y d}~dS d}~ww xY w)Ou�   
        ENSEMBLE ML-MODELLE TRAINING V7.0
        =================================
        Trainiert mehrere ML-Modelle für bessere Vorhersagen
        z%Trainiere Ensemble ML-Modelle V7.0...r�   u.   Nicht genügend Daten für Ensemble-Training: z < 100Fu0   Keine Features für Ensemble-Training verfügbaru/   Keine Targets für Ensemble-Training verfügbarN�2   u4   Nicht genügend aligned Daten für Ensemble-Trainingr|   )�n_splitsr0   r1   zTrainiere Random Forest...r   r  r3  �*   r�   )�n_estimators�	max_depth�min_samples_split�min_samples_leaf�random_state�n_jobsr,   )�	cv_scores�
mean_cv_score�std_cv_score�feature_importanceu   ✅ Random Forest: CV Score = r�   u    ± u#   ❌ Random Forest Training Fehler: zTrainiere XGBoost...r9  r�   g�������?)rd  re  �
learning_rate�	subsample�colsample_bytreerh  ri  )�eval_set�verboser-   u   ✅ XGBoost: CV Score = u   ❌ XGBoost Training Fehler: zTrainiere LSTM...r+   Tr�   )�return_sequences�input_shaper�   )rs  r�   �adam�mse�mae)�	optimizerrU  �metrics�val_loss)�monitor�patience�restore_best_weights�    r   )�validation_data�epochs�
batch_size�	callbacksrr  r.   )rr  rU  )�	val_score�
final_loss�final_val_loss�epochs_trainedu   ✅ LSTM: Validation Score = u   ❌ LSTM Training Fehler: zTrainiere Gradient Boosting...�   )rd  re  rn  ro  rh  r/   u"   ✅ Gradient Boosting: CV Score = u'   ❌ Gradient Boosting Training Fehler: rk  r�  r>   r?   u   🎯 Ensemble-Genauigkeit: r�   �   🏆 Beste Modell-Genauigkeit: u*   ✅ Ensemble ML-Modelle V7.0 trainiert in r  r�   rK   �����u#   ❌ FEHLER beim Ensemble-Training: )0rs   r�   ru   �_create_ensemble_features_v7r�   �_create_ensemble_targets_v7r�   r�   r   r_   �
fit_transformr   �split�fit�predictr   �appendr]   r�   r�   r�   �feature_importances_r�   �XGBOOST_AVAILABLE�xgb�XGBRegressor�TENSORFLOW_AVAILABLE�_prepare_lstm_data_v7r   r   �shaper   r   �compile�tf�kerasr�  �
EarlyStopping�intr  r
   �model_performancer  rq   r�   rr   �	traceback�	print_exc)0rv   r  r�   �features�targets�
min_length�tscv�features_robust�features_standard�ensemble_performance�rf_model�	rf_scores�	train_idx�val_idx�X_train�X_val�y_train�y_val�y_pred�score�rf_performancer�   �	xgb_model�
xgb_scores�xgb_performance�sequence_length�X_lstm�y_lstm�
lstm_model�early_stopping�	split_idx�X_train_lstm�
X_val_lstm�y_train_lstm�
y_val_lstmr  �y_pred_lstm�
lstm_score�lstm_performance�gb_model�	gb_scores�gb_performance�ensemble_scores�
model_name�perfr>   �
training_timer�  s0                                                   rw   �train_ensemble_models_v7z7UltimateBitcoinTradingSystemV7.train_ensemble_models_v7�  s.  � �|	��9�:�:�:�����J��2�w�w��}�}��V�s�2�w�w�V�V�V�W�W�W��u� �8�8��<�<�H��~� 
��H�I�I�I��u� �6�6�r�:�:�G��}� 
��G�H�H�H��u� �S��]�]�C��L�L�9�9�J��}�[�j�[�1�H��l�;�J�;�/�G��8�}�}�r�!�!��L�M�M�M��u� #�A�.�.�.�D� #�l�8�4�B�B�8�L�L�O� $��Z� 8� F� F�x� P� P��#%� �%
A��2�3�3�3�0�!$� �&'�%&�!#��
� � �� �	�*.�*�*�_�*E�*E� ,� ,�&�I�w�%4�Y�%?��QX�AY�U�G�%,�\�)�%<�g�l�7�>S�U�G��L�L��'�2�2�2�%�-�-�e�4�4�F�$�U�F�3�3�E��$�$�U�+�+�+�+� ���_�g�6�6�6�8@��$�_�5� "+�%'�W�Y�%7�%7�$&�F�9�$5�$5�*2�*G�	"� "�� 9G�$�_�5��j�r�w�y�7I�7I�j�j�j�SU�SY�Zc�Sd�Sd�j�j�j�k�k�k�k��� 
A� 
A� 
A��?�A�?�?�@�@�@�@�@�@�@�@�����
A���� !� '
?�&?��0�1�1�1� #� 0�%(�"$�&)�"%�),�%'�!�!� !� !�I� "$�J�.2�j�j��.I�.I� 1� 1�*�	�7�)8��)C�_�U\�E]���)0��i�)@�'�,�w�BW���!�
�
�g�w�5�%�.�AQ�[`�
�a�a�a�!*�!2�!2�5�!9�!9�� (��� 7� 7��"�)�)�%�0�0�0�0� �M�M�/�7�;�;�;�6?�D�(��3� &0�)+���)<�)<�(*��z�(:�(:�.7�.L�	'� '�O� 7F�(��3��j�R�W�Z�5H�5H�j�j�j�RT�RX�Yc�Rd�Rd�j�j�j�k�k�k�k�� � ?� ?� ?��=�!�=�=�>�>�>�>�>�>�>�>�����?���� $� 9
<�8<��-�.�.�.� ')�O�%)�%?�%?�@Q�SZ�\k�%l�%l�N�F�F��6�{�{�R�'�'�%/� ��d��Zb�Zh�ij�Zk�Hl�m�m�m�#�C�L�L� ��e�<�<�<�#�C�L�L�!�"�I�I�!�!�H�H�
1� &� &�
� #�*�*�V�%�RW�QX�*�Y�Y�Y� *,��);�)I�)I�$.��RV� *J� *� *��
 %(��F���c�(9�$:�$:�	�39�*�9�*�3E�v�i�j�j�GY�j��39�*�9�*�3E�v�i�j�j�GY�j��",�.�.�(�,�-7��,D�#%�')�'5�&6�$%�
 #1� #� #�� 8B��,�V�4� '1�&8�&8��Q�&8�&O�&O��%-�j�+�%F�%F�
� *4�*1�/�&�*A�"�*E�.5�o�j�.I�"�.M�.1�'�/�&�2I�.J�.J�	,� ,�(� 8H�,�V�4��N�j�N�N�N�O�O�O��� � <� <� <��:�q�:�:�;�;�;�;�;�;�;�;�����<����$
E��6�7�7�7�4�!$��"%�!�!#�� � �� �	�*.�*�*�_�*E�*E� ,� ,�&�I�w�%4�Y�%?��QX�AY�U�G�%,�\�)�%<�g�l�7�>S�U�G��L�L��'�2�2�2�%�-�-�e�4�4�F�$�U�F�3�3�E��$�$�U�+�+�+�+� ���_�g�6�6�6�9A��$�%5�6� "+�%'�W�Y�%7�%7�$&�F�9�$5�$5�*2�*G�	"� "�� :H�$�%5�6��n�2�7�9�;M�;M�n�n�n�WY�W]�^g�Wh�Wh�n�n�n�o�o�o�o��� 
E� 
E� 
E��C��C�C�D�D�D�D�D�D�D�D�����
E���� &:�D�"� !�O�$8�$>�$>�$@�$@� 
>� 
>� �
�D�"�d�*�*�#�*�*�4��+@�A�A�A�A� �D�(�(�#�*�*�4��+<�=�=�=��� 
T�$&�G�O�$<�$<�!�:K��"�#6�7�<?��<P�<P��"�#8�9��K�4E�K�K�K�L�L�L��R��O�8L�8L�R�R�R�S�S�S� �I�K�K�*�4�M��S�}�S�S�S�S�T�T�T� 
�$�%6�7�>�>�}�M�M�M��4�+�,=�>�?�?�"�D�D�>B�>V�Wh�>i�jm�jn�jn�>o��(�):�;��4��� 	� 	� 	��;��;�;�<�<�<��������!�!�!��5�5�5�5�5�����		���s�   �Ae �+e �+e �3A*e �Ae �2EJ> �=e �>
K$�K�e �K$�$e �0EQ �e �
Q.�Q)�$e �)Q.�.e �:G$Y �e �
Z�)Z �;e � Z�e �	E
_ �e �
_:�_5�0e �5_:�:Ee �
f�*f
�
fc                 �
  �� 	 t          j        �j        ��  �        }�d         �d         �                    d�  �        �                    �   �         z  |d<   �d         �d         z  |d<   �d         �d         z  |d	<   �d         �d         z   �d         z   �d         z   d
z  |d<   dD ]_}�d         �                    |�  �        |d
|� �<   t
          j        �d         �d         �                    |�  �        z  �  �        |d|� �<   �`dD ]z}�d         �                    �   �         �                    |�  �        �	                    �   �         |d|� �<   �d         �                    |�  �        �	                    �   �         |d|� �<   �{d�j
        v r��d         �d         �                    d�  �        �                    �   �         z  |d<   �d         �d         z  �                    d�  �        �                    �   �         |d<   �d         �                    �   �         �                    d�  �        �	                    �   �         |d<   �d         �                    �   �         }�d         �                    �   �         }||z
  |d<   g d�}|D ]5}|�j
        v r*�|         |d|� �<   |dvr�|         �d         z  |d|� d�<   �6t          �fd�dD �   �         �  �        rC�d         �d         z
  �d          �d         z
  z  |d!<   �d          �d         z
  �d"         z  |d#<   d$D ]r}�d         �d         �                    |�  �        k    �                    t          �  �        |d%|� �<   �d         �d         �                    |�  �        z  d&z
  |d'|� �<   �sd(D ]�}�d         �                    |�  �        �                    d)�*�  �        |d+|� �<   �d         �d         �                    |�  �        �                    �   �         z
  �d         �                    |�  �        �	                    �   �         z  |d,|� �<   ��t          ��  �        dk    r��d         �                    d�  �        �                    �   �         }	�d         �                    d�  �        �                    �   �         }
|	|
z
  }|
d-|z  z   |d.<   |
d/|z  z   |d0<   |
d1|z  z   |d2<   |
d3|z  z   |d4<   �d         |d2         z  |d5<   t%          �j        d6�  �        rI�j        j        |d6<   �j        j        |d7<   �j        j        d8k    �                    t          �  �        |d9<   d:D ]O}�d         �                    |�  �        |d;|� �<   d�j
        v r�d         �                    |�  �        nd<|d=|� �<   �Pd>D ]h}�d         �                    |�  �        �                    �   �         |d?|� �<   �d         �                    |�  �        �                    �   �         |d@|� �<   �i|�                    �   �         }t1          dAt          |j
        �  �        � dBt          |�  �        � dC��  �         |S # t2          $ r/}
t1          dD|
� ��  �         t          j        �   �         cY dE}
~
S dE}
~
ww xY w)Fu/   Erstelle erweiterte Features für Ensemble V7.0��indexr�   ra  �price_normalizedr�   r�   �high_low_ratior�   �close_open_ratio�   �ohlc_avg)r�   r3  �   r|   r9  r  �returns_�log_returns_)r|   r9  r  ra  �volatility_�
price_std_r�   r  �volume_normalizedr9  �volume_price_trend�volume_volatility�volume_price_divergence)
r  r  r"  r#  r$  r%  r(  r)  r*  r+  �
indicator_)r"  �_normc              3   �*   �K  � | ]
}|�j         v V � �d S )N)rJ  )r�   r  r  s     �rw   �	<genexpr>zNUltimateBitcoinTradingSystemV7._create_ensemble_features_v7.<locals>.<genexpr>�  s*   �� � � �I�I��3�"�*�$�I�I�I�I�I�Iry   )r(  r*  r*  r(  �bb_positionr)  �
bb_squeeze)r|   r9  r  �trend_r�   �	momentum_)r9  r  ra  Tr4  �price_percentile_�
price_zscore_gh��|?5�?zfib_23.6g��� �r�?zfib_38.2r�   zfib_50.0g-�����?zfib_61.8�price_vs_fib_50�hour�day_of_weekr|   �
is_weekend)r�   r3  r�  r|   �
close_lag_r   �volume_lag_)r9  r  �price_skew_�price_kurt_zEnsemble Features erstellt: z Features, � SampleszFEHLER bei Ensemble Features: N)rU   rV   r�  rB  r�   r?  r�   r@  rA  r�   rJ  �all�astyper�  rN  ru   r�   r�   �hasattrr�  �	dayofweek�skew�kurt�dropnars   r�   )rv   r  r�  r�   r/  r  �
volume_change�tech_indicators�	indicator�high_50�low_50�	fib_range�lagr�   s    `            rw   r�  z;UltimateBitcoinTradingSystemV7._create_ensemble_features_v7�  s�  �� �_	"��|�"�(�3�3�3�H� ,.�g�;��G��9L�9L�R�9P�9P�9U�9U�9W�9W�+W�H�'�(�)+�F��b��i�)?�H�%�&�+-�g�;��F��+C�H�'�(�$&�v�J��F��$;�b��i�$G�"�W�+�$U�YZ�#Z�H�Z� � /� 
d� 
d��02�7��0F�0F�v�0N�0N��,�F�,�,�-�46�F�2�g�;��G��IZ�IZ�[a�Ib�Ib�;b�4c�4c��0��0�0�1�1� *� 
T� 
T��35�g�;�3I�3I�3K�3K�3S�3S�TZ�3[�3[�3_�3_�3a�3a��/�v�/�/�0�24�W�+�2E�2E�f�2M�2M�2Q�2Q�2S�2S��.�f�.�.�/�/� �2�:�%�%�02�8��r�(�|�?S�?S�TV�?W�?W�?\�?\�?^�?^�0^��,�-�24�X�,��G��2L�1U�1U�VX�1Y�1Y�1^�1^�1`�1`��-�.�02�8��0G�0G�0I�0I�0Q�0Q�RT�0U�0U�0Y�0Y�0[�0[��,�-�  "�'�{�5�5�7�7�� "�8�� 7� 7� 9� 9�
�6B�]�6R��2�3�I� I� I�O� -� 
^� 
^�	���
�*�*�9;�I��H�5�)�5�5�6� !��/�/�BD�Y�-�RT�U\�R]�B]��!>�i�!>�!>�!>�?�� �I�I�I�I�0H�I�I�I�I�I� 
]�+-�g�;��J��+G�B�z�N�]_�`j�]k�Lk�*l���'�*,�Z�.�2�j�>�*I�R�P[�_�)\���&� &� 
]� 
]��/1�'�{�R��[�=N�=N�v�=V�=V�/V�.^�.^�_b�.c�.c��*�&�*�*�+�13�G��r�'�{�?P�?P�QW�?X�?X�1X�[\�1\��-�V�-�-�.�.� '� 
L� 
L��9;�G��9L�9L�V�9T�9T�9Y�9Y�^b�9Y�9c�9c��5�V�5�5�6�68��k�B�w�K�DW�DW�X^�D_�D_�Dd�Dd�Df�Df�6f�jl�mt�ju�j}�j}�  E�  kF�  kF�  kJ�  kJ�  kL�  kL�  6L��1��1�1�2�2� �2�w�w��|�|��V�*�,�,�R�0�0�4�4�6�6���E��*�*�2�.�.�2�2�4�4��#�f�,�	�'-��	�0A�'A���$�'-��	�0A�'A���$�'-��	�0A�'A���$�'-��	�0A�'A���$� /1��k�H�Z�<P�.P��*�+� �r�x��(�(� 
O�#%�8�=��� �*,�(�*<���'�*,�(�*<��*A�)I�)I�#�)N�)N���&� $� 
i� 
i��/1�'�{�/@�/@��/E�/E��+�c�+�+�,�KS�WY�Wa�Ka�Ka��8��0B�0B�3�0G�0G�0G�gh��,�s�,�,�-�-� #� 
V� 
V��35�g�;�3F�3F�v�3N�3N�3S�3S�3U�3U��/�v�/�/�0�35�g�;�3F�3F�v�3N�3N�3S�3S�3U�3U��/�v�/�/�0�0�  ���(�(�H��j��X�5E�1F�1F�j�j�SV�W_�S`�S`�j�j�j�k�k�k��O��� 	"� 	"� 	"��6�1�6�6�7�7�7��<�>�>�!�!�!�!�!�!�����	"���s   �YY
 �

Z�$Y>�8Z�>Zc                 ��  � 	 t          j        |j        ��  �        }|d         �                    d�  �        }||d<   dD ]:}|t	          |�  �        k     r%|d         �                    | �  �        }||d|� d�<   �;|d         �                    �   �         }t
          dt	          |�  �        � d	��  �         |S # t          $ r/}t
          d
|� ��  �         t          j        �   �         cY d}~S d}~ww xY w)u.   Erstelle erweiterte Targets für Ensemble V7.0r�  r�   r�   �price_1h)r�  r0  �   �price_�hzEnsemble Target erstellt: r�  zFEHLER bei Ensemble Target: N)	rU   rV   r�  rA  ru   r�  rs   r�   �Series)rv   r  r�  �future_price_1h�hours�future_price�targetr�   s           rw   r�  z:UltimateBitcoinTradingSystemV7._create_ensemble_targets_v7�  s  � �	� �l���2�2�2�G� !��k�/�/��3�3�O�"1�G�J�� %� 
>� 
>���3�r�7�7�?�?�#%�g�;�#4�#4�e�V�#<�#<�L�1=�G�-�U�-�-�-�.�� �Z�(�/�/�1�1�F��D�s�6�{�{�D�D�D�E�E�E��M��� 	� 	� 	��4��4�4�5�5�5��9�;�;�����������	���s   �B2B5 �5
C.�?$C)�#C.�)C.r�  r�  r�  c                 �  � 	 g g }}t          |t          |�  �        �  �        D ]B}|�                    |||z
  |�         �  �         |�                    |j        |         �  �         �Ct	          j        |�  �        t	          j        |�  �        fS # t          $ rD}t          d|� ��  �         t	          j        g �  �        t	          j        g �  �        fcY d}~S d}~ww xY w)u    Bereite Daten für LSTM vor V7.0z$FEHLER bei LSTM Daten-Vorbereitung: N)r�   ru   r�  r�   r�   �arrayr�   rs   )rv   r�  r�  r�  �X�y�ir�   s           rw   r�  z4UltimateBitcoinTradingSystemV7._prepare_lstm_data_v7  s�   � �	.��r�q�A��?�C��M�M�:�:� 
*� 
*������!�O�"3�A�"5�6�7�7�7������a��)�)�)�)��8�A�;�;������+�+��� 	.� 	.� 	.��<��<�<�=�=�=��8�B�<�<���"���-�-�-�-�-�-�-�����	.���s   �BB �
C�9C�C�Cc                 ��  � � 	 � j         rt          d�  �         dS t          d�  �         t          d� j        dz  d�d��  �         d� _         t          j        �   �         � _        � fd	�}t
          j        |dd
��  �        � _        � j        �	                    �   �          t          d�  �         dS # t          $ r$}t          d
|� ��  �         d� _         Y d}~dS d}~ww xY w)z~
        AUTO-TRAINING SYSTEM V7.0
        =========================
        Startet kontinuierliches Modell-Training
        u"   ⚠️ Auto-Training bereits aktivFu(   🚀 STARTE AUTO-TRAINING SYSTEM V7.0...zTraining-Intervall: r2   z.1fz StundenTc                  �  �� 	 �j         �r�	 t          j        �   �         �j        z
  j        �j        k    �rZt
          d�j        d         dz   � ��  �         ��                    �   �         } | �rd| v �r| d         d         }|j	        �s�t          |�  �        dk    �rՉ�                    |�  �        }|�r�dt          �j        �  �        dz   � dt          j        �   �         �
                    d	�  �        � �}t          j        �   �         �                    �   �         �j        �                    �   �         �j        �                    �   �         t          |�  �        �j        d         dz   d
��j        |<   �j        dxx         dz
  cc<   t          j        �   �         �_        t
          d|� ��  �         t
          d�j        d
         d���  �         �j        �                    t          j        �   �         �                    �   �         �j        d
         �j        d         t          |�  �        |d��  �         t          �j        �  �        dk    r�j        dd�         �_        n/t
          d�  �         nt
          d�  �         nt
          d�  �         t)          j        d�  �         n=# t,          $ r0}t
          d|� ��  �         t)          j        d�  �         Y d}~nd}~ww xY w�j         ���n)# t,          $ r}t
          d|� ��  �         Y d}~nd}~ww xY wd�_         t
          d�  �         dS # d�_         t
          d�  �         w xY w)zAuto-Training Worker Threadu   🔄 Auto-Training Zyklus #rD   r�   r�   r�   r�   �vr^  z
%Y%m%d_%H%M%S)r�   �models�performancer�   �training_cycleu(   ✅ Auto-Training erfolgreich - Version �   📊 Ensemble-Genauigkeit: r>   r�   r?   )r�   r>   r?   r�   �
version_idra  r�  Nu   ⚠️ Auto-Training suboptimalu/   ⚠️ Nicht genügend Daten für Auto-Trainingu5   ⚠️ Keine Multi-Timeframe Daten für Auto-Training�<   u$   ❌ FEHLER im Auto-Training Worker: i,  u(   ❌ KRITISCHER FEHLER im Auto-Training: Fu   🔴 Auto-Training gestoppt)r`   r   rQ   rb   �secondsra   rs   rq   r  r�   ru   r�  rd   rt   rp   r]   �copyr�  re   r�  r�   �sleepr�   )r
  r  �training_successr  r�   rv   s        �rw   �auto_training_workerzSUltimateBitcoinTradingSystemV7.start_auto_training_v7.<locals>.auto_training_worker8  s�  �� �E9��3� >,�=,� (�����1H� H�Q�UY�Uk�k�k� %�&t�D�DV�Wm�Dn�qr�Dr�&t�&t� u� u� u� .2�-M�-M�-O�-O�
�#-� .!c�$�*�2D�2D�)3�D�)9�&�)A�B�+-�8� (%a��B���#�
�
�;?�;X�;X�Y[�;\�;\�(8�+;� !)U� :G�S��AT�=U�=U�XY�=Y�  :G�  :G�\d�\h�\j�\j�\s�\s�  uD�  ]E�  ]E�  :G�  :G�J� >F�\�^�^�=U�=U�=W�=W�:>�:N�:S�:S�:U�:U�?C�?U�?Z�?Z�?\�?\�?B�2�w�w�BF�BT�Uk�Bl�op�Bp�O.� O.�D�,?�
�,K� -1�,>�?U�,V�,V�,V�Z[�,[�,V�,V�,V�FN�l�n�n�D�,C�,1�2i�]g�2i�2i�,j�,j�,j�,1�2}�PT�Pb�cv�Pw�2}�2}�2}�,~�,~�,~� -1�,D�,K�,K�=E�\�^�^�=U�=U�=W�=W�EI�EW�Xk�El�GK�GY�Zo�Gp�?B�2�w�w�>H�M.� M.� -/� -/� -/� 03�4�3K�/L�/L�r�/Q�/Q�KO�Kc�dg�dh�dh�Ki��0H�� -2�2S�,T�,T�,T�,T� ).�._�(`�(`�(`�(`� %*�*a�$b�$b�$b� !�J�r�N�N�N�N��(� ,� ,� ,�!�"L��"L�"L�M�M�M� �J�s�O�O�O�O�O�O�O�O�����,����y �3� >,���@ !� J� J� J��H�Q�H�H�I�I�I�I�I�I�I�I�����J���� 16�D�-��7�8�8�8�8�8�� 16�D�-��7�8�8�8�8���sY   �K+ �JJ% �$K+ �%
K�/&K�K+ �K�K+ �*L, �+
L�5L�L, �L�L, �,M�AutoTrainingWorker)r  �daemon�nameu'   ✅ Auto-Training System V7.0 gestartetu%   ❌ FEHLER beim Auto-Training Start: N)r`   rs   ra   r   rQ   rb   �	threading�Threadrc   �startr�   )rv   r  r�   s   `  rw   �start_auto_training_v7z5UltimateBitcoinTradingSystemV7.start_auto_training_v7'  s$  �� �^	��(� 
��:�;�;�;��u��<�=�=�=��R��)?��)D�R�R�R�R�S�S�S�(,�D�%�&.�l�n�n�D�#�G
9� G
9� G
9� G
9� G
9�T $-�#3�;O�X\�cw�#x�#x�#x�D� �� �&�&�(�(�(��;�<�<�<��4��� 	� 	� 	��=�!�=�=�>�>�>�(-�D�%��5�5�5�5�5�����	���s   �B0 �BB0 �0
C�:C�Cc                 �L  � 	 | j         st          d�  �         dS t          d�  �         d| _         | j        r4| j        �                    �   �         r| j        �                    d��  �         t          d�  �         dS # t
          $ r}t          d|� ��  �         Y d	}~dS d	}~ww xY w)
z Stoppe Auto-Training System V7.0u$   ⚠️ Auto-Training ist nicht aktivFu(   🔴 STOPPE AUTO-TRAINING SYSTEM V7.0...r9  r}   u&   ✅ Auto-Training System V7.0 gestopptTu%   ❌ FEHLER beim Auto-Training Stopp: N)r`   rs   rc   �is_alive�joinr�   )rv   r�   s     rw   �stop_auto_training_v7z4UltimateBitcoinTradingSystemV7.stop_auto_training_v7�  s�   � �	��,� 
��<�=�=�=��u��<�=�=�=�(-�D�%� �#� 
6��(<�(E�(E�(G�(G� 
6��$�)�)�"�)�5�5�5��:�;�;�;��4��� 	� 	� 	��=�!�=�=�>�>�>��5�5�5�5�5�����	���s   �A< �A A< �<
B#�B�B#c           
      �   � 	 | j         | j        dz  | j        r| j        �                    �   �         nd| j        d         t          | j        �  �        | j        d         | j        d         t          | j        �  �        d�}| j        r_| j        t          | j        ��  �        z   }|�                    �   �         |d<   |t          j
        �   �         z
  �                    �   �         |d	<   |S # t          $ r}t          |�  �        d
d�cY d}~S d}~ww xY w)zHole Auto-Training Status V7.0r2   NrD   r>   r?   )r3   �training_interval_hoursrb   �training_cyclesrd   �current_ensemble_accuracyr?   �performance_history_length)r  �next_training_time�time_until_next_trainingF)r�   r3   )r`   ra   rb   rp   rq   ru   rd   re   r   r   rQ   �
total_secondsr�   r�   )rv   �status�
next_trainingr�   s       rw   �get_auto_training_status_v7z:UltimateBitcoinTradingSystemV7.get_auto_training_status_v7�  s-  � �	6��3�+/�+A�D�+H�MQ�Md�&n�d�&=�&G�&G�&I�&I�&I�jn�#'�#5�6L�#M�"%�d�&9�":�":�-1�-?�@S�-T�'+�'9�:O�'P�.1�$�2J�.K�.K�	� 	�F� �&� 
f� $� 7�)�D�Lb�:c�:c�:c� c�
�/<�/F�/F�/H�/H��+�,�6C�h�l�n�n�6T�5c�5c�5e�5e��1�2��M��� 	6� 	6� 	6� ��V�V�u�5�5�5�5�5�5�5�5�����	6���s   �C$C' �'
D
�1D�D
�D
N)�__name__�
__module__�__qualname__�__doc__rx   �dictr�   r�   r  rU   rV   r�   r  �boolr�  r�  r  r�  r�   �ndarrayr�  r   r�  r  r#  r.  r�   ry   rw   r   r   T   s�  � � � � � �� �{A� {A� {A�z}:�4� }:� }:� }:� }:�~H�t� H� H� H� H�TJ�T� J� J� J� J�Xf��� f�#� f�RT�R^� f� f� f� f�PB�2�<� B�D� B� B� B� B�Ha"�r�|� a"��� a"� a"� a"� a"�F�b�l� �r�y� � � � �8
.�b�j� 
.�2�9� 
.�_b� 
.�gl�mo�mw�y{�  zD�  nD�  hE� 
.� 
.� 
.� 
.�d�� d� d� d� d�L�t� � � � �*6�T� 6� 6� 6� 6� 6� 6ry   r   c            
      �j  � t          d�  �         t          d�  �         t          d�  �         t          d�  �         	 t          �   �         } t          d�  �         | �                    �   �         }t          d�  �         t          d|�                    dd�  �        d	���  �         t          d
|�                    di �  �        �                    dd�  �        d
���  �         t          d|�                    dd�  �        � d|�                    dd�  �        � ��  �         t          d|�                    dd�  �        d�d��  �         t          d�  �         | �                    �   �         }t          d�  �         |�                    �   �         D ]O\  }}d|vr+t          d|� d|�                    dd�  �        � d��  �         �4t          d|� d|d         � ��  �         �P|�rud|v �rpd|d         v�re|d         d          }|j        �s?t          |�  �        d!k    �r+t          d"�  �         | �                    |�  �        }|r�t          d#�  �         t          d$| j	        �                    d%d�  �        d
���  �         t          d&| j	        �                    d'd�  �        d
���  �         t          d(�  �         | �
                    �   �         }|rYt          d)�  �         | �                    �   �         }t          d*|� ��  �         | �                    �   �          t          d+�  �         n?t          d,�  �         n/t          d-�  �         nt          d.�  �         nt          d/�  �         t          d0�  �         d1S # t          $ r5}	t          d2|	� ��  �         dd3l}
|
�                    �   �          Y d3}	~	d4S d3}	~	ww xY w)5u7   Hauptfunktion für Ultimate Bitcoin Trading System V7.0zP================================================================================z7ULTIMATE BITCOIN TRADING SYSTEM V7.0 - ULTIMATE EDITIONuL   REVOLUTIONÄRE OPTIMIERUNGEN • ENSEMBLE ML • AUTO-TRAINING • 3D-CHARTSu#   
🔍 SAMMLE ENHANCED LIVE-DATEN...u   
📊 LIVE-DATEN ERGEBNIS:z   Bitcoin-Preis: $r�   r   r�   u      Datenqualität: r�   r�   r�   z   APIs erfolgreich: r�   r�   r�   �   z   Fetch-Zeit: r�   r  r�   u%   
🔍 SAMMLE MULTI-TIMEFRAME DATEN...u   
📊 MULTI-TIMEFRAME ERGEBNIS:r�   z   r   r�   r  z: Fehler - r�   r�   r�   u#   
🧠 TRAINIERE ENSEMBLE-MODELLE...u"   ✅ Ensemble-Training erfolgreich!r  r>   r�  r?   u#   
🤖 TESTE AUTO-TRAINING SYSTEM...u#   ✅ Auto-Training System gestartet!u   📊 Auto-Training Status: u7   🔴 Auto-Training System gestoppt (Test abgeschlossen)u6   ❌ Auto-Training System konnte nicht gestartet werdenu#   ⚠️ Ensemble-Training suboptimalu3   ⚠️ Nicht genügend Daten für Ensemble-Trainingu;   ⚠️ Keine Multi-Timeframe Daten für Training verfügbaruJ   
🏆 ULTIMATE BITCOIN TRADING SYSTEM V7.0 - ULTIMATE EDITION ERFOLGREICH!Tz2FEHLER beim Ultimate Bitcoin Trading System V7.0: NF)rs   r   r�   r�   r  r  r�   ru   r�  rq   r  r.  r#  r�   r�  r�  )�systemr  r
  r  �tf_datar  r  �auto_successr,  r�   r�  s              rw   �&run_ultimate_bitcoin_trading_system_v7r;  �  s&  � �	�(�O�O�O�	�
C�D�D�D�	�
X�Y�Y�Y�	�(�O�O�O�E�/�1�1�� 	�4�5�5�5��4�4�6�6�	�
�,�-�-�-�
�N�I�M�M�2C�Q�$G�$G�N�N�N�O�O�O�
�h�I�M�M�2C�R�$H�$H�$L�$L�M^�`a�$b�$b�h�h�h�i�i�i�
�l�i�m�m�4E�q�&I�&I�l�l�I�M�M�Zf�hi�Lj�Lj�l�l�m�m�m�
�E�	�
�
�l�A� >� >�E�E�E�E�F�F�F� 	�6�7�7�7��7�7�9�9�
�
�1�2�2�2� *� 0� 0� 2� 2� 	D� 	D��G�W��g�%�%��R�G�R�R�w�{�{�=�!�'D�'D�R�R�R�S�S�S�S��B�G�B�B���0@�B�B�C�C�C�C� � !	Q�$�*�,�,��
�4�@P�1P�1P��D�!�&�)�B��8� 
M��B���#�
�
��<�=�=�=�#)�#B�#B�2�#F�#F� �#� A��?�@�@�@��n��8L�8P�8P�Qd�fg�8h�8h�n�n�n�o�o�o��t�F�<P�<T�<T�Uj�lm�<n�<n�t�t�t�u�u�u� �@�A�A�A�#)�#@�#@�#B�#B�L�#� X��C�D�D�D� "(�!C�!C�!E�!E���D�F�D�D�E�E�E� �4�4�6�6�6��W�X�X�X�X��V�W�W�W�W��?�@�@�@�@��K�L�L�L�L��O�P�P�P�
�\�]�]�]��t��� � � �
�F�1�F�F�G�G�G������������u�u�u�u�u�����	���s   �L3M3 �3
N2�=*N-�-N2�__main__)Lr2  �yfinancer�   �pandasrU   �numpyr�   r�   r�   r   r   r�   �os�warningsr  �pickle�typingr   r   r   r   �filterwarnings�sklearn.preprocessingr
   r   �sklearn.ensembler   r
   �sklearn.metricsr   r   r   �sklearn.model_selectionr   r   r-   r�  r�  �ImportErrorrs   �
tensorflowr�  �tensorflow.keras.modelsr   �tensorflow.keras.layersr   r   r   r�  �matplotlib.pyplot�pyplot�plt�mpl_toolkits.mplot3dr   �seaborn�sns�plotly.graph_objects�
graph_objects�go�plotly.express�express�px�plotly.subplotsr   �PLOTLY_AVAILABLErD  rC  �scipyr   r   �scipy.signalr   rO  r   r;  r/  r�   ry   rw   �<module>r]     s�  ��� � � � � � � � � � � � � � ���� ���� (� (� (� (� (� (� (� (� ���� 	�	�	�	� ���� � � � � 
�
�
�
� .� .� .� .� .� .� .� .� .� .� .� .� �� �� !� !� !� ?� >� >� >� >� >� >� >� M� M� M� M� M� M� M� M� M� M� M� M� M� M� M� M� M� M� A� A� A� A� A� A� A� A�Y���������� Y� Y� Y���	�E�
W�X�X�X�X�X�Y����_�����2�2�2�2�2�2�<�<�<�<�<�<�<�<�<�<������ _� _� _� ��	�E�
]�^�^�^�^�^�_����
  � � � � � � '� '� '� '� '� '� � � � �G�%�%�%�%�%�%�������-�-�-�-�-�-������ G� G� G���	�E�
E�F�F�F�F�F�G����
\��L�L�L��O�O��� \� \� \��O�	�E�
Z�[�[�[�[�[�\����
�%�%�%�%�%�%�%�%�'�'�'�'�'�'��O�O��� � � ��O�O�O�����e6� e6� e6� e6� e6� e6� e6� e6�P#L� L� L�\ �z���*�*�,�,�,�,�,� �sZ   �2A9 �9B�
B�B) �)B>�=B>�C' �'C<�;C<� D �D�D� D1 �1D;�:D;