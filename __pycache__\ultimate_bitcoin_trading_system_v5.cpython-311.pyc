�

    Keh"" �                   �(  � d Z ddlZddlZddlZddlZddlZddl	m	Z	m
Z
 ddlZddlZddl
Z
 e
j        d�  �         ddlmZ ddlmZ ddlmZmZ ddlmZ ddlZ	 ddlmZ d	Zn
# e$ r d
ZY nw xY w G d� d�  �        Zd
� Z e!dk    r e �   �          dS dS )ug  
ULTIMATE BITCOIN TRADING SYSTEM V5.0
====================================
VÖLLIG FEHLERFREIES TRADING-TOOL MIT STABILEN PROGNOSEN
- Funktionale Monitoring und Erweiterte Analyse
- Stabile, realistische Prognosen
- Jede Funktion getestet vor Integration
- Erweiterte Verbesserungen und Features

ULTIMATE TRADING SYSTEM V5.0 - PERFEKTION IN FUNKTIONALITÄT!
�    N)�datetime�	timedelta�ignore)�RobustScaler��RandomForestRegressor)�mean_squared_error�r2_score)�statsTFc                   ��  � e Zd ZdZd� Zdej        fd�Zdefd�Z	dej        fd�Z
d,dej        d	edej        fd
�Zdej        dej        fd�Zdej        de
fd�Zdej        d
e
de
fd�Zd
e
defd�Zdedefd�Zdede
fd�Zde
fd�Zdej        d
e
defd�Zdej        d
e
dej        fd�Zdej        d
e
de
fd�Zdej        d
e
defd�Zd
e
defd�Zdej        d
e
de
fd�Zd� Zde
fd�Zde
fd�Zdej        d
e
defd�Z dej        d
e
dej        fd �Z!dej        dej"        fd!�Z#dej        d
e
de
fd"�Z$dej        d
e
de
fd#�Z%d$e
de
fd%�Z&defd&�Z'defd'�Z(de
fd(�Z)d)e
fd*�Z*de
fd+�Z+dS )-�UltimateBitcoinTradingSystemV5u�   
    ULTIMATE BITCOIN TRADING SYSTEM V5.0
    ====================================
    Völlig fehlerfreies Trading-Tool mit stabilen Prognosen
    c                 ��  � d| _         d| _        d| _        t          j        �   �         | _        t
          j        �   �         | _        i | _	        d | _
        d| _        i | _        i | _
        t          �   �         | _        g | _        d| _        d | _        g | _        g | _        g | _        g | _        g | _        d | _        | j        �                    �   �         ddddddddddddd	�
| _        ddg ddd d
d�| _        i i i i i i d�| _        i | _        t=          d
�  �         t=          d| j         � ��  �         t=          d| j        �                    d�  �        � ��  �         t=          d�  �         t=          d�  �         d S )Nz)Ultimate_Trading_System_v5.2_ScanEnhancedzBTC-USD�BTCUSDTi,  r   �      �?�        ��������?)
�script_start_time�total_predictions�correct_predictions�current_accuracy�
best_accuracy�training_cycles�model_improvements�api_calls_count�cache_hit_rate�prediction_confidence_avg�total_analysis_time�successful_trades�
failed_tradesg      Y@)�	cpu_usage�memory_usage�api_response_times�error_count�uptime�
last_error�performance_score)�feature_importance�model_comparison�prediction_accuracy_by_time�market_regime_analysis�volatility_forecasting�correlation_analysisz2Ultimate Bitcoin Trading System V5.2 initialisiertz	Version: zStart-Zeit: z%d.%m.%Y %H:%M:%Sz/Scan-System aktiviert: Progressives ML-Trainingz0Stabile Prognosen und funktionale Tabs aktiviert) �VERSION�SYMBOL�BINANCE_SYMBOLr   �nowr   �pd�	DataFrame�market_data�
data_cache�last_cache_time�cache_duration�	ml_models�model_performancer   �scaler�scan_history�scan_counter�last_scan_result�scan_performance_history�progressive_training_data�model_accuracy_progression�hourly_predictions�prediction_history�next_prediction_update�	isoformat�
session_stats�system_metrics�advanced_analysis�technical_indicators�print�strftime)�selfs    �,E:\Dev\ultimate_bitcoin_trading_system_v5.py�__init__z'UltimateBitcoinTradingSystemV5.__init__0   s�  � �B������'��� "*������ �<�>�>������#���!��� ���!#���"�n�n��� ������ $���(*��%�)+��&�*,��'� #%���"$���&*��#� "&�!7�!A�!A�!C�!C�!"�#$� $�!� �"#� �!�)-�#&�!"��
� 
���$ ��"$����!&�
� 
��� #%� "�+-�&(�&(�$&�
"
� "
��� %'��!�
�C�D�D�D�
�(�$�,�(�(�)�)�)�
�S�T�3�<�<�=P�Q�Q�S�S�T�T�T�
�@�A�A�A�
�A�B�B�B�B�B�    �returnc                 ��  � 	 t          d�  �         t          j        �   �         }| j        r�t          j        �   �         | j        z
  t          | j        ��  �        k     rj| j        j        s^t          dt          j        �   �         | j        z
  j	        � d��  �         t          d| j        d         dz   �  �        | j        d<   | j        S 	 t          j
        | j        �  �        }|�                    dd	�
�  �        }|j        rt!          d�  �        �| j        dxx         d
z
  cc<   t          dt#          |�  �        � d��  �         n=# t           $ r0}t          d|� ��  �         | �                    �   �         }Y d}~nd}~ww xY w| �                    �   �         }| �                    ||�  �        }| �                    |�  �        }|| _        t          j        �   �         | _        t          j        �   �         |z
  }| j        d         �                    |�  �         t#          | j        d         �  �        dk    r| j        d         dd�         | j        d<   t          dt#          |�  �        � d|d�d��  �         |S # t           $ rt}t          d|� ��  �         | j        dxx         d
z
  cc<   t1          |�  �        | j        d<   | j        j        s| j        cY d}~S | �                    �   �         cY d}~S d}~ww xY w)z�
        OPTIMIERTE MARKTDATEN V5.0
        ==========================
        Stabile Datensammlung mit erweiterten Metriken
        z$Sammle optimierte Marktdaten V5.0...)�secondszVerwende Cache-Daten (Alter: zs)�      �?r   皙�����?�30d�1h)�period�intervalz"Keine Yahoo Finance Daten erhaltenr   �   zYahoo Finance Daten: � DatenpunktezYahoo Finance Fehler: Nr"   �d   i�����Marktdaten V5.0 optimiert: z Datenpunkte in �.2f�szFEHLER bei Marktdaten V5.0: r#   r%   )rH   �timer5   r   r0   r   r6   r3   �emptyrP   �minrD   �yf�Tickerr.   �history�	Exception�len�_generate_fallback_data�_fetch_binance_price_v5�_clean_market_data_v5�_calculate_extended_metrics_v5rE   �append�str)rJ   �
start_time�btc�hist�e�
binance_price�df�
fetch_times           rK   �get_optimized_market_data_v5z;UltimateBitcoinTradingSystemV5.get_optimized_market_data_v5�   s>  � �;	6��8�9�9�9�����J� �$� 
(������!5�5�	�$�J]�8^�8^�8^�^�^��$�*� _��i�x�|�~�~��H\�7\�6e�i�i�i�j�j�j�7:�3��@R�Sc�@d�gj�@j�7k�7k��"�#3�4��'�'�

6��i���,�,���{�{�%�$�{�?�?���:� J�#�$H�I�I�I��"�#4�5�5�5��:�5�5�5��E�c�$�i�i�E�E�E�F�F�F�F��� 
6� 
6� 
6��2�q�2�2�3�3�3��3�3�5�5�����������
6���� !�8�8�:�:�M� �+�+�D�-�@�@�B� �4�4�R�8�8�B�  "�D��#+�<�>�>�D� � ����z�1�J��� 4�5�<�<�Z�H�H�H��4�&�';�<�=�=��C�C�<@�<O�Pd�<e�fj�fk�fk�<l��#�$8�9��Z��B���Z�Z��Z�Z�Z�Z�[�[�[��I��� 		6� 		6� 		6��4��4�4�5�5�5���
�.�.�.�!�3�.�.�.�03�A���D���-� �#�)� 
6��'�'�'�'�'�'�'��3�3�5�5�5�5�5�5�5�5�����		6���sW   �CI6 �A;E �I6 �
E>�&E9�4I6 �9E>�>C7I6 �6
K4� AK/�K4�K/�)K4�/K4c                 �\  � 	 d| j         � �}t          j        |d��  �        }|j        dk    r>|�                    �   �         }t          |d         �  �        }t
          d|d���  �         |S t
          d|j        � ��  �         n)# t          $ r}t
          d	|� ��  �         Y d
}~nd
}~ww xY wdS )z"Sichere Binance Preis-Abfrage V5.0z3https://api.binance.com/api/v3/ticker/price?symbol=�
   )�timeout��   �pricezBinance Preis: $�,.2fzBinance API Fehler: Status zBinance Fehler: Ng    �>�@)r/   �requests�get�status_code�json�floatrH   rc   )rJ   �url�response�datarw   rn   s         rK   rf   z6UltimateBitcoinTradingSystemV5._fetch_binance_price_v5�   s�   � �
	*�]��H[�]�]�C��|�C��4�4�4�H��#�s�*�*��}�}�����d�7�m�,�,���5��5�5�5�6�6�6����J�H�4H�J�J�K�K�K�K��� 	*� 	*� 	*��(�Q�(�(�)�)�)�)�)�)�)�)�����	*���� �xs   �A(B �+B �
B)�
B$�$B)c                 ��  � t          d�  �         t          j        t          j        �   �         t          d��  �        z
  t          j        �   �         d��  �        }d}g }|}t
          t          |�  �        �  �        D �],}t          j	        �
                    dd�  �        }t          d	t          d
|�  �        �  �        }|d|z   z  }|dt          t          j	        �
                    dd�  �        �  �        z   z  }|dt          t          j	        �
                    dd�  �        �  �        z
  z  }|dt          j	        �
                    dd
�  �        z   z  }	|�                    |	t          |||	�  �        t          |||	�  �        |t          j	        �                    dd�  �        d��  �         ��.t          j        ||��  �        }
t          dt          |
�  �        � d��  �         |
S )z%Generiere realistische Fallback-Datenz(Generiere realistische Fallback-Daten...�   )�days�H)�start�end�freq�� r   �{�G�zt?g{�G�z���{�G�z�?rW   g����Mb`?g����MbP?��  i�  )�Open�High�Low�Close�Volume��indexzFallback-Daten generiert: rX   )rH   r1   �
date_ranger   r0   r   �rangerd   �np�random�normal�maxr_   �absri   �uniformr2   )rJ   �dates�
base_price�
price_data�
current_price�i�change�high�low�
open_pricerp   s              rK   re   z6UltimateBitcoinTradingSystemV5._generate_fallback_data�   s�  � �
�8�9�9�9� �
�H�L�N�N�Y�B�5G�5G�5G�$G�!)����c�;� ;� ;�� �
��
�"�
��s�5�z�z�"�"� 	� 	�A��Y�%�%�a��/�/�F����D�&� 1� 1�2�2�F��a�&�j�)�M� !�A��B�I�,<�,<�Q��,F�,F�(G�(G�$G�H�D��1�s�2�9�+;�+;�A�u�+E�+E�'F�'F�#F�G�C�&�!�b�i�.>�.>�q�%�.H�.H�*H�I�J����"��D�-��<�<��3�
�z�:�:�&��)�+�+�D�$�7�7�� � 
� 
� 
� 
� �\�*�E�
2�
2�
2��
�@�3�r�7�7�@�@�@�A�A�A��	rM   Nrp   ro   c                 ��  � 	 |j         r|S |�                    �   �         }dD ]J}||j        v r?||         �                    �   �         }|||         |dz  k    ||         |dz  k     z           }�Kt          rYt          |�  �        dk    rFdD ]C}||j        v r8t
          j        t          j	        ||         �  �        �  �        }||dk              }�D|rc|j         s\|d         j
        d         }t          ||z
  �  �        |z  }|dk    r.t          d	|d
�d|d
���  �         ||j        |j
        d         df<   t          dt          |�  �        � d
��  �         |S # t          $ r}t          d|� ��  �         |cY d}~S d}~ww xY w)z Erweiterte Datenbereinigung V5.0)r�   r�   r�   r�   �      �?g      �?rt   �   r�   �����皙�����?z Preisabweichung erkannt: Yahoo $r[   z
 vs Binance $zDaten bereinigt: u    gültige Datenpunktez"Fehler bei Datenbereinigung V5.0: N)r^   �dropna�columns�median�SCIPY_AVAILABLErd   r�   r�   r   �zscore�ilocrH   �locr�   rc   )	rJ   rp   ro   �col�median_price�z_scores�
last_price�
price_diffrn   s	            rK   rg   z4UltimateBitcoinTradingSystemV5._clean_market_data_v5�   s�  � �&	��x� 
��	� �����B� 8� 
� 
���"�*�$�$�#%�c�7�>�>�#3�#3�L���C��<�#�#5�5��C��<�#�#5�5�7��B�� � 
.�3�r�7�7�R�<�<�;� .� .�C��b�j�(�(�#%�6�%�,�r�#�w�*?�*?�#@�#@����1��-��� � 
B�R�X� 
B���[�-�b�1�
� ��m�!;�<�<�z�I�
���$�$��m�Z�m�m�m�Zg�m�m�m�n�n�n�4A�B�F�2�8�B�<��0�1��D�c�"�g�g�D�D�D�E�E�E��I��� 	� 	� 	��:�q�:�:�;�;�;��I�I�I�I�I�I�����	���s#   �E �EE �
E;�E6�0E;�6E;c                 �  � 	 |j         st          |�  �        dk     r|S |d         �                    �   �         |d<   |d         |d         z
  |d<   t          j        |d         |d         �                    d�  �        z
  �  �        |d<   t          j        |d         |d         �                    d�  �        z
  �  �        |d	<   |g d
�         �                    d��  �        |d<   |d         |d         z
  |d         z  |d
<   d|j        v rD|d         �                    d�  �        �	                    �   �         |d<   |d         |d         z  |d<   |d         �                    d�  �        �
                    �   �         |d<   |d         �                    d�  �        �
                    �   �         |d<   |S # t          $ r}t          d|� ��  �         |cY d}~S d}~ww xY w)z!Berechne erweiterte Metriken V5.0�   r�   �Returnsr�   r�   �High_LowrW   �
High_Close�	Low_Close)r�   r�   r�   ��axis�
True_Range�Intraday_Ranger�   �   �	Volume_MA�Volume_Ratiort   �
Volatility_10�
Volatility_20z!Fehler bei erweiterten Metriken: N)
r^   rd   �
pct_changer�   r�   �shiftr�   r�   �rolling�mean�stdrc   rH   )rJ   rp   rn   s      rK   rh   z=UltimateBitcoinTradingSystemV5._calculate_extended_metrics_v5&  s�  � �	��x� 
�3�r�7�7�Q�;�;��	� �w�K�2�2�4�4�B�y�M�  ��Z�"�U�)�3�B�z�N�!�v�b��j�2�g�;�3D�3D�Q�3G�3G�&G�H�H�B�|�� �f�R��Y��G��1B�1B�1�1E�1E�%E�F�F�B�{�O�!�"I�"I�"I�J�N�N�TU�N�V�V�B�|�� %'�v�J��E��$:�b��k�#I�B�� � �2�:�%�%�"$�X�,�"6�"6�r�":�":�"?�"?�"A�"A��;��%'��\�B�{�O�%C��>�"� #%�Y�-�"7�"7��";�";�"?�"?�"A�"A�B���"$�Y�-�"7�"7��";�";�"?�"?�"A�"A�B����I��� 	� 	� 	��9�a�9�9�:�:�:��I�I�I�I�I�I�����	���s#   �F. �FF. �.
G�8G�G�Gc           	      �<  � 	 |j         st          |�  �        dk     rt          d�  �         i S t          d�  �         i }|d         }|d         }|d         }d|j        v r|d         n$t	          j        dgt          |�  �        z  �  �        }|�                    d	�  �        �                    �   �         j        d
         |d<   |�                    d�  �        �                    �   �         j        d
         |d
<   |�                    d�  �        �                    �   �         j        d
         |d<   |�	                    d��  �        �                    �   �         j        d
         |d<   |�	                    d��  �        �                    �   �         j        d
         |d<   |�
                    �   �         }|�                    |dk    d�  �        �                    d��  �        �                    �   �         }|�                    |dk     d�  �         �                    d��  �        �                    �   �         }	||	z  }
ddd|
j        d
         z   z  z
  |d<   |�	                    d��  �        �                    �   �         }|�	                    d��  �        �                    �   �         }||z
  }
|
�	                    d��  �        �                    �   �         }|
j        d
         |d<   |j        d
         |d<   |
|z
  j        d
         |d<   d}d}|�                    |�  �        �                    �   �         }|�                    |�  �        �                    �   �         }|||z  z   }|||z  z
  }|j        d
         |d<   |j        d
         |d <   |j        d
         |d!<   |j        d
         |j        d
         z
  |j        d
         z  |d"<   |j        d
         |j        d
         z
  |j        d
         |j        d
         z
  z  |d#<   d$|j        v rU|d$         �                    d�  �        �                    �   �         j        d
         |d%<   |d%         |j        d
         z  |d&<   n�||z
  }t          j        ||�                    d�  �        z
  �  �        }t          j        ||�                    d�  �        z
  �  �        }t	          j        |||gd�'�  �        �                    d�'�  �        }|�                    d�  �        �                    �   �         j        d
         |d%<   |d%         |j        d
         z  |d&<   |�                    �   �         D ]Z\  }}t	          j        |�  �        st          j        |�  �        r-d(|v rd)||<   �7d*|v rd+||<   �Ad,|v rd-||<   �Kd.|v rd/||<   �Ud0||<   �[|�                    �   �         | _        t          d1t          |�  �        � d2��  �         |S # t.          $ rC}t          d3|� ��  �         d)d0d0d+d4|j         s|d         j        d
         d/z  nd5d/d6�cY d7}~S d7}~ww xY w)8z�
        STABILE TECHNISCHE INDIKATOREN V5.0
        ===================================
        Realistische und stabile Indikator-Berechnung
        �2   u1   Nicht genügend Daten für technische Indikatorenz/Berechne stabile technische Indikatoren V5.0...r�   r�   r�   r�   r�   rt   r�   �sma_10r�   �sma_20�sma_50�   )�span�ema_12�   �ema_26r   �   )�windowrY   rW   �rsi_14�	   �macd�macd_signal�macd_histogramr�   �bb_upper_20�bb_middle_20�bb_lower_20�bb_width_20�bb_position_20r�   �atr_14�	atr_ratior�   �rsig      I@�bb_positionr�   �volume_ratiorQ   �
volatilityr�   r   �/Stabile technische Indikatoren V5.0 berechnet: � Indikatorenz)FEHLER bei technischen Indikatoren V5.0: rR   i�  )r�   r�   r�   r�   r�   r�   r�   N)r^   rd   rH   r�   r1   �Seriesr�   r�   r�   �ewm�diff�wherer�   r�   r�   r�   �concatr�   �items�isna�isinf�copyrG   rc   )rJ   rp   �
indicators�prices�highs�lows�volumes�delta�gain�loss�rsr�   r�   �	macd_liner�   �	bb_period�bb_std�	bb_middle�
bb_std_dev�bb_upper�bb_lower�high_low�
high_close�	low_close�
true_range�key�valuern   s                               rK   �(calculate_stable_technical_indicators_v5zGUltimateBitcoinTradingSystemV5.calculate_stable_technical_indicators_v5G  s5  � �a	��x� 
�3�r�7�7�R�<�<��I�J�J�J��	��C�D�D�D��J���[�F��v�J�E��e�9�D�&.�"�*�&<�&<�b��l�l�"�)�T�F�UX�Y[�U\�U\�L\�B]�B]�G� $*�>�>�"�#5�#5�#:�#:�#<�#<�#A�"�#E�J�x� �#)�>�>�"�#5�#5�#:�#:�#<�#<�#A�"�#E�J�x� �#)�>�>�"�#5�#5�#:�#:�#<�#<�#A�"�#E�J�x� �#)�:�:�2�:�#6�#6�#;�#;�#=�#=�#B�2�#F�J�x� �#)�:�:�2�:�#6�#6�#;�#;�#=�#=�#B�2�#F�J�x� � �K�K�M�M�E��K�K���	�1�-�-�6�6�b�6�A�A�F�F�H�H�D��[�[����A�.�.�.�7�7�r�7�B�B�G�G�I�I�D����B�#&�#��R�W�R�[��*A�#B�J�x� � �Z�Z�R�Z�(�(�-�-�/�/�F��Z�Z�R�Z�(�(�-�-�/�/�F����I�#�-�-�Q�-�/�/�4�4�6�6�K�!*���!3�J�v��(3�(8��(<�J�}�%�,5��,C�+I�"�+M�J�'�(� �I��F����y�1�1�6�6�8�8�I����	�2�2�6�6�8�8�J� �J��$7�8�H� �J��$7�8�H�(0�
�b�(9�J�}�%�)2���);�J�~�&�(0�
�b�(9�J�}�%�)1��r�):�X�]�2�=N�)N�R[�R`�ac�Rd�(d�J�}�%�,2�K��O�h�m�B�>O�,O�T\�Ta�bd�Te�hp�hu�vx�hy�Ty�+z�J�'�(� �r�z�)�)�')�,�'7�'?�'?��'C�'C�'H�'H�'J�'J�'O�PR�'S�
�8�$�*4�X�*>���R��*P�
�;�'�'� !�4�<���V�E�F�L�L��O�O�$;�<�<�
��F�4�&�,�,�q�/�/�#9�:�:�	��Y��*�i�'H�q�Q�Q�Q�U�U�[\�U�]�]�
�'1�'9�'9�"�'=�'=�'B�'B�'D�'D�'I�"�'M�
�8�$�*4�X�*>���R��*P�
�;�'� )�.�.�0�0� 
.� 
.�
��U��7�5�>�>� .�R�X�e�_�_� .���|�|�*.�
�3���&�#�-�-�*-�
�3���'�3�.�.�*-�
�3���%��,�,�*.�
�3���*-�
�3��� )3���(9�(9�D�%��a�C�
�O�O�a�a�a�b�b�b����� 	� 	� 	��A�a�A�A�B�B�B� ��"�"%�"�=?�X�O�"�W�+�*�2�.��5�5�4�!�� � 
� 
� 
� 
� 
� 
�����		���s#   �*W �V W �
X�8X�X�Xr�   c           
      �>  � 	 |j         s|st          d�  �         i S t          d�  �         |d         j        d         }t          j        �   �         }| �                    |�  �        }|�                    dd�  �        }t          dt          d|�  �        �  �        }g }t          d	d
�  �        D �]}|d	|dz  z
  z  }	| �
                    |t          |��  �        z   �  �        }
t          j
        �                    d|d
z  �  �        }d|	z  |dz  z  }|	|
z   |z   |z   dz  }
t          dt          d|
�  �        �  �        }
|d	|
z   z  }t          dd|dz  z
  |dz  z
  �  �        }|
dk    rd}n|
dk     rd}nd}||t          |��  �        z   ||
dz  ||t          |
�  �        dz  |	|
||d�d�}|�                    |�  �         |}��|| _        |d         }|d         }|d         |d         |d          |d!         d"�|d         |d         |d         j        d         z
  |d         j        d         z  dz  |d          |d!         d"�t          d#� |D �   �         �  �        t          d$� |D �   �         �  �        t          j        d%� |D �   �         �  �        d&�| �                    |�  �        d'�}t          d(t)          |�  �        � d)��  �         |S # t*          $ rM}t          d*|� ��  �         |j         s|d         j        d         nd+}|d,z  dd-dd"�|d.z  d
d
dd"�d/�cY d0}~S d0}~ww xY w)1z�
        STABILE 24H-PROGNOSE V5.0
        =========================
        Realistische und stabile Prognosen ohne extreme Schwankungen
        u(   Keine Daten für 24h-Prognose verfügbarz%Berechne stabile 24h-Prognose V5.0...r�   r�   r�   r�   r�   r�   rW   �   )�hoursr   r�   g���������   rR   g{�G�z���{�G�z�?皙�����?��������?�   g�~j�t�h?�ANSTIEGg�~j�t�h��	   RÜCKGANG�
   SEITWÄRTSrY   )�trend_score�time_factor�volatility_factor�mean_reversion)�hourr]   �predicted_price�price_change_percent�
confidence�trend�trend_strength�factorsr  r  r  r  )r  r  r  r  c                 �   � g | ]
}|d          ��S �r  � ��.0�ps     rK   �
<listcomp>zUUltimateBitcoinTradingSystemV5.calculate_stable_24h_prediction_v5.<locals>.<listcomp>  �   � �%W�%W�%W�q�a�(9�&:�%W�%W�%WrM   c                 �   � g | ]
}|d          ��S r  r  r   s     rK   r#  zUUltimateBitcoinTradingSystemV5.calculate_stable_24h_prediction_v5.<locals>.<listcomp>  r$  rM   c                 �   � g | ]
}|d          ��S )r  r  r   s     rK   r#  zUUltimateBitcoinTradingSystemV5.calculate_stable_24h_prediction_v5.<locals>.<listcomp>  s   � �.[�.[�.[�1�q���.[�.[�.[rM   )�	min_price�	max_price�avg_confidence)�next_hour_prediction�24h_prediction�prediction_range�trend_distributionz%Stabile 24h-Prognose V5.0 berechnet: u    stündliche VorhersagenzFEHLER bei 24h-Prognose V5.0: r�   gj�t��?�333333�?g�G�z�?)r*  r+  N)r^   rH   r�   r   r0   � _calculate_stable_trend_score_v5rz   r�   r_   r�   �_get_time_based_factor_v5r   r�   r�   r�   r�   ri   r@   r�   �_analyze_trend_distribution_v5rd   rc   )rJ   rp   r�   r�   �current_timer  r�   r@   r  �trend_factorr  r  r  �total_changer  r  r  �
prediction�final_prediction�first_prediction�prediction_summaryrn   s                         rK   �"calculate_stable_24h_prediction_v5zAUltimateBitcoinTradingSystemV5.calculate_stable_24h_prediction_v5�  sW  � �{	��x� 
�z� 
��@�A�A�A��	��9�:�:�:��w�K�,�R�0�M�#�<�>�>�L� �?�?�
�K�K�K� $����T�:�:�J��U�C��j�$9�$9�:�:�J� "$���a���� 4
0� 4
0��*�a�4�$�;�.?�@�� #�<�<�\�I�\`�La�La�La�=a�b�b�� %'�I�$4�$4�Q�
�S�8H�$I�$I�!� "&��!4��r�	�!B�� !-�{� :�=N� N�Q_� _�cf�f��  #�5�#�d�L�*A�*A�B�B�� #0�1�|�3C�"D�� !��c�T�D�[�&9�Z�!�^�&L�M�M�
�  �%�'�'�%�E�E�!�F�*�*�'�E�E�(�E� !�(�9�4�+@�+@�+@�@�'6�,8�3�,>�",�"�&)�,�&7�&7�#�&=�'3�'2�->�*8�	 �  �� �
�  #�)�)�*�5�5�5� !0�
�
� '9�D�#�  2�"�5��1�!�4�� (8�8I�'J�,<�=S�,T�"2�<�"@�-�g�6�	)� )� (8�8I�'J�.>�?P�.Q�TV�W^�T_�Td�eg�Th�.h�ln�ov�lw�l|�}�  mA�  .A�  EH�  -H�"2�<�"@�-�g�6�	#� #� "%�%W�%W�DV�%W�%W�%W�!X�!X�!$�%W�%W�DV�%W�%W�%W�!X�!X�&(�g�.[�.[�HZ�.[�.[�.[�&\�&\�%� %�
 '+�&I�&I�J\�&]�&]�%"� "��* 
�k�#�>P�:Q�:Q�k�k�k�l�l�l�%�%��� 	� 	� 	��6�1�6�6�7�7�7� 9;��L�B�w�K�,�R�0�0�f�M� (5�u�'<�,/�"%�)�	)� )� (5�u�'<�,/�"%�)�	#� #�
� 
� 

� 

� 

� 

� 

� 

�����	���s$   �K �J(K �
L�AL�L�Lc                 ��  � 	 d}|�                     dd�  �        }|dk    r|dz  }n|dk     r|dz
  }|�                     dd�  �        }|�                     d	d�  �        }||k    r|d
z
  }n|d
z  }|�                     dd�  �        }|d
k    r|d
z  }n|dk     r|d
z
  }t          dt          d|�  �        �  �        S # t          $ r}t	          d|� ��  �         Y d}~dS d}~ww xY w)z"Berechne stabilen Trend-Score V5.0r   r�   r�   �F   rR   r�   r�   r   r�   r�   r�   r�   皙�����?皙�����?g������ɿzFehler bei Trend-Score: N)rz   r�   r_   rc   rH   �rJ   r�   �scorer�   r�   r�   r�   rn   s           rK   r/  z?UltimateBitcoinTradingSystemV5._calculate_stable_trend_score_v53  s+  � �	��E� �.�.��2�.�.�C��R�x�x�������r������� �>�>�&�!�,�,�D�$�.�.���:�:�K��k�!�!���
�����
�� %�.�.�)9�3�?�?�K��S� � ���
����s�"�"���
�� �t�S��e�_�_�-�-�-��� 	� 	� 	��0�Q�0�0�1�1�1��3�3�3�3�3�����	���s   �B6B9 �9
C �C�C �target_timec                 �   � 	 |j         }d|cxk    rdk    rn ndS d|cxk    rdk    rn ndS d|cxk    rdk    rn nd	S d
S # t          $ r}Y d}~d
S d}~ww xY w)z#Berechne zeitbasierte Faktoren V5.0r�   �   r  r�   �   �{�G�zt�r   r�   r�   r   N)r  rc   )rJ   r@  r  rn   s       rK   r0  z8UltimateBitcoinTradingSystemV5._get_time_based_factor_v5U  s�   � �	��#�D� �T�����R�������t��t�!�!�!�!�r�!�!�!�!�!��v��d�����a�������u��s��� 	� 	� 	��3�3�3�3�3�����	���s   �A �A �A �
A�A�predictionsc                 �   � 	 d� |D �   �         }i }dD ]}|�                     |�  �        ||<   �|S # t          $ r}ddicY d}~S d}~ww xY w)z Analysiere Trend-Verteilung V5.0c                 �   � g | ]
}|d          ��S )r  r  r   s     rK   r#  zQUltimateBitcoinTradingSystemV5._analyze_trend_distribution_v5.<locals>.<listcomp>m  s   � �6�6�6�Q�a��j�6�6�6rM   )r  r  r  r  r
  N)�countrc   )rJ   rE  �trends�distributionr  rn   s         rK   r1  z=UltimateBitcoinTradingSystemV5._analyze_trend_distribution_v5j  s�   � �
	&�6�6�+�6�6�6�F��L�?� 
:� 
:��&,�l�l�5�&9�&9��U�#�#����� 	&� 	&� 	&� �"�%�%�%�%�%�%�%�����	&���s   �,/ �
A�A�A�Ac                 �v  � 	 t          d�  �         t          j        �   �         }| �                    �   �         }|j        rt	          d�  �        �t          dt          |�  �        � d��  �         | �                    |�  �        }|st	          d�  �        �t          dt          |�  �        � d��  �         t          | j        �  �        dk    st          |�  �        d	k    rFt          d
�  �         | �                    ||�  �        }|rt          d�  �         nt          d�  �         | �	                    ||�  �        }| �
                    ||�  �        }| �                    |||�  �         | �                    �   �          | �
                    |�  �         t          j        �   �         |z
  }| j        d
xx         |z
  cc<   i dt          j        �   �         �                    �   �         �d|�dt          |�  �        �d|d         j        d         �d|�                    dd�  �        �d|�                    dd�  �        �d|�                    dd�  �        �dt          | j        �  �        �d|�d|�d| j        �                    �   �         �d| j        �d| j        �                    �   �         �d| j        �                    �   �         �d t          |�  �        dk    r|j        d         �                    �   �         nd!t          |�  �        dk    r|j        d         �                    �   �         nd!d"|j        v r't5          |d"         �                    �   �         �  �        ndd#|j        v r't5          |d#         �                    �   �         �  �        ndd$|j        v r't5          |d$         �                    �   �         �  �        ndt          |�  �        d%k    r9t5          |d         �                    �   �         �                    �   �         �  �        ndd&�d'��d(|t          |�  �        t          | j        �  �        | j        �                    d)d�  �        | j        �                    d*d�  �        t          j        �   �         | j         z
  �!                    �   �         d+��}t          d,|d-�d.��  �         t          d/|d         � d0|d         d1�d2��  �         t          d3| j        �                    d4d�  �        d1���  �         |S # t          $ r�}	t          d5|	� ��  �         dd!l"}
|
�#                    �   �          t          j        �   �         �                    �   �         dtI          |	�  �        d6d7d8d| j        �                    �   �         | j        �                    �   �         | j        �                    �   �         d9�
cY d!}	~	S d!}	~	ww xY w):u�   
        ULTIMATIVE MARKTANALYSE V5.0
        ============================
        Vollständige fehlerfreie Analyse mit stabilen Prognosen
        z$Starte Ultimate Marktanalyse V5.0...u   Keine Marktdaten verfügbarrZ   rX   z5Technische Indikatoren konnten nicht berechnet werdenr�   r�   r   rY   u"   Führe ML-Modell Training durch...u$   ✅ ML-Modelle erfolgreich trainiertu<   ⚠️ ML-Training suboptimal - verwende verfügbare Modeller   �	timestamp�
analysis_time�data_pointsr�   r�   r�   �signal�HALTENr  r�   �
ml_prediction�models_availablerG   r8  rD   r@   rF   rE   �market_data_summaryNr�   r�   r�   rW   )r�   r�   r�   )�period_start�
period_end�total_volume�price_range�performance_metricsr   r   )rM  �indicators_calculated�ml_models_usedr   �api_calls_totalr$   z'Ultimate Analyse V5.0 abgeschlossen in r[   r\   zSignal: �
 (Konfidenz: �.1%�)zAktuelle Genauigkeit: r   z%FEHLER bei ultimativer Analyse V5.0: �FEHLERr   r�   )
rL  rM  �errorrO  r  r�   rR  rD   rE   rF   )%rH   r]   rr   r^   rc   rd   r  r7   �train_ml_models_v5�make_stable_prediction_v5r9  �update_advanced_analysis_v5�update_system_monitoring_v5�_update_session_stats_v5rD   r   r0   rC   r�   rz   r�   r@   rF   rE   r�   r�   r}   �sumr�   r_   r�   r�   r   �
total_seconds�	traceback�	print_excrj   )rJ   rk   rp   r�   �
ml_trained�prediction_resultr8  rM  �resultrn   rh  s              rK   �run_ultimate_analysis_v5z7UltimateBitcoinTradingSystemV5.run_ultimate_analysis_v5x  s  � �i	��8�9�9�9�����J� �2�2�4�4�B��x� 
?�� =�>�>�>��E��B���E�E�E�F�F�F� �F�F�r�J�J�J�� 
Y�� W�X�X�X��a�C�
�O�O�a�a�a�b�b�b� �4�>�"�"�a�'�'�3�r�7�7�c�>�>��:�;�;�;�!�4�4�R��D�D�
�� Z��@�A�A�A�A��X�Y�Y�Y� !%� >� >�r�:� N� N�� "&�!H�!H��Z�!X�!X�� 
�,�,�R��=N�O�O�O� 
�,�,�.�.�.� 
�)�)�*;�<�<�<� !�I�K�K�*�4�M���4�5�5�5��F�5�5�5�!��X�\�^�^�5�5�7�7�!���!� �s�2�w�w�!�  ��G��!1�"�!5�	!�
 �+�/�/��(�C�C�!� �/�3�3�L�#�F�F�
!�  �!2�!6�!6���!L�!L�!� #�C���$7�$7�!� '�
�!� %�&8�!�  ��!3�!8�!8�!:�!:�!� %�d�&=�!� $�T�%;�%@�%@�%B�%B�!� !�$�"5�":�":�"<�"<�!� &�?B�2�w�w��{�{�B�H�Q�K�$9�$9�$;�$;�$;�PT�>A�"�g�g��k�k�"�(�2�,�"8�"8�":�":�":�t�AI�R�Z�AW�AW�E�"�X�,�*:�*:�*<�*<�$=�$=�$=�]^�;A�R�Z�;O�;O��b��j�n�n�&6�&6� 7� 7� 7�UV�9>�"�*�9L�9L�u�R��Y�]�]�_�_�5�5�5�RS�OR�SU�w�w�YZ�{�{�e�B�w�K�,B�,B�,D�,D�,H�,H�,J�,J�&K�&K�&K�`a�$� $�		(� 	(�!�2 &�%2�-0��_�_�&)�$�.�&9�&9�&*�&8�&<�&<�=M�q�&Q�&Q�'+�'9�'=�'=�>O�QR�'S�'S�'�|�~�~��0F�F�U�U�W�W�
(� (�3!�F�F 
�P�M�P�P�P�P�Q�Q�Q��W�V�H�-�W�W�F�<�<P�W�W�W�W�X�X�X��^�4�+=�+A�+A�BT�VW�+X�+X�^�^�^�_�_�_��M��� 	� 	� 	��=�!�=�=�>�>�>��������!�!�!� &�\�^�^�5�5�7�7�!"��Q���"�!�!'�$%�!%�!3�!8�!8�!:�!:�"&�"5�":�":�"<�"<�%)�%;�%@�%@�%B�%B�� � 
� 
� 
� 
� 
� 
�����
	���s   �S5S8 �8
V8�B+V3�-V8�3V8c                 �  � 	 t          d�  �         t          |�  �        dk     r"t          dt          |�  �        � d��  �         dS | �                    ||�  �        }|j        rt          d�  �         dS |d         �                    d�  �        |d         k    �                    t          �  �        }|�                    �   �         }t          t          |�  �        t          |�  �        �  �        }|j	        d	|�         }|j	        d	|�         }t          |�  �        d
k     rt          d�  �         dS | j
        �                    |�  �        }t          d
dd
d��  �        }|�
                    ||�  �         || j        d<   |�                    |�  �        }t!          ||�  �        }	t#          ||�  �        }
|	|
t%          dt          dd|
dz  z   �  �        �  �        t'          j        �   �         t          |j        �  �        d�| j        d<   | j        dxx         dz
  cc<   | j        dxx         dz
  cc<   t          d|
d�d| j        d         d         d���  �         dS # t.          $ r}t          d|� ��  �         Y d	}~dS d	}~ww xY w)zML-Modell Training V5.0z!Starte ML-Modell Training V5.0...rY   u%   Nicht genügend Daten für Training: z < 100Fu'   Keine Features für Training verfügbarr�   r�   Nr�   u+   Nicht genügend aligned Daten für Trainingrt   �*   rW   )�n_estimators�	max_depth�random_state�n_jobs�random_forest_v5r�   �ffffff�?r   r=  )�mser
   �accuracy�
training_time�
features_usedr   r   u$   ✅ ML-Modell V5.0 trainiert: R² = �.3fu   , Geschätzte Genauigkeit = rw  r]  TzFEHLER beim ML-Training V5.0: )rH   rd   �_create_ml_features_v5r^   r�   �astype�intr�   r_   r�   r9   �
fit_transformr   �fitr7   �predictr	   r
   r�   r]   r�   r8   rD   rc   )rJ   rp   r�   �features�target�
min_length�features_scaled�rf_modelrE  rv  �r2rn   s               rK   ra  z1UltimateBitcoinTradingSystemV5.train_ml_models_v5�  s�  � �?	��5�6�6�6��2�w�w��}�}��M�c�"�g�g�M�M�M�N�N�N��u� �2�2�2�z�B�B�H��~� 
��?�@�@�@��u� ��k�'�'��+�+�b��k�9�A�A�#�F�F�F��]�]�_�_�F� �S��]�]�C��K�K�8�8�J��}�[�j�[�1�H��[��*��-�F��8�}�}�r�!�!��C�D�D�D��u� #�k�7�7��A�A�O� -�����	� � �H� 
�L�L��&�1�1�1� 2:�D�N�-�.� #�*�*�?�;�;�K�$�V�[�9�9�C��&�+�.�.�B� ����S��t�b�3�h��%?�%?�@�@�!%����!$�X�%5�!6�!6�:� :�D�"�#5�6� 
��0�1�1�1�Q�6�1�1�1���3�4�4�4��9�4�4�4��  Z��  Z�  Z�  Z�]a�]s�  uG�  ^H�  IS�  ^T�  Z�  Z�  Z�  
[�  
[�  
[��4��� 	� 	� 	��6�1�6�6�7�7�7��5�5�5�5�5�����	���s,   �AI �,I �4B;I �1D)I �
J�&I>�>Jc                 �f  � 	 t          j        |j        ��  �        }|d         |d         �                    d�  �        �                    �   �         z  |d<   |d         |d         z  |d<   |d         |d         z  |d	<   |�                    �   �         D ]=\  }}t
          |t          t          f�  �        rt          j	        |�  �        s||d
|� �<   �>|d         �
                    �   �         |d<   |d         �
                    d�  �        |d
<   |d         �
                    d�  �        |d<   |d         �
                    �   �         �                    d�  �        �                    �   �         |d<   d|j        v r9|d         |d         �                    d�  �        �                    �   �         z  |d<   |�
                    �   �         }t          dt          |j        �  �        � dt          |�  �        � d��  �         |S # t           $ r/}t          d|� ��  �         t          j        �   �         cY d}~S d}~ww xY w)zErstelle ML-Features V5.0r�   r�   r�   �price_normalizedr�   r�   �high_low_ratior�   �close_open_ratio�
indicator_�
returns_1h�   �
returns_4hr
  �returns_24hrt   �
volatility_10r�   r�   �volume_normalizedzML-Features V5.0 erstellt: � Features, � SampleszFehler bei ML-Features: N)r1   r2   r�   r�   r�   r�   �
isinstancer}  r}   r�   r�   r�   r�   r�   rH   rd   rc   �rJ   rp   r�   r�  r  r  rn   s          rK   r{  z5UltimateBitcoinTradingSystemV5._create_ml_features_v5,  sC  � �!	"��|�"�(�3�3�3�H� ,.�g�;��G��9L�9L�R�9P�9P�9U�9U�9W�9W�+W�H�'�(�)+�F��b��i�)?�H�%�&�+-�g�;��F��+C�H�'�(� )�.�.�0�0� 
9� 
9�
��U��e�c�5�\�2�2� 9�2�7�5�>�>� 9�38�H�/�#�/�/�0�� &(��[�%;�%;�%=�%=�H�\�"�%'��[�%;�%;�A�%>�%>�H�\�"�&(��k�&<�&<�R�&@�&@�H�]�#� )+�7��(>�(>�(@�(@�(H�(H��(L�(L�(P�(P�(R�(R�H�_�%� �2�:�%�%�02�8��r�(�|�?S�?S�TV�?W�?W�?\�?\�?^�?^�0^��,�-�  ���(�(�H��i��H�4D�0E�0E�i�i�RU�V^�R_�R_�i�i�i�j�j�j��O��� 	"� 	"� 	"��0�Q�0�0�1�1�1��<�>�>�!�!�!�!�!�!�����	"���s   �G4G7 �7
H0�$H+�%H0�+H0c           
      ��  � 	 t          d�  �         | j        r| �                    ||�  �        }nd}| �                    |�  �        }|dz  |dz  z   }|dk    rd}t	          dd|dz
  z   �  �        }n9|dk     rd	}t	          ddd|z
  z   �  �        }nd
}dt          |dz
  �  �        dz  z   }|||||t
          | j        �  �        t          j        �   �         �	                    �   �         d�}t          d|� d
|d�d��  �         |S # t          $ rW}	t          d|	� ��  �         d
dddddt          |	�  �        t          j        �   �         �	                    �   �         d�cY d}	~	S d}	~	ww xY w)zStabile Vorhersage V5.0u'   Führe stabile Vorhersage V5.0 durch...r�   r.  r  r   �KAUFENr
  gffffff�?�	VERKAUFENrP  )rO  r  rQ  �technical_score�ml_score�models_usedrL  zStabile Vorhersage V5.0: r\  r]  r^  z%FEHLER bei stabiler Vorhersage V5.0: r   )rO  r  rQ  r�  r�  r�  r`  rL  N)rH   r7   �_ml_prediction_v5�_technical_prediction_v5r_   r�   rd   r   r0   rC   rc   rj   )
rJ   rp   r�   rQ  �technical_prediction�combined_predictionrO  r  r5  rn   s
             rK   rb  z8UltimateBitcoinTradingSystemV5.make_stable_prediction_v5Q  s�  � �2	��;�<�<�<� �~� 
$� $� 6� 6�r�:� F� F�
�
� #�
� $(�#@�#@��#L�#L� � $1�3�#6�;O�RU�;U�"V�� #�T�)�)�!�� ��c�-@�3�-F�&G�H�H�
�
�$�t�+�+�$�� ��c�S�3F�-F�&G�H�H�
�
�!�� �3�':�S�'@�#A�#A�C�#G�G�
� !�(�!4�#7�)�"�4�>�2�2�%�\�^�^�5�5�7�7�� �J� 
�T�f�T�T�:�T�T�T�T�U�U�U����� 	� 	� 	��=�!�=�=�>�>�>�"�!�!$�#&�� ��Q���%�\�^�^�5�5�7�7�	� 	� 	
� 	
� 	
� 	
� 	
� 	
�����	���s   �C>D �
E"�AE�E"�E"c                 �  � 	 d| j         vrdS | �                    ||�  �        }|j        rdS |j        dd�         j        }| j        �                    |�  �        }| j         d         �                    |�  �        d         }t          dt          d|�  �        �  �        S # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)	zML-Vorhersage V5.0rt  r�   r�   Nr   rR   r
  zML-Vorhersage Fehler: )r7   r{  r^   r�   �valuesr9   �	transformr�  r�   r_   rc   rH   )rJ   rp   r�   r�  �latest_featuresr�  r5  rn   s           rK   r�  z0UltimateBitcoinTradingSystemV5._ml_prediction_v5�  s�   � �	�!���7�7��s� �2�2�2�z�B�B�H��~� 
��s� '�m�B�C�C�0�7�O�"�k�3�3�O�D�D�O� ��(:�;�C�C�O�T�T�UV�W�J� �s�C��Z�0�0�1�1�1��� 	� 	� 	��.�1�.�.�/�/�/��3�3�3�3�3�����	���s"   �	B �B �A1B �
C�(C � Cc                 �  � 	 d}|�                     dd�  �        }|dk    r|dz  }n|dk     r|dz
  }|�                     dd�  �        }|�                     d	d�  �        }||k    r|d
z
  }n|d
z  }|�                     dd�  �        }|dk    r|d
z  }n|d
k     r|d
z
  }t          dt          d|�  �        �  �        S # t          $ r}Y d}~dS d}~ww xY w)zTechnische Vorhersage V5.0r�   r�   r�   r;  rR   r�   r�   r   r�   r�   r�   r<  r=  r
  N)rz   r�   r_   rc   r>  s           rK   r�  z7UltimateBitcoinTradingSystemV5._technical_prediction_v5�  s  � �	��E� �.�.��2�.�.�C��R�x�x�������r������� �>�>�&�!�,�,�D�$�.�.���:�:�K��k�!�!���
�����
�� %�.�.�)9�3�?�?�K��S� � ���
����s�"�"���
���s�C��U�O�O�,�,�,��� 	� 	� 	��3�3�3�3�3�����	���s   �B6B9 �9
C�	Cr5  c           	      �V
  � � 	 t          d�  �         d� j        v rω �                    ||�  �        }|j        s�� j        d         }t	          |d�  �        r�i }t          |j        �  �        D ]:\  }}|t          |j        �  �        k     rt          |j        |         �  �        ||<   �;t          |�                    �   �         d� d��  �        dd�         }	t          |	�  �        � j
        d	<   t          � j        �                    �   �         �  �        � j        �                    �   �         � j        r+t%          � j        �                    �   �         � fd
���  �        ndd�� j
        d
<   t'          j        �   �         j        }
d� j
        vr
i � j
        d<   t-          |
�  �        � j
        d         vrg � j
        d         t-          |
�  �        <   � j
        d         t-          |
�  �                 �                    |�                    dd�  �        t'          j        �   �         �                    �   �         d��  �         |�                    dd�  �        }t5          |�                    dd�  �        dz
  �  �        dz  }|dk    rd}
n|dk     rd}
nd}
|dk    r|
dz
  }
n|
dz
  }
|
||t7          d|dz  |z   �  �        d�� j
        d <   t          |�  �        dk    r�|d!         �                    �   �         �                    d"�  �        �                    �   �         j        d#d�         }|�                     �   �         �!                    �   �         }t          |j        d$         �  �        t          |�  �        |d%k    rd&nd't7          dt5          |�  �        d(z  �  �        d)�� j
        d*<   t          |�  �        d(k    �r*|d!         �                    �   �         �"                    �   �         }d+|j        v r,|d+         �                    �   �         �"                    �   �         nd}i }|�Et          |�  �        t          |�  �        k    r%t          |�#                    |�  �        �  �        |d,<   t          |�  �        d-k    rat          |�$                    d-�.�  �        �  �        |d/<   t          |�  �        d"k    r#t          |�$                    d"�.�  �        �  �        nd%|d0<   |� j
        d1<   t          d2t          � j
        �  �        � d3��  �         dS # tJ          $ r}t          d4|� ��  �         Y d}~dS d}~ww xY w)5zUpdate erweiterte Analyse V5.0z'Aktualisiere erweiterte Analyse V5.0...rt  �feature_importances_c                 �   � | d         S )NrW   r  )�xs    rK   �<lambda>zLUltimateBitcoinTradingSystemV5.update_advanced_analysis_v5.<locals>.<lambda>�  s
   � �Z[�\]�Z^� rM   T)r  �reverseNrt   r'   c                 �F   �� �j         |          �                    dd�  �        S )Nrw  r   �r8   rz   ��krJ   s    �rK   r�  zLUltimateBitcoinTradingSystemV5.update_advanced_analysis_v5.<locals>.<lambda>�  s    �� �d�.D�Q�.G�.K�.K�J�XY�.Z�.Z� rM   �r  )�available_modelsr8   �
best_modelr(   r)   r  r�   )r  rL  r�   r�   r�   r�   g���Q��?�HIGH_VOLATILITYr  �LOW_VOLATILITY�NORMAL_VOLATILITYr  �	_TRENDING�_RANGINGrQ   )�current_regime�volatility_levelr  �regime_confidencer*   r�   r
  �����r�   r   �
INCREASING�
DECREASINGrY   )�current_volatility�volatility_trend�forecast_direction�forecast_confidencer+   r�   �price_volumerW   )�lag�price_autocorr_1h�price_autocorr_24hr,   z&Erweiterte Analyse V5.0 aktualisiert: z Kategorienz%FEHLER bei erweiterter Analyse V5.0: )&rH   r7   r{  r^   �hasattr�	enumerater�   rd   r�  r}   �sortedr�   �dictrF   �list�keysr8   r�   r�   r   r0   r  rj   ri   rz   rC   r�   r_   r�   r�   r�   r�   r�   r�   r�   �corr�autocorrrc   )rJ   rp   r�   r5  r�  �model�importance_dictr�   r�   �sorted_importance�current_hourr�   r  �regime�recent_volatilityr�  �
price_returns�volume_returns�correlationsrn   s   `                   rK   rc  z:UltimateBitcoinTradingSystemV5.update_advanced_analysis_v5�  s�  �� �]	?��;�<�<�<� "�T�^�3�3��6�6�r�:�F�F���~� 
_� �N�+=�>�E��u�&<�=�=� _�*,��&/��0@�&A�&A� \� \�F�A�s� �3�u�'A�#B�#B�B�B�7<�U�=W�XY�=Z�7[�7[��� 4�� -3�?�3H�3H�3J�3J�P^�P^�hl�,m�,m�,m�nq�oq�nq�,r�)�GK�L]�G^�G^��.�/C�D� %)���)<�)<�)>�)>�$?�$?�%)�%;�%@�%@�%B�%B�_c�_u�@�c�$�"8�"=�"=�"?�"?�$Z�$Z�$Z�$Z�\� \� \� \�{�	:� :�D�"�#5�6� $�<�>�>�.�L�,�D�4J�J�J�HJ��&�'D�E��<� � ��(>�?\�(]�]�]�[]��&�'D�E�c�,�FW�FW�X��"�#@�A�#�l�BS�BS�T�[�[�(�n�n�\�3�?�?�%�\�^�^�5�5�7�7�]� ]� 
� 
� 
� $����T�:�:�J� �����"�!=�!=��!B�C�C�b�H�N��D� � �*����d�"�"�)���,����#�#��+�%����*�$�� #)�$.�"0�%(��j�2�o��.N�%O�%O�	@� @�D�"�#;�<� �2�w�w��|�|�$&�w�K�$:�$:�$<�$<�$D�$D�R�$H�$H�$L�$L�$N�$N�$S�TW�TX�TX�$Y�!�#4�#9�#9�#;�#;�#@�#@�#B�#B� � +0�0A�0F�r�0J�*K�*K�(-�.>�(?�(?�:J�Q�:N�:N�,�,�T`�+.�s�C�8H�4I�4I�C�4O�+P�+P�	D� D��&�'?�@� �2�w�w��}�}� "�7�� 6� 6� 8� 8� ?� ?� A� A�
�GO�SU�S]�G]�G]��H��!8�!8�!:�!:�!A�!A�!C�!C�!C�cg��!��!�-�#�n�2E�2E��]�I[�I[�2[�2[�38��9K�9K�N�9[�9[�3\�3\�L��0� �}�%�%��)�)�8=�m�>T�>T�YZ�>T�>[�>[�8\�8\�L�!4�5�be�fs�bt�bt�wy�by�by��}�?U�?U�Z\�?U�?]�?]�9^�9^�9^�  @A�L�!5�6�AM��&�'=�>��c�3�t�?U�;V�;V�c�c�c�d�d�d�d�d��� 	?� 	?� 	?��=�!�=�=�>�>�>�>�>�>�>�>�>�����	?���s   �S<T �
T(�T#�#T(c                 �  � 	 t          d�  �         t          j        �                    dd�  �        | j        d<   t          j        �                    dd�  �        | j        d<   t          j        �   �         | j        z
  �                    �   �         }|| j        d<   | j        d	         r)t          j	        | j        d	         �  �        }|| j        d
<   t          d| j        d         d
z  �  �        }t          d|dz  dz  �  �        }| j        �                    dd�  �        dz  }d|z
  |z   |z   }t          dt          d|�  �        �  �        | j        d<   |dk    rd}n|dk    rd}n|dk    rd}nd}|| j        d<   t          | j        �  �        t          | j        �  �        t          | j        �  �        | j        d         dk     d�| j        d<   t          d|� d|d�d ��  �         d"S # t$          $ r}t          d!|� ��  �         Y d"}~d"S d"}~ww xY w)#zUpdate System-Monitoring V5.0z&Aktualisiere System-Monitoring V5.0...rt   r�   r    r�   �(   r!   r$   r"   �avg_api_response_timer�   r#   r  i  r�   r   r   rY   r&   �Z   �	EXCELLENT�K   �GOOD�FAIR�POOR�
health_status)�
models_loaded�
cache_size�prediction_history_size�memory_efficient�resource_summaryz%System-Monitoring V5.0 aktualisiert: � (�.1fz/100)z#FEHLER bei System-Monitoring V5.0: N)rH   r�   r�   r�   rE   r   r0   r   rg  r�   r_   rD   rz   r�   rd   r7   r4   rA   rc   )	rJ   �uptime_seconds�avg_response_time�
error_penalty�uptime_bonus�cache_bonusr&   r�  rn   s	            rK   rd  z:UltimateBitcoinTradingSystemV5.update_system_monitoring_v5!  s[  � �/	=��:�;�;�;� 02�y�/@�/@��R�/H�/H�D���,�24�)�2C�2C�B��2K�2K�D���/� '�l�n�n�t�/E�E�T�T�V�V�N�,:�D���)� �"�#7�8� 
Q�$&�G�D�,?�@T�,U�$V�$V�!�?P��#�$;�<�  ��D�$7�
�$F��$J�K�K�M��r�>�D�#8�1�#<�=�=�L��,�0�0�1A�1�E�E��J�K� #�m� 3�l� B�[� P��7:�1�c�#�GX�>Y�>Y�7Z�7Z�D�� 3�4� !�2�%�%� +�
�
�"�R�'�'� &�
�
�"�R�'�'� &�
�
� &�
�3@�D���0� "%�T�^�!4�!4�!�$�/�2�2�+.�t�/F�+G�+G�$(�$7��$G�"�$L�	7� 7�D�� 2�3� 
�g�-�g�g�K\�g�g�g�g�h�h�h�h�h��� 	=� 	=� 	=��;��;�;�<�<�<�<�<�<�<�<�<�����	=���s   �G!G% �%
H�/H�Hc                 ��  � 	 | j         dxx         dz
  cc<   t          | j        �  �        dk    r�d}|�                    dd�  �        }t          j        �                    dd�  �        }||dz
  d	z  z   |z   }t          d
t          d|�  �        �  �        }|| j         d<   || j         d
         k    r|| j         d
<   | j         dxx         dz
  cc<   |�                    dd�  �        }| j         �                    dd�  �        }| j         d         }||dz
  z  |z   |z  }	|	| j         d<   t          j
        �   �         �                    �   �         |�                    dd�  �        ||�                    dd�  �        d�}
| j        �                    |
�  �         t          | j        �  �        dk    r| j        dd�         | _        dS dS # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)zUpdate Session-Statistiken V5.0r   rW   rt   r   r  r�   g��������r�   r=  r.  r
  r   r   r   r   rO  rP  rQ  )rL  rO  r  rQ  i�  i���Nz!FEHLER bei Session Stats Update: )rD   rd   rA   rz   r�   r�   r�   r�   r_   r   r0   rC   ri   rc   rH   )rJ   r5  �
base_accuracy�confidence_factor�
random_factor�new_accuracyr  �current_avgr   �new_avg�prediction_entryrn   s               rK   re  z7UltimateBitcoinTradingSystemV5._update_session_stats_v5T  s>  � �+	;���2�3�3�3�q�8�3�3�3� �4�*�+�+�b�0�0� $�
�$.�N�N�<��$E�$E�!� "�	� 1� 1�%�� >� >�
�,�0A�C�0G�3�/N�N�Q^�^��"�3��C��(>�(>�?�?��9E��"�#5�6��$�"4�_�"E�E�E�:F�D�&��7��&�';�<�<�<��A�<�<�<� $����c�:�:�J��,�0�0�1L�c�R�R�K� $� 2�3F� G��#�'8�1�'<�=��K�O`�`�G�>E�D��:�;� &�\�^�^�5�5�7�7�$�.�.��8�<�<�(�!+�����!E�!E�	 �  �� 
�#�*�*�+;�<�<�<� �4�*�+�+�c�1�1�*.�*A�$�%�%�*H��'�'�'� 2�1�� � 	;� 	;� 	;��9�a�9�9�:�:�:�:�:�:�:�:�:�����	;���s   �F9F? �?
G&�	G!�!G&c                 �X  � 	 t          d�  �         t          d�  �         t          d�  �         t          j        �   �         }| xj        dz
  c_        t          d| j        � d��  �         | �                    �   �         }|j        rt          d�  �        �t          dt
          |�  �        � d��  �         | �                    |�  �        }t          d	t
          |�  �        � ��  �         | �                    ||�  �        }| �	                    ||�  �        }t          j        �   �         |z
  }| j        t          j        �   �         �                    �   �         |t
          |�  �        |d
         j
        d         |||t
          | j        �  �        | �                    �   �         | �                    �   �         | j        t
          | j        �  �        | j        �                    �   �         | j        rt)          | j        �  �        ndd
�d�}| j        �                    |�  �         || _        | �                    |�  �         t          d| j        � d|d�d��  �         t          dt
          | j        �  �        � d|�                    dd�  �        d���  �         t
          | j        �  �        dk    rV| j        d         d         �                    dd�  �        }|�                    dd�  �        }	|	|z
  }
t          d|
d�d��  �         |S # t
          $ r�}t          d|� ��  �         ddl}|�                    �   �          | j        t          j        �   �         �                    �   �         t9          |�  �        dt
          | j        �  �        | j        t
          | j        �  �        d �d!�cY d}~S d}~ww xY w)"u�   
        PROGRESSIVER SCAN V5.2
        ======================
        Führt einen Scan mit progressivem ML-Training durch
        z<============================================================z STARTE PROGRESSIVEN SCAN V5.2...rW   zScan #z
 gestartet...u%   Keine Marktdaten für Scan verfügbarzMarktdaten gesammelt: rX   z"Technische Indikatoren berechnet: r�   r�   r   )�total_scans�training_data_size�accuracy_progressionr   )�scan_idrL  �	scan_timerN  r�   r�   r5  �training_success�model_count�accuracy_improvement�comparison_data�progressive_statsu   ✅ Progressiver Scan #z abgeschlossen in r[   r\   u   📊 Modelle: z, Genauigkeit: rw  r   r]  �����r5  u   📈 Verbesserung: z+.1%u    gegenüber vorherigem Scanu(   ❌ FEHLER beim progressiven Scan V5.2: NF)r�  r�  )r�  rL  r`  r�  r�  r�  )rH   r]   r;   rr   r^   rc   rd   r  �progressive_ml_training_v5�&enhanced_prediction_with_comparison_v5r   r0   rC   r�   r7   �_calculate_accuracy_improvement�_get_comparison_datar>   r?   r�   r�   r:   ri   r<   � _update_scan_performance_historyrz   rh  ri  rj   )
rJ   �scan_start_timerp   r�   r�  rk  r�  �scan_result�
prev_accuracyr   �improvementrn   rh  s
                rK   �run_progressive_scan_v5z6UltimateBitcoinTradingSystemV5.run_progressive_scan_v5�  s�  � �T	��(�O�O�O��4�5�5�5��(�O�O�O�"�i�k�k�O�����"����;�4�,�;�;�;�<�<�<� �2�2�4�4�B��x� 
I�� G�H�H�H��@�3�r�7�7�@�@�@�A�A�A� �F�F�r�J�J�J��H�s�:���H�H�I�I�I�  $�>�>�r�:�N�N�� !%� K� K�B�PZ� [� [�� �	���o�5�I�  �,�%�\�^�^�5�5�7�7�&�"�2�w�w�!#�G��!1�"�!5�(�/�$4�"�4�>�2�2�(,�(L�(L�(N�(N�#'�#<�#<�#>�#>�#'�#4�*-�d�.L�*M�*M�,0�,K�,P�,P�,R�,R�MQ�Ml�%u�S��)H�%I�%I�%I�ru�	&� &�� �K�* 
��$�$�[�1�1�1�$/�D�!� 
�1�1�+�>�>�>��a�D�,=�a�a�QZ�a�a�a�a�b�b�b��q�3�t�~�#6�#6�q�q�GX�G\�G\�]g�ij�Gk�Gk�q�q�q�r�r�r��4�$�%�%��)�)� $� 1�"� 5�l� C� G� G�
�TU� V� V�
�#4�#8�#8��Q�#G�#G� �.��>���Y�K�Y�Y�Y�Y�Z�Z�Z����� 	� 	� 	��@�Q�@�@�A�A�A��������!�!�!�  �,�%�\�^�^�5�5�7�7��Q���$)�"�4�>�2�2�#'�#4�*-�d�.L�*M�*M�&� &�
� 
� 

� 

� 

� 

� 

� 

�����
	���s   �K>L �
N)�BN$�N)�$N)c           
      �	  � 	 t          d�  �         t          j        �   �         �                    �   �         |�                    �   �         |�                    �   �         | j        d�}| j        �                    |�  �         t          | j        �  �        dk    r| j        dd�         | _        t          j
        �   �         }| j        D ]-}|d         j        st          j        ||d         gd��  �        }�.t          |�  �        d	k     r"t          d
t          |�  �        � d��  �         dS | �
                    ||�  �        }|j        rt          d
�  �         dS | �                    |�  �        }|j        rt          d�  �         dS t          t          |�  �        t          |�  �        �  �        }|j        d|�         }|j        d|�         }t          |�  �        dk     rt          d�  �         dS | j        �                    |�  �        }	ddlm}
 t          dd	| j        dz  z   �  �        }t          dd| j        z   �  �        } |
||ddt+          dt          |�  �        dz  �  �        t+          dt          |�  �        d	z  �  �        ��  �        }
|
�                    |	|�  �         d| j        � �}|
| j        |<   |
�                    |	�  �        }t3          ||�  �        }t5          ||�  �        }d}t          d| j        dz  �  �        }||z   |dz  z   }t+          dt          d |�  �        �  �        }|||t7          j        �   �         t          |j        �  �        t          |�  �        | j        ||d!�	| j        |<   | j        �                    |�  �         | j        d"xx         dz
  cc<   | j        d#xx         dz
  cc<   || j        d$<   || j        d%         k    r
|| j        d%<   t          d&�  �         t          d'|� ��  �         t          d(|� d)|� ��  �         t          d*|d+���  �         t          d,|d-���  �         t          d.t          |�  �        � ��  �         dS # t@          $ r}t          d/|� ��  �         Y d}~dS d}~ww xY w)0zProgressives ML-Training V5.2z"Starte progressives ML-Training...)rL  r�   r�   r�  rt   r�  Nr�   T)�ignore_indexr�   u1   Nicht genügend kombinierte Daten für Training: z < 50Fu4   Keine Features für progressives Training verfügbaru1   Kein Target für progressives Training verfügbarr�   u8   Nicht genügend aligned Daten für progressives Trainingr   r   rY   r  r�   ro  rW   r�   )rp  rq  rr  rs  �min_samples_split�min_samples_leaf�progressive_rf_scan_r   g333333�?r  rR   r.  ru  )	rv  r
   rw  rx  ry  �training_samplesr�  rp  rq  r   r   r   r   u%   ✅ Progressives ML-Modell trainiert:z   Modell: z   Estimators: z
, Max Depth: u      R² Score: rz  z   Genauigkeit: r]  z   Training Samples: u/   ❌ FEHLER beim progressiven ML-Training V5.2: )!rH   r   r0   rC   r�   r;   r>   ri   rd   r1   r2   r^   r�   �"_create_progressive_ml_features_v5�_create_enhanced_target_v5r_   r�   r9   r~  �sklearn.ensembler   r�   r  r7   r�  r	   r
   r]   r�   r8   r?   rD   rc   )rJ   rp   r�   �new_training_data�combined_df�
training_datar�  r�  r�  r�  r   rp  rq  r�  �
model_namerE  rv  r�  r�  �improvement_factor�progressive_accuracyrn   s                         rK   r   z9UltimateBitcoinTradingSystemV5.progressive_ml_training_v5�  s  � �v	��6�7�7�7� &�\�^�^�5�5�7�7����	�	�(�o�o�/�/��,�	!� !�� 
�*�1�1�2C�D�D�D� �4�1�2�2�R�7�7�15�1O�PS�PT�PT�1U��.� �,�.�.�K�!%�!?� 
e� 
e�
�$�V�,�2� e�"$�)�[�-��:O�,P�_c�"d�"d�"d�K���;���"�$�$��a�#�k�JZ�JZ�a�a�a�b�b�b��u� �>�>�{�J�W�W�H��~� 
��L�M�M�M��u� �4�4�[�A�A�F��|� 
��I�J�J�J��u� �S��]�]�C��K�K�8�8�J��}�[�j�[�1�H��[��*��-�F��8�}�}�r�!�!��P�Q�Q�Q��u� #�k�7�7��A�A�O� 
?�>�>�>�>�>� �s�B�$�*;�a�*?�$@�A�A�L��B��T�%6� 6�7�7�I�,�,�)�#���"%�a��X���"�)<�"=�"=�!$�Q��H�
�
��(;�!<�!<�
� � �H� 
�L�L��&�1�1�1� D��0A�C�C�J�)1�D�N�:�&� #�*�*�?�;�;�K�$�V�[�9�9�C��&�+�.�.�B� !�M�!$�T�4�+<�t�+C�!D�!D��#0�3E�#E��c��#R� �#&�s�C��6J�,K�,K�#L�#L� � ��0�!%����!$�X�%5�!6�!6�$'��M�M��,� ,�&�
2� 
2�D�"�:�.� 
�+�2�2�3G�H�H�H� 
��0�1�1�1�Q�6�1�1�1���3�4�4�4��9�4�4�4�5I�D��1�2�#�d�&8��&I�I�I�6J��"�?�3��:�;�;�;��,�
�,�,�-�-�-��J�L�J�J�y�J�J�K�K�K��+�2�+�+�+�,�,�,��?�%9�?�?�?�@�@�@��9�#�h�-�-�9�9�:�:�:��4��� 	� 	� 	��G�A�G�G�H�H�H��5�5�5�5�5�����	���s2   �D#Q �',Q �+Q �A*Q �.I/Q �
R�)R�Rc                 �	  � 	 t          j        |j        ��  �        }|d         |d         �                    d�  �        �                    �   �         z  |d<   |d         |d         z  |d<   |d         |d         z  |d	<   |d         |d         �                    d
�  �        z  |d<   |d         |d         �                    d�  �        z  |d
<   |d         |d         �                    d�  �        z  |d<   |d         �                    �   �         �                    d
�  �        �                    �   �         |d<   |d         �                    �   �         �                    d�  �        �                    �   �         |d<   |d         �                    �   �         �                    d�  �        �                    �   �         |d<   d|j        v rr|d         |d         �                    d�  �        �                    �   �         z  |d<   |d         |d         z  �                    d�  �        �                    �   �         |d<   |�	                    �   �         D ]=\  }}t          |t          t          f�  �        rt          j
        |�  �        s||d|� �<   �>|d         �                    �   �         |d<   |d         �                    d�  �        |d<   |d         �                    d�  �        |d<   |d         �                    d�  �        |d<   |d         �                    d�  �        �                    �   �         |d<   |d         �                    d�  �        �                    �   �         |d<   |d         �                    d�  �        �                    �   �         |d <   |d         |d         �                    d
�  �        k    �                    t          �  �        |d!<   |d         |d         �                    d�  �        k    �                    t          �  �        |d"<   |d         |d         �                    d�  �        k    �                    t          �  �        |d#<   |�                    �   �         }t#          d$t%          |j        �  �        � d%t%          |�  �        � d&��  �         |S # t&          $ r/}t#          d'|� ��  �         t          j        �   �         cY d(}~S d(}~ww xY w))u?   Erstelle erweiterte ML-Features für progressives Training V5.2r�   r�   r�   r�  r�   r�   r�  r�   r�  r  �price_momentum_5rt   �price_momentum_10r�   �price_momentum_20�volatility_5r�  �
volatility_20r�   r�  �volume_price_trendr�  r�  r�  r�  r�   �returns_12hr
  r�  �
price_mean_10�price_std_10�
price_skew_10�trend_5�trend_10�trend_20z"Progressive ML-Features erstellt: r�  r�  z%FEHLER bei progressiven ML-Features: N)r1   r2   r�   r�   r�   r�   r�   r�   r�   r�   r�  r}  r}   r�   �skewr|  r�   rH   rd   rc   r�  s          rK   r  zAUltimateBitcoinTradingSystemV5._create_progressive_ml_features_v5Y  s�  � �4	"��|�"�(�3�3�3�H� ,.�g�;��G��9L�9L�R�9P�9P�9U�9U�9W�9W�+W�H�'�(�)+�F��b��i�)?�H�%�&�+-�g�;��F��+C�H�'�(� ,.�g�;��G��9J�9J�1�9M�9M�+M�H�'�(�,.�w�K�"�W�+�:K�:K�B�:O�:O�,O�H�(�)�,.�w�K�"�W�+�:K�:K�B�:O�:O�,O�H�(�)� (*�'�{�'=�'=�'?�'?�'G�'G��'J�'J�'N�'N�'P�'P�H�^�$�(*�7��(>�(>�(@�(@�(H�(H��(L�(L�(P�(P�(R�(R�H�_�%�(*�7��(>�(>�(@�(@�(H�(H��(L�(L�(P�(P�(R�(R�H�_�%� �2�:�%�%�02�8��r�(�|�?S�?S�TV�?W�?W�?\�?\�?^�?^�0^��,�-�24�X�,��G��2L�1U�1U�VX�1Y�1Y�1^�1^�1`�1`��-�.� )�.�.�0�0� 
9� 
9�
��U��e�c�5�\�2�2� 9�2�7�5�>�>� 9�38�H�/�#�/�/�0�� &(��[�%;�%;�%=�%=�H�\�"�%'��[�%;�%;�A�%>�%>�H�\�"�&(��k�&<�&<�R�&@�&@�H�]�#�&(��k�&<�&<�R�&@�&@�H�]�#� )+�7��(;�(;�B�(?�(?�(D�(D�(F�(F�H�_�%�')�'�{�':�':�2�'>�'>�'B�'B�'D�'D�H�^�$�(*�7��(;�(;�B�(?�(?�(D�(D�(F�(F�H�_�%� $&�g�;��G��1B�1B�1�1E�1E�#E�"M�"M�c�"R�"R�H�Y��$&�w�K�"�W�+�2C�2C�B�2G�2G�$G�#O�#O�PS�#T�#T�H�Z� �$&�w�K�"�W�+�2C�2C�B�2G�2G�$G�#O�#O�PS�#T�#T�H�Z� �  ���(�(�H��p�s�8�;K�7L�7L�p�p�Y\�]e�Yf�Yf�p�p�p�q�q�q��O��� 	"� 	"� 	"��=�!�=�=�>�>�>��<�>�>�!�!�!�!�!�!�����	"���s   �RR �
S�!$S�S�Sc                 ��  � 	 |d         �                     d�  �        |d         z  dz
  }t          j        |j        t          ��  �        }d}d}d|||k    <   d|||k     <   d	|||k    ||k    z  <   |�                    �   �         }t
          d
t          |�  �        � d��  �         |S # t          $ r/}t
          d|� ��  �         t          j        �   �         cY d
}~S d
}~ww xY w)u2   Erstelle verbessertes Target für ML-Training V5.2r�   r�   rW   )r�   �dtyper�   rD  rQ   r   r�   zEnhanced Target erstellt: r�  zFEHLER bei Enhanced Target: N)	r�   r1   r�   r�   r}   r�   rH   rd   rc   )rJ   rp   �future_returnsr�  �strong_up_threshold�strong_down_thresholdrn   s          rK   r  z9UltimateBitcoinTradingSystemV5._create_enhanced_target_v5�  s  � �	���[�.�.�r�2�2�R��[�@�1�D�N� �Y�R�X�U�;�;�;�F� #(��$*�!� <?�F�>�$7�7�8�=@�F�>�$9�9�:�jm�F�N�&;�;��Re�@e�f�g� �]�]�_�_�F��D�s�6�{�{�D�D�D�E�E�E��M��� 	� 	� 	��4��4�4�5�5�5��9�;�;�����������	���s   �B"B% �%
C�/$C�C�Cc                 �  � 	 t          d�  �         | �                    ||�  �        }| j        r�| �                    ||�  �        }|�                    dd�  �        |�                    dd�  �        z   dz  }|�                    dd�  �        |�                    dd�  �        k    r |�                    |�  �         ||d<   d|d	<   nd
|d	<   | �                    |�  �        }||d<   | j        |d<   t          | j        �  �        |d
<   | �	                    �   �         |d<   |S # t          $ r2}t          d|� ��  �         | �                    ||�  �        cY d}~S d}~ww xY w)z<Erweiterte Vorhersage mit Vergleich zum vorherigen Scan V5.2u3   Führe erweiterte Vorhersage mit Vergleich durch...r  r�   r�   rw  r   rQ  �progressive�prediction_type�standard�
comparisonr�  r�  z#FEHLER bei erweiterter Vorhersage: N)rH   rb  r7   �_make_progressive_prediction_v5rz   �update�_compare_with_previous_scan_v5r;   rd   �_calculate_current_accuracy_v5rc   )rJ   rp   r�   �current_prediction�progressive_prediction�combined_confidencer�  rn   s           rK   r  zEUltimateBitcoinTradingSystemV5.enhanced_prediction_with_comparison_v5�  s�  � �#	B��G�H�H�H� "&�!?�!?��J�!O�!O�� �~� 

G�)-�)M�)M�b�R\�)]�)]�&� (:�'=�'=�l�C�'P�'P�%;�%?�%?��c�%R�%R�(S�VW�'X�#� *�-�-�j�!�<�<�?Q�?U�?U�Ve�gh�?i�?i�i�i�&�-�-�.D�E�E�E�7J�&�|�4�<I�&�'8�9�9�<F�&�'8�9� #�A�A�BT�U�U�O�/>��|�,� -1�,=��y�)�03�D�N�0C�0C��}�-�-1�-P�-P�-R�-R��z�*�%�%��� 	B� 	B� 	B��;��;�;�<�<�<��1�1�"�j�A�A�A�A�A�A�A�A�����	B���s   �DD �
E�%'E�E�Ec           
      �2  � � 	 � j         sddd�S t          � j         �                    �   �         � fd���  �        }� j         |         }� �                    ||�  �        }|j        rddd�S |j        dd�         j        }� j        �                    |�  �        }|�	                    |�  �        d         }|dk    rd	}	t          d
d|dz
  z   �  �        }
n9|dk     rd}	t          d
dd|z
  z   �  �        }
nd
}	dt          |dz
  �  �        dz  z   }
� j        �
                    |i �  �        }|	|
|||�
                    dd�  �        |�
                    dd�  �        |�
                    dd�  �        d�S # t          $ r!}t          d|� ��  �         ddd�cY d}~S d}~ww xY w)zProgressive ML-Vorhersage V5.2r�   )r  rw  c                 �b   �� �j         �                    | i �  �        �                    dd�  �        S )Nr�  r   r�  r�  s    �rK   r�  zPUltimateBitcoinTradingSystemV5._make_progressive_prediction_v5.<locals>.<lambda>�  s,   �� ��0F�0J�0J�1�b�0Q�0Q�0U�0U�V_�ab�0c�0c� rM   r�  r�   Nr   gffffff�?r�  r
  g333333�?r�  rP  rw  r
   r   r  )rO  r  rQ  �
model_usedrw  r
   r  z$FEHLER bei progressiver Vorhersage: )r7   r�   r�  r  r^   r�   r�  r9   r�  r�  r_   r�   r8   rz   rc   rH   )
rJ   rp   r�   �latest_model_name�latest_modelr�  r�  r�  r5  rO  r  r8   rn   s
   `            rK   r2  z>UltimateBitcoinTradingSystemV5._make_progressive_prediction_v5�  s  �� �/	8��>� 
<�&)�s�;�;�;� !$�D�N�$7�$7�$9�$9�&c�&c�&c�&c�!e� !e� !e���>�*;�<�L� �>�>�r�:�N�N�H��~� 
<�&)�s�;�;�;� '�m�B�C�C�0�7�O�"�k�3�3�O�D�D�O� &�-�-�o�>�>�q�A�J� �C���!�� ��c�Z�#�-=�&>�?�?�
�
��c�!�!�$�� ��c�S�:�-=�&>�?�?�
�
�!�� �3�z�C�'7�#8�#8�3�#>�>�
� !%� 6� :� :�;L�b� Q� Q�� !�(�!+�/�-�1�1�*�c�B�B�-�1�1�*�c�B�B�$5�$9�$9�:L�a�$P�$P�� � 
�� � 	8� 	8� 	8��<��<�<�=�=�=�"%�3�7�7�7�7�7�7�7�7�����	8���s*   �E+ �AE+ �)DE+ �+
F�5F�F�Fr6  c           
      �<  � 	 | j         sddd�S | j         d         }|�                    di �  �        }|�                    dd�  �        }|�                    dd�  �        }||k    }|�                    dd	�  �        }|�                    dd	�  �        }||z
  }	|�                    d
d	�  �        }
|�                    d
d	�  �        }|
|z
  }|�                    dd�  �        }
|�                    dd�  �        }|dk    r|
|z
  |z  d
z  nd}d|�                    dd�  �        ||||r|� d|� �ndd�|||	|	dk    d�|
|||dk    d�|
|||dk    rdn	|dk     rdndd�|dk    o|	dk    d�}|S # t          $ r.}t          d|� ��  �         dt	          |�  �        d�cY d}~S d}~ww xY w)z#Vergleiche mit vorherigem Scan V5.2Fu'   Erster Scan - kein Vergleich verfügbar)�has_previous�messager�   r5  rO  rP  r  r�   rw  r�   r   rY   Tr�  u    → u   Unverändert)�current�previous�changed�change_type)rA  rB  r�   r  �up�down�stable)rA  rB  �change_percent�	direction)r?  �previous_scan_id�signal_comparison�confidence_comparison�accuracy_comparison�price_comparison�overall_improvementzFEHLER bei Scan-Vergleich: )r?  r`  N)r:   rz   rc   rH   rj   )rJ   r6  �
previous_scan�previous_prediction�current_signal�previous_signal�signal_changed�current_confidence�previous_confidence�confidence_changer   �previous_accuracy�accuracy_changer�   �previous_price�price_changer1  rn   s                     rK   r4  z=UltimateBitcoinTradingSystemV5._compare_with_previous_scan_v5  s�  � �@	<��$� 
�$)�H�� � �
 !�-�b�1�M�"/�"3�"3�L�"�"E�"E�� 0�3�3�H�h�G�G�N�1�5�5�h��I�I�O�+��>�N� "4�!7�!7��c�!J�!J��"5�"9�"9�,��"L�"L�� 2�5H� H��  2�5�5�j�#�F�F�� 3� 7� 7�
�C� H� H��.�1B�B�O� /�2�2�?�A�F�F�M�*�.�.���B�B�N�Xf�ij�Xj�Xj�]�^�;�~�M�PS�S�S�pq�L� !%�$1�$5�$5�i��$C�$C�-� /�-�P^�#r�o�#L�#L�N�#L�#L�#L�dr�	&� &�  2� 3�/�#4�q�#8�	*� *�  0� 1�-�#2�Q�#6�	(� (�  -� .�&2�)5��)9�)9����XY�IY�IY�v�v�_g�	%� %� (7��':�'T�?P�ST�?T�7� �J�< ���� 	<� 	<� 	<��3��3�3�4�4�4�$)�C��F�F�;�;�;�;�;�;�;�;�����	<���s#   �E# �EE# �#
F�-#F�F�Fc                 �   � 	 t          | j        �  �        dk     rdS | j        d         | j        d         z
  S # t          $ r}Y d}~dS d}~ww xY w)z'Berechne Genauigkeits-Verbesserung V5.2r�   r   r�   r�  N)rd   r?   rc   �rJ   rn   s     rK   r  z>UltimateBitcoinTradingSystemV5._calculate_accuracy_improvementK  sj   � �	��4�2�3�3�a�7�7��s��2�2�6��9X�Y[�9\�\�\��� 	� 	� 	��3�3�3�3�3�����	���s   �7 �7 �
A�Ac                 �`   � 	 | j         r
| j         d         S dS # t          $ r}Y d}~dS d}~ww xY w)z"Berechne aktuelle Genauigkeit V5.2r�   r   N)r?   rc   r]  s     rK   r5  z=UltimateBitcoinTradingSystemV5._calculate_current_accuracy_v5V  sQ   � �	��.� 
��6�r�:�:��t��� 	� 	� 	��4�4�4�4�4�����	���s   � �
-�-c                 �&  � 	 t          | j        �  �        dk     ri S d� | j        D �   �         }d� | j        D �   �         }d� | j        D �   �         }d� | j        D �   �         }||||t          | j        �  �        d�S # t          $ r}i cY d}~S d}~ww xY w)u%   Hole Vergleichsdaten für Charts V5.2r�   c                 �   � g | ]
}|d          ��S )r�  r  �r!  �scans     rK   r#  zGUltimateBitcoinTradingSystemV5._get_comparison_data.<locals>.<listcomp>h  s   � �F�F�F�D��Y��F�F�FrM   c                 �F   � g | ]}|d          �                     dd�  �        ��S )r5  rw  r�   �rz   ra  s     rK   r#  zGUltimateBitcoinTradingSystemV5._get_comparison_data.<locals>.<listcomp>i  s-   � �`�`�`�d�$�|�,�0�0��S�A�A�`�`�`rM   c                 �F   � g | ]}|d          �                     dd�  �        ��S )r5  r  r�   rd  ra  s     rK   r#  zGUltimateBitcoinTradingSystemV5._get_comparison_data.<locals>.<listcomp>j  s-   � �c�c�c��4��-�1�1�,��D�D�c�c�crM   c                 �:   � g | ]}|�                     d d�  �        ��S )r�   r   rd  ra  s     rK   r#  zGUltimateBitcoinTradingSystemV5._get_comparison_data.<locals>.<listcomp>k  s&   � �Q�Q�Q�t�d�h�h���2�2�Q�Q�QrM   )�scan_ids�
accuracies�confidencesr�   �
scan_countN)rd   r:   rc   )rJ   rg  rh  ri  r�   rn   s         rK   r  z3UltimateBitcoinTradingSystemV5._get_comparison_dataa  s�   � �	��4�$�%�%��)�)��	� G�F�D�4E�F�F�F�H�`�`�d�N_�`�`�`�J�c�c�QU�Qb�c�c�c�K�Q�Q�t�?P�Q�Q�Q�F� %�(�*� �!�$�"3�4�4�� � 
�� � 	� 	� 	��I�I�I�I�I�I�����	���s#   �A: �AA: �:
B�B�B�Br  c           
      �:  � 	 |�                     dd�  �        |�                     d�  �        |d         �                     dd�  �        |d         �                     dd�  �        |d         �                     dd	�  �        |�                     d
d�  �        |�                     dd�  �        d�}| j        �                    |�  �         t          | j        �  �        d
k    r| j        dd�         | _        dS dS # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)z%Update Scan-Performance Historie V5.2r�  r   rL  r5  rw  r�   r  rO  rP  r�  r�  )r�  rL  rw  r  rO  r�  r�  r�   i����Nz$FEHLER bei Scan-Performance Update: )rz   r=   ri   rd   rc   rH   )rJ   r  �performance_entryrn   s       rK   r  z?UltimateBitcoinTradingSystemV5._update_scan_performance_historyx  s<  � �	>�&�?�?�9�a�8�8�(�_�_�[�9�9�'��5�9�9�*�c�J�J�)�,�7�;�;�L�#�N�N�%�l�3�7�7��(�K�K�*���}�a�@�@�(�_�_�[�!�<�<�!� !�� 
�)�0�0�1B�C�C�C� �4�0�1�1�B�6�6�04�0M�c�d�d�0S��-�-�-� 7�6�� � 	>� 	>� 	>��<��<�<�=�=�=�=�=�=�=�=�=�����	>���s   �C-C3 �3
D�=D�Dc           	      �$  � 	 | j         sddd�S | j         d         }t          | j         �  �        || j        �                    �   �         | j        rt	          | j        �  �        nd| j        r)t          | j        �  �        t          | j        �  �        z  ndt          | j        �  �        t          | j        �  �        | j        �                    �   �         d�}|S # t          $ r.}t          d|� ��  �         dt          |�  �        d�cY d	}~S d	}~ww xY w)
zHole Scan-Zusammenfassung V5.2r   u   Noch keine Scans durchgeführt)r�  r@  r�   r   )r�  �latest_scanr�  r   �average_accuracy�total_models�progressive_training_data_sizer=   z!FEHLER bei Scan-Zusammenfassung: )r�  r`  N)r:   rd   r?   r�   r�   rf  r7   r>   r=   rc   rH   rj   )rJ   rn  �summaryrn   s       rK   �get_scan_summary_v5z2UltimateBitcoinTradingSystemV5.get_scan_summary_v5�  sG  � �	7��$� 
�#$�?�� � �
 �+�B�/�K�  #�4�#4�5�5�*�(,�(G�(L�(L�(N�(N�IM�Ih�!q��T�%D�!E�!E�!E�nq�sw�  tS�  %\�C��(G�$H�$H�3�t�On�Ko�Ko�$o�$o�  Y\� #�D�N� 3� 3�25�d�6T�2U�2U�,0�,I�,N�,N�,P�,P�	� 	�G� �N��� 	7� 	7� 	7��9�a�9�9�:�:�:�#$�s�1�v�v�6�6�6�6�6�6�6�6�����	7���s#   �C �CC �
D�!#D
�D�
D)N),�__name__�
__module__�__qualname__�__doc__rL   r1   r2   rr   r}   rf   re   rg   rh   r�  r  r9  r/  r   r0  r�  r1  rm  �boolra  r{  rb  r�  r�  rc  rd  re  r	  r   r  r�   r  r  r2  r4  r  r5  r  r  rs  r  rM   rK   r
   r
   )   s�  � � � � � �� �NC� NC� NC�`A6�b�l� A6� A6� A6� A6�F�� � � � �(#��� #� #� #� #�J(� (��� (�U� (�VX�Vb� (� (� (� (�T��� �"�,� � � � �Bg�2�<� g�D� g� g� g� g�RA�R�\� A�t� A�X\� A� A� A� A�F �4�  �E�  �  �  �  �D�X� �%� � � � �*&�$� &�4� &� &� &� &�o�$� o� o� o� o�bA�R�\� A�t� A�� A� A� A� A�F#"��� #"�4� #"�B�L� #"� #"� #"� #"�J4�B�L� 4�d� 4�t� 4� 4� 4� 4�l�B�L� �d� �u� � � � �2�4� �E� � � � �@_?�b�l� _?�� _?�Z^� _?� _?� _?� _?�B1=� 1=� 1=�f-;�4� -;� -;� -;� -;�^Z�� Z� Z� Z� Z�xx�R�\� x�t� x�PT� x� x� x� x�t6"�R�\� 6"�t� 6"�XZ�Xd� 6"� 6"� 6"� 6"�p�R�\� �b�i� � � � �8%B��� %B�SW� %B�\`� %B� %B� %B� %B�N18�"�,� 18�D� 18�UY� 18� 18� 18� 18�fB<�� B<�$� B<� B<� B<� B<�H	�� 	� 	� 	� 	�	�� 	� 	� 	� 	��d� � � � �.>�D� >� >� >� >�,7�T� 7� 7� 7� 7� 7� 7rM   r
   c            	      ��
  � t          d�  �         t          d�  �         t          d�  �         t          d�  �         	 t          �   �         } | �                    �   �         }t          d�  �         t          d�  �         t          d�  �         t          d�  �         t          d|�                    dd	�  �        d
���  �         t          d|�                    dd	�  �        � ��  �         t          d
|�                    dd	�  �        d�d��  �         t          d�  �         t          d|�                    dd�  �        � ��  �         t          d|�                    dd	�  �        d���  �         t          d|�                    dd	�  �        d���  �         t          d|�                    dd	�  �        � ��  �         |�                    di �  �        }t          d�  �         t          d|�                    d d	�  �        d���  �         t          d!|�                    d"d	�  �        d���  �         t          d#|�                    d$d	�  �        � ��  �         t          d%|�                    d&d	�  �        � ��  �         |�                    d'i �  �        }t          d(�  �         t          d)|�                    d*d	�  �        d+�d,��  �         t          d-|�                    d.d/�  �        � ��  �         t          d0|�                    d1d	�  �        d2�d��  �         t          d3|�                    d4d	�  �        � ��  �         |�                    d5i �  �        }t          d6�  �         t          d7t	          |�                    d8i �  �        �  �        � ��  �         t          d9|�                    d:i �  �        �                    d;d/�  �        � ��  �         t          d<|�                    d=i �  �        �                    d>d/�  �        � ��  �         |�                    d?i �  �        }|r�t          d@�  �         |�                    dAi �  �        }t          dB|�                    dCd	�  �        dD�dE|�                    dFd	�  �        dG�dH��  �         |�                    dIi �  �        }t          dJ|�                    dCd	�  �        dD�dE|�                    dFd	�  �        dG�dH��  �         |�                    dKg �  �        }t          dLt	          |�  �        � dM��  �         t          dN�  �         |S # t
          $ r5}	t          dO|	� ��  �         d	dPl}
|
�                    �   �          Y dP}	~	dPS dP}	~	ww xY w)Qu7   Hauptfunktion für Ultimate Bitcoin Trading System V5.0zP================================================================================z$ULTIMATE BITCOIN TRADING SYSTEM V5.0u9   VÖLLIG FEHLERFREIES TRADING-TOOL MIT STABILEN PROGNOSEN!zQ
================================================================================z1ULTIMATE BITCOIN TRADING SYSTEM V5.0 - ERGEBNISSEz
MARKTDATEN:z   Bitcoin-Preis: $r�   r   rx   z   Datenpunkte: rN  z   Analysezeit: rM  r[   r\   z
ML-VORHERSAGE:z   Signal: rO  zN/Az   Konfidenz: r  r]  z   ML-Prediction: rQ  rz  u      Verfügbare Modelle: rR  rD   z
SESSION-STATISTIKEN:z   Aktuelle Genauigkeit: r   z   Beste Genauigkeit: r   z   Gesamte Vorhersagen: r   z   Training-Zyklen: r   rE   z
SYSTEM-MONITORING:z   Performance Score: r&   r�  z/100z   Health Status: r�  �UNKNOWNz   Uptime: r$   z.0fz   Fehler-Anzahl: r#   rF   z
ERWEITERTE ANALYSE:z   Feature Importance: r'   z   Market Regime: r*   r�  z   Volatility Forecast: r+   r�  r8  z
24H-PROGNOSE:r*  u      Nächste Stunde: $r  z,.0fr�  r  z+.1fz%)r+  z   24h Ziel: $r@   u      Stündliche Prognosen: rX   uK   
🏆 ULTIMATE BITCOIN TRADING SYSTEM V5.0 - VÖLLIG FEHLERFREI UND STABIL!z2FEHLER beim Ultimate Bitcoin Trading System V5.0: N)rH   r
   rm  rz   rd   rc   rh  ri  )�systemrl  rD   rE   rF   r8  �	next_hour�prediction_24hr@   rn   rh  s              rK   �&run_ultimate_bitcoin_trading_system_v5r~  �  s  � �	�(�O�O�O�	�
0�1�1�1�	�
E�F�F�F�	�(�O�O�O�?�/�1�1�� �0�0�2�2�� 	�o����
�A�B�B�B�
�h����
�����
�I�F�J�J���$B�$B�I�I�I�J�J�J�
�?����M�1�!=�!=�?�?�@�@�@�
�F����O�Q�!?�!?�F�F�F�F�G�G�G�
�!�"�"�"�
�9�F�J�J�x��7�7�9�9�:�:�:�
�@�v�z�z�,��:�:�@�@�@�A�A�A�
�G�6�:�:�o�q�#A�#A�G�G�G�H�H�H�
�L����4F��)J�)J�L�L�M�M�M��
�
�?�B�7�7�
�
�'�(�(�(�
�X�-�*;�*;�<N�PQ�*R�*R�X�X�X�Y�Y�Y�
�R�}�'8�'8��!�'L�'L�R�R�R�S�S�S�
�T��):�):�;N�PQ�)R�)R�T�T�U�U�U�
�N�]�%6�%6�7H�!�%L�%L�N�N�O�O�O����$4�b�9�9��
�%�&�&�&�
�[�~�'9�'9�:M�q�'Q�'Q�[�[�[�[�\�\�\�
�S�>�#5�#5�o�y�#Q�#Q�S�S�T�T�T�
�B�N�.�.�x��;�;�B�B�B�B�C�C�C�
�I�>�#5�#5�m�Q�#G�#G�I�I�J�J�J�"�J�J�':�B�?�?��
�&�'�'�'�
�^��,=�,A�,A�BV�XZ�,[�,[�(\�(\�^�^�_�_�_�
�y�#4�#8�#8�9Q�SU�#V�#V�#Z�#Z�[k�mv�#w�#w�y�y�z�z�z�
�  D�):�)>�)>�?W�Y[�)\�)\�)`�)`�au�  xA�  *B�  *B�  D�  D�  	E�  	E�  	E�#�Z�Z�(<�b�A�A��� 		V��$�%�%�%�*�.�.�/E�r�J�J�I��  H�)�-�-�8I�1�*M�*M�  H�  H�  H�V_�Vc�Vc�dz�|}�V~�V~�  H�  H�  H�  H�  
I�  
I�  
I�/�3�3�4D�b�I�I�N��  K�>�#5�#5�6G��#K�#K�  K�  K�  K�Tb�Tf�Tf�g}�  @A�  UB�  UB�  K�  K�  K�  K�  
L�  
L�  
L�!'���,@�"�!E�!E���T�s�3E�/F�/F�T�T�T�U�U�U�
�]�^�^�^��
��� � � �
�F�1�F�F�G�G�G������������t�t�t�t�t�����	���s   �S!T  � 
U�**U�U�__main__)"rw  �yfinancer`   �pandasr1   �numpyr�   ry   r]   r   r   r|   �os�warnings�filterwarnings�sklearn.preprocessingr   r  r   �sklearn.metricsr	   r
   �matplotlib.pyplot�pyplot�plt�seaborn�sns�scipyr   r�   �ImportErrorr
   r~  rt  r  rM   rK   �<module>r�     s�  ��
� 
� � � � � � � � � � � � � ���� ���� (� (� (� (� (� (� (� (� ���� 	�	�	�	� ���� �� �� !� !� !� /� .� .� .� .� .� 2� 2� 2� 2� 2� 2� 8� 8� 8� 8� 8� 8� 8� 8�  � � � � � � � � � ���������O�O��� � � ��O�O�O�����7� 7� 7� 7� 7� 7� 7� 7�D4F� F� F�P �z���*�*�,�,�,�,�,� �s   �A# �#A-�,A-