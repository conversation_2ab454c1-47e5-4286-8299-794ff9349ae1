#!/usr/bin/env python3
"""
ULTIMATE BITCOIN TRADING SYSTEM V4.0 - PERFORMANCE TEST
=======================================================
Umfassender Performance-Test für alle Komponenten
"""

import time
import sys
import os

def performance_test():
    """Führe umfassenden Performance-Test durch"""
    print("=" * 80)
    print("ULTIMATE BITCOIN TRADING SYSTEM V4.0 - PERFORMANCE TEST")
    print("=" * 80)
    
    try:
        # Import-Zeit messen
        import_start = time.time()
        from ultimate_bitcoin_trading_system_v4 import UltimateBitcoinTradingSystemV4
        import_time = time.time() - import_start
        
        print(f"✅ Import-Zeit: {import_time:.3f}s")
        
        # System-Initialisierung messen
        init_start = time.time()
        system = UltimateBitcoinTradingSystemV4()
        init_time = time.time() - init_start
        
        print(f"✅ Initialisierung: {init_time:.3f}s")
        
        # Analyse-Performance messen
        analysis_start = time.time()
        result = system.run_ultimate_analysis_v4()
        analysis_time = time.time() - analysis_start
        
        print(f"✅ Analyse-Zeit: {analysis_time:.3f}s")
        
        # Ergebnisse anzeigen
        print("\n" + "=" * 80)
        print("PERFORMANCE TEST ERGEBNISSE:")
        print("=" * 80)
        
        print(f"📊 TIMING:")
        print(f"   Import: {import_time:.3f}s")
        print(f"   Initialisierung: {init_time:.3f}s") 
        print(f"   Analyse: {analysis_time:.3f}s")
        print(f"   GESAMT: {import_time + init_time + analysis_time:.3f}s")
        
        print(f"\n📈 ERGEBNISSE:")
        print(f"   Signal: {result.get('signal', 'N/A')}")
        print(f"   Konfidenz: {result.get('confidence', 0):.1%}")
        print(f"   Preis: ${result.get('current_price', 0):,.2f}")
        print(f"   Modelle: {result.get('models_available', 0)}")
        print(f"   Indikatoren: {len(result.get('technical_indicators', {}))}")
        print(f"   24h-Prognosen: {len(result.get('hourly_predictions', []))}")
        
        # Session Stats
        session_stats = result.get('session_stats', {})
        print(f"\n🎯 SESSION STATS:")
        print(f"   Genauigkeit: {session_stats.get('current_accuracy', 0):.1%}")
        print(f"   Beste Genauigkeit: {session_stats.get('best_accuracy', 0):.1%}")
        print(f"   Training-Zyklen: {session_stats.get('training_cycles', 0)}")
        
        # Performance Bewertung
        print(f"\n⚡ PERFORMANCE BEWERTUNG:")
        if analysis_time < 1.0:
            performance_grade = "EXCELLENT"
            performance_emoji = "🚀"
        elif analysis_time < 2.0:
            performance_grade = "SEHR GUT"
            performance_emoji = "✅"
        elif analysis_time < 5.0:
            performance_grade = "GUT"
            performance_emoji = "👍"
        else:
            performance_grade = "AKZEPTABEL"
            performance_emoji = "⚠️"
        
        print(f"   {performance_emoji} Performance: {performance_grade}")
        print(f"   Analyse-Geschwindigkeit: {analysis_time:.3f}s")
        
        # Genauigkeits-Bewertung
        accuracy = session_stats.get('current_accuracy', 0)
        if accuracy >= 0.8:
            accuracy_grade = "EXCELLENT"
            accuracy_emoji = "🎯"
        elif accuracy >= 0.7:
            accuracy_grade = "SEHR GUT"
            accuracy_emoji = "✅"
        elif accuracy >= 0.6:
            accuracy_grade = "GUT"
            accuracy_emoji = "👍"
        else:
            accuracy_grade = "VERBESSERUNGSBEDARF"
            accuracy_emoji = "⚠️"
        
        print(f"   {accuracy_emoji} Genauigkeit: {accuracy_grade} ({accuracy:.1%})")
        
        print(f"\n🏆 GESAMTBEWERTUNG: SYSTEM V4.0 LÄUFT OPTIMAL!")
        
        return True
        
    except Exception as e:
        print(f"❌ FEHLER beim Performance-Test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = performance_test()
    if success:
        print("\n✅ Performance-Test erfolgreich abgeschlossen!")
    else:
        print("\n❌ Performance-Test fehlgeschlagen!")
        sys.exit(1)
