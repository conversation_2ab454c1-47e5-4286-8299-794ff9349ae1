�

    ^tehqf  �                   �J  � d Z ddlZddlZddlZddlZddlZddlZddlmZmZ ddl	Z	ddl
Z
ddlZddlZ
ddlZddlmZ 	 ddlmZmZ ddlmZ ddlmZmZ ddlmZmZ dd	lmZmZ d
Z n# e!$ r dZ  e"d�  �         Y nw xY w	 ddl#Z$d
Z%n# e!$ r dZ% e"d
�  �         Y nw xY w	 ddl&Z'd
Z(n# e!$ r dZ( e"d�  �         Y nw xY w	 ddl)Z)d
Z*n# e!$ r dZ* e"d�  �         Y nw xY w	 ddl+m,Z- ddl.Z/d
Z0n# e!$ r dZ0 e"d�  �         Y nw xY w ej1        d�  �          ej2        ej3        d ej4        d�  �         ej5        �   �         g��  �          G d� d�  �        Z6d� Z7 ej        ej8        e7�  �          ej        ej9        e7�  �         e:dk    r e6�   �         Z; e"d�  �         dS dS )us  
ULTIMATE BITCOIN TRADING SYSTEM - FINAL VERSION
===============================================

Der perfekte Kompromiss aus Komplexibilität, Genauigkeit, Effizienz und Zuverlässigkeit
- Selbsttrainierendes System mit 24h Prognoseberechnung
- Keine Emojis für maximale Kompatibilität
- Optimierte Performance und Stabilität

Version: FINAL 1.0
Erstellt: 2025-07-02
�    N)�datetime�	timedelta)�deque)�RandomForestRegressor�GradientBoostingRegressor)�MLPRegressor)�RobustScaler�MinMaxScaler)�cross_val_score�TimeSeriesSplit)�mean_squared_error�r2_scoreTFu&   WARNUNG: Scikit-Learn nicht verfügbaru!   WARNUNG: XGBoost nicht verfügbaru"   WARNUNG: yfinance nicht verfügbaru"   WARNUNG: requests nicht verfügbaru$   WARNUNG: matplotlib nicht verfügbar�ignorez)%(asctime)s - %(levelname)s - %(message)sz"ultimate_bitcoin_trading_final.log)�level�format�handlersc                   �   � e Zd ZdZdZd� Zd� Zd� Zd� Zd� Z	d� Z
d	� Zd
� Zd� Z
d� Zd
� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� ZdS )�UltimateBitcoinTradingFinalu[  
    ULTIMATE BITCOIN TRADING SYSTEM - FINAL VERSION
    
    Perfekter Kompromiss aus:
    - Komplexibilität: Erweiterte ML-Modelle ohne Überengineering
    - Genauigkeit: Optimierte Algorithmen mit Cross-Validation
    - Effizienz: Schnelle Ausführung und Memory-Optimierung
    - Zuverlässigkeit: Robuste Fehlerbehandlung und Fallbacks
    z	FINAL 1.0c           	      �$  � t          d| j        � ��  �         t          d�  �         ddddddd	d
d�| _        d| _        d| _        d
| _        d
| _        d
| _        t          j	        �   �         | _
        t          j	        �   �         | _        d| _        d| _
        i | _        i | _        i | _        t#          d��  �        | _        t#          d��  �        | _        t#          d��  �        | _        d| _        d| _        d| _        t1          j        �   �         | _        t1          j        �   �         | _        | �                    �   �          | �                    �   �          | �                    �   �          t          d�  �         tA          j!        d�  �         d
S )z,Initialisierung des Ultimate Trading Systemsz#
ULTIMATE BITCOIN TRADING SYSTEM - z<============================================================�,  �   i  �   �d   �2   皙�����?gffffff�?)�update_interval�prediction_horizon�training_interval�data_history_days�min_data_points�max_features�model_retrain_threshold�confidence_thresholdFN�        ��  ��maxleni�  �HOLDz!System erfolgreich initialisiert!z)UltimateBitcoinTradingFinal initialisiert)"�print�VERSION�config�
is_running�is_training�
last_training�training_thread�prediction_thread�pd�	DataFrame�market_data�processed_features�
current_price�data_quality�models�model_performance�scalersr   �predictions_history�accuracy_history�trading_signals�current_signal�signal_confidence�prediction_24h�	threading�Event�
stop_event�Lock�	data_lock�_setup_directories�_initialize_models�_load_persistent_data�logging�info��selfs    �(E:\Dev\ULTIMATE_BITCOIN_TRADING_FINAL.py�__init__z$UltimateBitcoinTradingFinal.__init__e   s�  � �
�C�T�\�C�C�D�D�D�
�h����  #�"$�!%�!#�"��'*�$'�	
� 	
���  ��� ���!���#���!%��� �<�>�>���"$�,�.�.��� ������ ���!#������#(��#5�#5�#5�� � %�S� 1� 1� 1���  %�C�0�0�0���$���!$���!��� $�/�+�+���"��)�)��� 	
���!�!�!����!�!�!��"�"�$�$�$�
�1�2�2�2���@�A�A�A�A�A�    c                 �D   � g d�}|D ]}t          j        |d��  �         �dS )z!Erstelle notwendige Verzeichnisse)r7   �data�logs�exports�backupsT)�exist_okN)�os�makedirs)rK   �directories�	directorys      rL   rE   z.UltimateBitcoinTradingFinal._setup_directories�   s>   � �F�F�F��$� 	2� 	2�I��K�	�D�1�1�1�1�1�	2� 	2rN   c           
      �  � t          d�  �         t          rit          ddddd��  �        t          ddd	d�
�  �        t	          ddd
ddd��  �        d�| _        t          r!t          j        ddd	dd��  �        | j        d<   t          �   �         t          �   �         d�| _        t          dt          | j        �  �        � ��  �         dS )z#Initialisiere optimierte ML-ModellezInitialisiere ML-Modelle...r   �
   �   �*   �����)�n_estimators�	max_depth�min_samples_split�random_state�n_jobs�   r   )r^   r_   �
learning_ratera   )r   r   �relu�adamg����MbP?r   )�hidden_layer_sizes�
activation�solver�alpha�max_iterra   )�
random_forest�gradient_boost�neural_network)r^   r_   rd   ra   rb   �xgboost)�robust�minmaxzModelle initialisiert: N)
r)   �SKLEARN_AVAILABLEr   r   r   r7   �XGBOOST_AVAILABLE�xgb�XGBRegressorr	   r
   r9   �lenrJ   s    rL   rF   z.UltimateBitcoinTradingFinal._initialize_models�   s  � �
�+�,�,�,�� "	� "7�!$� �&'�!#��"� "� "� #<�!$��"%�!#�	#� #� #� #/�'0�%�!�� �!#�
#� #� #�� �D�K�2 !� 
�),�)9�!$��"%�!#��*� *� *���I�&� #�n�n�"�n�n�
� 
���
 	�:��D�K�(8�(8�:�:�;�;�;�;�;rN   c                 ��  � 	 d}t           j        �                    |�  �        r�t          |d�  �        5 }t	          j        |�  �        }t
          |�                    dg �  �        d��  �        | _        |�                    di �  �        | _	        ddd�  �         n# 1 swxY w Y   t          d�  �         | �                    �   �          dS # t          $ r"}t          j        d	|� ��  �         Y d}~dS d}~ww xY w)
z#Lade gespeicherte Daten und Modelle�data/performance_history.json�rr;   r   r&   r8   NzPerformance-Daten geladenz&Konnte persistente Daten nicht laden: )rU   �path�exists�open�json�loadr   �getr;   r8   r)   �_load_trained_models�	ExceptionrH   �warning)rK   �	perf_file�frP   �es        rL   rG   z1UltimateBitcoinTradingFinal._load_persistent_data�   sW  � �	J�7�I��w�~�~�i�(�(� 
3��)�S�)�)� O�Q��9�Q�<�<�D�,1�$�(�(�;M�r�2R�2R�[^�,_�,_�,_�D�)�-1�X�X�6I�2�-N�-N�D�*�O� O� O� O� O� O� O� O� O� O� O���� O� O� O� O� �1�2�2�2� 
�%�%�'�'�'�'�'��� 	J� 	J� 	J��O�H�Q�H�H�I�I�I�I�I�I�I�I�I�����	J���s;   �1C	 �AB�
C	 �B�C	 � B�!&C	 �	
C5�C0�0C5c                 �  � 	 | j         �                    �   �         D ]}}d|� d�}t          j        �                    |�  �        rVt          |d�  �        5 }t
          j        |�  �        | j         |<   ddd�  �         n# 1 swxY w Y   t          d|� ��  �         �~dS # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)zLade trainierte Modelle�models/�.pkl�rbNzModell geladen: zKonnte Modelle nicht laden: )r7   �keysrU   rz   r{   r|   �pickler~   r)   r�   rH   r�   )rK   �name�
model_filer�   r�   s        rL   r�   z0UltimateBitcoinTradingFinal._load_trained_models�   s3  � �	@���(�(�*�*� 
5� 
5��1�t�1�1�1�
��7�>�>�*�-�-� 5��j�$�/�/� ;�1�,2�K��N�N���D�)�;� ;� ;� ;� ;� ;� ;� ;� ;� ;� ;���� ;� ;� ;� ;��3�T�3�3�4�4�4��
5� 
5�� � 	@� 	@� 	@��O�>�1�>�>�?�?�?�?�?�?�?�?�?�����	@���s<   �AB �A;�/B �;A?	�?B �A?	�B �
C�%C�Cc                 �p  � t          d�  �         	 | j        5  t          �r(t          j        d�  �        }|�                    | j        d         � d�d��  �        }t          |�  �        | j        d         k    r�d� |j        D �   �         |_        |�	                    �   �         }t          |d	         j        d
         �  �        | _        | �
                    |�  �        | _        |�                    d�  �        | _        t          dt          |�  �        � d
| j        d���  �         t          d| j        d���  �         	 ddd�  �         dS | �                    �   �         cddd�  �         S # 1 swxY w Y   dS # t$          $ r5}t'          j        d|� ��  �         | �                    �   �         cY d}~S d}~ww xY w)zSammle optimierte MarktdatenzSammle Marktdaten...zBTC-USDr   �d�1h)�period�intervalr    c                 �6   � g | ]}|�                     �   �         ��S � )�lower��.0�cols     rL   �
<listcomp>zCUltimateBitcoinTradingFinal.collect_market_data.<locals>.<listcomp>�   s    � �%H�%H�%H�c�c�i�i�k�k�%H�%H�%HrN   �closer]   �float32zMarktdaten: u    Stunden, Qualität: �.1%zAktueller BTC-Preis: $�,.2fNTzFehler bei Datensammlung: )r)   rD   �YFINANCE_AVAILABLE�yf�Ticker�historyr+   rv   �columns�dropna�float�ilocr5   �_assess_data_qualityr6   �astyper3   �_generate_fallback_datar�   rH   �error)rK   �btc�dfr�   s       rL   �collect_market_dataz/UltimateBitcoinTradingFinal.collect_market_data�   s&  � �
�$�%�%�%�	2��� 
6� 
6�%� $��)�I�.�.�C����t�{�;N�/O�,R�,R�,R�]a��b�b�B��2�w�w�$�+�.?�"@�@�@�%H�%H�R�Z�%H�%H�%H��
��Y�Y�[�[�� .3�2�g�;�3C�B�3G�-H�-H��*� -1�,E�,E�b�,I�,I��)�+-�9�9�Y�+?�+?��(��b�S��W�W�b�b�4�K\�b�b�b�c�c�c��P�t�7I�P�P�P�Q�Q�Q�#�+
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6�0 �3�3�5�5�1
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6���� 
6� 
6� 
6� 
6� 
6� 
6��4 � 	2� 	2� 	2��M�:�q�:�:�;�;�;��/�/�1�1�1�1�1�1�1�1�����	2���sM   �E6 �D$E)�<E6 �	E)�E6 �)E-�-E6 �0E-�1E6 �6
F5� *F0�*F5�0F5c                 �  � |j         rdS d|�                    �   �         �                    �   �         �                    �   �         t          |�  �        t          |j        �  �        z  z  z
  }|j        d         }t
          j        �   �         |�                    �   �         �	                    d��  �        z
  }t          dd|�                    �   �         dz  z
  �  �        }t          d|d	         �
                    �   �         dk    r5|d	         �                    �   �         |d	         �
                    �   �         z  nd�  �        }t          j
        |||g�  �        S )
u   Bewerte Datenqualitätr$   �   r]   N)�tzinfor   i   �      �?r�   )�empty�isnull�sumrv   r�   �indexr   �now�
to_pydatetime�replace�max�
total_seconds�min�mean�std�np)rK   r�   �completeness�	last_time�	time_diff�recency�variabilitys          rL   r�   z0UltimateBitcoinTradingFinal._assess_data_quality  s  � �
�8� 	��3� �B�I�I�K�K�O�O�-�-�1�1�3�3�s�2�w�w��R�Z���7P�Q�R�� �H�R�L�	��L�N�N�Y�%<�%<�%>�%>�%F�%F�d�%F�%S�%S�S�	��a��i�5�5�7�7�$�>�?�@�@�� �#��G��IY�IY�I[�I[�^_�I_�I_�r�'�{���0�0�2�g�;�3C�3C�3E�3E�E�E�ef�g�g���w��g�{�;�<�<�<rN   c                 �x  � t          d�  �         	 | j        d         dz  }t          j        �   �         }|t	          |��  �        z
  }t          j        ||d��  �        }t          j        �	                    d�  �         d}t          j        �
                    d	d
|�  �        }|g}|D ]3}|d         d|z   z  }	|�                    t          |	d
�  �        �  �         �4|dd�         }t          j
        |dt          |�  �        �         ��  �        }
||
d<   |
d         �                    d�  �        �                    |
d         j        d	         �  �        |
d<   |
ddg         �                    d��  �        t          j        �                    ddt          |
�  �        �  �        z  |
d<   |
ddg         �                    d��  �        t          j        �                    ddt          |
�  �        �  �        z  |
d<   t          j        �                    ddt          |
�  �        �  �        |
d<   |
�                    d�  �        | _        t-          |
d         j        d         �  �        | _        d| _        t          dt          |
�  �        � d��  �         dS # t2          $ r"}t5          j        d |� ��  �         Y d}~d!S d}~ww xY w)"zGeneriere Fallback-DatenzGeneriere Fallback-Daten...r   r   )�hours�H)�start�end�freqr\   iȯ  r   �{�G�z�?r]   r�   r%   N)r�   r�   r|   ��axisr�   gR���Q�?�highg\���(\�?�lowi@B i@KL �volumer�   g�������?zFallback-Daten generiert: z StundenTzFehler bei Fallback-Daten: F)r)   r+   r   r�   r   r1   �
date_ranger�   �random�seed�normal�appendr�   r2   rv   �shift�fillnar�   �uniformr�   r�   r3   r�   r5   r6   r�   rH   r�   )rK   r�   �end_time�
start_time�
time_index�
base_price�returns�prices�ret�	new_pricer�   r�   s               rL   r�   z3UltimateBitcoinTradingFinal._generate_fallback_data%  s�  � �
�+�,�,�,�"	��K� 3�4�r�9�E��|�~�~�H�!�I�E�$:�$:�$:�:�J���Z�X�C�P�P�P�J� 
�I�N�N�2�����J��i�&�&�q�$��6�6�G� �\�F�� 
4� 
4��"�2�J�!�c�'�2�	��
�
�c�)�T�2�2�3�3�3�3��A�B�B�Z�F���J�|��F���|�$<�=�=�=�B� �B�w�K��G��*�*�1�-�-�4�4�R��[�5E�a�5H�I�I�B�v�J��V�W�-�.�2�2��2�:�:�R�Y�=N�=N�s�TX�Z]�^`�Za�Za�=b�=b�b�B�v�J��F�G�,�-�1�1�q�1�9�9�B�I�<M�<M�d�TW�Y\�]_�Y`�Y`�<a�<a�a�B�u�I��9�,�,�W�g�s�2�w�w�G�G�B�x�L�!�y�y��3�3�D��!&�r�'�{�'7��';�!<�!<�D�� #�D���@�s�2�w�w�@�@�@�A�A�A��4��� 	� 	� 	��M�;��;�;�<�<�<��5�5�5�5�5�����	���s   �I:J
 �
J9�J4�4J9c                 �  � | j         j        rdS t          d�  �         	 | j         �                    �   �         }|d         �                    �   �         |d<   |d         �                    d�  �        |d<   |d         �                    d�  �        |d<   d	D ]P}|d         �                    |�  �        �                    �   �         |d
|� �<   |d         |d
|� �         z  |d|� d�<   �Q|d         �                    �   �         }|�                    |d
k    d
�  �        �                    d��  �        �                    �   �         }|�                    |d
k     d
�  �         �                    d��  �        �                    �   �         }||z  }ddd|z   z  z
  |d<   |d         �	                    d��  �        �                    �   �         }|d         �	                    d��  �        �                    �   �         }||z
  |d<   |d         �	                    d��  �        �                    �   �         |d<   |d         �                    d�  �        �
                    �   �         |d<   |d         �                    d�  �        �                    �   �         |d<   |d         |d         z  |d<   |j        j        |d<   |j        j
        |d<   |�                    d �!�  �        �                    d"�!�  �        �                    d
�  �        }|�                    t           j        g�#�  �        j        }	t'          |	�  �        | j        d$         k    rm||	         �                    �   �         �                    d�%�  �        }
|
�                    | j        d$         �  �        j        �                    �   �         }||         }|| _        t          d&t'          |j        �  �        � d'��  �         d(S # t4          $ r"}t7          j        d)|� ��  �         Y d*}~dS d*}~ww xY w)+zOptimiertes Feature EngineeringFzErstelle Features...r�   �	return_1hrc   �	return_6hr   �
return_24h)r[   �   r   �sma_�
price_sma_�_ratior   �   )�windowr   r�   �rsi�   )�span�   �macd�	   �macd_signal�
volatilityr�   r�   �
volume_sma�volume_ratio�hour�day_of_week�ffill)�method�bfill��includer!   )�	ascendingzFeatures erstellt: z	 FeaturesTz Fehler bei Feature Engineering: N)r3   r�   r)   �copy�
pct_change�rollingr�   �diff�where�ewmr�   r�   r�   �	dayofweekr�   �
select_dtypesr�   �numberr�   rv   r+   �var�sort_values�head�tolistr4   r�   rH   r�   )
rK   r�   r�   �delta�gain�loss�rs�ema_12�ema_26�numeric_cols�	variances�top_featuresr�   s
                rL   �engineer_featuresz-UltimateBitcoinTradingFinal.engineer_featuresM  s�  � ���!� 	��5�
�$�%�%�%�8	��!�&�&�(�(�B� !��k�4�4�6�6�B�{�O� ��k�4�4�Q�7�7�B�{�O�!�'�{�5�5�b�9�9�B�|�� &� 
T� 
T��&(��k�&9�&9�&�&A�&A�&F�&F�&H�&H��?�&�?�?�#�24�W�+��?�&�?�?�@S�2S��.��.�.�.�/�/� �w�K�$�$�&�&�E��K�K���	�1�-�-�6�6�b�6�A�A�F�F�H�H�D��[�[����A�.�.�.�7�7�r�7�B�B�G�G�I�I�D����B��s�a�"�f�~�.�B�u�I� ��[�_�_�"�_�-�-�2�2�4�4�F���[�_�_�"�_�-�-�2�2�4�4�F��&��B�v�J� "�6�
���A�� 6� 6� ;� ;� =� =�B�}��  "�+��6�6�r�:�:�>�>�@�@�B�|��  "�(�|�3�3�B�7�7�<�<�>�>�B�|��!#�H���<�0@�!@�B�~�� ���B�v�J� "�� 2�B�}�� ���'��*�*�1�1��1�A�A�H�H��K�K�B� �+�+�R�Y�K�+�@�@�H�L��<� � �4�;�~�#>�>�>��|�,�0�0�2�2�>�>��>�O�O�	�(�~�~�d�k�.�.I�J�J�P�W�W�Y�Y����%��&(�D�#��B��B�J���B�B�B�C�C�C��4��� 	� 	� 	��M�@�Q�@�@�A�A�A��5�5�5�5�5�����	���s   �N4O �
P�O<�<Pc           	      ��  � | j         j        rt          d�  �         dS t          d�  �         d| _        	 | �                    �   �         \  }}t          |�  �        | j        d         k     r)t          dt          |�  �        � ��  �         	 d| _        dS d}| j        �                    �   �         D �]\  }}	 t          d|� d	��  �         t          |||t          d
��  �        d�
�  �        }|�                    ||�  �         |�                    �   �          |�
                    �   �         t          j        �   �         �                    �   �         t          |�  �        d�| j        |<   |dz
  }t          |� d|�                    �   �          d���  �         ��# t$          $ r%}t'          j        d|� d|� ��  �         Y d}~��d}~ww xY wt          j        �   �         | _        | �                    �   �          | �                    �   �          t          d|� dt          | j        �  �        � d��  �         |dk    d| _        S # t$          $ r)}t'          j        d|� ��  �         Y d}~d| _        dS d}~ww xY w# d| _        w xY w)z1Trainiere alle Modelle mit optimierten Parameternu'   Keine Features verfügbar für TrainingFzStarte Modell-Training...Tr    u   Zu wenig Daten für Training: r   z
Trainiere z...�   )�n_splits�neg_mean_squared_error)�cv�scoring)�cv_score�cv_std�last_trained�data_pointsr�   z trainiert - CV Score: z.4fzFehler beim Training von �: NzTraining abgeschlossen: �/z ModellezFehler beim Training: )r4   r�   r)   r-   �_prepare_training_datarv   r+   r7   �itemsr   r   �fitr�   r�   r   r�   �	isoformatr8   r�   rH   r�   r.   �_save_models�_save_performance_data)rK   �X�y�
trained_countr�   �model�	cv_scoresr�   s           rL   �train_modelsz(UltimateBitcoinTradingFinal.train_models�  s�  � ��"�(� 	��;�<�<�<��5�
�)�*�*�*����1	%��.�.�0�0�D�A�q��1�v�v���$5�6�6�6��?�s�1�v�v�?�?�@�@�@��V  %�D����S �M�#�{�0�0�2�2� 
K� 
K���e�K��0�t�0�0�0�1�1�1� !0��q�!�*�A�6�6�6� 8�!� !� !�I� �I�I�a��O�O�O� &/�^�^�%5�%5�$5�"+�-�-�/�/�(0����(@�(@�(B�(B�'*�1�v�v�	4� 4�D�*�4�0� "�Q�&�M��T�Q�Q�9�>�>�;K�;K�:K�Q�Q�Q�R�R�R�R�� � K� K� K��M�"I�d�"I�"I�a�"I�"I�J�J�J�J�J�J�J�J�����K���� "*����D���������'�'�)�)�)��W�]�W�W�S���=M�=M�W�W�W�X�X�X� �1�$�  %�D����	 � 	� 	� 	��M�6�1�6�6�7�7�7��5�5�5�$�D��������		�����  %�D��$�$�$�$s\   �AH9 �!H9 �5CF�H9 �
G�F<�6H9 �<G�A0H9 �9
I,�I'�I/ �'I,�,I/ �/	I8c                 �
  � | j         d         �                    d�  �        | j         d         z  dz
  }|�                    �   �         }| j         �                    t          j        g��  �        j        }d� |D �   �         }| j         |         j        dd�         }|j        }|�	                    �   �         �
                    d��  �        t	          j        |�  �        z   }||         }||         }t          |�  �        dk    r^| j
        d	         �                    |�  �        }|�                    t          j        �  �        |�                    t          j        �  �        fS t	          j        g �  �        t	          j        g �  �        fS )
zBereite Trainingsdaten vorr�   r]   r�   r�   c                 �   � g | ]
}|d k    �|��S �r�   r�   r�   s     rL   r�   zFUltimateBitcoinTradingFinal._prepare_training_data.<locals>.<listcomp>�  s   � �F�F�F��s�g�~�~��~�~�~rN   Nr�   r   rp   )r4   r�   r�   r  r�   r  r�   r�   �valuesr�   �any�isnanrv   r9   �
fit_transformr�   r�   �array)rK   �target�feature_colsr$  r%  �
valid_indices�X_scaleds          rL   r  z2UltimateBitcoinTradingFinal._prepare_training_data�  sC  � � �(��1�7�7��;�;�d�>U�V]�>^�^�ab�b�������� �.�<�<�b�i�[�<�Q�Q�Y��F�F�|�F�F�F���#�L�1�6�s��s�;���M�� �(�(�*�*�.�.�a�.�0�0�2�8�A�;�;�>�?�
�
�m���
�m��� �q�6�6�A�:�:��|�H�-�;�;�A�>�>�H��?�?�2�:�.�.������0D�0D�D�D��x��|�|�R�X�b�\�\�)�)rN   c                 �  � | j         r| j        j        rdS 	 | j        �                    t          j        g��  �        j        }d� |D �   �         }| j        |         j        dd�         j        }| j	        d         �
                    |�  �        }g }g }| j         �                    �   �         D ]�\  }}	 |�                    |�  �        d         }| j
        �                    |i �  �        }	dd|	�                    dd�  �        z   z  }
|�                    |�  �         |�                    |
�  �         ��# t           $ r$}t#          j        d	|� d
|� ��  �         Y d}~��d}~ww xY w|r�t	          j        |�  �        }||�                    �   �         z  }t	          j        ||��  �        }
t	          j        |�  �        }|
}| j        d|z   z  | _        || _        | �                    ||�  �         | j        | j        ||| j        t9          j        �   �         �                    �   �         d�S dS # t           $ r"}t#          j        d
|� ��  �         Y d}~dS d}~ww xY w)zErstelle 24h VorhersageNr�   c                 �   � g | ]
}|d k    �|��S r,  r�   r�   s     rL   r�   z;UltimateBitcoinTradingFinal.predict_24h.<locals>.<listcomp>�  s   � �J�J�J�C�3�'�>�>�C�>�>�>rN   r]   rp   r   r�   r  zVorhersage-Fehler bei r  )�weights)r5   �predicted_price_24h�predicted_return�
confidence�signal�	timestampzFehler bei Vorhersage: ) r7   r4   r�   r  r�   r  r�   r�   r-  r9   �	transformr  �predictr8   r   r�   r�   rH   r�   r1  r�   �averager�   r5   r?   r>   �_generate_trading_signalr=   r   r�   r!  r�   )rK   r3  �current_features�current_features_scaled�predictions�confidencesr�   r'  �pred�perfr;  r�   r8  �ensemble_prediction�ensemble_confidencer:  s                   rL   �predict_24hz'UltimateBitcoinTradingFinal.predict_24h�  s�  � ��{� 	�d�5�;� 	��4�6	��2�@�@�"�)��@�U�U�]�L�J�J�<�J�J�J�L�#�6�|�D�I�"�#�#�N�U��&*�l�8�&<�&F�&F�GW�&X�&X�#��K��K�#�{�0�0�2�2� 
J� 
J���e�J� �=�=�)@�A�A�!�D�D�  �1�5�5�d�B�?�?�D�!"�a�$�(�(�:�q�*A�*A�&A�!B�J��&�&�t�,�,�,��&�&�z�2�2�2�2�� � J� J� J��O�$H�T�$H�$H�Q�$H�$H�I�I�I�I�I�I�I�I�����J���� � 
��(�;�/�/��!�G�K�K�M�M�1��&(�j��g�&N�&N�&N�#�&(�g�k�&:�&:�#� $7� �&*�&8�A�@P�<P�&Q��#�)<��&� �-�-�.>�@S�T�T�T� &*�%7�+/�+>�(8�"5�"�1�!)����!9�!9�!;�!;�
� � � �4��� 	� 	� 	��M�7�A�7�7�8�8�8��4�4�4�4�4�����	���sD   �BH# �0A<D-�,H# �-
E�7E�H# �E�CH# �#
I�-I
�
Ic                 �"  � || j         d         k     r	d| _        dS d}d}||k    rd| _        n||k     rd| _        nd| _        | j        �                    t	          j        �   �         �                    �   �         | j        ||| j        d��  �         dS )	zGeneriere Trading-Signalr#   r(   Nr�   g{�G�z���BUY�SELL)r=  r<  r:  r;  �price)r+   r=   r<   r�   r   r�   r!  r5   )rK   r:  r;  �
buy_threshold�sell_thresholds        rL   rA  z4UltimateBitcoinTradingFinal._generate_trading_signal   s�   � ����$:�;�;�;�"(�D���F� �
����m�+�+�"'�D���
��
.�
.�"(�D���"(�D�� 	
��#�#�!����1�1�3�3��)� 0�$��'�%
� %
� 	� 	� 	� 	� 	rN   c                 �*  � 	 | j         �                    �   �         D ]H\  }}d|� d�}t          |d�  �        5 }t          j        ||�  �         ddd�  �         n# 1 swxY w Y   �IdS # t
          $ r"}t
          j        d|� ��  �         Y d}~dS d}~ww xY w)zSpeichere trainierte Modeller�   r�   �wbNz#Fehler beim Speichern der Modelle: )r7   r  r|   r�   �dumpr�   rH   r�   )rK   r�   r'  r�   r�   r�   s         rL   r"  z(UltimateBitcoinTradingFinal._save_models:  s  � �	E�#�{�0�0�2�2� 
*� 
*���e�1�t�1�1�1�
��*�d�+�+� *�q��K��q�)�)�)�*� *� *� *� *� *� *� *� *� *� *���� *� *� *� *��
*� 
*�� � 	E� 	E� 	E��M�C��C�C�D�D�D�D�D�D�D�D�D�����	E���s:   �4A& �A�A& �A	�A& �A	� A& �&
B�0B
�
Bc                 �f  � 	 t          | j        �  �        | j        t          j        �   �         �                    �   �         d�}t
          dd�  �        5 }t          j        ||d��  �         ddd�  �         dS # 1 swxY w Y   dS # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)zSpeichere Performance-Daten)r;   r8   �last_updaterx   �w�   )�indentNz-Fehler beim Speichern der Performance-Daten: )�listr;   r8   r   r�   r!  r|   r}   rS  r�   rH   r�   )rK   rP   r�   r�   s       rL   r#  z2UltimateBitcoinTradingFinal._save_performance_dataD  s  � �		O�$(��)>�$?�$?�%)�%;�'�|�~�~�7�7�9�9�� �D�
 �5�s�;�;� 
-�q��	�$��!�,�,�,�,�
-� 
-� 
-� 
-� 
-� 
-� 
-� 
-� 
-� 
-� 
-� 
-���� 
-� 
-� 
-� 
-� 
-� 
-��� 	O� 	O� 	O��M�M�!�M�M�N�N�N�N�N�N�N�N�N�����	O���s<   �AB �A7�*B �7A;�;B �>A;�?B �
B0�B+�+B0c                 �4  � t          d�  �         d| _        t          j        | j        d��  �        | _        | j        �                    �   �          t          j        | j        d��  �        | _        | j        �                    �   �          t          d�  �         dS )z Starte kontinuierliches Trainingz#Starte kontinuierliches Training...T)r2  �daemonz"Kontinuierliches System gestartet!N)	r)   r,   r@   �Thread�_continuous_training_loopr/   r�   �_continuous_prediction_loopr0   rJ   s    rL   �start_continuous_trainingz5UltimateBitcoinTradingFinal.start_continuous_trainingQ  s�   � �
�3�4�4�4����  )�/�t�7U�^b�c�c�c�����"�"�$�$�$� "+�!1��9Y�bf�!g�!g�!g�����$�$�&�&�&�
�2�3�3�3�3�3rN   c                 �n  � t          d�  �         d| _        | j        �                    �   �          | j        r4| j        �                    �   �         r| j        �                    d��  �         | j        r4| j        �                    �   �         r| j        �                    d��  �         t          d�  �         dS )z Stoppe kontinuierliches Trainingz!Stoppe kontinuierliches System...Fr[   )�timeoutzSystem gestoppt!N)r)   r,   rB   �setr/   �is_alive�joinr0   rJ   s    rL   �stop_continuous_trainingz4UltimateBitcoinTradingFinal.stop_continuous_training`  s�   � �
�1�2�2�2������������� 	1�D�$8�$A�$A�$C�$C� 	1�� �%�%�a�%�0�0�0��!� 	3�d�&<�&E�&E�&G�&G� 	3��"�'�'��'�2�2�2�
� �!�!�!�!�!rN   c                 �  � | j         r�| j        �                    �   �         s�	 | �                    �   �         r<| �                    �   �         r(| �                    �   �         r| �                    �   �          | j        �                    | j        d         �  �         nH# t          $ r;}t          j        d|� ��  �         | j        �                    d�  �         Y d}~nd}~ww xY w| j         r| j        �                    �   �         ��dS dS dS dS )zKontinuierlicher Training-Loopr   zFehler im Training-Loop: �<   N)r,   rB   �is_setr�   r  �_should_retrainr)  �waitr+   r�   rH   r�   )rK   r�   s     rL   r]  z5UltimateBitcoinTradingFinal._continuous_training_loopn  s=  � ��o� 	)�d�o�&<�&<�&>�&>� 	)�
)��+�+�-�-� 0��-�-�/�/� 0��/�/�1�1� 0� �-�-�/�/�/� ��$�$�T�[�1D�%E�F�F�F�F��� 
)� 
)� 
)��
�=�!�=�=�>�>�>���$�$�R�(�(�(�(�(�(�(�(�����
)���� �o� 	)�d�o�&<�&<�&>�&>� 	)� 	)� 	)� 	)� 	)� 	)� 	)� 	)� 	)s   �A5B �
C�"1C�Cc                 �B  � | j         �r�| j        �                    �   �         �sz	 | �                    �   �         }|r�t	          d�  �         t	          dt          j        �   �         �                    d�  �        � ��  �         t	          d|d         d���  �         t	          d|d         d���  �         t	          d	|d
         d���  �         t	          d|d
         d���  �         t	          d|d         � ��  �         t	          d�  �         | j        �                    | j	        d         �  �         nH# t          $ r;}t          j        d|� ��  �         | j        �                    d�  �         Y d}~nd}~ww xY w| j         r | j        �                    �   �         ��vdS dS dS dS )z Kontinuierlicher Prediction-Loopz
--- LIVE PREDICTION ---zZeit: z%Y-%m-%d %H:%M:%SzAktueller Preis: $r5   r�   z24h Vorhersage: $r9  u   Erwartete Änderung: r:  z+.2%zKonfidenz: r;  r�   zSignal: r<  z-------------------------r   zFehler im Prediction-Loop: rg  N)
r,   rB   rh  rJ  r)   r   r�   �strftimerj  r+   r�   rH   r�   )rK   �
predictionr�   s      rL   r^  z7UltimateBitcoinTradingFinal._continuous_prediction_loop�  s�  � ��o� 	)�d�o�&<�&<�&>�&>� 	)�
)�!�-�-�/�/�
�� $��6�7�7�7��Q�8�<�>�>�#:�#:�;N�#O�#O�Q�Q�R�R�R��Q�z�/�/J�Q�Q�Q�R�R�R��V�j�9N�.O�V�V�V�W�W�W��W�*�=O�2P�W�W�W�X�X�X��F�
�<�(@�F�F�F�G�G�G��;�Z��%9�;�;�<�<�<��(�O�O�O� ��$�$�T�[�1B�%C�D�D�D�D��� 
)� 
)� 
)��
�?�A�?�?�@�@�@���$�$�R�(�(�(�(�(�(�(�(�����
)����% �o� 	)�d�o�&<�&<�&>�&>� 	)� 	)� 	)� 	)� 	)� 	)� 	)� 	)� 	)s   �DD0 �0
E5�:1E0�0E5c                 �   � | j         sdS t          j        �   �         | j         z
  }|�                    �   �         | j        d         k    rdS dS )u   Prüfe ob Retraining nötig istTr   F)r.   r   r�   r�   r+   )rK   �time_since_trainings     rL   ri  z+UltimateBitcoinTradingFinal._should_retrain�  sQ   � ��!� 	��4� '�l�n�n�t�/A�A���,�,�.�.���=P�1Q�Q�Q��4��urN   c           
      �6  � | j         | j        | j        | j        | j        | j        | j        t          | j        �  �        | j	        r| j	        �
                    �   �         ndt          | j        �  �        | j        j
        st          | j        j        �  �        ndd�S )zHole aktuellen System-StatusNr   )r,   r-   r5   r6   r?   r=   r>   �models_countr.   r  �features_count)r,   r-   r5   r6   r?   r=   r>   rv   r7   r.   r!  r3   r4   r�   r�   rJ   s    rL   �get_system_statusz-UltimateBitcoinTradingFinal.get_system_status�  s�   � � �/��+�!�/� �-�"�1�"�1�!%�!7����,�,�?C�?Q�[�T�/�9�9�;�;�;�W[��t�/�0�0�JN�Ja�Jg�n�c�$�"9�"A�B�B�B�mn�
� 
� 	
rN   c                 �   � t          d�  �         | �                    �   �         r(| �                    �   �         r| �                    �   �         S dS )zManuelles TrainingzStarte manuelles Training...F)r)   r�   r  r)  rJ   s    rL   �manual_trainingz+UltimateBitcoinTradingFinal.manual_training�  sP   � �
�,�-�-�-��#�#�%�%� 	+��%�%�'�'� 
+��(�(�*�*�*��urN   c                 �   � t          d�  �         | �                    �   �         r(| �                    �   �         r| �                    �   �         S dS )zEinzelne VorhersagezErstelle Einzelvorhersage...N)r)   r�   r  rJ  rJ   s    rL   �single_predictionz-UltimateBitcoinTradingFinal.single_prediction�  sP   � �
�,�-�-�-��#�#�%�%� 	*��%�%�'�'� 
*��'�'�)�)�)��trN   N)�__name__�
__module__�__qualname__�__doc__r*   rM   rE   rF   rG   r�   r�   r�   r�   r  r)  r  rJ  rA  r"  r#  r_  re  r]  r^  ri  rs  ru  rw  r�   rN   rL   r   r   X   s�  � � � � � �� � �G�5B� 5B� 5B�n2� 2� 2�.<� .<� .<�`J� J� J�$
@� 
@� 
@�!2� !2� !2�F=� =� =�$&� &� &�P?� ?� ?�B:%� :%� :%�x*� *� *�2;� ;� ;�z� � �4E� E� E�O� O� O�
4� 
4� 
4�"� "� "�)� )� )�&)� )� )�0
� 
� 
�
� 
� 
� � � �� � � � rN   r   c                 �   � t          d�  �         dt          �   �         v rt          �                    �   �          t	          j        d�  �         d S )Nz
Beende System sauber...�systemr   )r)   �globalsr}  re  �sys�exit)�signum�frames     rL   �signal_handlerr�  �  sC   � �	�
%�&�&�&��7�9�9����'�'�)�)�)��H�Q�K�K�K�K�KrN   �__main__u(   System bereit für Launcher-Integration!)<r{  rU   r  �timer@   �warningsr<  r   r   r}   r�   rH   �numpyr�   �pandasr1   �collectionsr   �sklearn.ensembler   r   �sklearn.neural_networkr   �sklearn.preprocessingr	   r
   �sklearn.model_selectionr   r   �sklearn.metricsr
   r   rr   �ImportErrorr)   ro   rt   rs   �yfinancer�   r�   �requests�REQUESTS_AVAILABLE�matplotlib.pyplot�pyplot�plt�seaborn�sns�MATPLOTLIB_AVAILABLE�filterwarnings�basicConfig�INFO�FileHandler�
StreamHandlerr   r�  �SIGINT�SIGTERMrx  r}  r�   rN   rL   �<module>r�     s�  ��� � 
�	�	�	� 
�
�
�
� ���� � � � � ���� 
�
�
�
� (� (� (� (� (� (� (� (� ���� 
�
�
�
� ���� � � � � � � � � � � � � � �	4�Q�Q�Q�Q�Q�Q�Q�Q�3�3�3�3�3�3�@�@�@�@�@�@�@�@�H�H�H�H�H�H�H�H�<�<�<�<�<�<�<�<������ 4� 4� 4���	�E�
2�3�3�3�3�3�4����/���������� /� /� /���	�E�
-�.�.�.�.�.�/����
0���������� 0� 0� 0���	�E�
.�/�/�/�/�/�0����0��O�O�O������ 0� 0� 0���	�E�
.�/�/�/�/�/�0����
2�#�#�#�#�#�#���������� 2� 2� 2� ��	�E�
0�1�1�1�1�1�2����
 �� �� !� !� !� �� �
�,�6����@�A�A�������� � � �o	� o	� o	� o	� o	� o	� o	� o	�d� � � ��
�f�m�^� ,� ,� ,� 
��
�f�n�n� -� -� -��z���
(�
(�
*�
*�F�	�E�
4�5�5�5�5�5� �sY   �(A' �'A<�;A<� B �B�B� B' �'B<�;B<� C �C�C� C- �-D�D