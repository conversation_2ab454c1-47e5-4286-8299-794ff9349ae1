#!/usr/bin/env python3
"""
ULTIMATE BITCOIN TRADING LAUNCHER V2.0
======================================
BENUTZERFREUNDLICHER LAUNCHER FÜR DAS ULTIMATE BITCOIN TRADING SYSTEM V2.0
- Moderne GUI mit diversen Buttons
- Live Visualisierungen und Charts
- Real-time API-Integration
- Trading Dashboard
- Erweiterte Kontrollen
- Desktop-Integration

ULTIMATE TRADING LAUNCHER - ALLE FEATURES INTEGRIERT!
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import threading
import subprocess
import sys
import os
import json
import time
import requests
import yfinance as yf
import math
import random

# Import des Ultimate Trading Systems
from ultimate_bitcoin_trading_system_v2 import UltimateBitcoinTradingSystemV2

class UltimateBitcoinTradingLauncherV2:
    """
    ULTIMATE BITCOIN TRADING LAUNCHER V2.0
    =====================================
    Benutzerfreundlicher Launcher mit allen Features
    """
    
    def __init__(self):
        # SYSTEM KONFIGURATION
        self.VERSION = "Ultimate_Launcher_v2.0"
        self.TITLE = "Ultimate Bitcoin Trading Launcher V2.0"
        
        # GUI PARAMETER
        self.root = tk.Tk()
        self.root.title(self.TITLE)
        self.root.geometry("1400x900")
        self.root.configure(bg='#0a0a0a')
        
        # TRADING SYSTEM
        self.trading_system = UltimateBitcoinTradingSystemV2()
        
        # STATUS VARIABLEN
        self.is_running = False
        self.auto_update = False
        self.update_interval = 30  # 30 Sekunden
        self.last_update = None
        self.current_result = None

        # PROGNOSE VARIABLEN
        self.script_start_time = datetime.now()
        self.next_prediction_time = None
        self.hourly_predictions = []
        self.prediction_accuracy_history = []
        
        # GUI KOMPONENTEN
        self.notebook = None
        self.status_log = None
        self.price_canvas = None
        self.indicators_canvas = None
        self.signals_canvas = None
        self.risk_canvas = None
        
        # DATEN
        self.price_history = []
        self.signal_history = []
        self.update_job_id = None
        
        print(f"ULTIMATE BITCOIN TRADING LAUNCHER V2.0 initialisiert")
        print(f"Version: {self.VERSION}")
        print(f"Integration: Ultimate Trading System V2.0")
        
        # Setup GUI
        self.setup_styles()
        self.create_gui()
        self.setup_cleanup_handlers()
        self.log_message("Ultimate Bitcoin Trading Launcher V2.0 bereit!")
        self.log_message("Klicken Sie 'TRADING STARTEN' um zu beginnen")
    
    def setup_styles(self):
        """Setup GUI Styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Notebook Style
        style.configure('Ultimate.TNotebook', background='#0a0a0a', borderwidth=0)
        style.configure('Ultimate.TNotebook.Tab', 
                       background='#1a1a1a', foreground='white', 
                       padding=[20, 10], font=('Arial', 10, 'bold'))
        style.map('Ultimate.TNotebook.Tab',
                 background=[('selected', '#00ff88'), ('active', '#333333')],
                 foreground=[('selected', 'black'), ('active', 'white')])

    def setup_cleanup_handlers(self):
        """Setup Cleanup-Handler für ordnungsgemäßes Schließen"""

        def on_closing():
            """Handler für Fenster-Schließen"""
            self.log_message("Schließe Ultimate Bitcoin Trading Launcher...")

            # Stoppe alle laufenden Prozesse
            self.cleanup_all_processes()

            # Zeige Bestätigung
            if messagebox.askokcancel("Beenden", "Möchten Sie den Ultimate Bitcoin Trading Launcher wirklich beenden?"):
                self.log_message("Launcher wird beendet...")
                self.root.quit()
                self.root.destroy()

        # Registriere Cleanup-Handler
        self.root.protocol("WM_DELETE_WINDOW", on_closing)

        # Keyboard-Handler für Ctrl+C
        def on_ctrl_c(event):
            on_closing()

        self.root.bind('<Control-c>', on_ctrl_c)

    def cleanup_all_processes(self):
        """Bereinige alle laufenden Prozesse"""
        try:
            self.log_message("Stoppe alle laufenden Prozesse...")

            # Stoppe Trading
            if self.is_running:
                self.stop_trading()

            # Stoppe Auto-Update
            self.auto_update = False
            if self.update_job_id:
                self.root.after_cancel(self.update_job_id)
                self.update_job_id = None

            # Speichere Session-Daten
            if hasattr(self.trading_system, '_save_session_data'):
                self.trading_system._save_session_data()

            # Schließe alle matplotlib Figures
            plt.close('all')

            self.log_message("Alle Prozesse gestoppt")

        except Exception as e:
            self.log_message(f"FEHLER beim Cleanup: {e}")

    def fix_timezone_issues(self, datetime_index):
        """
        ULTIMATE TIMEZONE-FIX
        =====================
        Behebt ALLE Timezone-Probleme komplett und endgültig
        """
        try:
            self.log_message("Starte Ultimate Timezone-Fix...")

            # METHODE 1: Direkte Timezone-Entfernung
            try:
                if hasattr(datetime_index, 'tz') and datetime_index.tz is not None:
                    # Entferne Timezone-Info direkt
                    fixed_index = datetime_index.tz_localize(None)
                    self.log_message("Timezone-Fix: Methode 1 erfolgreich (tz_localize)")
                    return fixed_index
            except Exception as e1:
                self.log_message(f"Timezone-Fix Methode 1 fehlgeschlagen: {e1}")

            # METHODE 2: Konvertierung zu naive datetime
            try:
                if isinstance(datetime_index, pd.DatetimeIndex):
                    # Konvertiere jedes Element einzeln
                    naive_dates = []
                    for dt in datetime_index:
                        if hasattr(dt, 'tz') and dt.tz is not None:
                            # Entferne Timezone
                            naive_dates.append(dt.tz_localize(None) if hasattr(dt, 'tz_localize') else dt.replace(tzinfo=None))
                        else:
                            naive_dates.append(dt)

                    fixed_index = pd.DatetimeIndex(naive_dates)
                    self.log_message("Timezone-Fix: Methode 2 erfolgreich (Element-weise)")
                    return fixed_index
            except Exception as e2:
                self.log_message(f"Timezone-Fix Methode 2 fehlgeschlagen: {e2}")

            # METHODE 3: String-Konvertierung und Re-Parsing
            try:
                # Konvertiere zu Strings und parse neu
                date_strings = [str(dt).split('+')[0].split('-')[0:3] + str(dt).split(' ')[1:2] if ' ' in str(dt) else str(dt) for dt in datetime_index]

                # Vereinfachte String-Konvertierung
                simple_strings = []
                for dt in datetime_index:
                    dt_str = str(dt)
                    # Entferne Timezone-Info aus String
                    if '+' in dt_str:
                        dt_str = dt_str.split('+')[0]
                    elif 'UTC' in dt_str:
                        dt_str = dt_str.replace(' UTC', '')
                    simple_strings.append(dt_str)

                fixed_index = pd.to_datetime(simple_strings, errors='coerce')
                self.log_message("Timezone-Fix: Methode 3 erfolgreich (String-Konvertierung)")
                return fixed_index
            except Exception as e3:
                self.log_message(f"Timezone-Fix Methode 3 fehlgeschlagen: {e3}")

            # METHODE 4: Numerische Timestamps
            try:
                # Konvertiere zu Unix-Timestamps und zurück
                if hasattr(datetime_index, 'astype'):
                    timestamps = datetime_index.astype('int64') // 10**9  # Nanosekunden zu Sekunden
                    fixed_index = pd.to_datetime(timestamps, unit='s')
                    self.log_message("Timezone-Fix: Methode 4 erfolgreich (Unix-Timestamps)")
                    return fixed_index
            except Exception as e4:
                self.log_message(f"Timezone-Fix Methode 4 fehlgeschlagen: {e4}")

            # METHODE 5: Fallback auf numerische Indizes
            self.log_message("Alle Timezone-Fix-Methoden fehlgeschlagen - verwende numerische Indizes")
            return range(len(datetime_index))

        except Exception as e:
            self.log_message(f"KRITISCHER FEHLER im Timezone-Fix: {e}")
            # Absoluter Fallback
            return range(100)  # Standard-Range

    def calculate_24h_prediction_from_start(self, current_result):
        """
        PRÄZISE 24H-PROGNOSE AB SCRIPTSTART
        ===================================
        Berechnet detaillierte stündliche Vorhersagen für die nächsten 24h
        """
        try:
            self.log_message("Berechne präzise 24h-Prognose ab Scriptstart...")

            current_time = datetime.now()
            current_price = current_result.get('current_price', 100000)

            # Berechne Zeitpunkte für die nächsten 24 Stunden
            prediction_times = []
            for hour in range(1, 25):  # 1h bis 24h
                future_time = self.script_start_time + timedelta(hours=hour)
                prediction_times.append(future_time)

            # Hole technische Indikatoren und Signale
            indicators = current_result.get('technical_indicators', {})
            weighted_signal = current_result.get('weighted_signal', 0)
            confidence = current_result.get('confidence', 0.5)

            # ERWEITERTE PROGNOSE-BERECHNUNG
            hourly_predictions = []

            for i, future_time in enumerate(prediction_times):
                hour = i + 1

                # 1. Basis-Trend basierend auf gewichtetem Signal
                base_trend = weighted_signal * 0.02 * hour  # 2% max pro Stunde

                # 2. Volatilitäts-Komponente
                volatility = indicators.get('volatility_20', 0.02)
                volatility_factor = volatility * math.sqrt(hour) * random.uniform(-1, 1) * 0.5

                # 3. RSI-basierte Korrektur
                rsi = indicators.get('rsi_14', 50)
                if rsi > 70:  # Überkauft
                    rsi_correction = -0.001 * (rsi - 70) * hour
                elif rsi < 30:  # Überverkauft
                    rsi_correction = 0.001 * (30 - rsi) * hour
                else:
                    rsi_correction = 0

                # 4. MACD-Momentum
                macd = indicators.get('macd', 0)
                macd_signal = indicators.get('macd_signal', 0)
                macd_momentum = (macd - macd_signal) / current_price * 0.1 * hour

                # 5. Bollinger Bands Reversion
                bb_position = indicators.get('bb_position', 0.5)
                bb_reversion = (0.5 - bb_position) * 0.005 * hour

                # 6. Zeitbasierte Faktoren
                hour_of_day = future_time.hour
                # Bitcoin-typische Handelszeiten (höhere Volatilität)
                if 8 <= hour_of_day <= 16:  # US/EU Handelszeiten
                    time_factor = 1.2
                elif 22 <= hour_of_day or hour_of_day <= 2:  # Asien Handelszeiten
                    time_factor = 1.1
                else:
                    time_factor = 0.9

                # 7. Wochentag-Effekt
                weekday = future_time.weekday()
                if weekday < 5:  # Werktage
                    weekday_factor = 1.0
                else:  # Wochenende
                    weekday_factor = 0.8

                # 8. Konfidenz-basierte Dämpfung
                confidence_factor = confidence * 0.8 + 0.2  # Min 20%, Max 100%

                # KOMBINIERE ALLE FAKTOREN
                total_change = (
                    base_trend +
                    volatility_factor +
                    rsi_correction +
                    macd_momentum +
                    bb_reversion
                ) * time_factor * weekday_factor * confidence_factor

                # Begrenze extreme Bewegungen
                max_hourly_change = 0.05  # Max 5% pro Stunde
                total_change = max(-max_hourly_change, min(max_hourly_change, total_change))

                # Berechne Prognose-Preis
                predicted_price = current_price * (1 + total_change)

                # Berechne Konfidenz für diese Vorhersage
                prediction_confidence = confidence * (1 - hour * 0.02)  # Abnehmendes Vertrauen
                prediction_confidence = max(0.1, min(0.95, prediction_confidence))

                # Erstelle Prognose-Objekt
                prediction = {
                    'hour': hour,
                    'time': future_time,
                    'time_str': future_time.strftime("%d.%m.%Y %H:%M:%S"),
                    'predicted_price': predicted_price,
                    'price_change': total_change,
                    'price_change_percent': total_change * 100,
                    'confidence': prediction_confidence,
                    'factors': {
                        'base_trend': base_trend,
                        'volatility': volatility_factor,
                        'rsi_correction': rsi_correction,
                        'macd_momentum': macd_momentum,
                        'bb_reversion': bb_reversion,
                        'time_factor': time_factor,
                        'weekday_factor': weekday_factor,
                        'confidence_factor': confidence_factor
                    }
                }

                hourly_predictions.append(prediction)

            # Speichere Prognosen
            self.hourly_predictions = hourly_predictions
            self.next_prediction_time = prediction_times[0]  # Nächste Stunde

            # Erstelle Zusammenfassung
            summary = {
                'script_start_time': self.script_start_time,
                'calculation_time': current_time,
                'current_price': current_price,
                'predictions_count': len(hourly_predictions),
                'next_hour_prediction': hourly_predictions[0] if hourly_predictions else None,
                '24h_prediction': hourly_predictions[-1] if hourly_predictions else None,
                'average_confidence': sum(p['confidence'] for p in hourly_predictions) / len(hourly_predictions),
                'max_predicted_price': max(p['predicted_price'] for p in hourly_predictions),
                'min_predicted_price': min(p['predicted_price'] for p in hourly_predictions),
                'total_24h_change': hourly_predictions[-1]['price_change_percent'] if hourly_predictions else 0
            }

            self.log_message(f"24h-Prognose berechnet: {len(hourly_predictions)} stündliche Vorhersagen")
            self.log_message(f"Nächste Stunde: ${hourly_predictions[0]['predicted_price']:,.2f} ({hourly_predictions[0]['price_change_percent']:+.2f}%)")
            self.log_message(f"24h Gesamt: ${hourly_predictions[-1]['predicted_price']:,.2f} ({hourly_predictions[-1]['price_change_percent']:+.2f}%)")

            return summary

        except Exception as e:
            self.log_message(f"FEHLER bei 24h-Prognose: {e}")
            return None
    
    def create_gui(self):
        """Erstelle Hauptgui"""
        
        # HEADER
        self.create_header()
        
        # MAIN CONTENT
        main_frame = tk.Frame(self.root, bg='#0a0a0a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # LEFT PANEL - Kontrollen
        self.create_control_panel(main_frame)
        
        # RIGHT PANEL - Visualisierungen
        self.create_visualization_panel(main_frame)
        
        # BOTTOM PANEL - Status Log
        self.create_status_panel()
    
    def create_header(self):
        """Erstelle Header"""
        header_frame = tk.Frame(self.root, bg='#1a1a1a', height=80)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(
            header_frame,
            text="ULTIMATE BITCOIN TRADING LAUNCHER V2.0",
            font=('Arial', 20, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # Status Indicators
        status_frame = tk.Frame(header_frame, bg='#1a1a1a')
        status_frame.pack(side=tk.RIGHT, padx=20, pady=20)
        
        # API Status
        self.api_status_label = tk.Label(
            status_frame,
            text="API: BEREIT",
            font=('Arial', 10, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        self.api_status_label.pack(side=tk.TOP)
        
        # System Status
        self.system_status_label = tk.Label(
            status_frame,
            text="SYSTEM: BEREIT",
            font=('Arial', 10, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        self.system_status_label.pack(side=tk.TOP)
        
        # Last Update
        self.last_update_label = tk.Label(
            status_frame,
            text="LETZTES UPDATE: --.--.---- --:--:--.---",
            font=('Arial', 8),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        self.last_update_label.pack(side=tk.TOP)

        # Next Prediction
        self.next_prediction_label = tk.Label(
            status_frame,
            text="NÄCHSTE PROGNOSE: --.--.---- --:--:--.---",
            font=('Arial', 8),
            fg='#ffaa00',
            bg='#1a1a1a'
        )
        self.next_prediction_label.pack(side=tk.TOP)
    
    def create_control_panel(self, parent):
        """Erstelle Kontroll-Panel"""
        control_frame = tk.Frame(parent, bg='#1a1a1a', width=350)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # TITLE
        control_title = tk.Label(
            control_frame,
            text="TRADING KONTROLLEN",
            font=('Arial', 14, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        )
        control_title.pack(pady=(20, 30))
        
        # MAIN BUTTONS
        self.create_main_buttons(control_frame)
        
        # SETTINGS
        self.create_settings_section(control_frame)
        
        # CURRENT DATA
        self.create_current_data_section(control_frame)
    
    def create_main_buttons(self, parent):
        """Erstelle Haupt-Buttons"""
        button_frame = tk.Frame(parent, bg='#1a1a1a')
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 30))
        
        # START TRADING
        self.start_button = tk.Button(
            button_frame,
            text="TRADING STARTEN",
            command=self.start_trading,
            font=('Arial', 12, 'bold'),
            bg='#00aa44',
            fg='white',
            relief=tk.FLAT,
            pady=15,
            cursor='hand2'
        )
        self.start_button.pack(fill=tk.X, pady=(0, 10))
        
        # STOP TRADING
        self.stop_button = tk.Button(
            button_frame,
            text="TRADING STOPPEN",
            command=self.stop_trading,
            font=('Arial', 12, 'bold'),
            bg='#cc3333',
            fg='white',
            relief=tk.FLAT,
            pady=15,
            cursor='hand2',
            state=tk.DISABLED
        )
        self.stop_button.pack(fill=tk.X, pady=(0, 10))
        
        # SINGLE ANALYSIS
        self.analyze_button = tk.Button(
            button_frame,
            text="EINMALIGE ANALYSE",
            command=self.run_single_analysis,
            font=('Arial', 12, 'bold'),
            bg='#3366cc',
            fg='white',
            relief=tk.FLAT,
            pady=15,
            cursor='hand2'
        )
        self.analyze_button.pack(fill=tk.X, pady=(0, 10))
        
        # REFRESH DATA
        self.refresh_button = tk.Button(
            button_frame,
            text="DATEN AKTUALISIEREN",
            command=self.refresh_data,
            font=('Arial', 12, 'bold'),
            bg='#9933cc',
            fg='white',
            relief=tk.FLAT,
            pady=15,
            cursor='hand2'
        )
        self.refresh_button.pack(fill=tk.X, pady=(0, 10))
        
        # SCREENSHOT
        self.screenshot_button = tk.Button(
            button_frame,
            text="SCREENSHOT ERSTELLEN",
            command=self.take_screenshot,
            font=('Arial', 11, 'bold'),
            bg='#ff9900',
            fg='white',
            relief=tk.FLAT,
            pady=15,
            cursor='hand2'
        )
        self.screenshot_button.pack(fill=tk.X, pady=(0, 10))

        # CLEAR LOG
        self.clear_button = tk.Button(
            button_frame,
            text="LOG LÖSCHEN",
            command=self.clear_log,
            font=('Arial', 10),
            bg='#666666',
            fg='white',
            relief=tk.FLAT,
            pady=10,
            cursor='hand2'
        )
        self.clear_button.pack(fill=tk.X)
    
    def create_settings_section(self, parent):
        """Erstelle Einstellungen-Sektion"""
        settings_frame = tk.LabelFrame(
            parent,
            text="EINSTELLUNGEN",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a',
            bd=2,
            relief=tk.GROOVE
        )
        settings_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # Auto Update
        auto_frame = tk.Frame(settings_frame, bg='#1a1a1a')
        auto_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.auto_update_var = tk.BooleanVar(value=False)
        auto_check = tk.Checkbutton(
            auto_frame,
            text="Auto-Update",
            variable=self.auto_update_var,
            command=self.toggle_auto_update,
            font=('Arial', 10),
            fg='#ffffff',
            bg='#1a1a1a',
            selectcolor='#333333'
        )
        auto_check.pack(side=tk.LEFT)
        
        # Update Interval
        interval_frame = tk.Frame(settings_frame, bg='#1a1a1a')
        interval_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        tk.Label(interval_frame, text="Update-Intervall (s):", 
                font=('Arial', 9), fg='#cccccc', bg='#1a1a1a').pack(side=tk.LEFT)
        
        self.interval_var = tk.StringVar(value="30")
        interval_entry = tk.Entry(
            interval_frame,
            textvariable=self.interval_var,
            width=8,
            font=('Arial', 9),
            bg='#333333',
            fg='white',
            insertbackground='white'
        )
        interval_entry.pack(side=tk.RIGHT)
        interval_entry.bind('<Return>', self.update_interval_changed)
    
    def create_current_data_section(self, parent):
        """Erstelle aktuelle Daten-Sektion"""
        data_frame = tk.LabelFrame(
            parent,
            text="AKTUELLE DATEN",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a',
            bd=2,
            relief=tk.GROOVE
        )
        data_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))
        
        # Current Price
        self.current_price_label = tk.Label(
            data_frame,
            text="Preis: $---.---",
            font=('Arial', 14, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        self.current_price_label.pack(pady=10)
        
        # Signal
        self.current_signal_label = tk.Label(
            data_frame,
            text="Signal: ---",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        )
        self.current_signal_label.pack(pady=5)
        
        # Confidence
        self.current_confidence_label = tk.Label(
            data_frame,
            text="Konfidenz: ---%",
            font=('Arial', 10),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        self.current_confidence_label.pack(pady=5)
        
        # Key Indicators
        indicators_text = tk.Text(
            data_frame,
            height=8,
            width=35,
            font=('Courier', 8),
            bg='#333333',
            fg='#ffffff',
            insertbackground='white',
            state=tk.DISABLED
        )
        indicators_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.indicators_text = indicators_text

    def create_visualization_panel(self, parent):
        """Erstelle Visualisierungs-Panel"""
        viz_frame = tk.Frame(parent, bg='#0a0a0a')
        viz_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # NOTEBOOK für verschiedene Charts
        self.notebook = ttk.Notebook(viz_frame, style='Ultimate.TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # TAB 1: Preis-Chart
        self.create_price_chart_tab()

        # TAB 2: Technische Indikatoren
        self.create_indicators_tab()

        # TAB 3: Trading Signale
        self.create_signals_tab()

        # TAB 4: Risk Management
        self.create_risk_tab()

    def create_price_chart_tab(self):
        """Erstelle Preis-Chart Tab"""
        price_frame = ttk.Frame(self.notebook)
        self.notebook.add(price_frame, text="PREIS-CHART")

        # Matplotlib Figure
        plt.style.use('dark_background')
        self.price_fig, (self.price_ax, self.volume_ax) = plt.subplots(2, 1, figsize=(12, 8),
                                                                       gridspec_kw={'height_ratios': [3, 1]})
        self.price_fig.patch.set_facecolor('#0a0a0a')

        # Canvas
        self.price_canvas = FigureCanvasTkAgg(self.price_fig, price_frame)
        self.price_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Initial empty chart
        self.show_waiting_chart(self.price_ax, "Klicken Sie 'TRADING STARTEN' für Live-Charts")
        self.show_waiting_chart(self.volume_ax, "Volume-Daten")

        # Initial draw
        self.price_canvas.draw()

    def create_indicators_tab(self):
        """Erstelle Technische Indikatoren Tab"""
        indicators_frame = ttk.Frame(self.notebook)
        self.notebook.add(indicators_frame, text="INDIKATOREN")

        # Matplotlib Figure für 4 Indikatoren
        self.indicators_fig, self.indicators_axes = plt.subplots(2, 2, figsize=(12, 8))
        self.indicators_fig.patch.set_facecolor('#0a0a0a')

        # Canvas
        self.indicators_canvas = FigureCanvasTkAgg(self.indicators_fig, indicators_frame)
        self.indicators_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Initial empty charts
        for ax in self.indicators_axes.flat:
            self.show_waiting_chart(ax, "Warten auf\nDaten")

        # Initial draw
        self.indicators_canvas.draw()

    def create_signals_tab(self):
        """Erstelle Trading Signale Tab"""
        signals_frame = ttk.Frame(self.notebook)
        self.notebook.add(signals_frame, text="SIGNALE")

        # Matplotlib Figure für Signale
        self.signals_fig, self.signals_axes = plt.subplots(2, 2, figsize=(12, 8))
        self.signals_fig.patch.set_facecolor('#0a0a0a')

        # Canvas
        self.signals_canvas = FigureCanvasTkAgg(self.signals_fig, signals_frame)
        self.signals_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Initial empty charts
        for ax in self.signals_axes.flat:
            self.show_waiting_chart(ax, "Signal\nAnalyse")

        # Initial draw
        self.signals_canvas.draw()

    def create_risk_tab(self):
        """Erstelle Risk Management Tab"""
        risk_frame = ttk.Frame(self.notebook)
        self.notebook.add(risk_frame, text="RISK MANAGEMENT")

        # Matplotlib Figure für Risk Metrics
        self.risk_fig, self.risk_axes = plt.subplots(2, 2, figsize=(12, 8))
        self.risk_fig.patch.set_facecolor('#0a0a0a')

        # Canvas
        self.risk_canvas = FigureCanvasTkAgg(self.risk_fig, risk_frame)
        self.risk_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Initial empty charts
        for ax in self.risk_axes.flat:
            self.show_waiting_chart(ax, "Risk\nMetrics")

        # Initial draw
        self.risk_canvas.draw()

    def create_status_panel(self):
        """Erstelle Status-Panel"""
        status_frame = tk.Frame(self.root, bg='#1a1a1a', height=150)
        status_frame.pack(fill=tk.X, padx=10, pady=(10, 10))
        status_frame.pack_propagate(False)

        # Title
        status_title = tk.Label(
            status_frame,
            text="STATUS LOG",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        )
        status_title.pack(pady=(10, 5))

        # Log Text Widget
        log_frame = tk.Frame(status_frame, bg='#1a1a1a')
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        self.status_log = tk.Text(
            log_frame,
            height=6,
            font=('Courier', 9),
            bg='#333333',
            fg='#00ff88',
            insertbackground='white',
            state=tk.DISABLED
        )

        # Scrollbar
        scrollbar = tk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.status_log.yview)
        self.status_log.configure(yscrollcommand=scrollbar.set)

        self.status_log.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def show_waiting_chart(self, ax, message):
        """Zeige Warteanzeige auf Chart"""
        ax.clear()
        ax.text(0.5, 0.5, message,
               horizontalalignment='center',
               verticalalignment='center',
               transform=ax.transAxes,
               fontsize=12,
               color='#00ff88',
               weight='bold',
               bbox=dict(boxstyle='round,pad=1', facecolor='#1a1a1a', alpha=0.8))
        ax.set_facecolor('#0a0a0a')
        ax.set_xticks([])
        ax.set_yticks([])
        for spine in ax.spines.values():
            spine.set_visible(False)

    def log_message(self, message):
        """Füge Nachricht zum Status-Log hinzu"""
        now = datetime.now()
        timestamp = now.strftime("%H:%M:%S.%f")[:-3]  # Millisekunden
        date_str = now.strftime("%d.%m.%Y")
        log_entry = f"[{date_str} {timestamp}] {message}\n"

        self.status_log.config(state=tk.NORMAL)
        self.status_log.insert(tk.END, log_entry)
        self.status_log.see(tk.END)
        self.status_log.config(state=tk.DISABLED)
        self.root.update()

        print(log_entry.strip())

    # BUTTON FUNKTIONEN
    def start_trading(self):
        """Starte Trading"""
        if self.is_running:
            self.log_message("Trading läuft bereits!")
            return

        self.log_message("STARTE ULTIMATE BITCOIN TRADING SYSTEM V2.0...")

        # Update GUI
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.system_status_label.config(text="SYSTEM: LÄUFT", fg='#00ff88')

        # Starte ersten Analyse-Lauf
        self.run_analysis_thread()

        # Starte Auto-Update wenn aktiviert
        if self.auto_update_var.get():
            self.start_auto_update()

    def stop_trading(self):
        """Stoppe Trading"""
        if not self.is_running:
            self.log_message("Trading läuft nicht!")
            return

        self.log_message("STOPPE ULTIMATE BITCOIN TRADING SYSTEM V2.0...")

        # Update GUI
        self.is_running = False
        self.auto_update = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.system_status_label.config(text="SYSTEM: GESTOPPT", fg='#cc3333')

        # Stoppe Auto-Update
        if self.update_job_id:
            self.root.after_cancel(self.update_job_id)
            self.update_job_id = None

        self.log_message("Trading gestoppt")

    def run_single_analysis(self):
        """Führe einmalige Analyse durch"""
        self.log_message("Führe einmalige Analyse durch...")
        self.run_analysis_thread()

    def refresh_data(self):
        """Aktualisiere Daten"""
        self.log_message("Aktualisiere Daten...")
        self.run_analysis_thread()

    def take_screenshot(self):
        """Erstelle Screenshot der GUI"""
        try:
            import tkinter as tk
            from PIL import Image, ImageTk
            import io

            # Erstelle Screenshot-Verzeichnis
            screenshot_dir = "screenshots"
            if not os.path.exists(screenshot_dir):
                os.makedirs(screenshot_dir)

            # Generiere Dateinamen mit Timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"bitcoin_trading_screenshot_{timestamp}.png"
            filepath = os.path.join(screenshot_dir, filename)

            # Screenshot der gesamten GUI
            x = self.root.winfo_rootx()
            y = self.root.winfo_rooty()
            width = self.root.winfo_width()
            height = self.root.winfo_height()

            # Verwende PIL für Screenshot
            try:
                from PIL import ImageGrab
                screenshot = ImageGrab.grab(bbox=(x, y, x + width, y + height))
                screenshot.save(filepath, 'PNG')

                self.log_message(f"Screenshot gespeichert: {filename}")

                # Zeige Bestätigung
                messagebox.showinfo("Screenshot",
                                  f"Screenshot erfolgreich gespeichert:\n{filepath}")

            except ImportError:
                # Fallback: Verwende tkinter's postscript
                self.root.update()
                ps_file = filepath.replace('.png', '.ps')
                self.root.postscript(file=ps_file)

                # Konvertiere PS zu PNG (wenn möglich)
                try:
                    from PIL import Image
                    img = Image.open(ps_file)
                    img.save(filepath, 'PNG')
                    os.remove(ps_file)  # Lösche PS-Datei
                    self.log_message(f"Screenshot gespeichert: {filename}")
                except:
                    self.log_message(f"PostScript-Datei gespeichert: {ps_file}")

        except Exception as e:
            self.log_message(f"FEHLER beim Screenshot: {e}")
            messagebox.showerror("Screenshot Fehler", f"Konnte Screenshot nicht erstellen:\n{e}")

    def clear_log(self):
        """Lösche Status-Log"""
        self.status_log.config(state=tk.NORMAL)
        self.status_log.delete(1.0, tk.END)
        self.status_log.config(state=tk.DISABLED)
        self.log_message("Log gelöscht")

    def toggle_auto_update(self):
        """Schalte Auto-Update um"""
        if self.auto_update_var.get() and self.is_running:
            self.start_auto_update()
        else:
            self.auto_update = False
            if self.update_job_id:
                self.root.after_cancel(self.update_job_id)
                self.update_job_id = None

        status = "EIN" if self.auto_update_var.get() else "AUS"
        self.log_message(f"Auto-Update: {status}")

    def update_interval_changed(self, event=None):
        """Update-Intervall geändert"""
        try:
            new_interval = int(self.interval_var.get())
            if new_interval >= 10:  # Mindestens 10 Sekunden
                self.update_interval = new_interval
                self.log_message(f"Update-Intervall geändert: {new_interval}s")
            else:
                self.log_message("Update-Intervall muss mindestens 10 Sekunden sein")
                self.interval_var.set(str(self.update_interval))
        except ValueError:
            self.log_message("Ungültiges Update-Intervall")
            self.interval_var.set(str(self.update_interval))

    # ANALYSE FUNKTIONEN
    def run_analysis_thread(self):
        """Führe Analyse in separatem Thread durch"""
        def analysis_worker():
            try:
                self.api_status_label.config(text="API: LÄDT...", fg='#ffaa00')
                self.root.update()

                # Hole Marktdaten
                df = self.trading_system.get_real_market_data()

                if df.empty:
                    self.log_message("FEHLER: Keine Marktdaten verfügbar")
                    self.api_status_label.config(text="API: FEHLER", fg='#cc3333')
                    return

                self.api_status_label.config(text="API: ANALYSIERT...", fg='#ffaa00')
                self.root.update()

                # Führe Ultimate Analyse durch
                result = self.trading_system.analyze_market_ultimate(df)

                if not result:
                    self.log_message("FEHLER: Analyse fehlgeschlagen")
                    self.api_status_label.config(text="API: FEHLER", fg='#cc3333')
                    return

                # Berechne Risk Management
                risk_metrics = self.trading_system.calculate_advanced_risk_management(result)
                result['risk_metrics'] = risk_metrics

                # BERECHNE 24H-PROGNOSE AB SCRIPTSTART
                prediction_summary = self.calculate_24h_prediction_from_start(result)
                result['prediction_summary'] = prediction_summary

                # Speichere Ergebnis
                self.current_result = result
                self.last_update = datetime.now()

                # Update GUI im Hauptthread
                self.root.after(0, lambda: self.update_gui_with_result(result))

                self.api_status_label.config(text="API: ERFOLGREICH", fg='#00ff88')

            except Exception as e:
                self.log_message(f"FEHLER bei Analyse: {e}")
                self.api_status_label.config(text="API: FEHLER", fg='#cc3333')

        # Starte Thread
        thread = threading.Thread(target=analysis_worker, daemon=True)
        thread.start()

    def update_gui_with_result(self, result):
        """Update GUI mit Analyse-Ergebnis"""
        try:
            # Update Header Status mit detaillierter Zeit
            last_update_str = self.last_update.strftime('%d.%m.%Y %H:%M:%S.%f')[:-3]
            self.last_update_label.config(
                text=f"LETZTES UPDATE: {last_update_str}")

            # Update Next Prediction Time
            if self.next_prediction_time:
                next_pred_str = self.next_prediction_time.strftime('%d.%m.%Y %H:%M:%S.%f')[:-3]
                self.next_prediction_label.config(
                    text=f"NÄCHSTE PROGNOSE: {next_pred_str}")

            # Update Current Data
            self.update_current_data(result)

            # Update Charts
            self.update_price_chart(result)
            self.update_indicators_chart(result)
            self.update_signals_chart(result)
            self.update_risk_chart(result)

            # Log Ergebnis
            signal = result.get('signal', 'N/A')
            confidence = result.get('confidence', 0)
            price = result.get('current_price', 0)

            self.log_message(f"ANALYSE: {signal} (Konfidenz: {confidence:.1%}) - Preis: ${price:,.2f}")

            # Speichere für Historie
            self.price_history.append({
                'timestamp': self.last_update,
                'price': price,
                'signal': signal,
                'confidence': confidence
            })

            # Behalte nur letzte 100 Einträge
            if len(self.price_history) > 100:
                self.price_history = self.price_history[-100:]

        except Exception as e:
            self.log_message(f"FEHLER bei GUI-Update: {e}")

    def update_current_data(self, result):
        """Update aktuelle Daten-Anzeige"""
        try:
            # Preis
            price = result.get('current_price', 0)
            self.current_price_label.config(text=f"Preis: ${price:,.2f}")

            # Signal
            signal = result.get('signal', 'N/A')
            signal_color = {'KAUFEN': '#00ff88', 'VERKAUFEN': '#ff3333', 'HALTEN': '#ffaa00'}.get(signal, '#ffffff')
            self.current_signal_label.config(text=f"Signal: {signal}", fg=signal_color)

            # Konfidenz
            confidence = result.get('confidence', 0)
            self.current_confidence_label.config(text=f"Konfidenz: {confidence:.1%}")

            # Technische Indikatoren
            indicators = result.get('technical_indicators', {})

            indicators_text = ""
            indicators_text += f"RSI (14):     {indicators.get('rsi_14', 0):.1f}\n"
            indicators_text += f"MACD:        {indicators.get('macd', 0):.2f}\n"
            indicators_text += f"BB Position: {indicators.get('bb_position', 0):.2f}\n"
            indicators_text += f"Stoch %K:    {indicators.get('stoch_k', 0):.1f}\n"
            indicators_text += f"Williams %R: {indicators.get('williams_r', 0):.1f}\n"
            indicators_text += f"Volume Ratio:{indicators.get('volume_ratio', 0):.2f}\n"

            # Risk Metrics
            risk_metrics = result.get('risk_metrics', {})
            if risk_metrics:
                indicators_text += f"\nRISK MANAGEMENT:\n"
                indicators_text += f"Position:    {risk_metrics.get('optimal_position_size', 0):.1%}\n"
                indicators_text += f"Stop Loss:   {risk_metrics.get('dynamic_stop_loss', 0):.1%}\n"
                indicators_text += f"Take Profit: {risk_metrics.get('dynamic_take_profit', 0):.1%}\n"
                indicators_text += f"Risk/Reward: {risk_metrics.get('risk_reward_ratio', 0):.2f}\n"
                indicators_text += f"Sharpe:      {risk_metrics.get('sharpe_ratio', 0):.2f}\n"

            # 24H-PROGNOSE
            prediction_summary = result.get('prediction_summary', {})
            if prediction_summary:
                next_hour = prediction_summary.get('next_hour_prediction', {})
                prediction_24h = prediction_summary.get('24h_prediction', {})

                indicators_text += f"\n24H-PROGNOSE:\n"
                if next_hour:
                    indicators_text += f"Nächste 1h:  ${next_hour.get('predicted_price', 0):,.0f}\n"
                    indicators_text += f"Änderung 1h: {next_hour.get('price_change_percent', 0):+.2f}%\n"
                if prediction_24h:
                    indicators_text += f"24h Gesamt:  ${prediction_24h.get('predicted_price', 0):,.0f}\n"
                    indicators_text += f"Änderung 24h:{prediction_24h.get('price_change_percent', 0):+.2f}%\n"

                avg_confidence = prediction_summary.get('average_confidence', 0)
                indicators_text += f"Ø Konfidenz: {avg_confidence:.1%}\n"

            self.indicators_text.config(state=tk.NORMAL)
            self.indicators_text.delete(1.0, tk.END)
            self.indicators_text.insert(1.0, indicators_text)
            self.indicators_text.config(state=tk.DISABLED)

        except Exception as e:
            self.log_message(f"FEHLER bei Current Data Update: {e}")

    def update_price_chart(self, result):
        """Update Preis-Chart"""
        try:
            # Hole Marktdaten
            df = self.trading_system.market_data
            if df is None or df.empty:
                self.log_message("Keine Marktdaten für Preis-Chart verfügbar")
                return

            # Clear axes
            self.price_ax.clear()
            self.volume_ax.clear()

            # Letzte 7 Tage für bessere Darstellung
            recent_df = df.tail(168)  # 7 Tage * 24 Stunden

            if len(recent_df) == 0:
                self.log_message("Keine aktuellen Daten für Chart")
                return

            # ULTIMATE TIMEZONE-FIX: Komplett robuste Lösung
            plot_dates = self.fix_timezone_issues(recent_df.index)

            # Erstelle neuen DataFrame mit timezone-naive Index
            plot_df = recent_df.copy()
            plot_df.index = plot_dates

            # Preis-Chart
            self.price_ax.plot(plot_df.index, plot_df['Close'],
                              color='#00ff88', linewidth=2, label='Bitcoin Preis')

            # Moving Averages
            if len(plot_df) >= 20:
                sma_20 = plot_df['Close'].rolling(20).mean()
                self.price_ax.plot(plot_df.index, sma_20,
                                  color='#ffaa00', linewidth=1, alpha=0.8, label='SMA 20')

            if len(plot_df) >= 50:
                sma_50 = plot_df['Close'].rolling(50).mean()
                self.price_ax.plot(plot_df.index, sma_50,
                                  color='#ff6600', linewidth=1, alpha=0.8, label='SMA 50')

            # Aktueller Preis Marker
            current_price = result.get('current_price', 0)
            self.price_ax.axhline(y=current_price, color='#ffffff', linestyle='--', alpha=0.7,
                                 label=f'Aktuell: ${current_price:,.0f}')

            # Styling
            self.price_ax.set_title('Bitcoin Preis (7 Tage)', color='white', fontsize=12, fontweight='bold')
            self.price_ax.set_ylabel('Preis ($)', color='white')
            self.price_ax.tick_params(colors='white', labelsize=8)
            self.price_ax.legend(loc='upper left', fontsize=8)
            self.price_ax.grid(True, alpha=0.3)
            self.price_ax.set_facecolor('#0a0a0a')

            # Volume-Chart mit korrekter Breite
            try:
                if isinstance(plot_df.index[0], (int, float)):
                    # Numerische Indizes
                    bar_width = 0.8
                else:
                    # Datetime-Indizes
                    bar_width = (plot_df.index[1] - plot_df.index[0]).total_seconds() / 86400 * 0.8
            except:
                bar_width = 0.8

            self.volume_ax.bar(plot_df.index, plot_df['Volume'],
                              color='#3366cc', alpha=0.6, width=bar_width)
            self.volume_ax.set_title('Handelsvolumen', color='white', fontsize=10)
            self.volume_ax.set_ylabel('Volume', color='white')
            self.volume_ax.tick_params(colors='white', labelsize=8)
            self.volume_ax.grid(True, alpha=0.3)
            self.volume_ax.set_facecolor('#0a0a0a')

            # Format x-axis nur für datetime-Indizes
            if not isinstance(plot_df.index[0], (int, float)):
                if len(plot_df) > 24:  # Mehr als 1 Tag
                    date_format = '%m-%d %H:%M'
                else:
                    date_format = '%H:%M'

                self.price_ax.xaxis.set_major_formatter(mdates.DateFormatter(date_format))
                self.volume_ax.xaxis.set_major_formatter(mdates.DateFormatter(date_format))

                # Rotiere Labels für bessere Lesbarkeit
                plt.setp(self.price_ax.xaxis.get_majorticklabels(), rotation=45)
                plt.setp(self.volume_ax.xaxis.get_majorticklabels(), rotation=45)
            else:
                # Für numerische Indizes: Einfache Beschriftung
                self.price_ax.set_xlabel('Datenpunkt', color='white')
                self.volume_ax.set_xlabel('Datenpunkt', color='white')

            # Tight layout
            self.price_fig.tight_layout()
            self.price_canvas.draw()

            self.log_message(f"Preis-Chart aktualisiert: {len(plot_df)} Datenpunkte")

        except Exception as e:
            self.log_message(f"FEHLER bei Preis-Chart Update: {e}")
            import traceback
            self.log_message(f"Traceback: {traceback.format_exc()}")

    def update_indicators_chart(self, result):
        """Update Technische Indikatoren Chart"""
        try:
            indicators = result.get('technical_indicators', {})

            # Clear axes
            for ax in self.indicators_axes.flat:
                ax.clear()

            # RSI Chart
            ax1 = self.indicators_axes[0, 0]
            rsi_values = [indicators.get('rsi_14', 50), indicators.get('rsi_21', 50)]
            rsi_labels = ['RSI 14', 'RSI 21']
            colors = ['#00ff88' if rsi < 70 else '#ff3333' for rsi in rsi_values]

            bars = ax1.bar(rsi_labels, rsi_values, color=colors, alpha=0.8)
            ax1.axhline(y=70, color='#ff3333', linestyle='--', alpha=0.7, label='Overbought')
            ax1.axhline(y=30, color='#00ff88', linestyle='--', alpha=0.7, label='Oversold')
            ax1.set_title('RSI Indikatoren', color='white', fontsize=10, fontweight='bold')
            ax1.set_ylabel('RSI Wert', color='white')
            ax1.tick_params(colors='white', labelsize=8)
            ax1.legend(fontsize=7)
            ax1.set_ylim(0, 100)

            # MACD Chart
            ax2 = self.indicators_axes[0, 1]
            macd = indicators.get('macd', 0)
            macd_signal = indicators.get('macd_signal', 0)
            macd_histogram = indicators.get('macd_histogram', 0)

            x_pos = [0, 1, 2]
            values = [macd, macd_signal, macd_histogram]
            labels = ['MACD', 'Signal', 'Histogram']
            colors = ['#00ff88', '#ffaa00', '#3366cc']

            ax2.bar(x_pos, values, color=colors, alpha=0.8)
            ax2.set_title('MACD Analyse', color='white', fontsize=10, fontweight='bold')
            ax2.set_ylabel('MACD Wert', color='white')
            ax2.set_xticks(x_pos)
            ax2.set_xticklabels(labels)
            ax2.tick_params(colors='white', labelsize=8)
            ax2.axhline(y=0, color='white', linestyle='-', alpha=0.5)

            # Bollinger Bands Position
            ax3 = self.indicators_axes[1, 0]
            bb_position = indicators.get('bb_position', 0.5)
            bb_width = indicators.get('bb_width', 0.1)

            # BB Position als Gauge
            theta = np.linspace(0, np.pi, 100)
            r = 1
            x = r * np.cos(theta)
            y = r * np.sin(theta)
            ax3.plot(x, y, 'white', linewidth=2)

            # Position Marker
            pos_angle = bb_position * np.pi
            marker_x = r * np.cos(pos_angle)
            marker_y = r * np.sin(pos_angle)
            ax3.plot([0, marker_x], [0, marker_y], color='#00ff88', linewidth=3)
            ax3.scatter(marker_x, marker_y, color='#00ff88', s=100, zorder=5)

            ax3.set_title(f'BB Position: {bb_position:.2f}', color='white', fontsize=10, fontweight='bold')
            ax3.set_xlim(-1.2, 1.2)
            ax3.set_ylim(-0.2, 1.2)
            ax3.set_aspect('equal')
            ax3.axis('off')

            # Stochastic Oscillator
            ax4 = self.indicators_axes[1, 1]
            stoch_k = indicators.get('stoch_k', 50)
            stoch_d = indicators.get('stoch_d', 50)

            x_pos = [0, 1]
            values = [stoch_k, stoch_d]
            labels = ['%K', '%D']
            colors = ['#00ff88' if val < 80 else '#ff3333' for val in values]

            ax4.bar(x_pos, values, color=colors, alpha=0.8)
            ax4.axhline(y=80, color='#ff3333', linestyle='--', alpha=0.7)
            ax4.axhline(y=20, color='#00ff88', linestyle='--', alpha=0.7)
            ax4.set_title('Stochastic Oscillator', color='white', fontsize=10, fontweight='bold')
            ax4.set_ylabel('Stoch Wert', color='white')
            ax4.set_xticks(x_pos)
            ax4.set_xticklabels(labels)
            ax4.tick_params(colors='white', labelsize=8)
            ax4.set_ylim(0, 100)

            # Styling für alle Axes
            for ax in self.indicators_axes.flat:
                ax.set_facecolor('#0a0a0a')
                ax.grid(True, alpha=0.3)

            self.indicators_fig.tight_layout()
            self.indicators_canvas.draw()

        except Exception as e:
            self.log_message(f"FEHLER bei Indikatoren-Chart Update: {e}")

    def update_signals_chart(self, result):
        """Update Trading Signale Chart"""
        try:
            signals = result.get('individual_signals', {})

            # Clear axes
            for ax in self.signals_axes.flat:
                ax.clear()

            # Signal Strength Chart
            ax1 = self.signals_axes[0, 0]
            if signals:
                signal_names = list(signals.keys())
                signal_values = list(signals.values())
                colors = ['#00ff88' if val > 0 else '#ff3333' if val < 0 else '#ffaa00' for val in signal_values]

                bars = ax1.barh(signal_names, signal_values, color=colors, alpha=0.8)
                ax1.axvline(x=0, color='white', linestyle='-', alpha=0.5)
                ax1.set_title('Individual Signale', color='white', fontsize=10, fontweight='bold')
                ax1.set_xlabel('Signal Stärke', color='white')
                ax1.tick_params(colors='white', labelsize=8)
                ax1.set_xlim(-1, 1)

            # Signal Historie
            ax2 = self.signals_axes[0, 1]
            if len(self.price_history) > 1:
                timestamps = [entry['timestamp'] for entry in self.price_history[-20:]]
                confidences = [entry['confidence'] for entry in self.price_history[-20:]]

                # ULTIMATE TIMEZONE-FIX für Signal-Historie
                plot_timestamps = self.fix_timezone_issues(pd.DatetimeIndex(timestamps))

                ax2.plot(plot_timestamps, confidences, color='#00ff88', linewidth=2, marker='o', markersize=4)
                ax2.set_title('Konfidenz-Verlauf', color='white', fontsize=10, fontweight='bold')
                ax2.set_ylabel('Konfidenz', color='white')
                ax2.tick_params(colors='white', labelsize=8)
                ax2.set_ylim(0, 1)

                # Format x-axis nur für datetime-Indizes
                if not isinstance(plot_timestamps[0] if len(plot_timestamps) > 0 else 0, (int, float)):
                    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
                else:
                    ax2.set_xlabel('Datenpunkt', color='white')

            # Weighted Signal
            ax3 = self.signals_axes[1, 0]
            weighted_signal = result.get('weighted_signal', 0)

            # Signal als Gauge
            theta = np.linspace(-np.pi/2, np.pi/2, 100)
            r = 1
            x = r * np.cos(theta)
            y = r * np.sin(theta)
            ax3.plot(x, y, 'white', linewidth=2)

            # Signal Marker
            signal_angle = weighted_signal * np.pi/2
            marker_x = r * np.cos(signal_angle)
            marker_y = r * np.sin(signal_angle)
            color = '#00ff88' if weighted_signal > 0 else '#ff3333' if weighted_signal < 0 else '#ffaa00'
            ax3.plot([0, marker_x], [0, marker_y], color=color, linewidth=4)
            ax3.scatter(marker_x, marker_y, color=color, s=150, zorder=5)

            ax3.set_title(f'Gewichtetes Signal: {weighted_signal:.3f}', color='white', fontsize=10, fontweight='bold')
            ax3.set_xlim(-1.2, 1.2)
            ax3.set_ylim(-0.2, 1.2)
            ax3.set_aspect('equal')
            ax3.axis('off')

            # Sentiment & ML
            ax4 = self.signals_axes[1, 1]
            sentiment = result.get('sentiment_score', 0.5)
            ml_predictions = result.get('ml_predictions', {})

            # Sentiment Bar
            ax4.barh(['Sentiment'], [sentiment], color='#3366cc', alpha=0.8, height=0.3)

            # ML Predictions (wenn verfügbar)
            if ml_predictions:
                ml_values = list(ml_predictions.values())
                ml_avg = np.mean(ml_values) if ml_values else 0
                ax4.barh(['ML Prediction'], [ml_avg], color='#9933cc', alpha=0.8, height=0.3)

            ax4.set_title('Sentiment & ML', color='white', fontsize=10, fontweight='bold')
            ax4.set_xlabel('Wert', color='white')
            ax4.tick_params(colors='white', labelsize=8)
            ax4.set_xlim(-1, 1)
            ax4.axvline(x=0, color='white', linestyle='-', alpha=0.5)

            # Styling für alle Axes
            for ax in self.signals_axes.flat:
                ax.set_facecolor('#0a0a0a')
                ax.grid(True, alpha=0.3)

            self.signals_fig.tight_layout()
            self.signals_canvas.draw()

        except Exception as e:
            self.log_message(f"FEHLER bei Signale-Chart Update: {e}")

    def update_risk_chart(self, result):
        """Update Risk Management Chart"""
        try:
            risk_metrics = result.get('risk_metrics', {})

            # Clear axes
            for ax in self.risk_axes.flat:
                ax.clear()

            # Position Size & Risk/Reward
            ax1 = self.risk_axes[0, 0]
            position_size = risk_metrics.get('optimal_position_size', 0) * 100
            risk_reward = risk_metrics.get('risk_reward_ratio', 0)

            x_pos = [0, 1]
            values = [position_size, risk_reward]
            labels = ['Position %', 'Risk/Reward']
            colors = ['#00ff88', '#3366cc']

            bars = ax1.bar(x_pos, values, color=colors, alpha=0.8)
            ax1.set_title('Position & Risk/Reward', color='white', fontsize=10, fontweight='bold')
            ax1.set_ylabel('Wert', color='white')
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels(labels)
            ax1.tick_params(colors='white', labelsize=8)

            # Performance Ratios
            ax2 = self.risk_axes[0, 1]
            sharpe = risk_metrics.get('sharpe_ratio', 0)
            calmar = risk_metrics.get('calmar_ratio', 0)
            sortino = risk_metrics.get('sortino_ratio', 0)

            ratios = [sharpe, calmar, sortino]
            ratio_labels = ['Sharpe', 'Calmar', 'Sortino']
            colors = ['#00ff88', '#ffaa00', '#3366cc']

            bars = ax2.bar(ratio_labels, ratios, color=colors, alpha=0.8)
            ax2.set_title('Performance Ratios', color='white', fontsize=10, fontweight='bold')
            ax2.set_ylabel('Ratio', color='white')
            ax2.tick_params(colors='white', labelsize=8)
            ax2.axhline(y=1, color='white', linestyle='--', alpha=0.5)

            # VaR Chart
            ax3 = self.risk_axes[1, 0]
            var_95 = risk_metrics.get('var_95', 0)
            var_99 = risk_metrics.get('var_99', 0)
            portfolio_value = risk_metrics.get('portfolio_value', 100000)

            var_values = [var_95, var_99]
            var_labels = ['VaR 95%', 'VaR 99%']
            colors = ['#ffaa00', '#ff3333']

            bars = ax3.bar(var_labels, var_values, color=colors, alpha=0.8)
            ax3.set_title('Value at Risk', color='white', fontsize=10, fontweight='bold')
            ax3.set_ylabel('VaR ($)', color='white')
            ax3.tick_params(colors='white', labelsize=8)

            # Kelly Criterion
            ax4 = self.risk_axes[1, 1]
            kelly = risk_metrics.get('kelly_fraction', 0)

            # Kelly als Gauge
            theta = np.linspace(0, np.pi, 100)
            r = 1
            x = r * np.cos(theta)
            y = r * np.sin(theta)
            ax4.plot(x, y, 'white', linewidth=2)

            # Kelly Marker (0 bis 0.25 = 0 bis π)
            kelly_angle = (kelly / 0.25) * np.pi if kelly <= 0.25 else np.pi
            marker_x = r * np.cos(kelly_angle)
            marker_y = r * np.sin(kelly_angle)
            color = '#00ff88' if kelly < 0.15 else '#ffaa00' if kelly < 0.20 else '#ff3333'
            ax4.plot([0, marker_x], [0, marker_y], color=color, linewidth=4)
            ax4.scatter(marker_x, marker_y, color=color, s=150, zorder=5)

            ax4.set_title(f'Kelly Criterion: {kelly:.3f}', color='white', fontsize=10, fontweight='bold')
            ax4.set_xlim(-1.2, 1.2)
            ax4.set_ylim(-0.2, 1.2)
            ax4.set_aspect('equal')
            ax4.axis('off')

            # Styling für alle Axes
            for ax in self.risk_axes.flat:
                ax.set_facecolor('#0a0a0a')
                ax.grid(True, alpha=0.3)

            self.risk_fig.tight_layout()
            self.risk_canvas.draw()

        except Exception as e:
            self.log_message(f"FEHLER bei Risk-Chart Update: {e}")

    # AUTO-UPDATE FUNKTIONEN
    def start_auto_update(self):
        """Starte Auto-Update"""
        if not self.is_running:
            return

        self.auto_update = True
        self.schedule_next_update()
        self.log_message(f"Auto-Update gestartet (Intervall: {self.update_interval}s)")

    def schedule_next_update(self):
        """Plane nächstes Update"""
        if self.auto_update and self.is_running:
            self.update_job_id = self.root.after(self.update_interval * 1000, self.auto_update_callback)

    def auto_update_callback(self):
        """Auto-Update Callback"""
        if self.auto_update and self.is_running:
            self.log_message("Auto-Update: Führe Analyse durch...")
            self.run_analysis_thread()
            self.schedule_next_update()

    def run(self):
        """Starte GUI"""
        self.log_message("ULTIMATE BITCOIN TRADING LAUNCHER V2.0 bereit!")
        self.log_message("Klicken Sie 'TRADING STARTEN' um zu beginnen")
        self.log_message("Alle Features: Echte APIs + ML + Erweiterte Analyse + Risk Management")

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("Launcher beendet")
        finally:
            # Cleanup
            if self.update_job_id:
                self.root.after_cancel(self.update_job_id)

def create_ultimate_desktop_shortcut_v2():
    """Erstelle Desktop-Verknüpfung für Ultimate Launcher V2.0"""
    try:
        import winshell
        from win32com.client import Dispatch

        # Desktop-Pfad
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "Ultimate Bitcoin Trading Launcher V2.0.lnk")

        # Shell-Objekt
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)

        # Python-Interpreter
        python_exe = sys.executable
        launcher_path = os.path.abspath(__file__)

        # Verknüpfungs-Eigenschaften
        shortcut.Targetpath = python_exe
        shortcut.Arguments = f'"{launcher_path}"'
        shortcut.WorkingDirectory = os.path.dirname(launcher_path)
        shortcut.Description = "Ultimate Bitcoin Trading Launcher V2.0 - Echte APIs + ML + Erweiterte Analyse"
        shortcut.IconLocation = python_exe

        # Speichern
        shortcut.save()

        print(f"✅ Desktop-Verknüpfung erstellt: {shortcut_path}")
        return True

    except Exception as e:
        print(f"❌ Fehler bei Desktop-Verknüpfung: {e}")
        return False

def main():
    """Hauptfunktion"""
    print("STARTE ULTIMATE BITCOIN TRADING LAUNCHER V2.0...")

    # Erstelle Desktop-Verknüpfung (optional)
    if '--create-shortcut' in sys.argv:
        create_ultimate_desktop_shortcut_v2()
        return

    # Starte Launcher
    try:
        launcher = UltimateBitcoinTradingLauncherV2()
        launcher.run()
    except Exception as e:
        print(f"FEHLER beim Starten des Launchers: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
