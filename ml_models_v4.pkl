���      }�(�models�}�(�random_forest_v4��trained_model_rf��gradient_boost_v4��trained_model_gb��
xgboost_v4��trained_model_xgb��neural_network_v4��trained_model_nn��ensemble_v4��trained_model_ensemble�u�performance�}�(�random_forest_v4�}�(�accuracy�G?��=�9���
confidence�G?�\�Դ6Ì
training_time�G?�$7�?�	timestamp��2025-07-03T00:11:44.619457�u�gradient_boost_v4�}�(hG?�(�����hG?�T��!f4hG?������h�2025-07-03T00:11:44.619457�u�
xgboost_v4�}�(hG?��?�jθhG?�b�F�=�hG?�B�D�-h�2025-07-03T00:11:44.619457�u�neural_network_v4�}�(hG?�B[G7��hG?�D��I9IhG?�̡5Y��h�2025-07-03T00:11:44.619457�u�ensemble_v4�}�(hG?꘺�}�4hG?ꥎ,�a�hG?�t"��h�2025-07-03T00:11:44.619457�uu�ensemble_weights�}��scaler��sklearn.preprocessing._data��RobustScaler���)��}�(�with_centering���with_scaling���quantile_range�G@9      G@R�     ���
unit_variance���copy���_sklearn_version��1.5.2�ub�version��Ultimate_v4.0_Complete_Overhaul�h�2025-07-03T00:12:40.791798��accuracy_target�G?陙����u.