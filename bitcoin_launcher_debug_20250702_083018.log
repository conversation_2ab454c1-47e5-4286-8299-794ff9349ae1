[08:28:20.059] [INFO] 🔍 STARTE UMFASSENDE SYSTEM-DIAGNOSE...
[08:28:20.171] [DEBUG] 📊 Sammle System-Informationen...
[08:28:20.190] [INFO] 🖥️ System: Windows 10
[08:28:20.200] [INFO] 🐍 Python: 3.11.2
[08:28:20.222] [INFO] 📁 Arbeitsverzeichnis: E:\Dev
[08:28:20.234] [INFO] 👤 Benutzer: <PERSON>
[08:28:20.250] [INFO] 💾 RAM: 31 GB (verfügbar: 16 GB)
[08:28:20.253] [INFO] ⚡ CPU: 16 Kerne
[08:28:20.256] [DEBUG] 🐍 Prüfe Python-Umgebung...
[08:28:20.259] [SUCCESS] ✅ Python-Executable gefunden: E:\Dev\tf_env\Scripts\python.exe
[08:28:20.264] [DEBUG] 📁 Prüfe Arbeitsverzeichnis...
[08:28:20.266] [SUCCESS] ✅ Script-Verzeichnis existiert: E:\Dev
[08:28:20.268] [INFO] 📄 Python-Dateien gefunden: 100
[08:28:20.272] [DEBUG] 📊 Prüfe Modell-Dateien...
[08:28:20.274] [SUCCESS] ✅ ultimate_complete_bitcoin_trading_FAVORITE.py: 41,725 Bytes
[08:28:20.276] [SUCCESS] ✅ btc_ultimate_optimized_complete.py: 30,674 Bytes
[08:28:20.279] [SUCCESS] ✅ ultimate_self_learning_ai_bitcoin_trading.py: 50,694 Bytes
[08:28:20.282] [SUCCESS] ✅ System-Diagnose abgeschlossen
[08:28:20.285] [SUCCESS] 🔧 Bitcoin Trading Launcher DEBUG bereit!
[08:28:20.289] [INFO] 💡 Detaillierte Fehlerbehebung und Diagnose aktiv
[08:28:20.304] [INFO] 🎯 Verwenden Sie DEBUG START für detaillierte Analyse
[08:28:31.586] [INFO] 🔧 DEBUG START: 🏅 FAVORIT - Das Bewährte System (Versuch #1)
[08:28:31.590] [DEBUG] 🔍 Pre-Start Diagnose...
[08:28:31.592] [SUCCESS] ✅ Datei gefunden: E:\Dev\ultimate_complete_bitcoin_trading_FAVORITE.py
[08:28:31.593] [SUCCESS] ✅ Python-Executable: E:\Dev\tf_env\Scripts\python.exe
[08:28:31.595] [SUCCESS] ✅ Arbeitsverzeichnis: E:\Dev
[08:28:31.596] [DEBUG] 🔧 Bereite subprocess-Parameter vor...
[08:28:31.598] [DEBUG] 📋 Kommando: E:\Dev\tf_env\Scripts\python.exe ultimate_complete_bitcoin_trading_FAVORITE.py
[08:28:31.600] [DEBUG] 🪟 Windows-spezifische Flags gesetzt
[08:28:31.610] [DEBUG] ⚙️ subprocess-Parameter: {'stdout': -1, 'stderr': -1, 'text': True, 'cwd': 'E:\\Dev', 'bufsize': 1, 'universal_newlines': True, 'creationflags': 512}
[08:28:31.614] [INFO] 🚀 Starte Prozess...
[08:28:31.621] [SUCCESS] ✅ Prozess gestartet - PID: 12508
[08:28:31.623] [DEBUG] 📊 Prozess-Status: None
[08:28:31.625] [DEBUG] 🔍 Starte Monitoring-Thread...
[08:28:31.630] [SUCCESS] ✅ Monitoring-Thread gestartet: Monitor-favorit
[08:28:31.630] [DEBUG] 🔍 Debug-Monitoring gestartet für 🏅 FAVORIT - Das Bewährte System
[08:28:31.633] [SUCCESS] 🎉 DEBUG START ERFOLGREICH: 🏅 FAVORIT - Das Bewährte System
[08:28:34.396] [WARNING] ⚠️ 🏅 FAVORIT - Das Bewährte System ERROR: Traceback (most recent call last):
[08:28:34.497] [ERROR] ❌ 🏅 FAVORIT - Das Bewährte System FINAL ERROR: File "C:\Tools\Python\Lib\encodings\cp1252.py", line 19, in encode
[08:28:34.502] [ERROR] ❌ 🏅 FAVORIT - Das Bewährte System FINAL ERROR: return codecs.charmap_encode(input,self.errors,encoding_table)[0]
[08:28:34.504] [ERROR] ❌ 🏅 FAVORIT - Das Bewährte System FINAL ERROR: ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[08:28:34.505] [ERROR] ❌ 🏅 FAVORIT - Das Bewährte System FINAL ERROR: UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3a8' in position 0: character maps to <undefined>
[08:28:34.511] [INFO] 🏁 🏅 FAVORIT - Das Bewährte System BEENDET
[08:28:34.529] [DEBUG] 📊 Return Code: 1
[08:28:34.534] [DEBUG] ⏱️ Laufzeit: 2.9s
[08:28:34.541] [DEBUG] 📄 Output-Zeilen: 0
[08:28:34.545] [ERROR] ❌ 🏅 FAVORIT - Das Bewährte System mit Fehler beendet
[08:28:57.248] [INFO] 🔍 DIAGNOSE: 🏅 FAVORIT - Das Bewährte System
[08:28:57.252] [DEBUG] 📄 Datei: ultimate_complete_bitcoin_trading_FAVORITE.py
[08:28:57.255] [DEBUG] 📊 Größe: 41,725 Bytes
[08:28:57.257] [DEBUG] 📅 Geändert: 2025-07-02 04:05:04
[08:28:57.269] [DEBUG] 🔍 Prüfe Python-Syntax...
[08:28:57.343] [SUCCESS] ✅ Syntax-Prüfung erfolgreich
[08:28:57.345] [DEBUG] 📦 Teste Imports...
[08:28:57.870] [SUCCESS] ✅ Import-Test erfolgreich
[08:28:57.872] [SUCCESS] ✅ Diagnose abgeschlossen: 🏅 FAVORIT - Das Bewährte System
[08:29:07.924] [INFO] ▶️ NORMAL START: 🏅 FAVORIT - Das Bewährte System
[08:29:07.931] [SUCCESS] ✅ Normal gestartet - PID: 17740
[08:29:09.672] [ERROR] ❌ 🏅 FAVORIT - Das Bewährte System Fehler
[08:29:21.082] [INFO] 🌍 PRÜFE UMGEBUNGSVARIABLEN...
[08:29:21.087] [DEBUG] 🔧 PATH: c:\Users\<USER>\.vscode\extensions\ms-python.python-2025.8.0-win32-x64\python_files\deactivate\powers...
[08:29:21.089] [DEBUG] 🔧 PYTHONPATH: Nicht gesetzt
[08:29:21.090] [DEBUG] 🔧 PYTHON: Nicht gesetzt
[08:29:21.092] [DEBUG] 🔧 USERNAME: Denny
[08:29:21.094] [DEBUG] 🔧 USERPROFILE: C:\Users\<USER>\Users\Denny\AppData\Local\Temp
[08:29:21.099] [DEBUG] 📦 Prüfe wichtige Python-Module...
[08:29:21.102] [SUCCESS] ✅ tkinter: Verfügbar
[08:29:21.104] [SUCCESS] ✅ subprocess: Verfügbar
[08:29:21.107] [SUCCESS] ✅ threading: Verfügbar
[08:29:21.109] [SUCCESS] ✅ os: Verfügbar
[08:29:21.111] [SUCCESS] ✅ sys: Verfügbar
[08:29:21.114] [SUCCESS] ✅ json: Verfügbar
[08:29:21.115] [SUCCESS] ✅ datetime: Verfügbar
[08:29:21.517] [SUCCESS] ✅ pandas: Verfügbar
[08:29:21.518] [SUCCESS] ✅ numpy: Verfügbar
[08:29:21.598] [SUCCESS] ✅ matplotlib: Verfügbar
[08:29:22.031] [SUCCESS] ✅ sklearn: Verfügbar
[08:29:26.898] [SUCCESS] ✅ tensorflow: Verfügbar
[08:29:26.900] [SUCCESS] ✅ psutil: Verfügbar
[08:29:26.901] [SUCCESS] ✅ Umgebungsprüfung abgeschlossen
[08:29:31.477] [INFO] 📦 PRÜFE MODELL-ABHÄNGIGKEITEN...
[08:29:31.480] [DEBUG] 🔧 Teste subprocess-Funktionalität...
[08:29:31.495] [SUCCESS] ✅ subprocess funktioniert: Python 3.11.2
[08:29:31.497] [DEBUG] 🧵 Teste Threading-Funktionalität...
[08:29:32.510] [WARNING] ⚠️ Threading-Timeout
[08:29:31.611] [SUCCESS] ✅ Threading funktioniert
[08:29:32.512] [SUCCESS] ✅ Abhängigkeitsprüfung abgeschlossen
[08:29:43.626] [INFO] 🔍 STARTE UMFASSENDE SYSTEM-DIAGNOSE...
[08:29:43.628] [DEBUG] 📊 Sammle System-Informationen...
[08:29:43.630] [INFO] 🖥️ System: Windows 10
[08:29:43.631] [INFO] 🐍 Python: 3.11.2
[08:29:43.633] [INFO] 📁 Arbeitsverzeichnis: E:\Dev
[08:29:43.634] [INFO] 👤 Benutzer: Denny
[08:29:43.641] [INFO] 💾 RAM: 31 GB (verfügbar: 16 GB)
[08:29:43.642] [INFO] ⚡ CPU: 16 Kerne
[08:29:43.644] [DEBUG] 🐍 Prüfe Python-Umgebung...
[08:29:43.645] [SUCCESS] ✅ Python-Executable gefunden: E:\Dev\tf_env\Scripts\python.exe
[08:29:43.649] [DEBUG] 📁 Prüfe Arbeitsverzeichnis...
[08:29:43.651] [SUCCESS] ✅ Script-Verzeichnis existiert: E:\Dev
[08:29:43.652] [INFO] 📄 Python-Dateien gefunden: 100
[08:29:43.654] [DEBUG] 📊 Prüfe Modell-Dateien...
[08:29:43.656] [SUCCESS] ✅ ultimate_complete_bitcoin_trading_FAVORITE.py: 41,725 Bytes
[08:29:43.657] [SUCCESS] ✅ btc_ultimate_optimized_complete.py: 30,674 Bytes
[08:29:43.660] [SUCCESS] ✅ ultimate_self_learning_ai_bitcoin_trading.py: 50,694 Bytes
[08:29:43.662] [SUCCESS] ✅ System-Diagnose abgeschlossen
[08:30:00.042] [INFO] ▶️ NORMAL START: 🏅 FAVORIT - Das Bewährte System
[08:30:00.046] [SUCCESS] ✅ Normal gestartet - PID: 15852
[08:30:01.890] [ERROR] ❌ 🏅 FAVORIT - Das Bewährte System Fehler

