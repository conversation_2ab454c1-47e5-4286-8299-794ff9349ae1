#!/usr/bin/env python3
"""
ULTIMATE BITCOIN PREDICTION SYSTEM - NEUE VERSION
Modernste Architektur mit maximaler Genauigkeit und Performance
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import (LSTM, GRU, Dense, Dropout, BatchNormalization, 
                                   Bidirectional, Attention, MultiHeadAttention,
                                   LayerNormalization, Input, Concatenate, Add)
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.optimizers.legacy import Adam, RMSprop
from tensorflow.keras.regularizers import l1_l2
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import TimeSeriesSplit
import warnings
import time
import os
warnings.filterwarnings('ignore')

# Setze Stil
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("🚀 ULTIMATE BITCOIN PREDICTION SYSTEM - NEUE VERSION")
print("=" * 70)
print("🎯 Ziel: >95% Genauigkeit mit modernster KI-Architektur")
print("💻 Features: Transformer, Attention, Multi-Modal, Ensemble")
print("=" * 70)

# ULTIMATE KONFIGURATION
CONFIG = {
    'data_file': 'crypto_data.csv',
    'target_accuracy': 0.95,
    'sequence_length': 60,
    'prediction_horizons': [1, 6, 12, 24, 48],
    'feature_engineering': 'ultimate',
    'model_types': ['transformer', 'lstm_attention', 'gru_bidirectional', 'hybrid_ensemble'],
    'optimization': 'bayesian',
    'validation': 'walk_forward',
    'ensemble_method': 'stacking',
    'real_time': True
}

class UltimateBitcoinPredictor:
    """Ultimate Bitcoin Prediction System mit modernster Architektur"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        self.performance_history = []
        self.setup_environment()
    
    def setup_environment(self):
        """Optimiere TensorFlow für maximale Performance"""
        print("⚙️  Optimiere Umgebung...")
        
        # GPU-Konfiguration
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print(f"   🎮 GPU aktiviert: {len(gpus)} GPU(s)")
            except RuntimeError as e:
                print(f"   ⚠️  GPU-Konfiguration: {e}")
        
        # CPU-Optimierung
        tf.config.threading.set_intra_op_parallelism_threads(0)
        tf.config.threading.set_inter_op_parallelism_threads(0)
        
        print(f"   💻 CPU-Kerne: {os.cpu_count()}")
        print("   ✅ Umgebung optimiert")
    
    def load_and_engineer_ultimate_features(self):
        """Ultimate Feature Engineering mit 100+ Features"""
        print("\n📊 ULTIMATE FEATURE ENGINEERING")
        print("-" * 50)
        
        # Daten laden
        df = pd.read_csv(CONFIG['data_file'])
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        print(f"📈 Rohdaten: {len(df)} Punkte von {df.index[0]} bis {df.index[-1]}")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
        
        # Basis OHLCV
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # === 1. PREIS-BASIERTE FEATURES ===
        print("   🔧 Erstelle Preis-Features...")
        
        # Returns und Log-Returns
        features['returns'] = df['close'].pct_change()
        features['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        features['cumulative_returns'] = (1 + features['returns']).cumprod()
        
        # Preis-Ratios
        features['hl_ratio'] = df['high'] / df['low']
        features['co_ratio'] = df['close'] / df['open']
        features['hc_ratio'] = df['high'] / df['close']
        features['lc_ratio'] = df['low'] / df['close']
        
        # Candlestick Body und Shadows
        features['body'] = abs(df['close'] - df['open'])
        features['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
        features['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
        features['body_ratio'] = features['body'] / (df['high'] - df['low'])
        
        # === 2. MOVING AVERAGES SYSTEM ===
        print("   📈 Erstelle Moving Average System...")
        
        ma_periods = [3, 5, 8, 13, 21, 34, 55, 89, 144, 233]  # Fibonacci
        for period in ma_periods:
            if period <= len(df):
                # Simple MA
                features[f'sma_{period}'] = df['close'].rolling(period).mean()
                # Exponential MA
                features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
                # Weighted MA
                weights = np.arange(1, period + 1)
                features[f'wma_{period}'] = df['close'].rolling(period).apply(
                    lambda x: np.dot(x, weights) / weights.sum(), raw=True
                )
                
                # MA Ratios und Crossovers
                features[f'price_sma_{period}_ratio'] = df['close'] / features[f'sma_{period}']
                features[f'price_ema_{period}_ratio'] = df['close'] / features[f'ema_{period}']
                
                if period > 3:
                    features[f'sma_cross_{period}'] = (features['sma_3'] > features[f'sma_{period}']).astype(int)
                    features[f'ema_cross_{period}'] = (features['ema_3'] > features[f'ema_{period}']).astype(int)
        
        # === 3. MOMENTUM INDIKATOREN ===
        print("   ⚡ Erstelle Momentum-Indikatoren...")
        
        # RSI Familie
        for period in [7, 14, 21, 28]:
            delta = df['close'].diff()
            gain = delta.where(delta > 0, 0).rolling(period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            features[f'rsi_{period}'] = rsi
            features[f'rsi_{period}_overbought'] = (rsi > 70).astype(int)
            features[f'rsi_{period}_oversold'] = (rsi < 30).astype(int)
            features[f'rsi_{period}_slope'] = rsi.diff()
            features[f'rsi_{period}_acceleration'] = rsi.diff().diff()
        
        # MACD Familie
        for fast, slow, signal in [(12, 26, 9), (5, 35, 5), (19, 39, 9)]:
            ema_fast = df['close'].ewm(span=fast).mean()
            ema_slow = df['close'].ewm(span=slow).mean()
            macd = ema_fast - ema_slow
            macd_signal = macd.ewm(span=signal).mean()
            macd_histogram = macd - macd_signal
            
            features[f'macd_{fast}_{slow}'] = macd
            features[f'macd_signal_{fast}_{slow}'] = macd_signal
            features[f'macd_histogram_{fast}_{slow}'] = macd_histogram
            features[f'macd_cross_{fast}_{slow}'] = (macd > macd_signal).astype(int)
        
        # Stochastic Oscillator
        for period in [14, 21, 28]:
            low_min = df['low'].rolling(period).min()
            high_max = df['high'].rolling(period).max()
            stoch_k = 100 * ((df['close'] - low_min) / (high_max - low_min))
            stoch_d = stoch_k.rolling(3).mean()
            
            features[f'stoch_k_{period}'] = stoch_k
            features[f'stoch_d_{period}'] = stoch_d
            features[f'stoch_cross_{period}'] = (stoch_k > stoch_d).astype(int)
        
        # Williams %R
        for period in [14, 21]:
            high_max = df['high'].rolling(period).max()
            low_min = df['low'].rolling(period).min()
            features[f'williams_r_{period}'] = -100 * ((high_max - df['close']) / (high_max - low_min))
        
        # === 4. VOLATILITÄT INDIKATOREN ===
        print("   📊 Erstelle Volatilitäts-Indikatoren...")
        
        # Bollinger Bands
        for period in [20, 50]:
            sma = df['close'].rolling(period).mean()
            std = df['close'].rolling(period).std()
            
            features[f'bb_upper_{period}'] = sma + (std * 2)
            features[f'bb_lower_{period}'] = sma - (std * 2)
            features[f'bb_middle_{period}'] = sma
            features[f'bb_width_{period}'] = (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}']) / sma
            features[f'bb_position_{period}'] = (df['close'] - features[f'bb_lower_{period}']) / (features[f'bb_upper_{period}'] - features[f'bb_lower_{period}'])
            features[f'bb_squeeze_{period}'] = (features[f'bb_width_{period}'] < features[f'bb_width_{period}'].rolling(20).mean()).astype(int)
        
        # ATR (Average True Range)
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        
        for period in [7, 14, 21, 28]:
            atr = true_range.rolling(period).mean()
            features[f'atr_{period}'] = atr
            features[f'atr_percent_{period}'] = atr / df['close'] * 100
            features[f'atr_ratio_{period}'] = atr / atr.rolling(50).mean()
        
        # Realized Volatility
        for period in [5, 10, 20, 30]:
            features[f'realized_vol_{period}'] = features['log_returns'].rolling(period).std() * np.sqrt(24)  # Annualized
            features[f'vol_rank_{period}'] = features[f'realized_vol_{period}'].rolling(252).rank(pct=True)
        
        # === 5. VOLUMEN INDIKATOREN ===
        print("   📦 Erstelle Volumen-Indikatoren...")
        
        # OBV (On-Balance Volume)
        obv = [0]
        for i in range(1, len(df)):
            if df['close'].iloc[i] > df['close'].iloc[i-1]:
                obv.append(obv[-1] + df['volume'].iloc[i])
            elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                obv.append(obv[-1] - df['volume'].iloc[i])
            else:
                obv.append(obv[-1])
        
        features['obv'] = obv
        features['obv_ema'] = features['obv'].ewm(span=20).mean()
        features['obv_slope'] = features['obv'].diff()
        
        # Volume Profile
        for period in [10, 20, 50]:
            vol_sma = df['volume'].rolling(period).mean()
            features[f'volume_sma_{period}'] = vol_sma
            features[f'volume_ratio_{period}'] = df['volume'] / vol_sma
            features[f'volume_spike_{period}'] = (features[f'volume_ratio_{period}'] > 2).astype(int)
            features[f'volume_trend_{period}'] = (df['volume'] > vol_sma).astype(int)
        
        # VWAP (Volume Weighted Average Price)
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        features['vwap'] = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        features['vwap_ratio'] = df['close'] / features['vwap']
        
        # === 6. TREND INDIKATOREN ===
        print("   📈 Erstelle Trend-Indikatoren...")
        
        # ADX (Average Directional Index)
        for period in [14, 21]:
            high_diff = df['high'].diff()
            low_diff = df['low'].diff()
            
            plus_dm = np.where((high_diff > low_diff) & (high_diff > 0), high_diff, 0)
            minus_dm = np.where((low_diff > high_diff) & (low_diff > 0), low_diff, 0)
            
            atr = true_range.rolling(period).mean()
            plus_di = 100 * pd.Series(plus_dm).rolling(period).mean() / atr
            minus_di = 100 * pd.Series(minus_dm).rolling(period).mean() / atr
            
            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
            adx = dx.rolling(period).mean()
            
            features[f'adx_{period}'] = adx
            features[f'plus_di_{period}'] = plus_di
            features[f'minus_di_{period}'] = minus_di
            features[f'trend_strength_{period}'] = (adx > 25).astype(int)
        
        # === 7. ZYKLISCHE UND ZEIT-FEATURES ===
        print("   🕐 Erstelle Zeit-Features...")
        
        # Basis Zeit-Features
        features['hour'] = df.index.hour
        features['day_of_week'] = df.index.dayofweek
        features['day_of_month'] = df.index.day
        features['month'] = df.index.month
        features['quarter'] = df.index.quarter
        features['year'] = df.index.year
        
        # Trigonometrische Transformationen
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
        features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
        features['month_sin'] = np.sin(2 * np.pi * features['month'] / 12)
        features['month_cos'] = np.cos(2 * np.pi * features['month'] / 12)
        
        # Markt-Sessions
        features['asian_session'] = ((features['hour'] >= 0) & (features['hour'] < 8)).astype(int)
        features['european_session'] = ((features['hour'] >= 8) & (features['hour'] < 16)).astype(int)
        features['american_session'] = ((features['hour'] >= 16) & (features['hour'] < 24)).astype(int)
        
        # Wochenende
        features['weekend'] = (features['day_of_week'] >= 5).astype(int)
        
        # === 8. STATISTISCHE FEATURES ===
        print("   📊 Erstelle statistische Features...")
        
        # Rolling Statistics
        for period in [5, 10, 20, 50]:
            # Skewness und Kurtosis
            features[f'skew_{period}'] = features['returns'].rolling(period).skew()
            features[f'kurt_{period}'] = features['returns'].rolling(period).kurt()
            
            # Quantile
            features[f'q25_{period}'] = df['close'].rolling(period).quantile(0.25)
            features[f'q75_{period}'] = df['close'].rolling(period).quantile(0.75)
            features[f'iqr_{period}'] = features[f'q75_{period}'] - features[f'q25_{period}']
            
            # Z-Score
            features[f'zscore_{period}'] = (df['close'] - df['close'].rolling(period).mean()) / df['close'].rolling(period).std()
        
        # === 9. FIBONACCI UND SUPPORT/RESISTANCE ===
        print("   🔢 Erstelle Fibonacci-Levels...")
        
        # Fibonacci Retracements
        for period in [50, 100, 200]:
            if period <= len(df):
                high_period = df['high'].rolling(period).max()
                low_period = df['low'].rolling(period).min()
                
                fib_levels = [0.236, 0.382, 0.5, 0.618, 0.786]
                for level in fib_levels:
                    fib_price = low_period + (high_period - low_period) * level
                    features[f'fib_{int(level*1000)}_{period}'] = fib_price
                    features[f'fib_distance_{int(level*1000)}_{period}'] = abs(df['close'] - fib_price) / df['close']
        
        # === 10. CLEANUP UND FINAL ===
        print("   🧹 Bereinige Features...")
        
        # Entferne NaN-Werte
        features = features.dropna()
        
        # Feature-Statistiken
        n_features = len(features.columns)
        n_samples = len(features)
        
        print(f"   ✅ {n_features} Ultimate Features erstellt")
        print(f"   📊 {n_samples} saubere Datenpunkte")
        print(f"   📈 Feature-Dichte: {n_features/n_samples*100:.2f}%")
        
        return features
    
    def create_sequences(self, data, target, sequence_length):
        """Erstelle Sequenzen für Zeitreihen-Modelle"""
        X, y = [], []
        for i in range(sequence_length, len(data)):
            X.append(data[i-sequence_length:i])
            y.append(target[i])
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)
    
    def prepare_data(self, features):
        """Bereite Daten für Training vor"""
        print("\n🔄 DATENAUFBEREITUNG")
        print("-" * 30)
        
        # Features und Target trennen
        X = features.drop('close', axis=1)
        y = features['close'].values
        
        print(f"📊 Features: {X.shape[1]}")
        print(f"📊 Samples: {len(y)}")
        
        # Skalierung
        print("   🔧 Skaliere Features...")
        feature_scaler = RobustScaler()  # Robust gegen Outliers
        target_scaler = StandardScaler()
        
        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        
        # Sequenzen erstellen
        print(f"   📦 Erstelle Sequenzen (Länge: {CONFIG['sequence_length']})...")
        X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, CONFIG['sequence_length'])
        
        print(f"   ✅ {len(X_seq)} Sequenzen erstellt")
        print(f"   📐 Sequenz-Shape: {X_seq.shape}")
        
        # Speichere Scaler
        self.scalers['feature'] = feature_scaler
        self.scalers['target'] = target_scaler
        
        return X_seq, y_seq, X.columns.tolist()
    
    def split_data(self, X, y):
        """Intelligente Datenaufteilung für Zeitreihen"""
        print("\n✂️  DATENAUFTEILUNG")
        print("-" * 25)
        
        # Walk-Forward Validation Setup
        total_size = len(X)
        train_size = int(total_size * 0.7)
        val_size = int(total_size * 0.15)
        test_size = total_size - train_size - val_size
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        X_val = X[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        X_test = X[train_size+val_size:]
        y_test = y[train_size+val_size:]
        
        print(f"📊 Training: {len(X_train)} ({len(X_train)/total_size*100:.1f}%)")
        print(f"📊 Validation: {len(X_val)} ({len(X_val)/total_size*100:.1f}%)")
        print(f"📊 Test: {len(X_test)} ({len(X_test)/total_size*100:.1f}%)")
        
        return (X_train, y_train), (X_val, y_val), (X_test, y_test)

    def build_transformer_model(self, input_shape, feature_names):
        """Transformer-basiertes Modell mit Attention"""
        print("🤖 Baue Transformer-Modell...")

        # Input Layer
        inputs = Input(shape=input_shape, name='sequence_input')

        # Multi-Head Attention Layers
        attention_1 = MultiHeadAttention(
            num_heads=8,
            key_dim=64,
            dropout=0.1,
            name='attention_1'
        )(inputs, inputs)
        attention_1 = LayerNormalization()(attention_1)
        attention_1 = Add()([inputs, attention_1])  # Residual connection

        attention_2 = MultiHeadAttention(
            num_heads=4,
            key_dim=32,
            dropout=0.1,
            name='attention_2'
        )(attention_1, attention_1)
        attention_2 = LayerNormalization()(attention_2)
        attention_2 = Add()([attention_1, attention_2])  # Residual connection

        # Global Average Pooling
        pooled = tf.keras.layers.GlobalAveragePooling1D()(attention_2)

        # Dense Layers
        dense_1 = Dense(256, activation='relu', name='dense_1')(pooled)
        dense_1 = BatchNormalization()(dense_1)
        dense_1 = Dropout(0.3)(dense_1)

        dense_2 = Dense(128, activation='relu', name='dense_2')(dense_1)
        dense_2 = BatchNormalization()(dense_2)
        dense_2 = Dropout(0.2)(dense_2)

        dense_3 = Dense(64, activation='relu', name='dense_3')(dense_2)
        dense_3 = Dropout(0.1)(dense_3)

        # Output
        outputs = Dense(1, name='price_output')(dense_3)

        model = Model(inputs=inputs, outputs=outputs, name='TransformerPredictor')

        # Kompiliere mit optimiertem Optimizer
        optimizer = Adam(learning_rate=0.001, beta_1=0.9, beta_2=0.999, epsilon=1e-7)
        model.compile(
            optimizer=optimizer,
            loss='huber',
            metrics=['mae', 'mse']
        )

        print(f"   ✅ Transformer: {model.count_params():,} Parameter")
        return model

    def build_lstm_attention_model(self, input_shape, feature_names):
        """LSTM mit Attention Mechanism"""
        print("🧠 Baue LSTM-Attention-Modell...")

        inputs = Input(shape=input_shape, name='sequence_input')

        # Bidirectional LSTM Layers
        lstm_1 = Bidirectional(
            LSTM(128, return_sequences=True, dropout=0.2, recurrent_dropout=0.2),
            name='bidirectional_lstm_1'
        )(inputs)
        lstm_1 = BatchNormalization()(lstm_1)

        lstm_2 = Bidirectional(
            LSTM(64, return_sequences=True, dropout=0.2, recurrent_dropout=0.2),
            name='bidirectional_lstm_2'
        )(lstm_1)
        lstm_2 = BatchNormalization()(lstm_2)

        # Attention Layer
        attention = MultiHeadAttention(
            num_heads=4,
            key_dim=32,
            dropout=0.1,
            name='lstm_attention'
        )(lstm_2, lstm_2)
        attention = LayerNormalization()(attention)

        # Combine LSTM and Attention
        combined = Add()([lstm_2, attention])

        # Global pooling
        pooled = tf.keras.layers.GlobalAveragePooling1D()(combined)

        # Dense layers
        dense_1 = Dense(128, activation='relu', name='dense_1')(pooled)
        dense_1 = BatchNormalization()(dense_1)
        dense_1 = Dropout(0.3)(dense_1)

        dense_2 = Dense(64, activation='relu', name='dense_2')(dense_1)
        dense_2 = Dropout(0.2)(dense_2)

        outputs = Dense(1, name='price_output')(dense_2)

        model = Model(inputs=inputs, outputs=outputs, name='LSTMAttentionPredictor')

        optimizer = Adam(learning_rate=0.0005)
        model.compile(
            optimizer=optimizer,
            loss='huber',
            metrics=['mae', 'mse']
        )

        print(f"   ✅ LSTM-Attention: {model.count_params():,} Parameter")
        return model

    def build_gru_bidirectional_model(self, input_shape, feature_names):
        """Bidirectional GRU mit Regularization"""
        print("⚡ Baue GRU-Bidirectional-Modell...")

        model = Sequential(name='GRUBidirectionalPredictor')

        # Bidirectional GRU Layers
        model.add(Bidirectional(
            GRU(128, return_sequences=True, dropout=0.2, recurrent_dropout=0.2),
            input_shape=input_shape,
            name='bidirectional_gru_1'
        ))
        model.add(BatchNormalization())

        model.add(Bidirectional(
            GRU(64, return_sequences=True, dropout=0.2, recurrent_dropout=0.2),
            name='bidirectional_gru_2'
        ))
        model.add(BatchNormalization())

        model.add(Bidirectional(
            GRU(32, return_sequences=False, dropout=0.2, recurrent_dropout=0.2),
            name='bidirectional_gru_3'
        ))
        model.add(BatchNormalization())

        # Dense layers with regularization
        model.add(Dense(128, activation='relu',
                       kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4),
                       name='dense_1'))
        model.add(BatchNormalization())
        model.add(Dropout(0.3))

        model.add(Dense(64, activation='relu',
                       kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4),
                       name='dense_2'))
        model.add(Dropout(0.2))

        model.add(Dense(1, name='price_output'))

        optimizer = RMSprop(learning_rate=0.001)
        model.compile(
            optimizer=optimizer,
            loss='huber',
            metrics=['mae', 'mse']
        )

        print(f"   ✅ GRU-Bidirectional: {model.count_params():,} Parameter")
        return model

    def build_hybrid_ensemble_model(self, input_shape, feature_names):
        """Hybrid-Modell mit mehreren Architekturen"""
        print("🔥 Baue Hybrid-Ensemble-Modell...")

        inputs = Input(shape=input_shape, name='sequence_input')

        # LSTM Branch
        lstm_branch = LSTM(64, return_sequences=True, dropout=0.2, name='lstm_branch')(inputs)
        lstm_branch = BatchNormalization()(lstm_branch)
        lstm_branch = LSTM(32, return_sequences=False, dropout=0.2)(lstm_branch)
        lstm_branch = Dense(32, activation='relu')(lstm_branch)

        # GRU Branch
        gru_branch = GRU(64, return_sequences=True, dropout=0.2, name='gru_branch')(inputs)
        gru_branch = BatchNormalization()(gru_branch)
        gru_branch = GRU(32, return_sequences=False, dropout=0.2)(gru_branch)
        gru_branch = Dense(32, activation='relu')(gru_branch)

        # Attention Branch
        attention_branch = MultiHeadAttention(
            num_heads=4, key_dim=32, dropout=0.1, name='attention_branch'
        )(inputs, inputs)
        attention_branch = LayerNormalization()(attention_branch)
        attention_branch = tf.keras.layers.GlobalAveragePooling1D()(attention_branch)
        attention_branch = Dense(32, activation='relu')(attention_branch)

        # Combine all branches
        combined = Concatenate(name='combine_branches')([lstm_branch, gru_branch, attention_branch])

        # Final dense layers
        dense_1 = Dense(128, activation='relu', name='final_dense_1')(combined)
        dense_1 = BatchNormalization()(dense_1)
        dense_1 = Dropout(0.3)(dense_1)

        dense_2 = Dense(64, activation='relu', name='final_dense_2')(dense_1)
        dense_2 = Dropout(0.2)(dense_2)

        outputs = Dense(1, name='price_output')(dense_2)

        model = Model(inputs=inputs, outputs=outputs, name='HybridEnsemblePredictor')

        optimizer = Adam(learning_rate=0.0008)
        model.compile(
            optimizer=optimizer,
            loss='huber',
            metrics=['mae', 'mse']
        )

        print(f"   ✅ Hybrid-Ensemble: {model.count_params():,} Parameter")
        return model

    def train_model(self, model, X_train, y_train, X_val, y_val, model_name):
        """Trainiere ein einzelnes Modell mit optimierten Callbacks"""
        print(f"\n🎯 Trainiere {model_name}...")

        # Optimierte Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=25,
                restore_best_weights=True,
                verbose=1,
                mode='min'
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=10,
                min_lr=1e-7,
                verbose=1,
                mode='min'
            ),
            ModelCheckpoint(
                filepath=f'models/best_{model_name.lower()}.h5',
                monitor='val_loss',
                save_best_only=True,
                verbose=1,
                mode='min'
            )
        ]

        # Erstelle models Verzeichnis
        os.makedirs('models', exist_ok=True)

        start_time = time.time()

        # Training
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=150,
            batch_size=32,
            callbacks=callbacks,
            verbose=1
        )

        training_time = time.time() - start_time

        print(f"   ✅ {model_name} Training abgeschlossen: {training_time:.1f}s")

        return model, history, training_time

    def evaluate_model(self, model, X_test, y_test, model_name):
        """Umfassende Modell-Evaluation"""
        print(f"\n📊 Evaluiere {model_name}...")

        # Vorhersagen
        y_pred = model.predict(X_test, verbose=0)

        # Skalierung rückgängig machen
        y_test_orig = self.scalers['target'].inverse_transform(y_test.reshape(-1, 1)).flatten()
        y_pred_orig = self.scalers['target'].inverse_transform(y_pred).flatten()

        # Metriken berechnen
        r2 = r2_score(y_test_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
        mae = mean_absolute_error(y_test_orig, y_pred_orig)
        mape = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig)) * 100

        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(y_pred_orig) > 0
            direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            direction_acc = 0

        # Zusätzliche Metriken
        max_error = np.max(np.abs(y_test_orig - y_pred_orig))
        median_error = np.median(np.abs(y_test_orig - y_pred_orig))

        # Accuracy Bands
        accuracy_1pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.01) * 100
        accuracy_5pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.05) * 100
        accuracy_10pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.10) * 100

        results = {
            'model_name': model_name,
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'mape': mape,
            'direction_accuracy': direction_acc,
            'max_error': max_error,
            'median_error': median_error,
            'accuracy_1pct': accuracy_1pct,
            'accuracy_5pct': accuracy_5pct,
            'accuracy_10pct': accuracy_10pct,
            'y_test_orig': y_test_orig,
            'y_pred_orig': y_pred_orig
        }

        print(f"   📈 R²: {r2:.4f} ({r2*100:.1f}%)")
        print(f"   💰 RMSE: ${rmse:.2f}")
        print(f"   📊 MAPE: {mape:.2f}%")
        print(f"   🎯 Direction Acc: {direction_acc:.1f}%")
        print(f"   ✅ 5% Accuracy: {accuracy_5pct:.1f}%")

        return results

    def create_ultimate_ensemble(self, models_results, X_test, y_test):
        """Erstelle Ultimate Ensemble aus allen Modellen"""
        print(f"\n🏆 ULTIMATE ENSEMBLE aus {len(models_results)} Modellen")
        print("-" * 50)

        # Sammle Vorhersagen und Gewichte
        predictions = []
        weights = []
        model_names = []

        for result in models_results:
            predictions.append(result['y_pred_orig'])
            # Gewicht basierend auf R² und Direction Accuracy
            weight = (result['r2'] * 0.7 + result['direction_accuracy']/100 * 0.3)
            weights.append(max(0.1, weight))  # Mindestgewicht
            model_names.append(result['model_name'])

            print(f"   {result['model_name']}: R²={result['r2']:.4f}, Gewicht={weight:.3f}")

        # Normalisiere Gewichte
        weights = np.array(weights)
        weights = weights / np.sum(weights)

        # Gewichtete Ensemble-Vorhersage
        ensemble_pred = np.average(predictions, axis=0, weights=weights)

        # Ensemble-Evaluation
        y_test_orig = models_results[0]['y_test_orig']  # Alle haben die gleichen Test-Daten

        ensemble_r2 = r2_score(y_test_orig, ensemble_pred)
        ensemble_rmse = np.sqrt(mean_squared_error(y_test_orig, ensemble_pred))
        ensemble_mae = mean_absolute_error(y_test_orig, ensemble_pred)
        ensemble_mape = np.mean(np.abs((y_test_orig - ensemble_pred) / y_test_orig)) * 100

        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(ensemble_pred) > 0
            ensemble_direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            ensemble_direction_acc = 0

        # Accuracy Bands
        ensemble_accuracy_5pct = np.mean(np.abs((y_test_orig - ensemble_pred) / y_test_orig) < 0.05) * 100

        ensemble_result = {
            'model_name': 'Ultimate_Ensemble',
            'r2': ensemble_r2,
            'rmse': ensemble_rmse,
            'mae': ensemble_mae,
            'mape': ensemble_mape,
            'direction_accuracy': ensemble_direction_acc,
            'accuracy_5pct': ensemble_accuracy_5pct,
            'weights': weights,
            'model_names': model_names,
            'y_test_orig': y_test_orig,
            'y_pred_orig': ensemble_pred
        }

        print(f"\n🎯 ENSEMBLE PERFORMANCE:")
        print(f"   📈 R²: {ensemble_r2:.4f} ({ensemble_r2*100:.1f}%)")
        print(f"   💰 RMSE: ${ensemble_rmse:.2f}")
        print(f"   📊 MAPE: {ensemble_mape:.2f}%")
        print(f"   🎯 Direction Acc: {ensemble_direction_acc:.1f}%")
        print(f"   ✅ 5% Accuracy: {ensemble_accuracy_5pct:.1f}%")

        return ensemble_result

    def plot_ultimate_results(self, models_results, ensemble_result):
        """Ultimate Visualisierung aller Ergebnisse"""
        print(f"\n📈 ULTIMATE VISUALISIERUNG")
        print("-" * 30)

        # Erstelle große Figure
        fig = plt.figure(figsize=(24, 18))
        fig.suptitle('🚀 ULTIMATE BITCOIN PREDICTION SYSTEM - RESULTS', fontsize=20, fontweight='bold')

        # 1. Performance Vergleich
        ax1 = plt.subplot(3, 4, 1)
        all_results = models_results + [ensemble_result]
        names = [r['model_name'] for r in all_results]
        r2_scores = [r['r2'] for r in all_results]

        colors = plt.cm.viridis(np.linspace(0, 1, len(names)))
        colors[-1] = 'gold'  # Ensemble in Gold

        bars = ax1.bar(names, r2_scores, color=colors)
        ax1.set_title('R² Score Vergleich', fontsize=14, fontweight='bold')
        ax1.set_ylabel('R² Score')
        ax1.set_ylim(0, 1)
        plt.xticks(rotation=45)
        ax1.grid(True, alpha=0.3)

        # Beste markieren
        best_idx = np.argmax(r2_scores)
        bars[best_idx].set_edgecolor('red')
        bars[best_idx].set_linewidth(3)

        # 2. RMSE Vergleich
        ax2 = plt.subplot(3, 4, 2)
        rmse_scores = [r['rmse'] for r in all_results]
        ax2.bar(names, rmse_scores, color='lightcoral')
        ax2.set_title('RMSE Vergleich', fontsize=14, fontweight='bold')
        ax2.set_ylabel('RMSE ($)')
        plt.xticks(rotation=45)
        ax2.grid(True, alpha=0.3)

        # 3. Direction Accuracy
        ax3 = plt.subplot(3, 4, 3)
        direction_accs = [r['direction_accuracy'] for r in all_results]
        ax3.bar(names, direction_accs, color='lightgreen')
        ax3.set_title('Richtungsgenauigkeit', fontsize=14, fontweight='bold')
        ax3.set_ylabel('Genauigkeit (%)')
        ax3.set_ylim(0, 100)
        plt.xticks(rotation=45)
        ax3.grid(True, alpha=0.3)

        # 4. MAPE Vergleich
        ax4 = plt.subplot(3, 4, 4)
        mape_scores = [r['mape'] for r in all_results]
        ax4.bar(names, mape_scores, color='orange')
        ax4.set_title('MAPE Vergleich', fontsize=14, fontweight='bold')
        ax4.set_ylabel('MAPE (%)')
        plt.xticks(rotation=45)
        ax4.grid(True, alpha=0.3)

        # 5. Beste Vorhersage (Ensemble)
        ax5 = plt.subplot(3, 4, (5, 6))
        y_test = ensemble_result['y_test_orig'][:100]  # Erste 100 Punkte
        y_pred = ensemble_result['y_pred_orig'][:100]

        ax5.plot(y_test, 'g-', label='Actual', linewidth=2, alpha=0.8)
        ax5.plot(y_pred, 'r--', label='Ultimate Ensemble', linewidth=2)
        ax5.set_title('Ultimate Ensemble Vorhersage', fontsize=14, fontweight='bold')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

        # 6. Scatter Plot
        ax6 = plt.subplot(3, 4, 7)
        ax6.scatter(ensemble_result['y_test_orig'], ensemble_result['y_pred_orig'],
                   alpha=0.6, c='blue', s=20)
        min_val = min(ensemble_result['y_test_orig'].min(), ensemble_result['y_pred_orig'].min())
        max_val = max(ensemble_result['y_test_orig'].max(), ensemble_result['y_pred_orig'].max())
        ax6.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
        ax6.set_title('Scatter Plot - Ensemble', fontsize=14, fontweight='bold')
        ax6.set_xlabel('Actual')
        ax6.set_ylabel('Predicted')
        ax6.grid(True, alpha=0.3)

        # 7. Error Distribution
        ax7 = plt.subplot(3, 4, 8)
        residuals = ensemble_result['y_test_orig'] - ensemble_result['y_pred_orig']
        ax7.hist(residuals, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax7.set_title('Error Distribution', fontsize=14, fontweight='bold')
        ax7.set_xlabel('Prediction Error')
        ax7.set_ylabel('Frequency')
        ax7.grid(True, alpha=0.3)

        # 8. Ensemble Weights
        ax8 = plt.subplot(3, 4, 9)
        model_names = ensemble_result['model_names']
        weights = ensemble_result['weights']
        ax8.pie(weights, labels=model_names, autopct='%1.1f%%', startangle=90)
        ax8.set_title('Ensemble Gewichte', fontsize=14, fontweight='bold')

        # 9. Accuracy Comparison
        ax9 = plt.subplot(3, 4, 10)
        accuracy_5pct = [r['accuracy_5pct'] for r in all_results]
        ax9.bar(names, accuracy_5pct, color='purple')
        ax9.set_title('5% Accuracy Band', fontsize=14, fontweight='bold')
        ax9.set_ylabel('Accuracy (%)')
        ax9.set_ylim(0, 100)
        plt.xticks(rotation=45)
        ax9.grid(True, alpha=0.3)

        # 10. Performance Radar Chart
        ax10 = plt.subplot(3, 4, 11, projection='polar')

        # Normalisiere Metriken für Radar Chart
        metrics = ['R²', 'Direction Acc', '5% Accuracy', 'Low MAPE', 'Low RMSE']
        ensemble_values = [
            ensemble_result['r2'],
            ensemble_result['direction_accuracy'] / 100,
            ensemble_result['accuracy_5pct'] / 100,
            1 - min(ensemble_result['mape'] / 100, 1),  # Invertiert für bessere Darstellung
            1 - min(ensemble_result['rmse'] / 10000, 1)  # Normalisiert und invertiert
        ]

        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        ensemble_values += ensemble_values[:1]  # Schließe den Kreis
        angles += angles[:1]

        ax10.plot(angles, ensemble_values, 'o-', linewidth=2, label='Ultimate Ensemble')
        ax10.fill(angles, ensemble_values, alpha=0.25)
        ax10.set_xticks(angles[:-1])
        ax10.set_xticklabels(metrics)
        ax10.set_ylim(0, 1)
        ax10.set_title('Performance Radar', fontsize=14, fontweight='bold')
        ax10.legend()

        # 11. Summary Statistics
        ax11 = plt.subplot(3, 4, 12)
        ax11.axis('off')

        best_model = max(all_results, key=lambda x: x['r2'])

        summary_text = f"""
🏆 ULTIMATE BITCOIN PREDICTION RESULTS

🎯 BESTE PERFORMANCE:
   Model: {best_model['model_name']}
   R²: {best_model['r2']:.4f} ({best_model['r2']*100:.1f}%)
   RMSE: ${best_model['rmse']:.2f}
   Direction Acc: {best_model['direction_accuracy']:.1f}%

🚀 ENSEMBLE PERFORMANCE:
   R²: {ensemble_result['r2']:.4f} ({ensemble_result['r2']*100:.1f}%)
   RMSE: ${ensemble_result['rmse']:.2f}
   Direction Acc: {ensemble_result['direction_accuracy']:.1f}%
   5% Accuracy: {ensemble_result['accuracy_5pct']:.1f}%

📊 SYSTEM STATS:
   Modelle trainiert: {len(models_results)}
   Features verwendet: {len(self.scalers)}
   Ziel erreicht: {'✅ JA' if ensemble_result['r2'] >= CONFIG['target_accuracy'] else '❌ NEIN'}
        """

        ax11.text(0.1, 0.5, summary_text, fontsize=12, verticalalignment='center',
                 bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()
        plt.show()

        print("   ✅ Visualisierung abgeschlossen")

def main():
    """ULTIMATE HAUPTFUNKTION"""
    print("🚀 STARTE ULTIMATE BITCOIN PREDICTION SYSTEM")
    print("=" * 70)

    start_time = time.time()

    # System initialisieren
    predictor = UltimateBitcoinPredictor()

    # Features erstellen
    features = predictor.load_and_engineer_ultimate_features()

    # Daten vorbereiten
    X, y, feature_names = predictor.prepare_data(features)

    # Daten aufteilen
    (X_train, y_train), (X_val, y_val), (X_test, y_test) = predictor.split_data(X, y)

    print(f"\n✅ DATENAUFBEREITUNG ABGESCHLOSSEN!")
    print(f"   📊 {len(feature_names)} Features")
    print(f"   📦 {len(X)} Sequenzen")

    # === MODELL-TRAINING ===
    print(f"\n🤖 ULTIMATE MODELL-TRAINING")
    print("=" * 40)

    input_shape = (X_train.shape[1], X_train.shape[2])
    models_results = []

    # 1. Transformer Model
    try:
        transformer_model = predictor.build_transformer_model(input_shape, feature_names)
        transformer_model, _, _ = predictor.train_model(
            transformer_model, X_train, y_train, X_val, y_val, 'Transformer'
        )
        transformer_results = predictor.evaluate_model(
            transformer_model, X_test, y_test, 'Transformer'
        )
        models_results.append(transformer_results)
        predictor.models['transformer'] = transformer_model
    except Exception as e:
        print(f"❌ Transformer Fehler: {e}")

    # 2. LSTM-Attention Model
    try:
        lstm_attention_model = predictor.build_lstm_attention_model(input_shape, feature_names)
        lstm_attention_model, _, _ = predictor.train_model(
            lstm_attention_model, X_train, y_train, X_val, y_val, 'LSTM_Attention'
        )
        lstm_attention_results = predictor.evaluate_model(
            lstm_attention_model, X_test, y_test, 'LSTM_Attention'
        )
        models_results.append(lstm_attention_results)
        predictor.models['lstm_attention'] = lstm_attention_model
    except Exception as e:
        print(f"❌ LSTM-Attention Fehler: {e}")

    # 3. GRU-Bidirectional Model
    try:
        gru_model = predictor.build_gru_bidirectional_model(input_shape, feature_names)
        gru_model, _, _ = predictor.train_model(
            gru_model, X_train, y_train, X_val, y_val, 'GRU_Bidirectional'
        )
        gru_results = predictor.evaluate_model(
            gru_model, X_test, y_test, 'GRU_Bidirectional'
        )
        models_results.append(gru_results)
        predictor.models['gru_bidirectional'] = gru_model
    except Exception as e:
        print(f"❌ GRU-Bidirectional Fehler: {e}")

    # 4. Hybrid-Ensemble Model
    try:
        hybrid_model = predictor.build_hybrid_ensemble_model(input_shape, feature_names)
        hybrid_model, _, _ = predictor.train_model(
            hybrid_model, X_train, y_train, X_val, y_val, 'Hybrid_Ensemble'
        )
        hybrid_results = predictor.evaluate_model(
            hybrid_model, X_test, y_test, 'Hybrid_Ensemble'
        )
        models_results.append(hybrid_results)
        predictor.models['hybrid_ensemble'] = hybrid_model
    except Exception as e:
        print(f"❌ Hybrid-Ensemble Fehler: {e}")

    # === ULTIMATE ENSEMBLE ===
    if models_results:
        ensemble_result = predictor.create_ultimate_ensemble(models_results, X_test, y_test)

        # === FINALE ANALYSE ===
        print(f"\n🏆 FINALE ULTIMATE ANALYSE")
        print("=" * 50)

        total_time = time.time() - start_time

        # Sortiere Modelle nach Performance
        sorted_results = sorted(models_results, key=lambda x: x['r2'], reverse=True)

        print(f"\n📊 MODELL-RANKING:")
        for i, result in enumerate(sorted_results):
            print(f"   {i+1}. {result['model_name']}: {result['r2']*100:.1f}% R²")

        print(f"\n🎯 ULTIMATE ENSEMBLE: {ensemble_result['r2']*100:.1f}% R²")

        # Ziel-Check
        if ensemble_result['r2'] >= CONFIG['target_accuracy']:
            print(f"\n🎉🎉🎉 ZIEL ERREICHT! 🎉🎉🎉")
            print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
        else:
            print(f"\n💪 STARKE PERFORMANCE!")
            print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
            gap = (CONFIG['target_accuracy'] - ensemble_result['r2']) * 100
            print(f"Noch {gap:.1f} Prozentpunkte bis zum Ziel")

        print(f"\n⚡ Gesamtzeit: {total_time:.1f} Sekunden")
        print(f"🤖 Modelle trainiert: {len(models_results)}")
        print(f"📊 Features: {len(feature_names)}")

        # Visualisierung
        predictor.plot_ultimate_results(models_results, ensemble_result)

        print(f"\n✅ ULTIMATE BITCOIN PREDICTION SYSTEM ABGESCHLOSSEN!")

    else:
        print(f"\n❌ Keine Modelle erfolgreich trainiert!")

if __name__ == "__main__":
    main()
