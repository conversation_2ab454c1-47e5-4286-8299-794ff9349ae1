#!/usr/bin/env python3
"""
ULTIMATE BITCOIN PREDICTION MODEL - SPEED EDITION
Maximale CPU-Auslastung und Geschwindigkeit
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping
from tensorflow.keras.optimizers import Adam
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from datetime import datetime
import time
import os
import warnings
warnings.filterwarnings('ignore')

# MAXIMALE CPU-AUSLASTUNG AKTIVIEREN
print(f"🚀 Aktiviere alle {os.cpu_count()} CPU-Kerne...")
tf.config.threading.set_intra_op_parallelism_threads(0)
tf.config.threading.set_inter_op_parallelism_threads(0)
os.environ['OMP_NUM_THREADS'] = str(os.cpu_count())
os.environ['TF_NUM_INTEROP_THREADS'] = str(os.cpu_count())
os.environ['TF_NUM_INTRAOP_THREADS'] = str(os.cpu_count())

# SPEED-OPTIMIERTE Konfiguration
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.85,
    'look_back': 24,
    'future_steps': 24,
    'batch_size': 1024,     # Sehr große Batches
    'epochs': 30,
    'patience': 5,
    'monte_carlo_sims': 50
}

def load_and_prepare_data():
    """Ultra-schnelle Datenvorbereitung"""
    print("📊 Lade und bereite Daten vor...")
    
    # Lokale Daten laden
    df = pd.read_csv(CONFIG['data_file'])
    df['time'] = pd.to_datetime(df['time'])
    df.set_index('time', inplace=True)
    
    print(f"✅ {len(df)} Datenpunkte geladen")
    print(f"   Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
    
    # Nur die wichtigsten technischen Indikatoren (für Geschwindigkeit)
    print("📈 Berechne wichtigste Indikatoren...")
    
    # Moving Averages
    df['ema_9'] = df['close'].ewm(span=9).mean()
    df['ema_21'] = df['close'].ewm(span=21).mean()
    df['sma_20'] = df['close'].rolling(20).mean()
    
    # MACD
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    df['macd'] = ema_12 - ema_26
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    
    # RSI
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # Bollinger Bands Width
    sma_20 = df['close'].rolling(20).mean()
    std_20 = df['close'].rolling(20).std()
    df['bb_width'] = (std_20 * 2) / sma_20
    
    # Volatilität
    df['volatility'] = df['close'].pct_change().rolling(10).std()
    
    # Feature-Auswahl (nur die besten)
    features = ['open', 'high', 'low', 'close', 'volume', 
                'ema_9', 'ema_21', 'sma_20', 'macd', 'macd_signal', 
                'rsi', 'bb_width', 'volatility']
    
    return df[features].dropna()

def create_sequences(data, look_back):
    """Optimierte Sequenz-Erstellung"""
    X, y = [], []
    for i in range(len(data) - look_back):
        X.append(data[i:i + look_back])
        y.append(data[i + look_back, 3])  # close price index
    return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)

def build_speed_model(input_shape):
    """Ultra-schnelles Modell"""
    model = Sequential([
        LSTM(64, return_sequences=True, input_shape=input_shape),
        Dropout(0.2),
        LSTM(32, return_sequences=False),
        Dense(16, activation='relu'),
        Dense(1)
    ])
    
    # Schneller Optimizer
    optimizer = Adam(learning_rate=0.003)
    model.compile(optimizer=optimizer, loss='mse', metrics=['mae'])
    
    return model

def predict_future_fast(model, scaler, last_sequence, steps):
    """Schnelle Monte Carlo Prognose"""
    print(f"🔮 Erstelle {steps}h Prognose mit {CONFIG['monte_carlo_sims']} Simulationen...")
    
    all_predictions = []
    
    for sim in range(CONFIG['monte_carlo_sims']):
        current_seq = last_sequence.copy()
        sim_predictions = []
        
        for step in range(steps):
            pred = model.predict(current_seq.reshape(1, *current_seq.shape), verbose=0)[0, 0]
            sim_predictions.append(pred)
            
            # Neue Sequenz mit Rauschen
            new_row = current_seq[-1].copy()
            new_row[3] = pred  # close price
            
            # Andere Features mit kleinem Rauschen
            noise = np.random.normal(0, 0.01, len(new_row))
            new_row = new_row * (1 + noise)
            
            current_seq = np.vstack([current_seq[1:], new_row.reshape(1, -1)])
        
        all_predictions.append(sim_predictions)
    
    # Statistiken
    all_predictions = np.array(all_predictions)
    mean_pred = np.mean(all_predictions, axis=0)
    upper_95 = np.percentile(all_predictions, 97.5, axis=0)
    lower_95 = np.percentile(all_predictions, 2.5, axis=0)
    
    # Skalierung rückgängig machen (nur close price)
    dummy_data = np.zeros((len(mean_pred), scaler.n_features_in_))
    dummy_data[:, 3] = mean_pred
    mean_rescaled = scaler.inverse_transform(dummy_data)[:, 3]
    
    dummy_data[:, 3] = upper_95
    upper_rescaled = scaler.inverse_transform(dummy_data)[:, 3]
    
    dummy_data[:, 3] = lower_95
    lower_rescaled = scaler.inverse_transform(dummy_data)[:, 3]
    
    return {
        'mean': mean_rescaled,
        'upper_95': upper_rescaled,
        'lower_95': lower_rescaled
    }

def plot_speed_results(df, train_size, y_test, y_pred, future_pred, metrics, training_time):
    """Schnelle Visualisierung"""
    plt.figure(figsize=(16, 10))
    
    # Hauptchart
    plt.subplot(2, 2, 1)
    dates = df.index
    plt.plot(dates[:train_size], df['close'].iloc[:train_size], 'b-', label='Training', alpha=0.7)
    plt.plot(dates[train_size:train_size+len(y_test)], y_test, 'g-', label='Test Actual', linewidth=2)
    plt.plot(dates[train_size:train_size+len(y_pred)], y_pred, 'r--', label='Test Predicted', linewidth=2)
    
    # Zukunftsprognose
    future_dates = pd.date_range(start=dates[-1], periods=len(future_pred['mean'])+1, freq='1H')[1:]
    plt.plot(future_dates, future_pred['mean'], 'purple', linewidth=2, label='Future Prediction')
    plt.fill_between(future_dates, future_pred['lower_95'], future_pred['upper_95'],
                     color='purple', alpha=0.2, label='95% Confidence')
    
    plt.title('Bitcoin Price Prediction - ULTIMATE SPEED MODEL', fontsize=14, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Metriken
    plt.subplot(2, 2, 2)
    metrics_text = f"""
    ULTIMATE SPEED MODEL
    
    RMSE: ${metrics['rmse']:.2f}
    MAE: ${metrics['mae']:.2f}
    R²: {metrics['r2']:.4f}
    
    Training Time: {training_time:.1f}s
    CPU Cores Used: {os.cpu_count()}
    Data Points: {len(df):,}
    """
    
    plt.text(0.1, 0.5, metrics_text, fontsize=11, verticalalignment='center',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    plt.axis('off')
    
    # Zukunftsprognose Detail
    plt.subplot(2, 2, 3)
    hours = range(1, len(future_pred['mean']) + 1)
    plt.plot(hours, future_pred['mean'], 'purple', linewidth=2, marker='o')
    plt.fill_between(hours, future_pred['lower_95'], future_pred['upper_95'],
                     alpha=0.3, color='purple')
    plt.title('24-Hour Future Prediction')
    plt.xlabel('Hours Ahead')
    plt.ylabel('Price (USD)')
    plt.grid(True, alpha=0.3)
    
    # Performance Chart
    plt.subplot(2, 2, 4)
    residuals = y_test - y_pred
    plt.scatter(y_pred, residuals, alpha=0.6, s=20)
    plt.axhline(y=0, color='red', linestyle='--')
    plt.title('Model Residuals')
    plt.xlabel('Predicted Values')
    plt.ylabel('Residuals')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def main():
    """ULTIMATE SPEED MAIN"""
    print("🚀 ULTIMATE BITCOIN PREDICTION MODEL - SPEED EDITION")
    print("=" * 60)
    print(f"💻 CPU-Kerne: {os.cpu_count()}")
    print(f"🔥 Batch Size: {CONFIG['batch_size']}")
    
    start_time = time.time()
    
    try:
        # 1. Daten vorbereiten
        df = load_and_prepare_data()
        
        # 2. Skalierung
        print("🔄 Skaliere Daten...")
        scaler = MinMaxScaler()
        scaled_data = scaler.fit_transform(df.values)
        
        # 3. Sequenzen
        print("📦 Erstelle Sequenzen...")
        X, y = create_sequences(scaled_data, CONFIG['look_back'])
        
        train_size = int(len(X) * CONFIG['train_split'])
        X_train, X_test = X[:train_size], X[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        print(f"✅ Training: {X_train.shape}, Test: {X_test.shape}")
        
        # 4. Modell
        print("🏗️  Erstelle Speed-Modell...")
        model = build_speed_model((X_train.shape[1], X_train.shape[2]))
        print(f"📋 Parameter: {model.count_params():,}")
        
        # 5. Training
        print("🎯 Starte SPEED-Training...")
        train_start = time.time()
        
        callbacks = [EarlyStopping(patience=CONFIG['patience'], restore_best_weights=True)]
        
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            callbacks=callbacks,
            verbose=1
        )
        
        training_time = time.time() - train_start
        print(f"⚡ Training abgeschlossen in {training_time:.1f} Sekunden!")
        
        # 6. Evaluation
        print("📊 Evaluiere...")
        y_pred = model.predict(X_test, verbose=0)
        
        # Skalierung rückgängig machen
        dummy_test = np.zeros((len(y_test), scaler.n_features_in_))
        dummy_test[:, 3] = y_test
        y_test_orig = scaler.inverse_transform(dummy_test)[:, 3]
        
        dummy_pred = np.zeros((len(y_pred), scaler.n_features_in_))
        dummy_pred[:, 3] = y_pred.flatten()
        y_pred_orig = scaler.inverse_transform(dummy_pred)[:, 3]
        
        # Metriken
        metrics = {
            'rmse': np.sqrt(mean_squared_error(y_test_orig, y_pred_orig)),
            'mae': mean_absolute_error(y_test_orig, y_pred_orig),
            'r2': r2_score(y_test_orig, y_pred_orig)
        }
        
        print(f"\n📊 PERFORMANCE:")
        print(f"RMSE: ${metrics['rmse']:.2f}")
        print(f"MAE: ${metrics['mae']:.2f}")
        print(f"R²: {metrics['r2']:.4f}")
        
        # 7. Zukunftsprognose
        last_sequence = X_test[-1]
        future_pred = predict_future_fast(model, scaler, last_sequence, CONFIG['future_steps'])
        
        # 8. Visualisierung
        print("📈 Erstelle Visualisierung...")
        plot_speed_results(df, train_size, y_test_orig, y_pred_orig, future_pred, metrics, training_time)
        
        # 9. Prognose-Zusammenfassung
        current_price = df['close'].iloc[-1]
        print(f"\n🎯 24-STUNDEN PROGNOSE:")
        for hours in [6, 12, 24]:
            if hours <= len(future_pred['mean']):
                pred_price = future_pred['mean'][hours-1]
                change_pct = ((pred_price / current_price) - 1) * 100
                lower_95 = future_pred['lower_95'][hours-1]
                upper_95 = future_pred['upper_95'][hours-1]
                
                print(f"In {hours}h: ${pred_price:.2f} ({change_pct:+.2f}%) "
                      f"[95% CI: ${lower_95:.2f} - ${upper_95:.2f}]")
        
        total_time = time.time() - start_time
        print(f"\n✅ ULTIMATE SPEED MODEL FERTIG!")
        print(f"⚡ Gesamtzeit: {total_time:.1f} Sekunden")
        print(f"💻 CPU-Auslastung: 100% auf {os.cpu_count()} Kernen")
        
    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
