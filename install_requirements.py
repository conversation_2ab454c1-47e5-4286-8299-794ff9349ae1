#!/usr/bin/env python3
"""
ULTIMATE BITCOIN TRADING SYSTEM - INSTALLATIONSSCRIPT
=====================================================
Installiert alle benötigten Pakete automatisch
"""

import subprocess
import sys
import os

def install_package(package):
    """Installiere ein Paket mit pip"""
    try:
        print(f"Installiere {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} erfolgreich installiert")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Fehler beim Installieren von {package}: {e}")
        return False

def main():
    """Hauptfunktion für Installation"""
    print("=" * 60)
    print("ULTIMATE BITCOIN TRADING SYSTEM - INSTALLATION")
    print("=" * 60)
    
    # Liste aller benötigten Pakete
    required_packages = [
        # Basis-Pakete
        "pandas>=1.5.0",
        "numpy>=1.21.0",
        "requests>=2.28.0",
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0",
        
        # ML-Pakete
        "scikit-learn>=1.1.0",
        "yfinance>=0.2.0",
        
        # Optional aber empfohlen
        "scipy>=1.9.0",
        
        # GUI-Pakete (tkinter ist meist vorinstalliert)
        # tkinter ist Teil der Python-Standardbibliothek
    ]
    
    # Optionale Pakete (können fehlschlagen)
    optional_packages = [
        "xgboost",
        "tensorflow",
        "plotly",
        "TA-Lib"
    ]
    
    print(f"\n🔧 Installiere {len(required_packages)} benötigte Pakete...")
    
    success_count = 0
    failed_packages = []
    
    # Installiere benötigte Pakete
    for package in required_packages:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
    
    print(f"\n🎯 Installiere {len(optional_packages)} optionale Pakete...")
    
    optional_success = 0
    optional_failed = []
    
    # Installiere optionale Pakete
    for package in optional_packages:
        if install_package(package):
            optional_success += 1
        else:
            optional_failed.append(package)
    
    # Zusammenfassung
    print("\n" + "=" * 60)
    print("INSTALLATION ABGESCHLOSSEN")
    print("=" * 60)
    
    print(f"✅ Benötigte Pakete: {success_count}/{len(required_packages)} erfolgreich")
    print(f"🎯 Optionale Pakete: {optional_success}/{len(optional_packages)} erfolgreich")
    
    if failed_packages:
        print(f"\n❌ Fehlgeschlagene benötigte Pakete:")
        for pkg in failed_packages:
            print(f"   - {pkg}")
    
    if optional_failed:
        print(f"\n⚠️ Fehlgeschlagene optionale Pakete:")
        for pkg in optional_failed:
            print(f"   - {pkg}")
    
    if not failed_packages:
        print(f"\n🚀 INSTALLATION ERFOLGREICH!")
        print(f"Sie können jetzt das Ultimate Bitcoin Trading System starten.")
    else:
        print(f"\n⚠️ INSTALLATION TEILWEISE ERFOLGREICH")
        print(f"Das System funktioniert mit reduzierten Features.")
    
    print(f"\n📋 Nächste Schritte:")
    print(f"1. Führen Sie aus: python ultimate_bitcoin_trading_simple.py")
    print(f"2. Oder GUI: python ultimate_bitcoin_gui_simple.py")

if __name__ == "__main__":
    main()
