�

    �aeh�  �                   ��  � d Z ddlZddlZddlZddlZddlZddl	m	Z	m
Z
 ddlZddlZddl
Z
ddlZddlZddlmZmZmZmZ  e
j        d�  �         ddlmZ ddlmZ ddlmZmZmZ dd	lmZ 	 ddl Z!d
Z" e#d�  �         n# e$$ r dZ" e#d
�  �         Y nw xY w	 ddl%m&Z& d
Z' e#d�  �         n# e$$ r dZ' e#d�  �         Y nw xY w G d� d�  �        Z(d� Z)e*dk    r e)�   �          dS dS )u�  
ULTIMATE BITCOIN TRADING SYSTEM V9.0 - FEHLERFREIE EDITION
===========================================================
VOLLSTÄNDIG ÜBERARBEITET • ALLE FEHLER BEHOBEN • GETESTET
- DataFrame Ambiguity Error behoben
- Negative ML-Genauigkeit korrigiert
- Live-Daten Integration repariert
- Datenqualität maximiert
- Vorhersage-Berechnung optimiert
- Alle Funktionen vollständig implementiert

V9.0 FEHLERFREIE EDITION - GARANTIERT FUNKTIONAL!
�    N)�datetime�	timedelta)�Dict�List�Tuple�Optional�ignore)�RobustScaler)�RandomForestRegressor)�mean_squared_error�r2_score�accuracy_score)�TimeSeriesSplitTu   ✅ XGBoost verfügbarFu7   ⚠️ XGBoost nicht verfügbar - verwende RandomForest)�statsu   ✅ SciPy verfügbaru:   ⚠️ SciPy nicht verfügbar - verwende NumPy Statistikenc                   �  � e Zd ZdZd� Zdefd�Zdee         defd�Z	de
j        fd�Zdefd�Z
de
j        fd	�Zd
e
j        dede
j        fd�Zd
e
j        de
j        fd�Zd
e
j        de
j        fd�Zd
e
j        defd�Zd
e
j        de
j        fd�Zd
e
j        de
j        fd�Zdefd�Zd
e
j        defd�Zd
e
j        defd�Zdefd�Zde
j        fd�ZdS )�#UltimateBitcoinTradingSystemV9Fixedu�   
    ULTIMATE BITCOIN TRADING SYSTEM V9.0 - FEHLERFREIE EDITION
    ===========================================================
    Vollständig überarbeitet mit allen Fehlerbehebungen
    c                 ��  � d| _         d| _        t          j        �   �         | _        ddddd�| _        t
          j        �   �         | _        i | _	        d | _
        d| _        i | _        i | _
        t          �   �         | _        d	d
d
t           d
d
d�| _        g | _        d| _        d | _        d
dd�ddd�ddd�d�| _        i | _        | j        �                    �   �         dddddddddt1          | j        �                    �   �         �  �        d�| _        t7          d�  �         t7          d| j         � ��  �         t7          d| j        �                    d�  �        � ��  �         t7          dt;          | j        �  �        � d��  �         t7          d| j        d         � dt;          | j        �  �        � d ��  �         t7          d!t           � d"t<          � ��  �         t7          d#�  �         d S )$Nz"Ultimate_Trading_System_v9.0_FixedzBTC-USDz:https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDTz7https://api.coinbase.com/v2/exchange-rates?currency=BTCzKhttps://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usdz2https://api.kraken.com/0/public/Ticker?pair=XBTUSD)�binance�coinbase�	coingecko�kraken�<   FT)�sentiment_analysis�technical_indicators_extended�multi_timeframe_analysis�ensemble_predictions�volatility_modeling�trend_detectionr   �7d�1h��period�interval�30d�4h�90d�1d)r    r%   r'   �        )�script_start_time�total_scans�successful_scans�current_accuracy�
best_accuracy�api_calls_count�live_data_quality�prediction_accuracy�total_analysis_time�errors_count�features_enabledz8Ultimate Bitcoin Trading System V9.0 FIXED initialisiertz	Version: zStart-Zeit: z%d.%m.%Y %H:%M:%SzRobuste APIs: z QuellenzErweiterte Features: r3   �/z
 aktiviertu   ML-Verfügbarkeit: XGBoost=z, SciPy=u2   Fehlerbehandlung: Vollständig robust und getestet)�VERSION�SYMBOLr   �nowr)   �
api_endpoints�pd�	DataFrame�market_data�live_price_cache�last_cache_time�cache_duration�	ml_models�model_performancer
   �scaler�XGBOOST_AVAILABLE�advanced_features�scan_results�scan_counter�last_scan_result�
timeframes�multi_timeframe_data�	isoformat�sum�values�
session_stats�print�strftime�len�SCIPY_AVAILABLE)�selfs    �2E:\Dev\ultimate_bitcoin_trading_system_v9_fixed.py�__init__z,UltimateBitcoinTradingSystemV9Fixed.__init__=   s6  � �;������ "*������ T�Q�f�J�	
� 
��� �<�>�>��� "���#��� ��� ���!#���"�n�n��� #(�-1�(,�$5�#'�#�
"
� "
��� ������ $��� "�t�4�4�"��5�5�"��5�5�
� 
���
 %'��!� "&�!7�!A�!A�!C�!C�� !� #� � �!$�#&�#&�� #�D�$:�$A�$A�$C�$C� D� D�
� 
��� 	�I�J�J�J�
�(�$�,�(�(�)�)�)�
�S�T�3�<�<�=P�Q�Q�S�S�T�T�T�
�@�s�4�#5�6�6�@�@�@�A�A�A�
�v�d�&8�9K�&L�v�v�s�SW�Si�Oj�Oj�v�v�v�w�w�w�
�X�,=�X�X��X�X�Y�Y�Y�
�C�D�D�D�D�D�    �returnc                 ��  �� 	 t          d�  �         t          j        �   �         }i }i }d}g }	 t          j        �   �         }t          j        | j        d         d��  �        }t          j        �   �         |z
  }|j        dk    r{|�                    �   �         }	t          |	d         �  �        }
d|
cxk    rd	k    r+n n(|
|d<   ||d<   |d
z
  }t          d|
d�d
|d�d��  �         n7|�                    d|
d���  �         n|�                    d|j        � ��  �         nD# t          $ r7}|�                    dt          |�  �        dd�         � ��  �         Y d}~nd}~ww xY w	 t          j        �   �         }t          j        | j        d         d��  �        }t          j        �   �         |z
  }|j        dk    r�|�                    �   �         }	t          |	d         d         d         �  �        }
d|
cxk    rd	k    r+n n(|
|d<   ||d<   |d
z
  }t          d|
d�d
|d�d��  �         n7|�                    d|
d���  �         n|�                    d|j        � ��  �         nD# t          $ r7}|�                    dt          |�  �        dd�         � ��  �         Y d}~nd}~ww xY w	 t          j        �   �         }t          j        | j        d         d��  �        }t          j        �   �         |z
  }|j        dk    r�|�                    �   �         }	t          |	d         d         �  �        }
d|
cxk    rd	k    r+n n(|
|d<   ||d<   |d
z
  }t          d |
d�d
|d�d��  �         n7|�                    d!|
d���  �         n|�                    d"|j        � ��  �         nD# t          $ r7}|�                    d#t          |�  �        dd�         � ��  �         Y d}~nd}~ww xY w	 t          j        �   �         }t          j        | j        d$         d��  �        }t          j        �   �         |z
  }|j        dk    r�|�                    �   �         }	d%|	v r�|	d%         r�t          |	d%         �                    �   �         �  �        d         }t          |d&         d         �  �        }
d|
cxk    rd	k    r+n n(|
|d$<   ||d$<   |d
z
  }t          d'|
d�d
|d�d��  �         nM|�                    d(|
d���  �         n3|�                    d)�  �         n|�                    d*|j        � ��  �         nD# t          $ r7}|�                    d+t          |�  �        dd�         � ��  �         Y d}~nd}~ww xY w|�rt          |�                    �   �         �  �        }
t          j        |
�  �        }t          j        |
�  �        }t          j        |
�  �        }t#          |
�  �        t%          |
�  �        z
  }d}|}t&          r�t)          |
�  �        d,k    r�	 t          j        t-          j        |
�  �        �  �        ��fd-�t1          |
�  �        D �   �         }|r3t          j        |�  �        }t)          |
�  �        t)          |�  �        z
  }n)# t          $ r}t          d.|� ��  �         Y d}~nd}~ww xY w|t)          | j        �  �        z  }|dk    rt#          d/d0||z  z
  �  �        nd/}|rJt          j        t          |�                    �   �         �  �        �  �        }t#          d/d0|d1z  z
  �  �        }nd/}|d2z  |d3z  z   |d4z  z   }t          j        �   �         |z
  }|||||t)          | j        �  �        |||d5�||||d6�|||t3          j        �   �         �                    �   �         d7d8�
}| j        d9xx         |z
  cc<   || j        d:<   t          d;|d���  �         t          d<|d=�d
|� d>t)          | j        �  �        � d?��  �         t          d@|d=���  �         |rt          dA|d�dB��  �         |dk    rt          dC|� ��  �         |r>t          dDt)          |�  �        � ��  �         |dd,�         D ]}t          dE|� ��  �         �|S t          dF�  �        �# t          $ rY}t          dG|� ��  �         | j        dHxx         d
z
  cc<   | �                    |dIt=          �   �         v r|ng �  �        cY d}~S d}~ww xY w)Jz�
        KORRIGIERTE LIVE-DATEN V9.0
        ===========================
        Sammelt Live-Daten mit korrigierter Fehlerbehandlung
        z%Sammle korrigierte Live-Daten V9.0...r   r   �   )�timeout��   �price�'  � � �   u   ✅ Binance: $�,.2fz (�.3f�s)z Binance: Unrealistischer Preis $zBinance: HTTP z	Binance: N�2   r   �data�rates�USDu   ✅ Coinbase: $z!Coinbase: Unrealistischer Preis $zCoinbase: HTTP z
Coinbase: r   �bitcoin�usdu   ✅ CoinGecko: $z"CoinGecko: Unrealistischer Preis $zCoinGecko: HTTP zCoinGecko: r   �result�cu
   ✅ Kraken: $zKraken: Unrealistischer Preis $zKraken: Keine Daten in Responsez
Kraken: HTTP zKraken: �   c                 �2   �� g | ]\  }}�|         d k     �|��S )ri   � )�.0�i�p�z_scoress      �rR   �
<listcomp>zWUltimateBitcoinTradingSystemV9Fixed.get_enhanced_live_data_v9_fixed.<locals>.<listcomp>�   s/   �� �'`�'`�'`�d�a��PX�YZ�P[�^_�P_�P_��P_�P_�P_rT   u"   ⚠️ Z-Score Berechnung Fehler: r(   �      �?g      @�      �?�333333�?皙�����?)�std�range�outliers_removed)�overall_quality�api_success_rate�price_consistency�response_time_quality�
v9.0_fixed)
�consensus_price�
raw_consensus�
mean_price�individual_prices�successful_apis�
total_apis�price_statistics�quality_metrics�api_response_times�
api_errors�
fetch_time�	timestamp�versionr.   r/   u   ✅ Korrigierte Live-Daten: $u   📊 Datenqualität: �.1%r4   z APIs)u   📈 Preis-Konsistenz: u    ⚡ Durchschnittliche API-Zeit: �su   🔍 Ausreißer entfernt: u   ⚠️ API-Fehler: z   - z#Keine Live-Preise von APIs erhaltenu(   ❌ FEHLER bei korrigierten Live-Daten: r2   r�   )rM   �time�requests�getr8   �status_code�json�float�append�	Exception�str�listrK   �np�median�meanru   �max�minrP   rO   �absr   �zscore�	enumerater   r7   rI   rL   �_get_corrected_fallback_data_v9�locals)rQ   �
start_time�live_pricesr�   r�   r�   �	api_start�response�api_timerb   rZ   �e�ticker_data�prices_listr}   r   �	price_std�price_rangerw   �clean_consensus�clean_pricesry   rz   �avg_response_timer{   rx   r�   rg   �errorro   s                                @rR   �get_enhanced_live_data_v9_fixedzCUltimateBitcoinTradingSystemV9Fixed.get_enhanced_live_data_v9_fixed�   s�
  �� �C	k��9�:�:�:�����J��K�!#���O��J�
=� �I�K�K�	�#�<��(:�9�(E�q�Q�Q�Q���9�;�;��2���'�3�.�.�#�=�=�?�?�D�!�$�w�-�0�0�E���/�/�/�/��/�/�/�/�/�16��I�.�8@�*�9�5�'�1�,���M�u�M�M�M�X�M�M�M�M�N�N�N�N�"�)�)�*Y�U�*Y�*Y�*Y�Z�Z�Z�Z��%�%�&M�x�7K�&M�&M�N�N�N���� 
=� 
=� 
=��!�!�";�c�!�f�f�S�b�S�k�";�";�<�<�<�<�<�<�<�<�����
=����
>� �I�K�K�	�#�<��(:�:�(F�PQ�R�R�R���9�;�;��2���'�3�.�.�#�=�=�?�?�D�!�$�v�,�w�"7��">�?�?�E���/�/�/�/��/�/�/�/�/�27��J�/�9A�*�:�6�'�1�,���N��N�N�N�h�N�N�N�N�O�O�O�O�"�)�)�*Z�e�*Z�*Z�*Z�[�[�[�[��%�%�&N��8L�&N�&N�O�O�O���� 
>� 
>� 
>��!�!�"<�s�1�v�v�c�r�c�{�"<�"<�=�=�=�=�=�=�=�=�����
>����
?� �I�K�K�	�#�<��(:�;�(G�QR�S�S�S���9�;�;��2���'�3�.�.�#�=�=�?�?�D�!�$�y�/�%�"8�9�9�E���/�/�/�/��/�/�/�/�/�38��K�0�:B�*�;�7�'�1�,���O��O�O�O�x�O�O�O�O�P�P�P�P�"�)�)�*[�u�*[�*[�*[�\�\�\�\��%�%�&O��9M�&O�&O�P�P�P���� 
?� 
?� 
?��!�!�"=��A���s��s��"=�"=�>�>�>�>�>�>�>�>�����
?����
<� �I�K�K�	�#�<��(:�8�(D�a�P�P�P���9�;�;��2���'�3�.�.�#�=�=�?�?�D��4�'�'�D��N�'�&*�4��>�+@�+@�+B�+B�&C�&C�A�&F�� %�k�#�&6�q�&9� :� :�� �E�3�3�3�3�V�3�3�3�3�3�49�K��1�;C�.�x�8�+�q�0�O�!�"P�%�"P�"P�"P��"P�"P�"P�"P�Q�Q�Q�Q�&�-�-�.\�PU�.\�.\�.\�]�]�]�]�"�)�)�*K�L�L�L�L��%�%�&L�h�6J�&L�&L�M�M�M���� 
<� 
<� 
<��!�!�":�S��V�V�C�R�C�[�":�":�;�;�;�;�;�;�;�;�����
<���� � Z
G�"�;�#5�#5�#7�#7�8�8�� #%�)�K�"8�"8���W�[�1�1�
��F�;�/�/�	�!�+�.�.��[�1A�1A�A�� $%� �"1��"� H�s�;�'7�'7�!�';�';�H�#%�6�%�,�{�*C�*C�#D�#D��'`�'`�'`�'`�i��6L�6L�'`�'`�'`��'� T�.0�i��.E�.E�O�/2�;�/?�/?�#�l�BS�BS�/S�,���$� H� H� H��F�1�F�F�G�G�G�G�G�G�G�G�����H���� $3�S��9K�5L�5L�#L� �Ud�gh�Uh�Uh�C��S�I��4O�-P�$Q�$Q�$Q�nq�!� &� 0�(*���5G�5N�5N�5P�5P�0Q�0Q�(R�(R�%�,/��S�<M�PS�<S�5T�,U�,U�)�)�,/�)� %�s�*�%��+�,�)�C�/�0�  � "�Y�[�[�:�5�
� (7�%4�",�)4�'6�"%�d�&8�"9�"9�(�!,�,<�)� )� ,;�,<�->�1F�	(� (� +=�",�",�!)����!9�!9�!;�!;�+�-� ��4 �"�#4�5�5�5��H�5�5�5�:I��"�#6�7��L�o�L�L�L�M�M�M��v�o�v�v�v�_�v�v�WZ�[_�[m�Wn�Wn�v�v�v�w�w�w��G�0A�G�G�G�H�H�H�%� W��U�=N�U�U�U�U�V�V�V�#�a�'�'��I�7G�I�I�J�J�J�� /��A��J���A�A�B�B�B�!+�B�Q�B�� /� /���o�e�o�o�.�.�.�.��
�  � E�F�F�F��� 	k� 	k� 	k��@�Q�@�@�A�A�A���~�.�.�.�!�3�.�.�.� �7�7���Y_�Ya�Ya�Ia�Ia�:�:�gi�j�j�j�j�j�j�j�j�����	k���s�   �*b �C-D �b �
E�&-E�b �E�b �!C9I �b �
J�%-J�b �J�b � C3N �b �
O�-O�b �O�b �ET �b �
U�&-U�b �U�B b �>A6Y5 �4b �5
Z�?Z�b �Z�Gb �7b �
c)�Ac$�c)�$c)r�   c           
      �T  � 	 t          d�  �         	 t          d�  �         t          j        | j        �  �        }|j        }|�                    dd�  �        }|red|cxk    rdk    rXn nUt          d|d���  �         |d	|id
d
ddid
t
          j        �   �         �                    �   �         d	t          |�  �        |dd�S n)# t          $ r}t          d|� ��  �         Y d}~nd}~ww xY w| j        j        s�	 | j        d         j
        d         }t          j        �                    dd�  �        }t#          dt%          d|�  �        �  �        }|d
|z   z  }	t          d|	d���  �         |	d|	idt'          | j        �  �        ddidt
          j        �   �         �                    �   �         dt          |�  �        |dd�S # t          $ r}
t          d|
� ��  �         Y d}
~
nd}
~
ww xY wdt          j        �                    dd�  �        z   }t#          dt%          d |�  �        �  �        }t          d!|d���  �         |d"|idt'          | j        �  �        dd#idt
          j        �   �         �                    �   �         d$t          |�  �        |dd�S # t          $ r�}t          d%|� ��  �         | j        d&xx         d
z
  cc<   dd'didt'          | j        �  �        dd(idt
          j        �   �         �                    �   �         d)t          |�  �        t          |�  �        d*d+�cY d}~S d}~ww xY w),z!Korrigiertes Fallback System V9.0z.Aktiviere korrigiertes Fallback System V9.0...z"Versuche Yahoo Finance Fallback...�regularMarketPricer   r[   r\   u   ✅ Yahoo Finance Fallback: $r^   �
yahoo_financer]   rx   皙�����?rq   zv9.0_fallback_fixed)r}   r�   r�   r�   r�   r�   r�   �fallback�original_errorr�   r�   u#   ❌ Yahoo Finance Fallback Fehler: N�Close����������Mb`?�{�G�zt��{�G�zt?u   ✅ Historischer Fallback: $�historical_fallback�ffffff�?皙�����?�
historicalu"   ❌ Historischer Fallback Fehler: 鼫 i�  iP�  i@
 u)   ⚠️ Korrigierter Emergency Fallback: $�emergency_fallback�333333�?�	emergencyu7   ❌ KRITISCHER FEHLER im korrigierten Fallback System: r2   �critical_fallbackrr   �criticalzv9.0_critical_fixed)r}   r�   r�   r�   r�   r�   r�   r�   r�   �fallback_errorr�   )rM   �yf�Tickerr6   �infor�   r   r7   rI   r�   r�   r;   �empty�ilocr�   �random�normalr�   r�   rO   r8   rL   )
rQ   r�   r�   �btcr�   �
current_price�yf_error�
last_price�realistic_change�fallback_price�
hist_error�emergency_pricer�   s
                rR   r�   zCUltimateBitcoinTradingSystemV9Fixed._get_corrected_fallback_data_v9Q  s�  � �[	��B�C�C�C�
H��:�;�;�;��i���,�,���x�� $���)=�q� A� A�
� � �U�m�%E�%E�%E�%E�v�%E�%E�%E�%E�%E��N�-�N�N�N�O�O�O�+8�.=�}�-M�+,�&'�,=�s�+C�&)�%-�\�^�^�%=�%=�%?�%?�$3�*-�e�*�*�&0�#8�� � ��� � 
H� 
H� 
H��F�H�F�F�G�G�G�G�G�G�G�G�����
H���� �#�)� 
M�M�!%�!1�'�!:�!?��!C�J�')�y�'7�'7��5�'A�'A�$�'*�6�3�u�>N�3O�3O�'P�'P�$�%/�1�7G�3G�%H�N��N��N�N�N�O�O�O�+9�.C�^�-T�+,�&)�$�*<�&=�&=�,=�s�+C�&)�%-�\�^�^�%=�%=�%?�%?�$0�*-�e�*�*�&0�#8�� � �� !� M� M� M��K�z�K�K�L�L�L�L�L�L�L�L�����M����
 %�r�y�'7�'7��3�'?�'?�?�O�!�%��V�_�)E�)E�F�F�O��T�o�T�T�T�U�U�U� $3�&:�O�%L�#$�!�$�"4�5�5�$5�s�#;�!�%�\�^�^�5�5�7�7�'�"%�e�*�*�(�0�� � 
�� � 	� 	� 	��\�N�\�\�]�]�]���~�.�.�.�!�3�.�.�.� $*�&9�6�%B�#$�!�$�"4�5�5�$5�s�#;�!�%�\�^�^�5�5�7�7�&�"%�e�*�*�"%�n�"5�"5�0�� � 
� 
� 
� 
� 
� 
�����		���sn   �J �B+B? �>J �?
C%�	C �J � C%�%J �5CF: �:
G �G�J �G � B*J �
L'�BL"�L'�"L'c                 �d  � 	 t          d�  �         t          j        �   �         }| j        rvt          j        �   �         | j        z
  t          | j        ��  �        k     rD| j        j        s8t          dt          j        �   �         | j        z
  j	        � d��  �         | j        S d}| j
        d         rB| �                    �   �         }|r,d|v r(|d         }t          dt          |�  �        � d	��  �         |�|j        r| �
                    �   �         }	 | �                    �   �         }|d
         }|r:d|cxk    rdk    r-n n*| �                    ||�  �        }t          d
|d���  �         nt          d|d���  �         n)# t           $ r}t          d|� ��  �         Y d}~nd}~ww xY w| �                    |�  �        }| �                    |�  �        }|| _        t          j        �   �         | _        t          j        �   �         |z
  }t          dt          |�  �        � d|d�d��  �         |S # t           $ r{}t          d|� ��  �         | j        dxx         dz
  cc<   | j        j        st          d�  �         | j        cY d}~S t          d�  �         | �                    �   �         cY d}~S d}~ww xY w)z�
        KORRIGIERTE MARKTDATEN V9.0
        ===========================
        Sammelt korrigierte historische und Live-Daten (DATAFRAME AMBIGUITY BEHOBEN)
        z%Sammle korrigierte Marktdaten V9.0...)�secondszVerwende Cache-Daten (Alter: r`   Nr   r    zMulti-Timeframe Daten: z Datenpunkte (1h)r}   r[   r\   zLive-Preis integriert: $r^   u5   ⚠️ Live-Preis außerhalb realistischer Grenzen: $�Live-Preis Integration Fehler: zKorrigierte Marktdaten V9.0: z Datenpunkte in �.2fr�   u-   ❌ FEHLER bei korrigierten Marktdaten V9.0: r2   r]   z*Verwende vorherige Marktdaten als Fallbackz$Generiere korrigierte Fallback-Daten)rM   r�   r=   r   r7   r   r>   r;   r�   r�   rC   �&_get_corrected_multi_timeframe_data_v9rO   �_get_corrected_yahoo_data_v9r�   �"_integrate_corrected_live_price_v9r�   �_corrected_data_cleaning_v9�"_calculate_corrected_indicators_v9rL   �$_generate_corrected_fallback_data_v9)	rQ   r�   �hist�
multi_data�	live_datar�   r�   �dfr�   s	            rR   �get_corrected_market_data_v9z@UltimateBitcoinTradingSystemV9Fixed.get_corrected_market_data_v9�  s?  � �?	C��9�:�:�:�����J� �$� 
(������!5�5�	�$�J]�8^�8^�8^�^�^��$�*� _��i�x�|�~�~��H\�7\�6e�i�i�i�j�j�j��'�'� �D��%�&@�A� 
R�!�H�H�J�J�
�� R�$�*�"4�"4�%�d�+�D��P�C��I�I�P�P�P�Q�Q�Q� �|�t�z�|��8�8�:�:��
=� �@�@�B�B�	� )�*;� <�
� � h�U�m�%E�%E�%E�%E�v�%E�%E�%E�%E�%E��B�B�4��W�W�D��I�]�I�I�I�J�J�J�J��f�R_�f�f�f�g�g�g���� 
=� 
=� 
=��;��;�;�<�<�<�<�<�<�<�<�����
=���� �1�1�$�7�7�B� �8�8��<�<�B�  "�D��#+�<�>�>�D� �����z�1�J��\�#�b�'�'�\�\�:�\�\�\�\�]�]�]��I��� 
	C� 
	C� 
	C��E�!�E�E�F�F�F���~�.�.�.�!�3�.�.�.� �#�)� 
C��B�C�C�C��'�'�'�'�'�'�'��<�=�=�=��@�@�B�B�B�B�B�B�B�B�����
	C���s^   �BH* �!A.H* �A+E< �;H* �<
F"�F�H* �F"�"BH* �*
J/�4AJ*�<J/�"J*�$J/�*J/c           	      �  � 	 t          d�  �         i }| j        �                    �   �         D �]!\  }}	 t          j        | j        �  �        }|�                    |d         |d         ��  �        }|j        s�|�                    �   �         }||d         dk             }||d         dk             }t          |�  �        d	k    r)|||<   t          d
|� dt          |�  �        � d��  �         n7t          d
|� dt          |�  �        � d��  �         nt          d|� d��  �         ��# t          $ r }t          d|� d|� ��  �         Y d}~��d}~ww xY w|| _        |S # t          $ r}t          d|� ��  �         i cY d}~S d}~ww xY w)z-Sammle korrigierte Multi-Timeframe Daten V9.0z+Sammle korrigierte Multi-Timeframe Daten...r"   r#   r!   r�   ��  �Volumer   �
   u   ✅ z: � Datenpunkteu   ⚠️ u   : Nicht genügend Daten (�)u   ❌ z: Keine Daten erhaltenz	 Fehler: NzMulti-Timeframe Fehler: )rM   rG   �itemsr�   r�   r6   �historyr�   �dropnarO   r�   rH   )rQ   r�   �tf_name�	tf_configr�   r�   r�   s          rR   r�   zJUltimateBitcoinTradingSystemV9Fixed._get_corrected_multi_timeframe_data_v9�  s�  � � 	��?�@�@�@��J�&*�o�&;�&;�&=�&=� 
8� 
8�"���8��)�D�K�0�0�C��;�;�i��.A�I�V`�La�;�b�b�D��:� F�#�{�{�}�}��#�D��M�D�$8�9��#�D��N�Q�$6�7���t�9�9��?�?�26�J�w�/�!�"K��"K�"K�C��I�I�"K�"K�"K�L�L�L�L�!�"Z�G�"Z�"Z�c�RV�i�i�"Z�"Z�"Z�[�[�[�[��D�W�D�D�D�E�E�E��� � 8� 8� 8��6��6�6�1�6�6�7�7�7�7�7�7�7�7�����8���� )3�D�%����� 	� 	� 	��0�Q�0�0�1�1�1��I�I�I�I�I�I�����	���sG   �0E �C.D"�!E �"
E�,E�E �E�E �
F �"E;�5F �;F c                 �l  � 	 t          d�  �         t          j        | j        �  �        }|�                    dd��  �        }|j        rt
          d�  �        �t          dt          |�  �        � d��  �         |S # t          $ r0}t          d|� ��  �         | �                    �   �         cY d	}~S d	}~ww xY w)
z+Sammle korrigierte Yahoo Finance Daten V9.0z)Sammle korrigierte Yahoo Finance Daten...r   r    r!   z"Keine Yahoo Finance Daten erhaltenzYahoo Finance Daten: r�   zYahoo Finance Fehler: N)	rM   r�   r�   r6   r�   r�   r�   rO   r�   )rQ   r�   r�   r�   s       rR   r�   z@UltimateBitcoinTradingSystemV9Fixed._get_corrected_yahoo_data_v9  s�   � �	?��=�>�>�>��)�D�K�(�(�C��;�;�d�T�;�:�:�D��z� 
F�� D�E�E�E��A�#�d�)�)�A�A�A�B�B�B��K��� 	?� 	?� 	?��.�1�.�.�/�/�/��<�<�>�>�>�>�>�>�>�>�����	?���s   �A6A9 �9
B3�%B.�(B3�.B3r�   r�   c           
      �  � 	 |j         r|S t          j        �   �         }|d         j        d         }||z
  |z  }t	          dt          d|�  �        �  �        }|t	          ||dt
          |�  �        dz  z   z  �  �        t          ||dt
          |�  �        dz  z
  z  �  �        ||d         j        dd	�         �                    �   �         d
�}t          j	        |�  �        }|�
                    �   �         }|�                    �   �         D ]\  }	}
|
|j        ||	f<   �|S # t          $ r}t          d|� ��  �         |cY d	}~S d	}~ww xY w)z'Integriere Live-Preis V9.0 (KORRIGIERT)r�   r�   g���Q���g���Q��?r]   rr   r�   i����N��Open�High�Lowr�   r�   r�   )r�   r   r7   r�   r�   r�   r�   r�   r9   �	Timestamp�copyr�   �locr�   rM   )rQ   r�   r�   �current_time�
last_close�price_change�new_row�	new_index�	hist_copy�col�valuer�   s               rR   r�   zFUltimateBitcoinTradingSystemV9Fixed._integrate_corrected_live_price_v9-  sp  � �	��z� 
���#�<�>�>�L��g��+�B�/�J� *�J�6�*�D�L��u�c�$��&=�&=�>�>�L� #��M�:��S��=N�=N�QT�=T�9T�+U�V�V��=�*��C��<M�<M�PS�<S�8S�*T�U�U�&��x�.�-�c�d�d�3�8�8�:�:�� �G� ��\�2�2�I��	�	���I�%�m�m�o�o� 
6� 
6�
��U�05�	�
�i��n�-�-����� 	� 	� 	��7�A�7�7�8�8�8��K�K�K�K�K�K�����	���s#   �D �DD �
E�&D?�9E�?Er�   c                 �B  �� 	 �j         r�S t          d�  �         t          ��  �        }��                    �   �         �dD ]L}|�j        v rA��|         dk             ���|         dk              ��|         �                    �   �         �|<   �Mt
          �fd�dD �   �         �  �        rB�g d�         �                    d��  �        �d<   �g d�         �                    d��  �        �d	<   d
�j        v r|�d
         �                    �   �         �d
<   ��d
         dk             ��d
         �	                    �   �         }|dk    r|�j
        �d
         dk    d
f<   nd�j
        �d
         dk    d
f<   t          rvt          ��  �        d
k    rc	 t          j        t          j        �d         �  �        �  �        }�|dk              �n)# t          $ r}t          d|� ��  �         Y d}~nd}~ww xY w��                    �   �         ���j        �                    d��  �                  �t          ��  �        }||z
  }t          d|� d|� d��  �         t          d|� d��  �         �S # t          $ r3}t          d|� ��  �         | j        dxx         dz
  cc<   �cY d}~S d}~ww xY w)z!Korrigierte Datenbereinigung V9.0u,   Führe korrigierte Datenbereinigung durch...)r�   r�   r�   r�   �  i@B c              3   �*   �K  � | ]
}|�j         v V � �d S �N��columns�rl   r�   r�   s     �rR   �	<genexpr>zRUltimateBitcoinTradingSystemV9Fixed._corrected_data_cleaning_v9.<locals>.<genexpr>d  �*   �� � � �Q�Q��3�"�*�$�Q�Q�Q�Q�Q�QrT   r]   ��axisr�   r�   r�   r   r�   �   r�   �   u#   ⚠️ Z-Score Bereinigung Fehler: N�last)�keepzDatenbereinigung: z von z Datenpunkten entferntzBereinigte Daten: r�   z*FEHLER bei korrigierter Datenbereinigung: r2   )r�   rM   rO   r�   r  r�   �allr�   r�   r�   r�   rP   r�   r   r�   r�   �
sort_index�index�
duplicatedrL   )	rQ   r�   �original_lengthr�   �
volume_medianro   r�   �cleaned_length�
removed_counts	    `       rR   r�   z?UltimateBitcoinTradingSystemV9Fixed._corrected_data_cleaning_v9O  s   �� �<	��x� 
��	��@�A�A�A�!�"�g�g�O� �����B� 8� 
,� 
,���"�*�$�$��B�s�G�d�N�+�B��B�s�G�g�-�.�B� ��g�k�k�m�m�B�s�G�� �Q�Q�Q�Q�0P�Q�Q�Q�Q�Q� 
M�� @� @� @�A�E�E�1�E�M�M��6�
��?�?�?�@�D�D�!�D�L�L��5�	� �2�:�%�%�!�(�|�/�/�1�1��8����8���)�*�� !#�8�� 3� 3� 5� 5�
� �1�$�$�:G�B�F�2�h�<�1�,�h�6�7�7�:>�B�F�2�h�<�1�,�h�6�7� � 
E�3�r�7�7�R�<�<�E�!�v�e�l�2�g�;�&?�&?�@�@�H��H�q�L�)�B�B�� � E� E� E��C��C�C�D�D�D�D�D�D�D�D�����E���� �����B��R�X�(�(�f�(�5�5�5�6�B� ��W�W�N�+�n�<�M��b�}�b�b�?�b�b�b�c�c�c��C�~�C�C�C�D�D�D��I��� 	� 	� 	��B�q�B�B�C�C�C���~�.�.�.�!�3�.�.�.��I�I�I�I�I�I�����	���sN   �I! �E=I! �
8G �I! �
G)�
G$�I! �$G)�)A7I! �!
J�+(J�J�Jc                 ��  � 	 |j         st          |�  �        dk     rt          d�  �         |S t          d�  �         |d         �                    �   �         |d<   t	          j        |d         |d         �                    d�  �        z  �  �        |d<   dD ]H}t          |�  �        |k    r3|d         �                    |�  �        �                    �   �         |d	|� �<   �Id
D ]|}t          |�  �        |k    rg|d         �                    |�  �        �	                    �   �         |d|� �<   |d         �
                    |��  �        �	                    �   �         |d
|� �<   �}t          |�  �        dk    r�|d         �                    �   �         }|�                    |dk    d�  �        �                    d��  �        �	                    �   �         }|�                    |dk     d�  �         �                    d��  �        �	                    �   �         }||dz   z  }ddd|z   z  z
  |d<   |d         �
                    d�  �        �                    dd�  �        |d<   t          |�  �        dk    r�|d         �
                    d��  �        �	                    �   �         }|d         �
                    d��  �        �	                    �   �         }	||	z
  |d<   |d         �
                    d��  �        �	                    �   �         |d<   |d         |d         z
  |d<   t          |�  �        dk    r�d}
|d         �                    |
�  �        �	                    �   �         }|d         �                    |
�  �        �                    �   �         }||dz  z   |d<   ||d<   ||dz  z
  |d<   |d         |d         z
  }
|d         |d         z
  |
dz   z  |d<   |d         �
                    d �  �        �                    dd�  �        |d<   | j        d!         �r�t          |�  �        dk    r�|d"         |d#         z
  }t	          j        |d"         |d         �                    d�  �        z
  �  �        }t	          j        |d#         |d         �                    d�  �        z
  �  �        }t#          j        |||gd�$�  �        �                    d�$�  �        }|�                    d�  �        �	                    �   �         |d%<   t          |�  �        dk    r�|d#         �                    d�  �        �                    �   �         }|d"         �                    d�  �        �                    �   �         }d|d         |z
  ||z
  dz   z  z  }|�
                    d�  �        �                    dd�  �        |d&<   |d&         �                    d'�  �        �	                    �   �         |d(<   | j        d)         r�d*D ]�}t          |�  �        |k    rp|d         |d         �                    |�  �        k    �                    t,          �  �        |d+|� �<   |d         |d         �                    |�  �        z  dz
  |d,|� �<   ��| j        d-         r�t          |�  �        d.k    r�|d         dz  }|�                    d�  �        �	                    �   �         �                    t          j        �  �        |d/<   |d/         �                    �   �         }t#          j        |�  �        s(|d/         |k    �                    t,          �  �        |d0<   nd|d0<   d1|j        v r�dD ]H}t          |�  �        |k    r3|d1         �                    |�  �        �	                    �   �         |d2|� �<   �Id3|j        v rI|d1         |d3         dz   z  |d4<   |d4         �
                    d5�  �        �                    d6d7�  �        |d4<   |�                    t          j        g�8�  �        j        }|D �]q}||         �                    �   �         �                    �   �         �rAd|v sd9|v r||         �
                    d�  �        ||<   �Wd|v r||         �
                    d �  �        ||<   �zd4|v r||         �
                    d5�  �        ||<   ��d+|v sd0|v r||         �
                    d�  �        ||<   ��||         �
                    d:�;�  �        �
                    d<�;�  �        ||<   ||         �                    �   �         �                    �   �         rN||         �                    �   �         }t#          j        |�  �        rd}||         �
                    |�  �        ||<   ��st          d=� |j        D �   �         �  �        }t          d>|� d?t          |�  �        � d@��  �         |S # t>          $ r3}t          dA|� ��  �         | j         dBxx         dz
  cc<   |cY dC}~S dC}~ww xY w)Dz0Berechne korrigierte technische Indikatoren V9.0r
  u1   Nicht genügend Daten für technische Indikatorenz3Berechne korrigierte technische Indikatoren V9.0...r�   �Returnsr]   �Log_Returns)r�   r
  �Volatility_)r�   r
  ra   �SMA_)�span�EMA_�   r   )�window绽���|�=�d   �RSIra   �   �   �MACD�	   �MACD_Signal�MACD_Histogramri   �BB_Upper�	BB_Middle�BB_Lower�BB_Positionrr   r   r�   r�   r  �ATR�Stoch_Kr  �Stoch_Dr   ��   r�   r
  �Trend_�	Momentum_r   �   �	GARCH_Vol�
Vol_Regimer�   �Volume_SMA_�
Volume_SMA_20�Volume_Ratiorq   r�   g      $@)�include�Stoch�ffill)�method�bfillc                 �   � g | ]}|d v�|��	S )r�   rk   )rl   r�   s     rR   rp   zZUltimateBitcoinTradingSystemV9Fixed._calculate_corrected_indicators_v9.<locals>.<listcomp>  s#   � �"v�"v�"v�3��Ku�@u�@u�3�@u�@u�@urT   z.Korrigierte technische Indikatoren berechnet: u    Indikatoren für r�   z1FEHLER bei korrigierten technischen Indikatoren: r2   N)!r�   rO   rM   �
pct_changer�   �log�shift�rollingru   r�   �ewm�diff�where�fillna�cliprC   r�   r9   �concatr�   r�   �astype�int�apply�sqrtr�   �isnar  �
select_dtypes�number�anyr�   rL   )rQ   r�   r  r"   �delta�gain�loss�rs�ema_12�ema_26�	bb_period�	bb_middle�
bb_std_dev�bb_range�high_low�
high_close�	low_close�
true_range�
lowest_low�highest_high�	k_percent�returns_squared�
vol_median�numeric_columnsr�   �
median_val�indicator_countr�   s                               rR   r�   zFUltimateBitcoinTradingSystemV9Fixed._calculate_corrected_indicators_v9�  s�	  � �J	��x� 
�3�r�7�7�R�<�<��I�J�J�J��	��G�H�H�H� �w�K�2�2�4�4�B�y�M� "��r�'�{�R��[�5F�5F�q�5I�5I�'I� J� J�B�}�� #� 
U� 
U���r�7�7�f�$�$�13�I��1F�1F�v�1N�1N�1R�1R�1T�1T�B�-�V�-�-�.�� '� 
N� 
N���r�7�7�f�$�$�*,�W�+�*=�*=�f�*E�*E�*J�*J�*L�*L�B��f���'�*,�W�+�/�/�v�/�*F�*F�*K�*K�*M�*M�B��f���'�� �2�w�w�"�}�}��7��(�(�*�*�����E�A�I�q�1�1�:�:�"�:�E�E�J�J�L�L�����U�Q�Y��2�2�2�;�;�2�;�F�F�K�K�M�M�� �T�E�\�*���3�!�b�&�>�2��5�	� �u�I�,�,�R�0�0�5�5�a��=�=��5�	� �2�w�w�"�}�}��G����b��1�1�6�6�8�8���G����b��1�1�6�6�8�8��#�f�_��6�
�$&�v�J�N�N��N�$:�$:�$?�$?�$A�$A��=�!�')�&�z�B�}�4E�'E��#�$� �2�w�w�"�}�}��	��w�K�/�/�	�:�:�?�?�A�A�	���[�0�0��;�;�?�?�A�A�
�!*�j�1�n�!=��:��"+��;��!*�j�1�n�!=��:�� �j�>�B�z�N�:��%'��[�2�j�>�%A�h�QV�FV�$W��=�!�$&�}�$5�$<�$<�S�$A�$A�$F�$F�q�!�$L�$L��=�!� �%�&E�F� 
D��r�7�7�b�=�=�!�&�z�B�u�I�5�H�!#���6�
�R��[�5F�5F�q�5I�5I�(I�!J�!J�J� "��r�%�y�2�g�;�3D�3D�Q�3G�3G�'G� H� H�I�!#��H�j�)�+L�ST�!U�!U�!U�!Y�!Y�_`�!Y�!a�!a�J� *� 2� 2�2� 6� 6� ;� ;� =� =�B�u�I� �r�7�7�b�=�=�!#�E��!2�!2�2�!6�!6�!:�!:�!<�!<�J�#%�f�:�#5�#5�b�#9�#9�#=�#=�#?�#?�L� #��7��j�(@�\�T^�E^�af�Ef�'g� h�I�$-�$4�$4�R�$8�$8�$=�$=�a��$E�$E�B�y�M�$&�y�M�$9�$9�!�$<�$<�$A�$A�$C�$C�B�y�M� �%�&7�8� 
_�)� _� _�F��2�w�w�&�(�(�13�G��r�'�{�?P�?P�QW�?X�?X�1X�0`�0`�ad�0e�0e��,�F�,�,�-�35�g�;��G��AR�AR�SY�AZ�AZ�3Z�]^�3^��/�v�/�/�0�� �%�&;�<� 
-��r�7�7�b�=�=�&(��m�q�&8�O�&5�&=�&=�b�&A�&A�&F�&F�&H�&H�&N�&N�r�w�&W�&W�B�{�O� "$�K��!7�!7�!9�!9�J��7�:�.�.� -�,.�{�O�j�,H�+P�+P�QT�+U�+U��<�(�(�+,��<�(� �2�:�%�%�&� Y� Y�F��2�w�w�&�(�(�57��\�5I�5I�&�5Q�5Q�5V�5V�5X�5X��1��1�1�2�� #�b�j�0�0�)+�H���O�9L�u�9T�)U�B�~�&�)+�N�);�)B�)B�3�)G�)G�)L�)L�S�RV�)W�)W�B�~�&� !�.�.��	�{�.�C�C�K�O�&� 
A� 
A���c�7�<�<�>�>�%�%�'�'� A���|�|�w�#�~�~�"$�S�'�.�.��"4�"4��3���&�#�-�-�"$�S�'�.�.��"5�"5��3���'�3�.�.�"$�S�'�.�.��"5�"5��3���!�S���L�C�,?�,?�"$�S�'�.�.��"3�"3��3��� #%�S�'�.�.��.�"@�"@�"G�"G�w�"G�"W�"W��3���c�7�<�<�>�>�-�-�/�/� A�)+�C����)9�)9�J�!�w�z�2�2� /�-.�
�&(��g�n�n�Z�&@�&@�B�s�G��!�"v�"v�"�*�"v�"v�"v�w�w�O��{�?�{�{�fi�jl�fm�fm�{�{�{�|�|�|��I��� 	� 	� 	��I�a�I�I�J�J�J���~�.�.�.�!�3�.�.�.��I�I�I�I�I�I�����	���s#   �*f0 �ff0 �0
g-�:(g(�"g-�(g-c           
      ��
  � 	 t          d�  �         t          j        �   �         }t          |�  �        dk     r"t          dt          |�  �        � d��  �         dS | �                    |�  �        }|j        rt          d�  �         dS | �                    |�  �        }|j        rt          d�  �         dS t
          t          |�  �        t          |�  �        �  �        }|j        d|�         }|j        d|�         }t          |�  �        d	k     rt          d
�  �         dS | j        �	                    |�  �        }t          d��  �        }	 t          d
�  �         ddlm}  |dddddd��  �        }	g }
|�
                    |�  �        D ]\  }}||         ||         }}
|j        |         |j        |         }}|	�                    |
|�  �         |	�                    |�  �        }t!          ||�  �        }|
�                    |�  �         ��|	�                    ||�  �         |	| j        d<   |
t'          j        |
�  �        t'          j        |
�  �        |	j        dd�}|| j        d<   t          dt'          j        |
�  �        d�dt'          j        |
�  �        d���  �         n)# t0          $ r}t          d|� ��  �         Y d}~nd}~ww xY wt2          �r�| j        d         �r�	 t          d�  �         dd lm}  |dd!d"d#d#ddd�$�  �        }g }|�
                    |�  �        D ]�\  }}||         ||         }}
|j        |         |j        |         }}|�                    |
|||fgd�%�  �         |�                    |�  �        }t!          ||�  �        }|�                    |�  �         ��|�                    ||�  �         || j        d&<   |t'          j        |�  �        t'          j        |�  �        |j        d'd�}|| j        d&<   t          d(t'          j        |�  �        d�dt'          j        |�  �        d���  �         n)# t0          $ r}t          d)|� ��  �         Y d}~nd}~ww xY w| j        r�g }| j        �                    �   �         D ]$\  }}d*|v r|�                    |d*         �  �         �%|r]t'          j        |�  �        }t=          |�  �        }|| j        d+<   || j        d,<   t          d-|d���  �         t          d.|d���  �         t          j        �   �         |z
  }t          d/|d0�d1��  �         d2S # t0          $ r2}t          d3|� ��  �         | j        d4xx         dz
  cc<   Y d}~dS d}~ww xY w)5z�
        KORRIGIERTES ML-MODELL TRAINING V9.0
        ====================================
        Trainiert korrigierte ML-Modelle (NEGATIVE GENAUIGKEIT BEHOBEN)
        z(Trainiere korrigiertes ML-Modell V9.0...ra   u(   Nicht genügend Daten für ML-Training: z < 50Fu*   Keine Features für ML-Training verfügbaru'   Kein Target für ML-Training verfügbarNr3  u.   Nicht genügend aligned Daten für ML-Trainingr  )�n_splitsz'Trainiere korrigierten Random Forest...r   )�RandomForestClassifierr   �   r0  ri   �*   r]   )�n_estimators�	max_depth�min_samples_split�min_samples_leaf�random_state�n_jobs�
random_forestri  )�	cv_scores�
mean_cv_score�std_cv_score�feature_importance�
model_typeu!   ✅ Random Forest: CV Accuracy = r�   u    ± u#   ❌ Random Forest Training Fehler: r   z!Trainiere korrigierten XGBoost...)�
XGBClassifierrW   r�   r�   )rl  rm  �
learning_rate�	subsample�colsample_bytreerp  rq  �	verbosity)�eval_set�verbose�xgboostrx  u   ✅ XGBoost: CV Accuracy = u   ❌ XGBoost Training Fehler: rt  r,   r-   u'   🎯 Korrigierte Ensemble-Genauigkeit: u   🏆 Beste Modell-Genauigkeit: u-   ✅ Korrigiertes ML-Modell V9.0 trainiert in r�   r�   Tu*   ❌ FEHLER beim korrigierten ML-Training: r2   ) rM   r�   rO   �_create_corrected_features_v9r�   �_create_corrected_target_v9r�   r�   rA   �
fit_transformr   �sklearn.ensembleri  �split�fit�predictr   r�   r?   r�   r�   ru   �feature_importances_r@   r�   rB   rC   r  rx  r�   r�   rL   )rQ   r�   r�   �features�target�
min_length�features_scaled�tscvri  �rf_model�	rf_scores�	train_idx�val_idx�X_train�X_val�y_train�y_val�y_pred�score�rf_performancer�   rx  �	xgb_model�
xgb_scores�xgb_performance�
all_scores�
model_name�perf�ensemble_accuracyr-   �
training_times                                  rR   �train_corrected_ml_model_v9z?UltimateBitcoinTradingSystemV9Fixed.train_corrected_ml_model_v9  s�  � �U	��<�=�=�=�����J��2�w�w��|�|��O��R���O�O�O�P�P�P��u� �9�9�"�=�=�H��~� 
��B�C�C�C��u� �5�5�b�9�9�F��|� 
��?�@�@�@��u� �S��]�]�C��K�K�8�8�J��}�[�j�[�1�H��[��*��-�F��8�}�}�r�!�!��F�G�G�G��u� #�k�7�7��A�A�O� #�A�.�.�.�D�(
A��?�@�@�@�C�C�C�C�C�C�1�1�!$� �&'�%&�!#��
� � �� �	�*.�*�*�_�*E�*E� ,� ,�&�I�w�%4�Y�%?��QX�AY�U�G�%+�[��%;�V�[��=Q�U�G��L�L��'�2�2�2�%�-�-�e�4�4�F�*�5�&�9�9�E��$�$�U�+�+�+�+� ���_�f�5�5�5�2:����/� "+�%'�W�Y�%7�%7�$&�F�9�$5�$5�*2�*G�":�"� "�� ;I��&��7��m�"�'�)�:L�:L�m�m�m�VX�V\�]f�Vg�Vg�m�m�m�n�n�n�n��� 
A� 
A� 
A��?�A�?�?�@�@�@�@�@�@�@�@�����
A���� !� +
?�T�%;�<R�%S� +
?�*?��=�>�>�>�5�5�5�5�5�5� -�
�%(�"#�&)�"%�),�%'� �"#�	!� 	!� 	!�I� "$�J�.2�j�j��.I�.I� 1� 1�*�	�7�)8��)C�_�U\�E]���)/��Y�)?���W�AU���!�
�
�g�w�5�%�.�AQ�[`�
�a�a�a�!*�!2�!2�5�!9�!9�� .�u�f� =� =��"�)�)�%�0�0�0�0� �M�M�/�6�:�:�:�09�D�N�9�-� &0�)+���)<�)<�(*��z�(:�(:�.7�.L�&5�'� '�O� 9H�D�*�9�5��m���
�8K�8K�m�m�m�UW�U[�\f�Ug�Ug�m�m�m�n�n�n�n�� � ?� ?� ?��=�!�=�=�>�>�>�>�>�>�>�>�����?���� �%� 
Q��
�(,�(>�(D�(D�(F�(F� A� A�$�J��&�$�.�.�"�)�)�$��*?�@�@�@��� Q�(*��
�(;�(;�%�$'�
�O�O�M�=N�D�&�'9�:�:G�D�&��7��[�DU�[�[�[�\�\�\��O�M�O�O�O�P�P�P� �I�K�K�*�4�M��V�-�V�V�V�V�W�W�W��4��� 	� 	� 	��B�q�B�B�C�C�C���~�.�.�.�!�3�.�.�.��5�5�5�5�5�����	���s�   �AT5 �+T5 �+T5 �3A*T5 �*T5 �
EJ �T5 �
K�(J?�:T5 �?K�T5 �EP: �9T5 �:
Q �Q�T5 �Q � CT5 �5
U1�?'U,�,U1c                 �	  �� 	 t          j        �j        ��  �        }d�j        v r�d         �                    d�  �        �                    �   �         }�d         |dz   z  |d<   |d         �                    d�  �        |d<   �d         �                    �   �         �                    d�  �        |d<   �d         �                    d	�  �        �                    d�  �        |d
<   �d         �                    d�  �        �                    d�  �        |d<   t          �fd
�dD �   �         �  �        rW�d         �d         dz   z  |d<   �d         �d         dz   z  |d<   �d         �d         z   �d         z   �d         z   d	z  |d<   g d�}|D ]P}|�j        v rE|dk    r�|         dz  |d|� �<   �#d|v r�|         |d|� �<   �6�|         �d         dz   z  |d|� �<   �Qg d�}|D ],}|�j        v r!�|         �                    d�  �        |d|� �<   �-d�j        v r��d         �                    d�  �        �                    �   �         }�d         |dz   z  |d<   |d         �                    d�  �        |d<   d�j        v r�d         �                    d�  �        |d <   | j	        d!         r3g d"�}|D ],}|�j        v r!�|         �                    d�  �        |d#|� �<   �-t          �j        d$�  �        r�t          j        d%t          j
        z  �j        j        z  dz  �  �        |d&<   t          j        d%t          j
        z  �j        j        z  dz  �  �        |d'<   �j        j        d(z  |d)<   �j        j        d*k    �                    t$          �  �        |d+<   d�j        v rGd,D ]D}	�d         �                    |	�  �        �d         dz   z  }
|
�                    d�  �        |d-|	� �<   �E|�                    d�  �        }|�                    t          j        t          j         gd�  �        }|j        r"t/          d.�  �         t          j        �   �         S t/          d/t1          |j        �  �        � d0t1          |�  �        � d1��  �         |S # t2          $ r/}t/          d2|� ��  �         t          j        �   �         cY d3}~S d3}~ww xY w)4z"Erstelle korrigierte Features V9.0�r  r�   ra   r  �price_normalizedrq   r   �price_change_1h�   �price_change_4h�   �price_change_24hc              3   �*   �K  � | ]
}|�j         v V � �d S r  r  r  s     �rR   r  zTUltimateBitcoinTradingSystemV9Fixed._create_corrected_features_v9.<locals>.<genexpr>�  r  rT   )r�   r�   r�   r�   r�   r�   �high_low_ratior�   �close_open_ratio�ohlc_avg)�SMA_10�SMA_20�EMA_10�EMA_20r!  r$  r&  r(  r)  r*  r+  r!  g      Y@�
indicator_r+  )�
Volatility_10�
Volatility_20r,  �vol_r�   r
  �volume_normalizedr8  �volume_ratior   )�Trend_5�Trend_10�Trend_20�
Momentum_5�Momentum_10�Momentum_20�trend_�hourri   �hour_sin�hour_cosg      @�day_of_weekr0  �
is_weekend)r]   ri   r  �
close_lag_u&   ⚠️ Keine Features nach BereinigungzKorrigierte Features erstellt: z Features, z Samplesz"FEHLER bei korrigierten Features: N)r9   r:   r  r  rB  r�   rF  r?  r  rC   �hasattrr�   �sin�pir�  �cos�	dayofweekrI  rJ  rA  �replace�infr�   rM   rO   r�   )rQ   r�   r�  �sma_50�tech_indicators�	indicator�vol_indicators�
volume_sma�trend_indicators�lag�lag_featurer�   s    `          rR   r�  zAUltimateBitcoinTradingSystemV9Fixed._create_corrected_features_v9�  s�  �� �V	"��|�"�(�3�3�3�H� �"�*�$�$��G��,�,�R�0�0�5�5�7�7��/1�'�{�f�u�n�/M��+�,�/7�8J�/K�/R�/R�SV�/W�/W��+�,� /1��k�.D�.D�.F�.F�.M�.M�a�.P�.P��*�+�.0��k�.D�.D�Q�.G�.G�.N�.N�q�.Q�.Q��*�+�/1�'�{�/E�/E�b�/I�/I�/P�/P�QR�/S�/S��+�,��Q�Q�Q�Q�0P�Q�Q�Q�Q�Q� 
_�-/��Z�2�e�9�u�;L�-M��)�*�/1�'�{�b��j�5�>P�/Q��+�,�(*�6�
�R��Z�(?�"�U�)�(K�b�QX�k�(Y�]^�'^���$�Q� Q� Q�O� -� 	
c� 	
c�	���
�*�*� �E�)�)�=?�	�]�U�=R��!9�i�!9�!9�:�:�&�)�3�3�=?�	�]��!9�i�!9�!9�:�:� >@�	�]�b�QX�k�\a�Na�=b��!9�i�!9�!9�:�� G�F�F�N�+� 
K� 
K�	���
�*�*�35�i�=�3G�3G��3J�3J�H�/�I�/�/�0�� �2�:�%�%���\�1�1�"�5�5�:�:�<�<�
�02�8��
�U�@R�0S��,�-�08�9L�0M�0T�0T�UX�0Y�0Y��,�-�!�R�Z�/�/�/1�.�/A�/H�/H��/M�/M�H�^�,� �%�&7�8� 
Q�#r�#r�#r� �!1� Q� Q�I� �B�J�.�.�9;�I��9M�9M�a�9P�9P��!5�)�!5�!5�6�� �r�x��(�(� 
O�')�v�a�"�%�i�"�(�-�.G�"�.L�'M�'M���$�')�v�a�"�%�i�"�(�-�.G�"�.L�'M�'M���$�*,�(�*<�s�*B���'�*,�(�*<��*A�)I�)I�#�)N�)N���&� �"�*�$�$�$� K� K�C�"$�W�+�"3�"3�C�"8�"8�B�w�K�%�<O�"P�K�3>�3E�3E�c�3J�3J�H�/�#�/�/�0�0�  ���q�)�)�H�  �'�'���"�&��(9�1�=�=�H� �~� 
&��>�?�?�?��|�~�~�%��m�C��8H�4I�4I�m�m�VY�Zb�Vc�Vc�m�m�m�n�n�n��O��� 	"� 	"� 	"��:�q�:�:�;�;�;��<�>�>�!�!�!�!�!�!�����	"���s$   �Q
R �6R �
S�$R<�6S�<Sc                 �  � 	 d|j         vrt          j        �   �         S |d         �                    d�  �        }|d         }||k    �                    t
          �  �        }|�                    �   �         }t          dt          |�  �        � d��  �         |S # t          $ r/}t          d|� ��  �         t          j        �   �         cY d}~S d}~ww xY w)uC   Erstelle korrigiertes Target V9.0 (BINÄR für bessere Genauigkeit)r�   r�   zKorrigiertes Target erstellt: u    Samples (binär)z FEHLER bei korrigiertem Target: N)
r  r9   �SeriesrA  rI  rJ  r�   rM   rO   r�   )rQ   r�   �future_pricer�   r�  r�   s         rR   r�  z?UltimateBitcoinTradingSystemV9Fixed._create_corrected_target_v9  s�   � �	��b�j�(�(��y�{�{�"� �g�;�,�,�R�0�0�L��w�K�M� #�]�2�:�:�3�?�?�F� �]�]�_�_�F��Q�3�v�;�;�Q�Q�Q�R�R�R��M��� 	� 	� 	��8�Q�8�8�9�9�9��9�;�;�����������	���s#   �B �A6B �
C�$C	�C�	Cc                 ��  � 	 t          d�  �         t          d�  �         t          d�  �         t          d�  �         t          j        �   �         }| xj        dz
  c_        | �                    �   �         }|j        rt          d�  �        �|d         j        d         }t          dt          |�  �        � d	|d
���  �         t          | j        �  �        dk    s| j        dz  dk    r+| �	                    |�  �        }t          d
|rdnd� ��  �         | �
                    |�  �        }t          j        �   �         |z
  }| j        t          j        �   �         �
                    �   �         |t          |�  �        || j        �                    dd�  �        || �                    �   �         t#          | j        �                    �   �         �  �        dd�
}| j        �                    |�  �         || _        | j        dxx         dz
  cc<   | j        dxx         dz
  cc<   | j        dxx         |z
  cc<   |�                    dd�  �        dk    r4t/          d| j        �                    dd�  �        dz   �  �        | j        d<   t          d| j        � d|d�d ��  �         t          d!|�                    d"d#�  �        � d$|�                    dd�  �        d%�d&��  �         t          d'|d
���  �         t          d(| j        �                    dd�  �        d%���  �         t          d)|d*         � d+t          | j        �  �        � ��  �         |S # t
          $ r�}t          d,|� ��  �         | j        d-xx         dz
  cc<   | j        t          j        �   �         �
                    �   �         t1          |�  �        d.t3          �   �         v rt          j        �   �         |z
  nddd/d0dd1�d2d3�cY d4}~S d4}~ww xY w)5u�   
        KORRIGIERTER PROGNOSE-SCAN V9.0
        ===============================
        Führt korrigierten Prognose-Scan durch (ALLE FEHLER BEHOBEN)
        z<============================================================z)STARTE KORRIGIERTEN PROGNOSE-SCAN V9.0...z#Alle identifizierten Fehler behobenr]   u2   Keine korrigierten Marktdaten für Scan verfügbarr�   r�   zAktuelle Daten: z Punkte, Preis: $r^   r   r0  z
ML-Training: u   ✅ Erfolgreichu   ❌ Fehlgeschlagenr/   r(   r|   )
�scan_idr�   �	scan_time�data_pointsr�   r/   �
predictionr@   �advanced_features_usedr�   r*   r+   r1   �
confidencer�   �ffffff�?r0   g      �?g{�G�z�?u    ✅ Korrigierter Prognose-Scan #z abgeschlossen in r�   r�   u   📊 PROGNOSE: �signal�N/A�
 (Konfidenz: r�   r�   u   💰 AKTUELLER PREIS: $u   📈 DATENQUALITÄT: u   🎯 ERWEITERTE FEATURES: r�  r4   u1   ❌ FEHLER beim korrigierten Prognose-Scan V9.0: r2   �scan_start_timer�   �FEHLER)r�  r�  zv9.0_error_fixed)r�  r�   r�   r�  r�  r�   r�  r�   N)rM   r�   rE   r�   r�   r�   r�   rO   r?   r�  �"_calculate_corrected_prediction_v9r   r7   rI   rL   r�   �+_get_corrected_model_performance_summary_v9rJ   rC   rK   rD   r�   rF   r�   r�   r�   )	rQ   r�  r�   r�   �training_success�prediction_resultr�  �scan_resultr�   s	            rR   � run_corrected_prediction_scan_v9zDUltimateBitcoinTradingSystemV9Fixed.run_corrected_prediction_scan_v9+  s�  � �M	��(�O�O�O��=�>�>�>��7�8�8�8��(�O�O�O�"�i�k�k�O�����"��� �2�2�4�4�B��x� 
V�� T�U�U�U��w�K�,�R�0�M��S�S��W�W�S�S�}�S�S�S�T�T�T� �4�>�"�"�a�'�'�4�+<�q�+@�A�+E�+E�#'�#C�#C�B�#G�#G� ��g�;K�&e�&7�&7�Qe�g�g�h�h�h� !%� G� G�� K� K�� �	���o�5�I�  �,�%�\�^�^�5�5�7�7�&�"�2�w�w�!.�%)�%7�%;�%;�<O�QT�%U�%U�/�%)�%U�%U�%W�%W�*-�d�.D�.K�.K�.M�.M�*N�*N�'�� �K� 
��$�$�[�1�1�1�$/�D�!� 
��}�-�-�-��2�-�-�-���1�2�2�2�a�7�2�2�2���4�5�5�5��B�5�5�5� !�$�$�\�1�5�5��;�;�<?���&�*�*�+@�$�G�G�$�N�=P� =P��"�#8�9� 
�j�T�5F�j�j�Zc�j�j�j�j�k�k�k��  G�$5�$9�$9�(�E�$J�$J�  G�  G�Yj�Yn�Yn�o{�}~�Y�Y�  G�  G�  G�  G�  
H�  
H�  
H��@�M�@�@�@�A�A�A��^�$�*<�*@�*@�AT�VW�*X�*X�^�^�^�_�_�_��t�{�;S�/T�t�t�WZ�[_�[q�Wr�Wr�t�t�u�u�u����� 	� 	� 	��I�a�I�I�J�J�J���~�.�.�.�!�3�.�.�.�  �,�%�\�^�^�5�5�7�7��Q���>O�SY�S[�S[�>[�>[�T�Y�[�[�?�:�:�ab� �!'�)1��E�E�-�	� 	� 	
� 	
� 	
� 	
� 	
� 	
�����	���s   �MM �
O)�BO$�O)�$O)c                 �  � 	 t          d�  �         |d         j        d         }d}d}| j        �rM	 d}d}| j        �                    �   �         D ].\  }}|| j        v r |�                    dd�  �        }	|	|k    r|	}|}�/|r�| j        |         }
| �                    |�  �        }|j        s�|j        dd�         j        }| j	        �
                    |�  �        }
t          |
d	�  �        r9|
�                    |
�  �        d         }t          |�  �        d
k    r|d
         nd}n|
�                    |
�  �        d         }|}t          d|d�d
|� d��  �         n)# t          $ r}t          d|� ��  �         Y d}~nd}~ww xY w| �                    |�  �        }| j        r|dz  |dz  z   }n|}|dk    rd}d|dz
  dz  z   }n.|dk     rd}dd|z
  dz  z   }nd}dt#          |dz
  �  �        dz  z   }t%          dt'          d|�  �        �  �        }| j        �                    dd�  �        }|dk    r|d|dz  z   z  }||||||t+          j        �   �         �                    �   �         |t1          | j        �                    �   �         �  �        d�	}t          d|� d|d�d��  �         |S # t          $ rX}t          d |� ��  �         ddddd!dt+          j        �   �         �                    �   �         d"t5          |�  �        d#�	cY d}~S d}~ww xY w)$z=Berechne korrigierte Vorhersage V9.0 (KONFIDENZ 0.0% BEHOBEN)z'Berechne korrigierte Vorhersage V9.0...r�   r�   rr   zTechnische AnalyseNrt  r   �
predict_probar]   zML-Vorhersage: r_   z
 (Modell: r�   zML-Vorhersage Fehler: r�   rs   r�   �KAUFENrq   g�������?�	VERKAUFEN�HALTENr�   r�  r/   rt   )	r�  r�  �
ml_prediction�technical_score�
model_used�data_qualityr�   r�   �advanced_features_activezKorrigierte Vorhersage: r�  r�   z$FEHLER bei korrigierter Vorhersage: �Fallbackr�   )	r�  r�  r�  r�  r�  r�  r�   r�   r�   )rM   r�   r?   r@   r�   r�   r�  r�   rK   rA   �	transformr�  r�  rO   r�  r�   �'_calculate_corrected_technical_score_v9r�   r�   r�   rL   r   r7   rI   rJ   rC   r�   )rQ   r�   r�   r�  r�  �best_model_name�
best_scorer�  �performancer�  �modelr�  �latest_featuresr�  �ml_probar�   r�  �combined_predictionr�  r�  r�  r�  s                         rR   r�  zFUltimateBitcoinTradingSystemV9Fixed._calculate_corrected_prediction_v9�  s�  � �c	��;�<�<�<��w�K�,�R�0�M�  �M�-�J��~� !
8� 8�&*�O�!#�J�37�3I�3O�3O�3Q�3Q� =� =�/�
�K�%���7�7�$/�O�O�O�Q�$G�$G�E�$�z�1�1�-2�
�2<���&� e� $��� ?�� $(�#E�#E�b�#I�#I��'�~� e�.6�m�B�C�C�.@�.G�O�.2�k�.C�.C�O�.T�.T�O�  '�u�o�>�>� R�+0�+>�+>��+O�+O�PQ�+R��?B�8�}�}�q�?P�?P�����VY�
�
�05�
�
�o�0N�0N�q�0Q�
�)8�J�!�"c�M�"c�"c�"c�Q`�"c�"c�"c�d�d�d��� � 8� 8� 8��6�1�6�6�7�7�7�7�7�7�7�7�����8���� #�J�J�2�N�N�O� �~� 
6�'4�s�':��QT�?T�&U�#�#�&5�#� #�S�(�(�!�� �$7�#�$=��#D�D�
�
�$�s�*�*�$�� �C�*=�$=��#D�D�
�
�!�� �3�':�S�'@�#A�#A�C�#G�G�
� �S�#�d�J�"7�"7�8�8�J�  �-�1�1�2E�s�K�K�L��a����s�\�C�%7�7�8�
� !�(�!4�#2�(� ,�%�\�^�^�5�5�7�7�!.�,/��0F�0M�0M�0O�0O�,P�,P�
� 
�J� 
�S�V�S�S�*�S�S�S�S�T�T�T����� 	� 	� 	��<��<�<�=�=�=�"�!�!$�#&�(� #�%�\�^�^�5�5�7�7�!'��Q���
� 
� 

� 

� 

� 

� 

� 

�����	���sI   �.J �D"E �J �
E:�E5�0J �5E:�:D$J �
L�)A
K<�6L�<Lc                 �  � 	 d}d|j         v rT|d         j        d         }t          j        |�  �        s-|dk    r|dz  }n!|dk     r|dz
  }nd|cxk    rdk    rn n|d	z
  }d
|j         v rhd|j         v r_|d
         j        d         }|d         j        d         }t          j        |�  �        s%t          j        |�  �        s||k    r|dz
  }n|dz  }d
|j         v r>|d
         j        d         }t          j        |�  �        s|dk    r|dz  }n|dk     r|dz
  }d|j         v rK|d         j        d         }|d         j        d         }t          j        |�  �        s||k    r|d	z
  }n|d	z  }| j        d         r^d}	d}
dD ]A}d|� �}||j         v r1||         j        d         }
t          j        |
�  �        s
|	|
z
  }	|
dz
  }
�B|
dk    r|	|
z  }||dz
  dz  z
  }d|j         v r2|d         j        d         }t          j        |�  �        s|d	k    r|dz  }t          dt
          d|�  �        �  �        S # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)z,Berechne korrigierten technischen Score V9.0rr   r!  r�   �F   g333333�?r3  �(   r   皙�����?r$  r&  r�   r+  r�   rt   r�  r�   r   r   r/  r1  r]   r�  g�������?r(   rq   z+FEHLER bei korrigiertem technischem Score: N)	r  r�   r9   rM  rC   r�   r�   r�   rM   )rQ   r�   r�  �rsi�macd�macd_signal�bb_positionr�   �sma_20�trend_score�trend_countr"   �	trend_col�	trend_val�	avg_trend�
volatilityr�   s                    rR   r�  zKUltimateBitcoinTradingSystemV9Fixed._calculate_corrected_technical_score_v9�  s  � �H	��E� ��
�"�"���i�n�R�(���w�s�|�|� &��R�x�x���
����r�����
����s�����b��������
�� ���#�#�
���(C�(C��&�z��r�*�� ��/�4�R�8���w�t�}�}� %�R�W�[�-A�-A� %��k�)�)���������� ��
�*�*� ��/�4�R�8���w�{�+�+� %�"�S�(�(������$�s�*�*����� �2�:�%�%� "�7�� 0�� 4�
��H��*�2�.���w�v��� &�$�v�-�-���
�����
�� �%�&7�8� 
5�����)� -� -�F� 1�� 1� 1�I� �B�J�.�.�$&�y�M�$6�r�$:�	�!�w�y�1�1� -�'�9�4�K�'�1�,�K����?�?� +�k� 9�I��i�#�o��4�4�E� �"�*�,�,���0�5�b�9�
��w�z�*�*� %�!�D�(�(������s�C��U�O�O�,�,�,��� 	� 	� 	��C��C�C�D�D�D��3�3�3�3�3�����	���s   �H.H1 �1
I�;I�Ic                 ��  � � 	 � j         sddd�S t          � j         �                    �   �         � fd���  �        }t          � j         �  �        |� j         |         �                    dd�  �        t          j        d� � j         �                    �   �         D �   �         �  �        t          � j         �                    �   �         �  �        d�}|S # t          $ r}ddt          |�  �        d	�cY d
}~S d
}~ww xY w)z8Hole korrigierte Modell-Performance Zusammenfassung V9.0r   r(   )�models_countr-   c                 �F   �� �j         |          �                    dd�  �        S )Nrt  r   )r@   r�   )�krQ   s    �rR   �<lambda>zaUltimateBitcoinTradingSystemV9Fixed._get_corrected_model_performance_summary_v9.<locals>.<lambda>:  s    �� ��)?��)B�)F�)F��XY�)Z�)Z� rT   )�keyrt  c                 �:   � g | ]}|�                     d d�  �        ��S )rt  r   )r�   )rl   �ms     rR   rp   zcUltimateBitcoinTradingSystemV9Fixed._get_corrected_model_performance_summary_v9.<locals>.<listcomp>@  s&   � �,p�,p�,p�1�Q�U�U�?�A�-F�-F�,p�,p�,prT   )r
  �
best_modelr-   �average_accuracy�models_available)r
  r-   r�   N)r@   r�   �keysrO   r�   r�   r�   rK   r�   r�   r�   )rQ   r  �summaryr�   s   `   rR   r�  zOUltimateBitcoinTradingSystemV9Fixed._get_corrected_model_performance_summary_v93  s'  �� �	N��)� 
A�()�C�@�@�@��T�3�8�8�:�:�Z�Z�Z�Z�\� \� \�J� !$�D�$:� ;� ;�(�!%�!7�
�!C�!G�!G��Y\�!]�!]�$&�G�,p�,p�PT�Pf�Pm�Pm�Po�Po�,p�,p�,p�$q�$q�$(��)?�)D�)D�)F�)F�$G�$G�� �G� �N��� 	N� 	N� 	N�$%��c�!�f�f�M�M�M�M�M�M�M�M�����	N���s#   �C �B<C �
C3�C.�(C3�.C3c                 �N  � 	 t          d�  �         t          j        t          j        �   �         t          d��  �        z
  t          j        �   �         d��  �        }d}g }|dz  }t
          |�  �        D �]>\  }}|t          |�  �        dz
  k    r|}nV||z
  |z  d	z  }t          j	        �
                    d
d�  �        }||z   }	t          dt          d
|	�  �        �  �        }	|d|	z   z  }t          j	        �
                    dd�  �        }
|d|
z   z  }|d|
z
  z  }|dt          j	        �
                    d
d�  �        z   z  }
|�                    t          |t          ||
�  �        �  �        t          |||
�  �        t          |||
�  �        |t          j	        �
                    dd�  �        d��  �         ��@t          j        ||��  �        }t          dt          |�  �        � d��  �         |S # t           $ r/}t          d|� ��  �         t          j        �   �         cY d}~S d}~ww xY w)z)Generiere korrigierte Fallback-Daten V9.0z,Generiere korrigierte Fallback-Daten V9.0...�   )�days�H)�start�end�freqr�   gףp=
��?r]   r   r   g����MbP?r�   r�   g����Mb@?r�   g-C��6*?i�  r   r�   r�  z&Korrigierte Fallback-Daten generiert: r�   z(FEHLER bei korrigierten Fallback-Daten: N)rM   r9   �
date_ranger   r7   r   r�   rO   r�   r�   r�   r�   r�   �uniformr�   r:   r�   )rQ   �dates�
base_price�
price_datar�   rm   �date�mean_reversion�
random_change�total_changer  �high�low�
open_pricer�   r�   s                   rR   r�   zHUltimateBitcoinTradingSystemV9Fixed._generate_corrected_fallback_data_v9I  sV  � �.	"��@�A�A�A� �M�������9J�9J�9J�(J�%-�\�^�^�#�?� ?� ?�E�  �J� �J�&��.�M�$�U�+�+� 
� 
���4���E�
�
�Q��&�&�$.�M�M� '1�=�&@�J�%N�QU�%U�N�$&�I�$4�$4�Q��$>�$>�M�#1�M�#A�L�#&�v�s�5�,�/G�/G�#H�#H�L�!�a�,�&6�7�M�  �Y�.�.�v�u�=�=�
�$��J��7��#�q�:�~�6��*�a�"�)�2B�2B�1�f�2M�2M�.M�N�
��!�!���S��z�%:�%:�;�;���m�Z�@�@��s�M�:�>�>�*� �i�/�/��d�;�;�#� #� � � � � ��j��6�6�6�B��P�3�r�7�7�P�P�P�Q�Q�Q��I��� 	"� 	"� 	"��@�Q�@�@�A�A�A��<�>�>�!�!�!�!�!�!�����	"���s   �G(G+ �+
H$�5$H�H$�H$N)�__name__�
__module__�__qualname__�__doc__rS   �dictr�   r   r�   r�   r9   r:   r�   r�   r�   r�   r�   r�   r�   �boolr�  r�  r�  r�  r�  r�  r�  r�  r�   rk   rT   rR   r   r   6   s�  � � � � � �� �GE� GE� GE�RIk�� Ik� Ik� Ik� Ik�V]��c�� ]�t� ]� ]� ]� ]�~EC�b�l� EC� EC� EC� EC�N"�� "� "� "� "�H?�b�l� ?� ?� ?� ?�$ �r�|�  �TY�  �^`�^j�  �  �  �  �D>�b�l� >�r�|� >� >� >� >�@L�R�\� L�b�l� L� L� L� L�\[�b�l� [�t� [� [� [� [�zX"��� X"��� X"� X"� X"� X"�t�b�l� �r�y� � � � �.S�$� S� S� S� S�je�R�\� e�d� e� e� e� e�NJ�"�,� J�5� J� J� J� J�XN�T� N� N� N� N�,0"�b�l� 0"� 0"� 0"� 0"� 0"� 0"rT   r   c                  ��  � t          d�  �         t          d�  �         t          d�  �         t          d�  �         	 t          �   �         } | �                    �   �         }t          d�  �         t          d�  �         t          d�  �         t          d�  �         t          d|�                    dd	�  �        d
���  �         t          d|�                    dd	�  �        d
���  �         t          d|�                    dd	�  �        � ��  �         t          d|�                    dd	�  �        d�d��  �         |�                    di �  �        }t          d�  �         t          d|�                    dd�  �        � ��  �         t          d|�                    dd	�  �        d
���  �         t          d|�                    dd	�  �        d���  �         t          d|�                    dd	�  �        d���  �         t          d |�                    d!d�  �        � ��  �         |�                    d"i �  �        }t          d#�  �         t          d$|�                    d%d	�  �        � ��  �         t          d&|�                    d'd	�  �        d
���  �         t          d(|�                    d)d	�  �        d
���  �         t          d*�  �         t          d+|�                    d,d	�  �        � d-t	          | j        �  �        � ��  �         | j        �                    �   �         D ] \  }}|rd.nd/}t          d0|� d1|� ��  �         �!t          d2�  �         t          d3�  �         t          d4�  �         t          d5�  �         t          d6�  �         t          d7�  �         t          d8�  �         t          d9�  �         |S # t          $ r5}t          d:|� ��  �         d	d;l}|�	                    �   �          Y d;}~d;S d;}~ww xY w)<u=   Hauptfunktion für Ultimate Bitcoin Trading System V9.0 FIXEDzP================================================================================z:ULTIMATE BITCOIN TRADING SYSTEM V9.0 - FEHLERFREIE EDITIONu=   ALLE IDENTIFIZIERTEN FEHLER BEHOBEN • VOLLSTÄNDIG GETESTETzQ
================================================================================zBULTIMATE BITCOIN TRADING SYSTEM V9.0 - KORRIGIERTE SCAN-ERGEBNISSEz
KORRIGIERTE MARKTDATEN:z   Bitcoin-Preis: $r�   r   r^   u      Datenqualität: r/   r�   z   Datenpunkte: r�  z   Scan-Zeit: r�  r�   r�   r�  z
KORRIGIERTE PROGNOSE:z   Signal: r�  r�  z   Konfidenz: r�  z   ML-Prediction: r�  r_   z   Technical Score: r�  z   Modell: r�  r@   z 
KORRIGIERTE MODELL-PERFORMANCE:z   Modelle: r
  z   Beste Genauigkeit: r-   z   Durchschnitt: r  z
ERWEITERTE FEATURES:z   Features aktiv: r�  r4   u   ✅u   ⚠️z   � z
FEHLERBEHEBUNGEN:u(      ✅ DataFrame Ambiguity Error behobenu)      ✅ Negative ML-Genauigkeit korrigiertu'      ✅ Live-Daten Integration repariertu      ✅ Datenqualität maximiertu&      ✅ Vorhersage-Berechnung optimiertu&      ✅ Konfidenz-Berechnung korrigiertuM   
🏆 ULTIMATE BITCOIN TRADING SYSTEM V9.0 - FEHLERFREIE EDITION ERFOLGREICH!z8FEHLER beim Ultimate Bitcoin Trading System V9.0 FIXED: N)
rM   r   r�  r�   rO   rC   r�   r�   �	traceback�	print_exc)	�systemrg   r�  �
model_perf�feature�active�statusr�   r4  s	            rR   �,run_ultimate_bitcoin_trading_system_v9_fixedr;  |  s*  � �	�(�O�O�O�	�
F�G�G�G�	�
I�J�J�J�	�(�O�O�O�6�4�6�6�� �8�8�:�:�� 	�o����
�R�S�S�S�
�h����
�*�+�+�+�
�I�F�J�J���$B�$B�I�I�I�J�J�J�
�L�F�J�J�/B�A�$F�$F�L�L�L�M�M�M�
�?����M�1�!=�!=�?�?�@�@�@�
�@�v�z�z�+�q�9�9�@�@�@�@�A�A�A��Z�Z��b�1�1�
�
�(�)�)�)�
�=�J�N�N�8�U�;�;�=�=�>�>�>�
�D�z�~�~�l�A�>�>�D�D�D�E�E�E�
�K�:�>�>�/�1�#E�#E�K�K�K�L�L�L�
�O�Z�^�^�4E�q�%I�%I�O�O�O�P�P�P�
�A�J�N�N�<��?�?�A�A�B�B�B��Z�Z� 3�R�8�8�
�
�2�3�3�3�
�@�Z�^�^�N�A�>�>�@�@�A�A�A�
�O�z�~�~�o�q�'I�'I�O�O�O�P�P�P�
�M�*�.�.�1C�Q�"G�"G�M�M�M�N�N�N�
�'�(�(�(�
�m�F�J�J�/G��$K�$K�m�m�c�RX�Rj�Nk�Nk�m�m�n�n�n�%�7�=�=�?�?� 	,� 	,�O�G�V�$�2�U�U�(�F��*��*�*��*�*�+�+�+�+�
�$�%�%�%�
�9�:�:�:�
�:�;�;�;�
�8�9�9�9�
�0�1�1�1�
�7�8�8�8�
�7�8�8�8�
�_�`�`�`��
��� � � �
�L��L�L�M�M�M������������t�t�t�t�t�����	���s   �M6N5 �5
O4�?*O/�/O4�__main__)+r/  �yfinancer�   �pandasr9   �numpyr�   r�   r�   r   r   r�   �os�warnings�	threading�pickle�typingr   r   r   r   �filterwarnings�sklearn.preprocessingr
   r�  r   �sklearn.metricsr   r
   r   �sklearn.model_selectionr   r  �xgbrB   rM   �ImportError�scipyr   rP   r   r;  r,  rk   rT   rR   �<module>rL     s[  ��� � � � � � � � � � � � � � ���� ���� (� (� (� (� (� (� (� (� ���� 	�	�	�	� ���� � � � � 
�
�
�
� .� .� .� .� .� .� .� .� .� .� .� .� �� �� !� !� !� /� .� .� .� .� .� 2� 2� 2� 2� 2� 2� H� H� H� H� H� H� H� H� H� H� 3� 3� 3� 3� 3� 3�E�������	�E�
"�#�#�#�#��� E� E� E���	�E�
C�D�D�D�D�D�E����
H��������O�	�E�
 �!�!�!�!��� H� H� H��O�	�E�
F�G�G�G�G�G�H����C"� C"� C"� C"� C"� C"� C"� C"�L*=� =� =�~ �z���0�0�2�2�2�2�2� �s$   �,A> �>B�B�B+ �+C �?C 