#!/usr/bin/env python3
"""
🏆 ULTIMATE COMBINED MASTER - BITCOIN PREDICTION SYSTEM
Kombiniert die BESTEN Elemente aus ALLEN verfügbaren Scripts
Maximum Performance + Maximum Accuracy + Maximum Stability
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import (LSTM, GRU, Dense, Dropout, Bidirectional, 
                                   BatchNormalization, Input, Concatenate, Add)
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.optimizers import <PERSON>, RMSprop
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression, RFE
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
import time
import os
import warnings
import joblib
from scipy import stats
warnings.filterwarnings('ignore')

print("🏆 ULTIMATE COMBINED MASTER - BITCOIN PREDICTION SYSTEM")
print("=" * 70)
print("🚀 Kombiniert die BESTEN Elemente aus ALLEN Scripts")
print("💻 Maximum Performance + Accuracy + Stability")
print("=" * 70)

# ULTIMATE HARDWARE-OPTIMIERUNG (aus btc_ultimate_max_performance.py)
print(f"🚀 AKTIVIERE ULTIMATE PERFORMANCE MODE!")
print(f"💻 CPU-Kerne: {os.cpu_count()}")

# GPU-Konfiguration mit Memory Growth
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
            # Setze Memory Limit für Stabilität
            tf.config.experimental.set_virtual_device_configuration(
                gpu, [tf.config.experimental.VirtualDeviceConfiguration(memory_limit=4096)]
            )
        print(f"🎮 GPU ULTIMATE MODE: {len(gpus)} GPU(s) mit Memory Growth!")
    except RuntimeError as e:
        print(f"GPU-Konfiguration: {e}")
else:
    print("💻 CPU ULTIMATE MODE - Alle Kerne aktiviert!")

# EXTREME CPU-Optimierung (aus allen Scripts kombiniert)
tf.config.threading.set_intra_op_parallelism_threads(0)
tf.config.threading.set_inter_op_parallelism_threads(0)
os.environ['OMP_NUM_THREADS'] = str(os.cpu_count())
os.environ['TF_NUM_INTEROP_THREADS'] = str(os.cpu_count())
os.environ['TF_NUM_INTRAOP_THREADS'] = str(os.cpu_count())
os.environ['NUMEXPR_MAX_THREADS'] = str(os.cpu_count())

# ULTIMATE MASTER KONFIGURATION (Best of All Scripts)
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.80,           # Aus btc_final_ultimate.py (stabil)
    'validation_split': 0.15,      # Aus btc_ultimate_80percent_v2.py
    'test_split': 0.05,            # Kleine Test-Menge
    'look_back': 24,               # Aus btc_ultimate_prediction_model.py (optimal)
    'future_steps': 12,            # Aus btc_ultimate_80percent_v2.py
    'batch_size': 64,              # Kompromiss zwischen allen Scripts
    'epochs': 100,                 # Aus btc_final_ultimate.py
    'patience': 25,                # Aus btc_ultimate_80percent_v2.py
    'learning_rate': 0.0005,       # Aus btc_ultimate_80percent_v2.py
    'dropout_rate': 0.3,           # Aus btc_ultimate_80percent_v2.py
    'n_best_features': 15,         # Kompromiss zwischen allen
    'ensemble_size': 5,            # Aus btc_ultimate_max_performance.py
    'cross_validation_folds': 3,   # Aus btc_ultimate_80percent_v2.py
    'monte_carlo_runs': 500,       # Aus btc_ultimate_prediction_model.py
    'target_accuracy': 0.85        # Realistisches Ziel
}

class UltimateCombinedProcessor:
    """Ultimate Data Processor - Kombiniert beste Features aus allen Scripts"""
    
    def __init__(self):
        self.scalers = {}
        self.feature_selector = None
        self.selected_features = None
    
    def load_and_engineer_ultimate_features(self):
        """Ultimate Feature Engineering - Best of All Scripts"""
        print("\n📊 ULTIMATE FEATURE ENGINEERING")
        print("-" * 50)
        
        df = pd.read_csv(CONFIG['data_file'])
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        print(f"📈 Daten: {len(df)} Punkte")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
        
        # Basis OHLCV
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # === 1. PREIS-FEATURES (aus btc_ultimate_max_performance.py) ===
        print("   🔧 Erstelle Preis-Features...")
        
        # Returns Familie
        features['returns'] = df['close'].pct_change()
        features['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        features['squared_returns'] = features['returns'] ** 2
        
        # Preis-Ratios
        features['hl_ratio'] = df['high'] / df['low']
        features['co_ratio'] = df['close'] / df['open']
        features['hc_ratio'] = df['high'] / df['close']
        features['lc_ratio'] = df['low'] / df['close']
        
        # Candlestick Features
        features['body'] = abs(df['close'] - df['open'])
        features['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
        features['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
        features['body_ratio'] = features['body'] / (df['high'] - df['low'])
        
        # === 2. MOVING AVERAGES (aus btc_ultimate_80percent_v2.py) ===
        print("   📈 Erstelle Moving Averages...")
        
        # Optimale Perioden aus allen Scripts
        ma_periods = [5, 10, 20, 50]
        for period in ma_periods:
            if period <= len(df):
                # Simple und Exponential MA
                features[f'sma_{period}'] = df['close'].rolling(period).mean()
                features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
                
                # MA Ratios (wichtig für Trend-Erkennung)
                features[f'price_sma_{period}_ratio'] = df['close'] / features[f'sma_{period}']
                features[f'price_ema_{period}_ratio'] = df['close'] / features[f'ema_{period}']
                
                # MA Slopes (Trend-Stärke)
                features[f'sma_{period}_slope'] = features[f'sma_{period}'].diff()
                features[f'ema_{period}_slope'] = features[f'ema_{period}'].diff()
        
        # === 3. MOMENTUM INDIKATOREN (aus btc_ultimate_prediction_model.py) ===
        print("   ⚡ Erstelle Momentum-Indikatoren...")
        
        # RSI (Standard 14)
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        features['rsi_overbought'] = (features['rsi'] > 70).astype(int)
        features['rsi_oversold'] = (features['rsi'] < 30).astype(int)
        
        # MACD (Standard)
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        features['macd'] = ema_12 - ema_26
        features['macd_signal'] = features['macd'].ewm(span=9).mean()
        features['macd_histogram'] = features['macd'] - features['macd_signal']
        features['macd_cross'] = (features['macd'] > features['macd_signal']).astype(int)
        
        # Stochastic Oscillator
        low_min = df['low'].rolling(14).min()
        high_max = df['high'].rolling(14).max()
        features['stoch_k'] = 100 * ((df['close'] - low_min) / (high_max - low_min))
        features['stoch_d'] = features['stoch_k'].rolling(3).mean()
        
        # === 4. VOLATILITÄT (aus btc_final_ultimate.py) ===
        print("   📊 Erstelle Volatilitäts-Indikatoren...")
        
        # Bollinger Bands
        sma_20 = df['close'].rolling(20).mean()
        std_20 = df['close'].rolling(20).std()
        features['bb_upper'] = sma_20 + (std_20 * 2)
        features['bb_lower'] = sma_20 - (std_20 * 2)
        features['bb_width'] = (features['bb_upper'] - features['bb_lower']) / sma_20
        features['bb_position'] = (df['close'] - features['bb_lower']) / (features['bb_upper'] - features['bb_lower'])
        
        # ATR (Average True Range)
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        features['atr'] = true_range.rolling(14).mean()
        features['atr_percent'] = features['atr'] / df['close'] * 100
        
        # Realized Volatility
        features['realized_vol'] = features['log_returns'].rolling(20).std() * np.sqrt(24)
        
        # === 5. VOLUMEN INDIKATOREN ===
        print("   📦 Erstelle Volumen-Indikatoren...")
        
        # OBV (On-Balance Volume)
        obv = [0]
        for i in range(1, len(df)):
            if df['close'].iloc[i] > df['close'].iloc[i-1]:
                obv.append(obv[-1] + df['volume'].iloc[i])
            elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                obv.append(obv[-1] - df['volume'].iloc[i])
            else:
                obv.append(obv[-1])
        
        features['obv'] = obv
        features['obv_ema'] = features['obv'].ewm(span=20).mean()
        
        # Volume Profile
        features['volume_sma'] = df['volume'].rolling(20).mean()
        features['volume_ratio'] = df['volume'] / features['volume_sma']
        features['volume_spike'] = (features['volume_ratio'] > 2).astype(int)
        
        # VWAP (Volume Weighted Average Price)
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        features['vwap'] = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        features['vwap_ratio'] = df['close'] / features['vwap']
        
        # === 6. ZEIT-FEATURES ===
        print("   🕐 Erstelle Zeit-Features...")
        
        # Basis Zeit-Features
        features['hour'] = df.index.hour
        features['day_of_week'] = df.index.dayofweek
        features['day_of_month'] = df.index.day
        
        # Trigonometrische Transformationen (wichtig für Zyklizität)
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
        features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
        
        # Markt-Sessions
        features['asian_session'] = ((features['hour'] >= 0) & (features['hour'] < 8)).astype(int)
        features['european_session'] = ((features['hour'] >= 8) & (features['hour'] < 16)).astype(int)
        features['american_session'] = ((features['hour'] >= 16) & (features['hour'] < 24)).astype(int)
        
        # === 7. STATISTISCHE FEATURES ===
        print("   📊 Erstelle statistische Features...")
        
        # Rolling Statistics
        for period in [10, 20]:
            # Skewness und Kurtosis
            features[f'skew_{period}'] = features['returns'].rolling(period).skew()
            features[f'kurt_{period}'] = features['returns'].rolling(period).kurt()
            
            # Z-Score
            features[f'zscore_{period}'] = (df['close'] - df['close'].rolling(period).mean()) / df['close'].rolling(period).std()
            
            # Percentile Rank
            features[f'percentile_rank_{period}'] = df['close'].rolling(period).rank(pct=True)
        
        # === CLEANUP ===
        features = features.dropna()
        
        print(f"   ✅ {len(features.columns)} Ultimate Features erstellt")
        print(f"   📊 {len(features)} saubere Datenpunkte")
        
        return features

    def ultimate_feature_selection(self, features):
        """Ultimate Feature Selection - Kombiniert beste Methoden"""
        print("\n🧠 ULTIMATE FEATURE SELECTION")
        print("-" * 40)

        X = features.drop('close', axis=1)
        y = features['close']

        print(f"📊 Vor Selektion: {X.shape[1]} Features")

        # Kombiniere SelectKBest und RFE (aus btc_ultimate_max_performance.py)
        selector_kbest = SelectKBest(score_func=f_regression, k=CONFIG['n_best_features'])
        X_kbest = selector_kbest.fit_transform(X, y)

        # RFE mit Random Forest
        rf = RandomForestRegressor(n_estimators=50, random_state=42, n_jobs=-1)
        selector_rfe = RFE(rf, n_features_to_select=CONFIG['n_best_features'])
        X_rfe = selector_rfe.fit_transform(X, y)

        # Kombiniere beide Methoden
        kbest_features = set(X.columns[selector_kbest.get_support()])
        rfe_features = set(X.columns[selector_rfe.get_support()])

        # Intersection für die besten Features
        best_features = list(kbest_features.intersection(rfe_features))

        # Falls zu wenige, füge die besten aus beiden hinzu
        if len(best_features) < CONFIG['n_best_features']:
            remaining_needed = CONFIG['n_best_features'] - len(best_features)
            union_features = list(kbest_features.union(rfe_features))
            additional_features = [f for f in union_features if f not in best_features][:remaining_needed]
            best_features.extend(additional_features)

        self.selected_features = best_features[:CONFIG['n_best_features']]

        print(f"✅ Nach Selektion: {len(self.selected_features)} Features")

        # Feature Importance Scores
        feature_scores = selector_kbest.scores_[selector_kbest.get_support()]
        top_features = sorted(zip(self.selected_features, feature_scores), key=lambda x: x[1], reverse=True)

        print(f"\n🏆 TOP 10 FEATURES:")
        for i, (feature, score) in enumerate(top_features[:10]):
            print(f"   {i+1}. {feature}: {score:.2f}")

        # Erstelle finales DataFrame
        selected_df = X[self.selected_features].copy()
        selected_df['close'] = features['close']

        return selected_df

    def create_sequences_optimized(self, data, target, look_back):
        """Optimierte Sequenz-Erstellung"""
        X, y = [], []
        for i in range(look_back, len(data)):
            X.append(data[i-look_back:i])
            y.append(target[i])
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)

    def prepare_ultimate_data(self, features):
        """Ultimate Datenaufbereitung"""
        print("\n🔄 ULTIMATE DATENAUFBEREITUNG")
        print("-" * 35)

        # Feature Selection
        features_selected = self.ultimate_feature_selection(features)

        # Features und Target
        X = features_selected.drop('close', axis=1)
        y = features_selected['close'].values

        print(f"📊 Finale Features: {X.shape[1]}")
        print(f"📊 Samples: {len(y)}")

        # Ultimate Skalierung (aus btc_final_ultimate.py)
        print("   🔧 Ultimate Multi-Scaler...")

        # RobustScaler für Outlier-Resistenz
        robust_scaler = RobustScaler()
        X_robust = robust_scaler.fit_transform(X)

        # StandardScaler für Normalverteilung
        standard_scaler = StandardScaler()
        X_scaled = standard_scaler.fit_transform(X_robust)

        # Target Scaling
        target_scaler = StandardScaler()
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()

        # Sequenzen erstellen
        print(f"   📦 Erstelle Sequenzen (Look-back: {CONFIG['look_back']})...")
        X_seq, y_seq = self.create_sequences_optimized(X_scaled, y_scaled, CONFIG['look_back'])

        print(f"   ✅ {len(X_seq)} Sequenzen erstellt")
        print(f"   📐 Sequenz-Shape: {X_seq.shape}")

        # Speichere Scaler
        self.scalers['robust'] = robust_scaler
        self.scalers['standard'] = standard_scaler
        self.scalers['target'] = target_scaler

        return X_seq, y_seq

class UltimateCombinedModels:
    """Ultimate Model Builder - Beste Architekturen aus allen Scripts"""

    def __init__(self):
        self.models = {}

    def build_ultimate_lstm(self, input_shape):
        """Ultimate LSTM - Kombiniert beste LSTM Designs"""
        print("🧠 Baue Ultimate LSTM...")

        model = Sequential(name='UltimateLSTM')

        # Bidirectional LSTM Stack (aus btc_ultimate_80percent_v2.py)
        model.add(Bidirectional(
            LSTM(64, return_sequences=True, dropout=CONFIG['dropout_rate'],
                 recurrent_dropout=CONFIG['dropout_rate']),
            input_shape=input_shape
        ))
        model.add(BatchNormalization())

        model.add(Bidirectional(
            LSTM(32, return_sequences=False, dropout=CONFIG['dropout_rate'],
                 recurrent_dropout=CONFIG['dropout_rate'])
        ))
        model.add(BatchNormalization())

        # Dense Layers mit optimaler Regularisierung
        model.add(Dense(64, activation='relu'))
        model.add(BatchNormalization())
        model.add(Dropout(CONFIG['dropout_rate']))

        model.add(Dense(32, activation='relu'))
        model.add(Dropout(CONFIG['dropout_rate'] * 0.8))

        model.add(Dense(1))

        # Optimierter Optimizer (aus btc_ultimate_prediction_model.py)
        optimizer = Adam(learning_rate=CONFIG['learning_rate'])
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae'])

        print(f"   ✅ Ultimate LSTM: {model.count_params():,} Parameter")
        return model

    def build_ultimate_gru(self, input_shape):
        """Ultimate GRU - Schnell und effektiv"""
        print("⚡ Baue Ultimate GRU...")

        model = Sequential(name='UltimateGRU')

        # GRU Stack (aus btc_ultimate_max_performance.py)
        model.add(GRU(64, return_sequences=True, dropout=CONFIG['dropout_rate'], input_shape=input_shape))
        model.add(BatchNormalization())

        model.add(GRU(32, return_sequences=False, dropout=CONFIG['dropout_rate']))
        model.add(BatchNormalization())

        # Dense Layers
        model.add(Dense(64, activation='relu'))
        model.add(BatchNormalization())
        model.add(Dropout(CONFIG['dropout_rate']))

        model.add(Dense(32, activation='relu'))
        model.add(Dropout(CONFIG['dropout_rate'] * 0.8))

        model.add(Dense(1))

        optimizer = RMSprop(learning_rate=CONFIG['learning_rate'])
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae'])

        print(f"   ✅ Ultimate GRU: {model.count_params():,} Parameter")
        return model

    def build_ultimate_hybrid(self, input_shape):
        """Ultimate Hybrid - Kombiniert LSTM + GRU"""
        print("🔥 Baue Ultimate Hybrid...")

        inputs = Input(shape=input_shape, name='sequence_input')

        # LSTM Branch
        lstm_branch = LSTM(32, return_sequences=True, dropout=CONFIG['dropout_rate'])(inputs)
        lstm_branch = BatchNormalization()(lstm_branch)
        lstm_branch = LSTM(16, return_sequences=False, dropout=CONFIG['dropout_rate'])(lstm_branch)
        lstm_branch = Dense(32, activation='relu')(lstm_branch)

        # GRU Branch
        gru_branch = GRU(32, return_sequences=True, dropout=CONFIG['dropout_rate'])(inputs)
        gru_branch = BatchNormalization()(gru_branch)
        gru_branch = GRU(16, return_sequences=False, dropout=CONFIG['dropout_rate'])(gru_branch)
        gru_branch = Dense(32, activation='relu')(gru_branch)

        # Combine branches
        combined = Concatenate()([lstm_branch, gru_branch])

        # Final processing
        x = Dense(64, activation='relu')(combined)
        x = BatchNormalization()(x)
        x = Dropout(CONFIG['dropout_rate'])(x)

        x = Dense(32, activation='relu')(x)
        x = Dropout(CONFIG['dropout_rate'] * 0.8)(x)

        outputs = Dense(1)(x)

        model = Model(inputs=inputs, outputs=outputs, name='UltimateHybrid')

        optimizer = Adam(learning_rate=CONFIG['learning_rate'] * 1.2)
        model.compile(optimizer=optimizer, loss='huber', metrics=['mae'])

        print(f"   ✅ Ultimate Hybrid: {model.count_params():,} Parameter")
        return model

class UltimateCombinedTrainer:
    """Ultimate Training System"""

    def __init__(self):
        self.models = {}
        self.results = {}

    def train_ultimate_model(self, model, X_train, y_train, X_val, y_val, model_name):
        """Ultimate Training mit besten Callbacks"""
        print(f"\n🎯 Ultimate Training: {model_name}")
        print("-" * 35)

        # Ultimate Callbacks (aus allen Scripts kombiniert)
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=CONFIG['patience'],
                restore_best_weights=True,
                verbose=1,
                mode='min'
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=CONFIG['patience']//2,
                min_lr=1e-7,
                verbose=1,
                mode='min'
            )
        ]

        start_time = time.time()

        # Training
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            callbacks=callbacks,
            verbose=1
        )

        training_time = time.time() - start_time

        print(f"   ✅ {model_name} Training: {training_time:.1f}s")

        self.models[model_name] = model
        return model, history

    def evaluate_ultimate_model(self, model, X_test, y_test, scaler, model_name):
        """Ultimate Evaluation mit allen Metriken"""
        print(f"\n📊 Ultimate Evaluation: {model_name}")
        print("-" * 30)

        # Vorhersagen
        y_pred = model.predict(X_test, verbose=0)

        # Skalierung rückgängig
        y_test_orig = scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        y_pred_orig = scaler.inverse_transform(y_pred).flatten()

        # Ultimate Metriken
        r2 = r2_score(y_test_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
        mae = mean_absolute_error(y_test_orig, y_pred_orig)
        mape = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig)) * 100

        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(y_pred_orig) > 0
            direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            direction_acc = 0

        # Accuracy Bands
        accuracy_5pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.05) * 100
        accuracy_10pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.10) * 100

        results = {
            'model_name': model_name,
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'mape': mape,
            'direction_accuracy': direction_acc,
            'accuracy_5pct': accuracy_5pct,
            'accuracy_10pct': accuracy_10pct,
            'y_test_orig': y_test_orig,
            'y_pred_orig': y_pred_orig
        }

        print(f"   📈 R²: {r2:.4f} ({r2*100:.1f}%)")
        print(f"   💰 RMSE: ${rmse:.2f}")
        print(f"   📊 MAPE: {mape:.2f}%")
        print(f"   🎯 Direction: {direction_acc:.1f}%")
        print(f"   ✅ 5% Accuracy: {accuracy_5pct:.1f}%")

        self.results[model_name] = results
        return results

    def create_ultimate_ensemble(self, models_results):
        """Ultimate Ensemble - Beste Gewichtung"""
        print(f"\n🏆 ULTIMATE ENSEMBLE aus {len(models_results)} Modellen")
        print("-" * 45)

        # Intelligente Gewichtung (aus allen Scripts kombiniert)
        weights = []
        predictions = []

        for result in models_results:
            # Ultimate Gewichtung: R² 50% + Direction 30% + Low MAPE 20%
            r2_weight = max(0, result['r2'])
            direction_weight = result['direction_accuracy'] / 100
            mape_weight = max(0, 1 - result['mape'] / 100)

            combined_weight = (r2_weight * 0.5 + direction_weight * 0.3 + mape_weight * 0.2)
            weight = max(0.05, combined_weight)

            weights.append(weight)
            predictions.append(result['y_pred_orig'])

            print(f"   {result['model_name']}: R²={result['r2']:.4f}, Gewicht={weight:.3f}")

        # Normalisiere Gewichte
        weights = np.array(weights)
        weights = weights / np.sum(weights)

        # Gewichtete Ensemble-Vorhersage
        ensemble_pred = np.average(predictions, axis=0, weights=weights)

        # Ensemble-Evaluation
        y_test_orig = models_results[0]['y_test_orig']

        ensemble_r2 = r2_score(y_test_orig, ensemble_pred)
        ensemble_rmse = np.sqrt(mean_squared_error(y_test_orig, ensemble_pred))
        ensemble_mape = np.mean(np.abs((y_test_orig - ensemble_pred) / y_test_orig)) * 100

        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(ensemble_pred) > 0
            ensemble_direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            ensemble_direction_acc = 0

        ensemble_accuracy_5pct = np.mean(np.abs((y_test_orig - ensemble_pred) / y_test_orig) < 0.05) * 100

        ensemble_result = {
            'model_name': 'Ultimate_Ensemble',
            'r2': ensemble_r2,
            'rmse': ensemble_rmse,
            'mape': ensemble_mape,
            'direction_accuracy': ensemble_direction_acc,
            'accuracy_5pct': ensemble_accuracy_5pct,
            'weights': weights,
            'y_test_orig': y_test_orig,
            'y_pred_orig': ensemble_pred
        }

        print(f"\n🎯 ULTIMATE ENSEMBLE PERFORMANCE:")
        print(f"   📈 R²: {ensemble_r2:.4f} ({ensemble_r2*100:.1f}%)")
        print(f"   💰 RMSE: ${ensemble_rmse:.2f}")
        print(f"   📊 MAPE: {ensemble_mape:.2f}%")
        print(f"   🎯 Direction: {ensemble_direction_acc:.1f}%")
        print(f"   ✅ 5% Accuracy: {ensemble_accuracy_5pct:.1f}%")

        return ensemble_result

def main():
    """ULTIMATE COMBINED MASTER HAUPTFUNKTION"""
    print("🏆 STARTE ULTIMATE COMBINED MASTER SYSTEM")
    print("=" * 60)

    start_time = time.time()

    # === INITIALISIERUNG ===
    processor = UltimateCombinedProcessor()
    model_builder = UltimateCombinedModels()
    trainer = UltimateCombinedTrainer()

    # === ULTIMATE DATENAUFBEREITUNG ===
    print(f"\n📊 ULTIMATE DATENAUFBEREITUNG")
    print("=" * 40)

    # Features erstellen
    features = processor.load_and_engineer_ultimate_features()

    # Daten vorbereiten
    X, y = processor.prepare_ultimate_data(features)

    # Daten aufteilen
    total_size = len(X)
    train_size = int(total_size * CONFIG['train_split'])
    val_size = int(total_size * CONFIG['validation_split'])

    X_train = X[:train_size]
    y_train = y[:train_size]
    X_val = X[train_size:train_size+val_size]
    y_val = y[train_size:train_size+val_size]
    X_test = X[train_size+val_size:]
    y_test = y[train_size+val_size:]

    print(f"\n✅ ULTIMATE DATENAUFBEREITUNG ABGESCHLOSSEN!")
    print(f"   📊 Features: {len(processor.selected_features)}")
    print(f"   📦 Training: {len(X_train)}")
    print(f"   📦 Validation: {len(X_val)}")
    print(f"   📦 Test: {len(X_test)}")

    # === ULTIMATE MODELL-TRAINING ===
    print(f"\n🤖 ULTIMATE COMBINED TRAINING")
    print("=" * 40)

    input_shape = (X_train.shape[1], X_train.shape[2])
    models_results = []

    # 1. Ultimate LSTM
    try:
        print(f"\n🔥 Model 1/3: Ultimate LSTM")
        lstm_model = model_builder.build_ultimate_lstm(input_shape)
        lstm_model, _ = trainer.train_ultimate_model(
            lstm_model, X_train, y_train, X_val, y_val, 'Ultimate_LSTM'
        )
        lstm_results = trainer.evaluate_ultimate_model(
            lstm_model, X_test, y_test, processor.scalers['target'], 'Ultimate_LSTM'
        )
        models_results.append(lstm_results)
    except Exception as e:
        print(f"❌ Ultimate LSTM Fehler: {e}")

    # 2. Ultimate GRU
    try:
        print(f"\n🔥 Model 2/3: Ultimate GRU")
        gru_model = model_builder.build_ultimate_gru(input_shape)
        gru_model, _ = trainer.train_ultimate_model(
            gru_model, X_train, y_train, X_val, y_val, 'Ultimate_GRU'
        )
        gru_results = trainer.evaluate_ultimate_model(
            gru_model, X_test, y_test, processor.scalers['target'], 'Ultimate_GRU'
        )
        models_results.append(gru_results)
    except Exception as e:
        print(f"❌ Ultimate GRU Fehler: {e}")

    # 3. Ultimate Hybrid
    try:
        print(f"\n🔥 Model 3/3: Ultimate Hybrid")
        hybrid_model = model_builder.build_ultimate_hybrid(input_shape)
        hybrid_model, _ = trainer.train_ultimate_model(
            hybrid_model, X_train, y_train, X_val, y_val, 'Ultimate_Hybrid'
        )
        hybrid_results = trainer.evaluate_ultimate_model(
            hybrid_model, X_test, y_test, processor.scalers['target'], 'Ultimate_Hybrid'
        )
        models_results.append(hybrid_results)
    except Exception as e:
        print(f"❌ Ultimate Hybrid Fehler: {e}")

    # === ULTIMATE ENSEMBLE ===
    if models_results:
        ensemble_result = trainer.create_ultimate_ensemble(models_results)

        # === FINALE ULTIMATE ANALYSE ===
        print(f"\n🏆 FINALE ULTIMATE COMBINED ANALYSE")
        print("=" * 50)

        total_time = time.time() - start_time

        # Sortiere Modelle
        sorted_results = sorted(models_results, key=lambda x: x['r2'], reverse=True)

        print(f"\n📊 ULTIMATE MODEL RANKING:")
        for i, result in enumerate(sorted_results):
            print(f"   {i+1}. {result['model_name']}: {result['r2']*100:.1f}% R²")

        print(f"\n🏆 ULTIMATE ENSEMBLE: {ensemble_result['r2']*100:.1f}% R²")

        # Ziel-Check
        if ensemble_result['r2'] >= CONFIG['target_accuracy']:
            print(f"\n🎉🎉🎉 ULTIMATE ZIEL ERREICHT! 🎉🎉🎉")
            print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
            improvement = (ensemble_result['r2'] - CONFIG['target_accuracy']) * 100
            print(f"🚀 Übererfüllung: +{improvement:.1f} Prozentpunkte!")
        elif ensemble_result['r2'] >= 0.70:
            print(f"\n🔥🔥 ULTIMATE PERFORMANCE! 🔥🔥")
            print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
            gap = (CONFIG['target_accuracy'] - ensemble_result['r2']) * 100
            print(f"Nur noch {gap:.1f} Prozentpunkte bis zum Ziel!")
        elif ensemble_result['r2'] >= 0.50:
            print(f"\n💪💪 SEHR GUT! 💪💪")
            print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
        elif ensemble_result['r2'] >= 0:
            print(f"\n✅ POSITIVE ERGEBNISSE!")
            print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
        else:
            print(f"\n🔧 WEITERE OPTIMIERUNG NÖTIG")
            print(f"R²: {ensemble_result['r2']*100:.1f}% - System braucht Anpassungen")

        # Ultimate Performance Details
        print(f"\n📈 ULTIMATE PERFORMANCE DETAILS:")
        print(f"   R²: {ensemble_result['r2']:.4f} ({ensemble_result['r2']*100:.1f}%)")
        print(f"   RMSE: ${ensemble_result['rmse']:.2f}")
        print(f"   MAPE: {ensemble_result['mape']:.2f}%")
        print(f"   Direction: {ensemble_result['direction_accuracy']:.1f}%")
        print(f"   5% Accuracy: {ensemble_result['accuracy_5pct']:.1f}%")

        # Ultimate System Stats
        print(f"\n⚡ ULTIMATE SYSTEM STATS:")
        print(f"   Zeit: {total_time:.1f}s")
        print(f"   Modelle: {len(models_results)}")
        print(f"   Features: {len(processor.selected_features)}")
        print(f"   Test Samples: {len(ensemble_result['y_test_orig'])}")
        print(f"   CPU-Kerne: {os.cpu_count()}")
        print(f"   GPU: {'Ja' if gpus else 'Nein'}")

        # Top Features
        print(f"\n🏆 TOP 5 ULTIMATE FEATURES:")
        for i, feature in enumerate(processor.selected_features[:5]):
            print(f"   {i+1}. {feature}")

        # Finale Bewertung
        if ensemble_result['r2'] >= 0.85:
            print(f"\n🎉🎉🎉 ULTIMATE SUCCESS! ZIEL ERREICHT! 🎉🎉🎉")
            print(f"Das Ultimate Combined System erreicht {ensemble_result['r2']*100:.1f}% Genauigkeit!")
        elif ensemble_result['r2'] >= 0.70:
            print(f"\n🔥🔥 ULTIMATE PERFORMANCE! 🔥🔥")
            print(f"Hervorragende Performance mit {ensemble_result['r2']*100:.1f}% Genauigkeit!")
        elif ensemble_result['r2'] >= 0.50:
            print(f"\n💪💪 SEHR STARK! 💪💪")
            print(f"Starke Performance mit {ensemble_result['r2']*100:.1f}% Genauigkeit!")
        elif ensemble_result['r2'] >= 0:
            print(f"\n✅ GUTE BASIS!")
            print(f"Positive Grundlage mit {ensemble_result['r2']*100:.1f}% - Weitere Optimierung möglich!")
        else:
            print(f"\n🔧 OPTIMIERUNG ERFORDERLICH!")
            print(f"System braucht weitere Anpassungen für bessere Performance!")

        print(f"\n✅ ULTIMATE COMBINED MASTER SYSTEM ABGESCHLOSSEN!")

        return ensemble_result

    else:
        print(f"\n❌ Keine Modelle erfolgreich trainiert!")
        return None

if __name__ == "__main__":
    result = main()

    if result and result['r2'] >= CONFIG['target_accuracy']:
        print(f"\n🎯 ULTIMATE MISSION ACCOMPLISHED! 🎯")
        print(f"Combined Master System erreicht das Ziel von {CONFIG['target_accuracy']*100:.0f}%!")
    elif result and result['r2'] >= 0:
        print(f"\n💪 ULTIMATE BASIS GESCHAFFEN!")
        print(f"Positive R² erreicht - System funktioniert!")
    else:
        print(f"\n🔧 ULTIMATE OPTIMIERUNG MÖGLICH")
        print(f"Für noch bessere Ergebnisse können weitere Anpassungen vorgenommen werden.")
