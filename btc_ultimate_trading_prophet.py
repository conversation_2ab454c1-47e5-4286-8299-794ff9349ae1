#!/usr/bin/env python3
"""
🚀 ULTIMATE BITCOIN TRADING PROPHET 🚀
======================================
PERFEKTE KAUF/VERKAUF PROGNOSE - KONTINUIERLICHES TRAINING
Fokus: STEIGT oder FÄLLT? KAUFEN oder VERKAUFEN?
"""

import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report
import yfinance as yf
import multiprocessing
import os
from threading import Thread
import queue

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

# ULTIMATE TRADING KONFIGURATION
MAX_CORES = multiprocessing.cpu_count()
PREDICTION_INTERVAL = 30  # Alle 30 Sekunden neue Prognose
LOOKBACK_HOURS = 168      # 7 Tage Daten
PREDICTION_HORIZON = 6    # 6 Stunden Vorhersage

print("🚀 ULTIMATE BITCOIN TRADING PROPHET")
print("=" * 40)
print(f"💻 CPU: {MAX_CORES} Kerne")
print(f"🎯 FOKUS: KAUF/VERKAUF Signale")
print(f"⏱️ Update: Alle {PREDICTION_INTERVAL}s")
print(f"🔮 Horizont: {PREDICTION_HORIZON}h")
print(f"🕐 Start: {datetime.now().strftime('%H:%M:%S')}")

# Verzeichnis erstellen
os.makedirs('./trading_prophet', exist_ok=True)

class UltimateTradingProphet:
    def __init__(self):
        self.models = {}
        self.scaler = StandardScaler()
        self.last_prediction = None
        self.prediction_history = []
        self.accuracy_history = []
        self.current_data = None
        
    def get_live_data(self):
        """Live Bitcoin-Daten sammeln"""
        try:
            btc = yf.Ticker("BTC-USD")
            df = btc.history(period="7d", interval="1h")
            
            if len(df) > 50:
                df.columns = [col.lower() for col in df.columns]
                df = df.dropna()
                return df, True
            else:
                raise Exception("Zu wenig Daten")
                
        except Exception as e:
            # Fallback: Simulierte Daten
            end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
            start_time = end_time - timedelta(hours=LOOKBACK_HOURS)
            dates = pd.date_range(start=start_time, end=end_time, freq='H')
            
            n_points = len(dates)
            np.random.seed(int(time.time()) % 1000)  # Dynamischer Seed
            
            base_price = 67000
            trend = np.cumsum(np.random.normal(0, 300, n_points))
            volatility = np.random.normal(0, 800, n_points)
            
            prices = base_price + trend + volatility
            prices = np.maximum(prices, 30000)
            
            df = pd.DataFrame({
                'close': prices,
                'high': prices * np.random.uniform(1.001, 1.02, n_points),
                'low': prices * np.random.uniform(0.98, 0.999, n_points),
                'open': prices * np.random.uniform(0.999, 1.001, n_points),
                'volume': np.random.lognormal(15, 0.2, n_points)
            }, index=dates)
            
            return df, False
    
    def create_trading_features(self, df):
        """Ultimative Trading-Features für Kauf/Verkauf Signale"""
        df = df.copy()
        
        # === TRADING-FOKUSSIERTE FEATURES ===
        
        # Preis-Momentum (wichtigste für Trading)
        for period in [1, 3, 6, 12, 24]:
            df[f'returns_{period}'] = df['close'].pct_change(periods=period)
            df[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
        
        # Moving Average Signale
        for window in [6, 12, 24, 48]:
            df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
            df[f'price_above_sma_{window}'] = (df['close'] > df[f'sma_{window}']).astype(int)
        
        # Golden/Death Cross Signale
        df['sma_6_above_24'] = (df['sma_6'] > df['sma_24']).astype(int)
        df['sma_12_above_48'] = (df['sma_12'] > df['sma_48']).astype(int)
        
        # Volatilität (Risiko-Indikator)
        for window in [6, 12, 24]:
            df[f'volatility_{window}'] = df['close'].rolling(window=window).std()
            df[f'vol_spike_{window}'] = (df[f'volatility_{window}'] > 
                                        df[f'volatility_{window}'].rolling(window=48).mean() * 1.5).astype(int)
        
        # RSI Trading-Signale
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_oversold'] = (df['rsi'] < 30).astype(int)
        df['rsi_overbought'] = (df['rsi'] > 70).astype(int)
        
        # MACD Trading-Signale
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_bullish'] = (df['macd'] > df['macd_signal']).astype(int)
        
        # Volume-Preis Divergenz
        df['volume_sma'] = df['volume'].rolling(window=24).mean()
        df['volume_spike'] = (df['volume'] > df['volume_sma'] * 1.5).astype(int)
        df['price_volume_divergence'] = ((df['returns_1'] > 0) & (df['volume_spike'] == 1)).astype(int)
        
        # Bollinger Bands Trading-Signale
        bb_middle = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = bb_middle + 2 * bb_std
        df['bb_lower'] = bb_middle - 2 * bb_std
        df['bb_squeeze'] = ((df['bb_upper'] - df['bb_lower']) / bb_middle < 0.1).astype(int)
        df['price_near_bb_lower'] = (df['close'] < df['bb_lower'] * 1.02).astype(int)
        df['price_near_bb_upper'] = (df['close'] > df['bb_upper'] * 0.98).astype(int)
        
        # Zeit-basierte Trading-Signale
        df['hour'] = df.index.hour
        df['is_trading_hours'] = ((df['hour'] >= 9) & (df['hour'] <= 16)).astype(int)  # US Trading
        df['is_asian_session'] = ((df['hour'] >= 0) & (df['hour'] <= 8)).astype(int)
        
        # Trend-Stärke
        df['trend_strength'] = abs(df['returns_24'])
        df['strong_trend'] = (df['trend_strength'] > df['trend_strength'].rolling(window=48).quantile(0.8)).astype(int)
        
        # Support/Resistance Levels
        df['local_high'] = df['close'].rolling(window=12, center=True).max() == df['close']
        df['local_low'] = df['close'].rolling(window=12, center=True).min() == df['close']
        df['near_resistance'] = df['local_high'].rolling(window=48).sum().astype(int)
        df['near_support'] = df['local_low'].rolling(window=48).sum().astype(int)
        
        # Aggressive Bereinigung für Trading
        df = df.fillna(method='ffill').fillna(method='bfill').fillna(0)
        df = df.replace([np.inf, -np.inf], 0)

        # Alle boolean Spalten zu int konvertieren
        for col in df.columns:
            if df[col].dtype == 'bool':
                df[col] = df[col].astype(int)
        
        return df
    
    def create_trading_labels(self, df, horizon_hours=PREDICTION_HORIZON):
        """Erstelle Trading-Labels: 1=KAUF (Preis steigt), 0=VERKAUF (Preis fällt)"""
        
        # Future Returns berechnen
        future_returns = df['close'].shift(-horizon_hours) / df['close'] - 1
        
        # Trading-Labels: 1 = KAUF (>1% Gewinn), 0 = VERKAUF (sonst)
        # NaN-Werte durch 0 ersetzen
        future_returns = future_returns.fillna(0)
        trading_labels = (future_returns > 0.01).astype(int)
        
        return trading_labels
    
    def train_trading_models(self, df):
        """Trainiere ultimative Trading-Modelle"""
        
        # Features und Labels erstellen
        df_features = self.create_trading_features(df)
        labels = self.create_trading_labels(df_features)
        
        # Feature-Auswahl (nur Trading-relevante)
        feature_cols = [col for col in df_features.columns if col not in ['close', 'high', 'low', 'open', 'volume']]
        X = df_features[feature_cols].values
        y = labels.values
        
        # Entferne NaN-Werte
        valid_idx = ~(np.isnan(X).any(axis=1) | np.isnan(y))
        X = X[valid_idx]
        y = y[valid_idx]
        
        if len(X) < 50:
            return False
        
        # Skalierung
        X_scaled = self.scaler.fit_transform(X)
        
        # Train/Test Split (80/20)
        split_idx = int(len(X_scaled) * 0.8)
        X_train, X_test = X_scaled[:split_idx], X_scaled[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Ultimative Trading-Modelle
        models = {
            'RandomForest_TRADING': RandomForestClassifier(
                n_estimators=100,
                max_depth=15,
                min_samples_split=5,
                n_jobs=MAX_CORES,
                random_state=42
            ),
            'GradientBoosting_TRADING': GradientBoostingClassifier(
                n_estimators=50,
                max_depth=8,
                learning_rate=0.1,
                random_state=42
            ),
            'LogisticRegression_TRADING': LogisticRegression(
                max_iter=1000,
                random_state=42
            )
        }
        
        # Training und Evaluierung
        for name, model in models.items():
            try:
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                self.models[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'feature_cols': feature_cols
                }
                
                print(f"✅ {name}: Accuracy={accuracy:.3f}")
                
            except Exception as e:
                print(f"❌ {name}: Fehler - {e}")
        
        return len(self.models) > 0
    
    def predict_trading_signal(self, df):
        """Ultimative Trading-Signal Vorhersage"""
        
        if not self.models:
            return None
        
        # Features erstellen
        df_features = self.create_trading_features(df)
        
        # Letzte Zeile für Vorhersage
        feature_cols = list(self.models.values())[0]['feature_cols']
        X_latest = df_features[feature_cols].iloc[-1:].values
        
        # Skalierung
        X_scaled = self.scaler.transform(X_latest)
        
        # Ensemble-Vorhersage
        predictions = []
        weights = []
        
        for name, model_data in self.models.items():
            try:
                pred_proba = model_data['model'].predict_proba(X_scaled)[0]
                predictions.append(pred_proba[1])  # Wahrscheinlichkeit für KAUF
                weights.append(model_data['accuracy'])
            except:
                continue
        
        if not predictions:
            return None
        
        # Gewichtetes Ensemble
        weights = np.array(weights)
        weights = weights / weights.sum()
        ensemble_prob = np.average(predictions, weights=weights)
        
        # Trading-Signal
        signal = "KAUF 🔥" if ensemble_prob > 0.6 else "VERKAUF 🔻" if ensemble_prob < 0.4 else "HALTEN ⚖️"
        confidence = max(ensemble_prob, 1-ensemble_prob)
        
        current_price = df['close'].iloc[-1]
        current_time = df.index[-1]
        
        prediction = {
            'time': current_time,
            'price': current_price,
            'signal': signal,
            'probability': ensemble_prob,
            'confidence': confidence,
            'models_used': len(predictions)
        }
        
        return prediction

    def display_trading_dashboard(self, prediction, performance_stats):
        """Ultimatives Trading-Dashboard"""

        print("\n" + "="*80)
        print("🚀 ULTIMATE BITCOIN TRADING PROPHET - LIVE DASHBOARD 🚀")
        print("="*80)

        if prediction:
            print(f"\n📊 LIVE TRADING SIGNAL:")
            print(f"🕐 Zeit: {prediction['time'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"💰 Preis: ${prediction['price']:,.2f}")
            print(f"🎯 SIGNAL: {prediction['signal']}")
            print(f"📈 Wahrscheinlichkeit: {prediction['probability']:.1%}")
            print(f"🎪 Konfidenz: {prediction['confidence']:.1%}")
            print(f"🤖 Modelle: {prediction['models_used']}")

            # Trading-Empfehlung
            if "KAUF" in prediction['signal']:
                action = "🔥 SOFORT KAUFEN!"
                reason = f"Modelle sehen {prediction['probability']:.1%} Chance auf Gewinn"
            elif "VERKAUF" in prediction['signal']:
                action = "🔻 SOFORT VERKAUFEN!"
                reason = f"Modelle sehen {1-prediction['probability']:.1%} Chance auf Verlust"
            else:
                action = "⚖️ POSITION HALTEN"
                reason = "Unklare Marktrichtung - abwarten"

            print(f"\n💡 TRADING-EMPFEHLUNG: {action}")
            print(f"📝 Begründung: {reason}")

        if performance_stats:
            print(f"\n📊 MODELL-PERFORMANCE:")
            for name, stats in performance_stats.items():
                print(f"   {name}: {stats['accuracy']:.1%} Genauigkeit")

        print(f"\n⚡ SYSTEM STATUS:")
        print(f"   🔄 Auto-Update: Alle {PREDICTION_INTERVAL}s")
        print(f"   🎯 Vorhersage-Horizont: {PREDICTION_HORIZON}h")
        print(f"   💻 CPU-Nutzung: {MAX_CORES} Kerne")
        print(f"   📈 Kontinuierliches Training: AKTIV")

        print("="*80)

    def create_trading_visualization(self, df, prediction):
        """Ultimative Trading-Visualisierung"""

        if prediction is None:
            return

        plt.figure(figsize=(16, 10))
        plt.suptitle('🚀 ULTIMATE BITCOIN TRADING PROPHET 🚀',
                     fontsize=18, color='white', weight='bold')

        # Preis-Chart mit Signalen
        plt.subplot(2, 2, 1)

        # Letzte 48 Stunden
        recent_df = df.tail(48)
        times = recent_df.index
        prices = recent_df['close']

        plt.plot(times, prices, color='white', linewidth=2, label='Bitcoin Preis')

        # Moving Averages
        if len(recent_df) > 24:
            sma_6 = recent_df['close'].rolling(window=6).mean()
            sma_24 = recent_df['close'].rolling(window=24).mean()
            plt.plot(times, sma_6, color='#00ff88', linewidth=1, alpha=0.7, label='SMA 6h')
            plt.plot(times, sma_24, color='#ff6b35', linewidth=1, alpha=0.7, label='SMA 24h')

        # Aktueller Punkt
        current_price = prediction['price']
        current_time = prediction['time']

        if "KAUF" in prediction['signal']:
            color = '#00ff88'
            marker = '^'
        elif "VERKAUF" in prediction['signal']:
            color = '#ff4757'
            marker = 'v'
        else:
            color = '#ffa502'
            marker = 'o'

        plt.scatter([current_time], [current_price], color=color, s=200, marker=marker,
                   label=f'Signal: {prediction["signal"]}', zorder=5)

        plt.title('LIVE Bitcoin Preis + Trading Signal', fontsize=14, color='white', weight='bold')
        plt.xlabel('Zeit', color='white')
        plt.ylabel('Preis (USD)', color='white')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # Signal-Stärke
        plt.subplot(2, 2, 2)

        signal_strength = prediction['probability'] if "KAUF" in prediction['signal'] else 1-prediction['probability']
        confidence = prediction['confidence']

        categories = ['Signal\nStärke', 'Konfidenz']
        values = [signal_strength, confidence]
        colors = ['#00ff88' if "KAUF" in prediction['signal'] else '#ff4757', '#3742fa']

        bars = plt.bar(categories, values, color=colors, alpha=0.8)
        plt.ylim(0, 1)
        plt.title('Signal-Stärke & Konfidenz', fontsize=14, color='white', weight='bold')
        plt.ylabel('Wahrscheinlichkeit', color='white')

        # Werte auf Balken anzeigen
        for bar, value in zip(bars, values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                    f'{value:.1%}', ha='center', va='bottom', color='white', fontweight='bold')

        plt.grid(True, alpha=0.3)

        # Trading-Empfehlung
        plt.subplot(2, 2, 3)
        plt.axis('off')

        recommendation_text = f"""ULTIMATE TRADING SIGNAL:

🎯 SIGNAL: {prediction['signal']}
📈 Wahrscheinlichkeit: {prediction['probability']:.1%}
🎪 Konfidenz: {prediction['confidence']:.1%}
🤖 Modelle: {prediction['models_used']}

💰 Aktueller Preis: ${prediction['price']:,.2f}
🕐 Zeit: {prediction['time'].strftime('%H:%M:%S')}

🔄 Nächstes Update: {PREDICTION_INTERVAL}s"""

        plt.text(0.05, 0.95, recommendation_text, transform=plt.gca().transAxes,
                fontsize=12, color='white', verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='black', alpha=0.8))

        # Modell-Performance
        plt.subplot(2, 2, 4)

        if self.models:
            model_names = [name.split('_')[0] for name in self.models.keys()]
            accuracies = [data['accuracy'] for data in self.models.values()]

            bars = plt.bar(model_names, accuracies, color=['#00ff88', '#ff6b35', '#3742fa'], alpha=0.8)
            plt.ylim(0, 1)
            plt.title('Modell-Genauigkeit', fontsize=14, color='white', weight='bold')
            plt.ylabel('Accuracy', color='white')

            # Werte auf Balken
            for bar, acc in zip(bars, accuracies):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                        f'{acc:.1%}', ha='center', va='bottom', color='white', fontweight='bold')

        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # Speichern
        timestamp = datetime.now().strftime('%H%M%S')
        filename = f'trading_prophet/ultimate_trading_signal_{timestamp}.png'
        plt.savefig(filename, dpi=200, bbox_inches='tight', facecolor='black')
        plt.show()

        print(f"✅ Trading-Chart gespeichert: {filename}")

def run_ultimate_trading_prophet():
    """Hauptfunktion - Kontinuierlicher Trading-Prophet"""

    prophet = UltimateTradingProphet()
    iteration = 0

    print(f"\n🚀 STARTE ULTIMATE TRADING PROPHET...")
    print(f"🔄 Kontinuierliches Training und Vorhersage alle {PREDICTION_INTERVAL}s")

    while True:
        try:
            iteration += 1
            start_time = time.time()

            print(f"\n{'='*60}")
            print(f"🔄 ITERATION {iteration} - {datetime.now().strftime('%H:%M:%S')}")
            print(f"{'='*60}")

            # 1. Live-Daten sammeln
            print("📊 Sammle Live-Daten...")
            df, is_real = prophet.get_live_data()
            prophet.current_data = df

            # 2. Modelle trainieren
            print("🤖 Trainiere Trading-Modelle...")
            training_success = prophet.train_trading_models(df)

            if not training_success:
                print("❌ Training fehlgeschlagen - warte...")
                time.sleep(PREDICTION_INTERVAL)
                continue

            # 3. Trading-Signal vorhersagen
            print("🔮 Erstelle Trading-Signal...")
            prediction = prophet.predict_trading_signal(df)

            if prediction:
                prophet.last_prediction = prediction
                prophet.prediction_history.append(prediction)

                # Nur letzte 100 Vorhersagen behalten
                if len(prophet.prediction_history) > 100:
                    prophet.prediction_history = prophet.prediction_history[-100:]

            # 4. Performance-Statistiken
            performance_stats = {name: {'accuracy': data['accuracy']}
                               for name, data in prophet.models.items()}

            # 5. Dashboard anzeigen
            prophet.display_trading_dashboard(prediction, performance_stats)

            # 6. Visualisierung (nur alle 5. Iteration für Performance)
            if iteration % 5 == 0 and prediction:
                prophet.create_trading_visualization(df, prediction)

            # 7. Timing
            elapsed_time = time.time() - start_time
            print(f"\n⚡ Iteration {iteration} abgeschlossen in {elapsed_time:.1f}s")

            # 8. Warten bis nächste Iteration
            sleep_time = max(0, PREDICTION_INTERVAL - elapsed_time)
            if sleep_time > 0:
                print(f"💤 Warte {sleep_time:.1f}s bis nächste Iteration...")
                time.sleep(sleep_time)

        except KeyboardInterrupt:
            print(f"\n🛑 ULTIMATE TRADING PROPHET gestoppt durch Benutzer")
            break
        except Exception as e:
            print(f"❌ Fehler in Iteration {iteration}: {e}")
            print("🔄 Versuche weiter...")
            time.sleep(PREDICTION_INTERVAL)

if __name__ == "__main__":
    run_ultimate_trading_prophet()
