�

    Гdh��  �                   �  � d Z ddlZddlZddlZddlZddlmZ	 ddl
m
Z
mZ ddlm
Z
mZ ddlmZ ddlmZ ddlmZmZ ddlmZ ddlZdd	lmZmZ dd
lmZmZm Z m!Z!m"Z" ddl#Z#ddl$Z%ddl&Z'ddl(Z(ddl)Z)ddl*Z*ddl+m,Z, ddl-m.Z.  ej/        d
�  �         e	j0        �1                    d�  �         ej2        �3                    d�  �          G d� d�  �        Z4d� Z5defd�Z6e7dk    �r{ e5�   �         Z8e8�ra e9d�  �          e9de8�:                    dd�  �        d�d��  �         e8�:                    d�  �        r�e8�:                    di �  �        Z, e9d�  �          e9d�  �          e9d�  �          e9d �  �          e9d! e,j:        d"d�  �        d#z   � ��  �          e9d$ e,j:        d%d#�  �        d&���  �          e9d' e,j:        d(d�  �        � d)��  �          e9d* e,j:        d+d�  �        d,���  �          e9d- e,j:        d.d�  �        d&���  �         e8�:                    d/�  �        r e9d0�  �          e9d1�  �          e9d2�  �          e9d3�  �         dS  e9d4�  �         dS dS )5u
  
🎨 ULTIMATE KOMPLETTES BITCOIN TRADING SYSTEM 🎨
===============================================
🚀 VOLLSTÄNDIGER SCRIPT MIT KONTINUIERLICHEM TRAINING UND VISUALISIERUNG 🚀
✅ 4 Ensemble-Modelle (RF + GB + SVM + SGD)
✅ 221+ erweiterte Features
✅ Adaptive Learning mit Persistierung
✅ Umfassende 3x3 Visualisierung (9 Charts)
✅ Kontinuierliches Training zwischen Sessions
✅ Multi-Threading Performance-Optimierung
✅ Intelligentes Risk Management
✅ Real-Time Datensammlung + Fallback
✅ Marktregime-Erkennung
✅ Automatische Hyperparameter-Optimierung
✅ Konfidenz-basierte Signalfilterung
✅ Erweiterte Marktmikrostruktur-Analyse
✅ Volatilitäts-Clustering Erkennung
✅ Momentum-Regime Klassifikation

💡 REVOLUTIONÄRES KOMPLETTES TRADING SYSTEM!
�    N)�datetime�	timedelta)�RandomForestClassifier�GradientBoostingClassifier)�SVC)�
SGDClassifier)�StandardScaler�RobustScaler)�accuracy_score)�deque�defaultdict)�Dict�List�Optional�Tuple�Union)�stats)�
find_peaks�ignore�dark_background�*   c            	       �n  � e Zd ZdZd� Zd� Zd� Zdej        fd�Z	dej        fd�Z
dej        fd�Zd	ej        dej        fd
�Zd	ej        dej        fd�Z
d	ej        fd�Zdefd
�Zdej        dedeeeef         fd�Zded	ej        fd�Zd	ej        dee         fd�Zdededefd�Zdej        defd�ZdS )�UltimateCompleteBitcoinTradingu%  
    🎨 ULTIMATE KOMPLETTES BITCOIN TRADING SYSTEM
    ===========================================
    Das vollständigste Bitcoin Trading System mit:
    - 4 Ensemble-Modelle (RF, GB, SVM, SGD)
    - 221+ erweiterte technische Indikatoren
    - Adaptive Learning mit Session-Persistierung
    - Umfassende 3x3 Visualisierung (9 Charts)
    - Kontinuierliches Training zwischen Sessions
    - Multi-Threading Performance-Optimierung
    - Intelligentes Risk Management
    - Marktregime-Erkennung
    - Automatische Hyperparameter-Optimierung
    c                 �@  � d| _         d| _        d| _        t          dt	          j        �   �         �  �        | _        d| _        t          | j         ��  �        | _	        t          | j         ��  �        | _
        t          d��  �        | _        t          d��  �        | _        i | _
        i | _        d	d	d
dd�| _        i | _        t#          t$          �  �        | _        dd
ddddd�| _        ddddd�| _        d| _        d| _        d| _        d| _        d| _        d| _        t9          d�  �         t9          d| j        � d��  �         t9          d| j         � ��  �         t9          d�  �         t9          d�  �         t9          d�  �         | �                    �   �          d S )Ni@  �2   �{�G�z�?�   zultimate_trading_memory.pkl��maxlen��  ��  �333333�?g      �?g333333�?)�rf�gb�svm�sgdg{�G�z�?g���Q��?g�������?�      �?)�max_position_size�	stop_loss�take_profit�volatility_threshold�max_drawdown�sharpe_thresholdr   �unknown)�trend�sideways�volatile�current_regime�      �?��������?�        u=   🎨 ULTIMATE KOMPLETTES BITCOIN TRADING SYSTEM initialisiertu   ⚡ Multi-Threading: z Threadsu   💾 Memory-Größe: u*   🎯 Erweiterte Ensemble-Modelle aktiviertu    🧠 Adaptive Learning aktiviertu(   🎨 Umfassende Visualisierung aktiviert)�MEMORY_SIZE�MIN_TRAINING_SIZE�
LEARNING_RATE�min�mp�	cpu_count�	N_THREADS�PERSISTENCE_FILEr   �price_memory�feature_memory�prediction_memory�performance_history�ensemble_models�ensemble_scalers�
model_weights�hyperparametersr
   �float�feature_importance_global�risk_metrics�market_regimes�learning_momentum�adaptation_rate�confidence_threshold�
session_count�
best_accuracy�reward_score�print�_load_persistent_memory)�selfs    �4E:\Dev\ultimate_complete_bitcoin_trading_FAVORITE.py�__init__z'UltimateCompleteBitcoinTrading.__init__C   s�  � ����!#���!����Q�����/�/��� =��� "��)9�:�:�:���#�4�+;�<�<�<���!&�d�!3�!3�!3���#(��#4�#4�#4�� �  "��� "����S��d�
� 
���  "���)4�U�);�);��&� "&���$)� � #�

� 
��� ���'�	
� 
��� "%���#���$(��!���� ������
�M�N�N�N�
�>�d�n�>�>�>�?�?�?�
�8�d�&6�8�8�9�9�9�
�;�<�<�<�
�1�2�2�2�
�9�:�:�:� 	
�$�$�&�&�&�&�&�    c                 ��  � 	 t           j        �                    | j        �  �        �r�t	          | j        d�  �        5 }t          j        |�  �        }ddd�  �         n# 1 swxY w Y   |�                    dt          d��  �        �  �        | _	        |�                    dd�  �        | _
        |�                    dd	�  �        | _        |�                    d
i �  �        | _        |�                    dd�  �        | _
        |�                    d
d�  �        | _        |�                    dt          t           �  �        �  �        | _        t%          d| j        dz   � d��  �         t%          dt'          | j	        �  �        � d��  �         t%          d| j
        d���  �         t%          d| j
        d���  �         t%          d| j        d���  �         dS dS # t(          $ r}t%          d|� ��  �         Y d}~dS d}~ww xY w)zLade vorherige Lernerfahrungen�rbNrA   r!   r   rJ   r3   rM   r   rE   rN   r5   rO   rG   u
   ✅ Session #�   z  - Vorherige Erfahrungen geladenu      📈 Performance-Historie: z	 Sessionsu      ⚡ Lern-Momentum: �.2fu      🏆 Beste Genauigkeit: �.2%u      🎁 Belohnungs-Score: u   ⚠️ Fehler beim Laden: )�os�path�existsr=   �open�pickle�load�getr   rA   rJ   rM   rE   rN   rO   r
   rF   rG   rP   �len�	Exception)rR   �f�
saved_data�es       rS   rQ   z6UltimateCompleteBitcoinTrading._load_persistent_memory~   sD  � �	4��w�~�~�d�3�4�4� 
L��$�/��6�6� 0�!�!'��Q���J�0� 0� 0� 0� 0� 0� 0� 0� 0� 0� 0���� 0� 0� 0� 0� ,6�>�>�:O�QV�^a�Qb�Qb�Qb�+c�+c��(�)3���8K�S�)Q�)Q��&�%/�^�^�O�Q�%G�%G��"�'1�~�~�6G��'L�'L��$�%/�^�^�O�S�%I�%I��"�$.�N�N�>�3�$G�$G��!�1;���@[�]h�in�]o�]o�1p�1p��.��^�d�&8�1�&<�^�^�^�_�_�_��_�s�4�;S�7T�7T�_�_�_�`�`�`��K�t�/E�K�K�K�L�L�L��L�D�4F�L�L�L�M�M�M��J�4�3D�J�J�J�K�K�K�K�K�!
L� 
L��" � 	4� 	4� 	4��2�q�2�2�3�3�3�3�3�3�3�3�3�����	4���s;   �:G �A�G �A!�!G �$A!�%E(G �
G8�G3�3G8c           	      ��  � 	 | j         | j        | j        | j        | j        | j        t
          | j        �  �        t          j	        �   �         �
                    �   �         d�}t          | j        d�  �        5 }t          j        ||�  �         ddd�  �         n# 1 swxY w Y   t          d| j        � d��  �         dS # t           $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)zSpeichere Lernerfahrungen)rA   rJ   rM   rE   rN   rO   rG   �	timestamp�wbNu   💾 Session #z Erfahrungen gespeichertu   ⚠️ Fehler beim Speichern: )rA   rJ   rM   rE   rN   rO   �dictrG   r   �now�	isoformatr^   r=   r_   �dumprP   rc   )rR   �	save_datard   rf   s       rS   �_save_persistent_memoryz6UltimateCompleteBitcoinTrading._save_persistent_memory�   s@  � �	8�'+�'?�%)�%;�!%�!3�#'�#7�!%�!3� $� 1�-1�$�2P�-Q�-Q�%�\�^�^�5�5�7�7�	� 	�I� �d�+�T�2�2� 
*�a���I�q�)�)�)�
*� 
*� 
*� 
*� 
*� 
*� 
*� 
*� 
*� 
*� 
*���� 
*� 
*� 
*� 
*� 
�O�4�#5�O�O�O�P�P�P�P�P��� 	8� 	8� 	8��6�1�6�6�7�7�7�7�7�7�7�7�7�����	8���s<   �A3B< �5B�B< �B�B< �B�B< �<
C#�C�C#�returnc                 ��  � t          d�  �         	 t          j        �                    d��  �        5 }|�                    | j        �  �        }|�                    | j        �  �        }	 |�                    d��  �        }t          |�  �        dk    rAt          dt          |�  �        � d��  �         | �	                    |�  �        cd	d	d	�  �         S n#  Y nxY w|�                    �   �         }t          d
t          |�  �        � d��  �         | �	                    |�  �        cd	d	d	�  �         S # 1 swxY w Y   d	S # t          $ r0}t          d|� ��  �         | �                    �   �         cY d	}~S d	}~ww xY w)z-Erweiterte Bitcoin-Datensammlung mit Fallbacku'   📊 Sammle erweiterte Bitcoin-Daten...�   )�max_workers�   )�timeoutr   u   ✅ Live-Daten: z StundenNu   ✅ Enhanced Fallback-Daten: u   ⚠️ Datensammlung Fehler: )rP   �
concurrent�futures�ThreadPoolExecutor�submit�_fetch_yfinance_data�_generate_enhanced_fallback�resultrb   �_enhance_ohlcv_datarc   )rR   �executor�
future_btc�future_fallback�dfrf   s         rS   �get_enhanced_bitcoin_dataz8UltimateCompleteBitcoinTrading.get_enhanced_bitcoin_data�   s�  � �
�7�8�8�8�	6��#�6�6�1�6�E�E� 
4��%�_�_�T�-F�G�G�
�"*�/�/�$�2R�"S�"S���#�*�*�2�*�6�6�B��2�w�w��|�|��B��R���B�B�B�C�C�C�#�7�7��;�;�
4� 
4� 
4� 
4� 
4� 
4� 
4� 
4� $����D����$�+�+�-�-���G�c�"�g�g�G�G�G�H�H�H��/�/��3�3�
4� 
4� 
4� 
4� 
4� 
4� 
4� 
4� 
4� 
4� 
4� 
4���� 
4� 
4� 
4� 
4� 
4� 
4��  � 	6� 	6� 	6��5�!�5�5�6�6�6��3�3�5�5�5�5�5�5�5�5�����	6���s`   � D; �5D.�'AC�D; �D.�C�A
D.�!D; �.D2�2D; �5D2�6D; �;
E5�%E0�*E5�0E5c                 ��   � t          j        d�  �        }|�                    dd��  �        }d� |j        D �   �         |_        |�                    �   �         �                    d�  �        S )z&Erweiterte Yahoo Finance DatensammlungzBTC-USD�7d�1h)�period�intervalc                 �6   � g | ]}|�                     �   �         ��S � )�lower��.0�cols     rS   �
<listcomp>zGUltimateCompleteBitcoinTrading._fetch_yfinance_data.<locals>.<listcomp>�   s    � �8�8�8�c�c�i�i�k�k�8�8�8rU   �float32)�yf�Ticker�history�columns�dropna�astype)rR   �btcr�   s      rS   rz   z3UltimateCompleteBitcoinTrading._fetch_yfinance_data�   sY   � ��i�	�"�"��
�[�[��t�[�
4�
4��8�8�R�Z�8�8�8��
��y�y�{�{�!�!�)�,�,�,rU   c                 �  � t          j        �   �         �                    ddd��  �        }|t          d��  �        z
  }t	          j        ||d��  �        }t
          |�  �        }t          j        �	                    t          t          j        �   �         �  �        dz  | j        dz  z   �  �         d	| j        d
z  z   }t          j
        t          j        �                    dd|�  �        �  �        }t          j        �                    dd|�  �        }d
t          j        dt          j        z  t          j        |�  �        z  dz  �  �        z  }dt          j        dt          j        z  t          j        |�  �        z  dz  �  �        z  }	t          j        �                    ddg|ddg��  �        }
t          j
        |
t          j        �                    dd|�  �        z  �  �        }t          j        |�  �        }t)          d|�  �        D ]7}
d||
dz
           z  dt          j        �                    dd�  �        z  z   ||
<   �8t          j        �                    ddg|ddg��  �        }|t          j        �                    dd|�  �        z  }||z   |z   |z   |	z   |z   |z   |z   }t          j        |d�  �        }t	          j        ||t          j        �                    dd|�  �        z  |t          j        �                    dd |�  �        z  |t          j        �                    d!d"|�  �        z  t          j        �                    d#d$|�  �        d%�|�&�  �        �                    d'�  �        }t)          dt
          |�  �        �  �        D ]U}
|j        |j        |
dz
           d(f         t          j        �                    d!d"�  �        z  |j        |j        |
         d)f<   �V|S )*z0Generiere erweiterte realistische Fallback-Datenr   )�minute�second�microsecond�
   )�days�H)�start�end�freqr    �   i(� ��   �x   iX  ��   �   �   i�  �   rX   �ffffff�?g�������?)�p�ffffff�?r"   i,  gq=
ףp�?r   i�  iP�  gj�t��?g���(\��?g�z�G��?g+�����?gףp=
��?g�G�z�?g      /@�333333�?)�close�high�lowr^   �volume)�indexr�   r�   r^   )r   rk   �replacer   �pd�
date_rangerb   �np�random�seed�int�timerM   �cumsum�normal�sin�pi�arange�choice�zeros�range�maximum�	DataFrame�uniform�	lognormalr�   �locr�   )rR   �end_time�
start_time�dates�n_points�
base_pricer/   �
volatility�daily_cycle�weekly_cycle�regime_changes�
regime_impact�vol_clustering�i�news_events�news_impact�pricesr�   s                     rS   r{   z:UltimateCompleteBitcoinTrading._generate_enhanced_fallback�   s�  � ��<�>�>�)�)��1�!�)�L�L���	�r� 2� 2� 2�2�
��
�J�H�3�G�G�G���u�:�:��
�	���s�4�9�;�;�'�'�$�.��1C�c�1I�I�J�J�J� �d�0�3�6�6�
� �	�"�)�*�*�1�c�8�<�<�=�=���Y�%�%�a��h�7�7�
��B�F�1�r�u�9�r�y��/B�/B�#B�R�#G�H�H�H���R�V�A���I��	�(�0C�0C�$C�v�$N�O�O�O�� ��)�)�1�a�&�(�t�T�l�)�K�K���	�.�2�9�3C�3C�A�t�X�3V�3V�"V�W�W�
� ��(�+�+���q�(�#�#� 	[� 	[�A� #�n�Q�q�S�&9� 9�C�"�)�BR�BR�ST�VY�BZ�BZ�<Z� Z�N�1��� �i�&�&��1�v�x�D�$�<�&�H�H��!�B�I�$4�$4�Q��h�$G�$G�G���u�$�z�1�K�?��� -�.�0>�?�AL�M����F�E�*�*�� �\���R�Y�.�.�u�e�X�F�F�F��B�I�-�-�e�U�H�E�E�E��R�Y�.�.�u�e�X�F�F�F��i�)�)�$��X�>�>�
� 
� �
� � � ��y�)�)�
 	� �q�#�b�'�'�"�"� 	k� 	k�A�*,�&���!�A�#���1G�*H�2�9�K\�K\�]b�di�Kj�Kj�*j�B�F�2�8�A�;��&�'�'��	rU   r�   c                 �`  � t          j        |d         |d         z
  t          j        t          j        |d         |d         �                    d�  �        z
  �  �        t          j        |d         |d         �                    d�  �        z
  �  �        �  �        �  �        |d<   |d         |d         z   |d         z   dz  |d<   |d         |d         z
  |d         z  |d<   |d	         |d         �                    d�  �        z
  |d
<   |d
         |d         �                    d�  �        z  |d<   |S )u0   Erweitere OHLCV-Daten mit zusätzlichen Metrikenr�   r�   r�   rX   �trrr   �
typical_price�price_ranger^   �gap�gap_percent)r�   r�   �abs�shift)rR   r�   s     rS   r}   z2UltimateCompleteBitcoinTrading._enhance_ohlcv_data�   s!  � � �:��v�J��E��"��J���r�&�z�B�w�K�$5�$5�a�$8�$8�8�9�9���r�%�y�2�g�;�#4�#4�Q�#7�#7�7�8�8�
� 
�
� 
��4��  "�&�z�B�u�I�5��7��C�q�H��?��  ��Z�"�U�)�3�r�'�{�B��=�� �v�J��G��!2�!2�1�!5�!5�5��5�	��u�I��7��(9�(9�!�(<�(<�<��=���	rU   c                 �F
  � t          d�  �         |�                    �   �         }dD ]b}|d         �                    |��  �        |d|� d�<   t          j        |d         |d         �                    |�  �        z  �  �        |d|� d�<   �cdD ]�}|d         �                    |�	�  �        �                    �   �         |d
|� �<   |d         �                    |��  �        �                    �   �         |d|� �<   |d         |d
|� �         k    �	                    t          �  �        |d
|� �<   |d         |d|� �         k    �	                    t          �  �        |d|� �<   ��dD ]�}|d         �                    �   �         }|�                    |dk    d�  �        �                    |�	�  �        �                    �   �         }|�                    |dk     d�  �         �                    |�	�  �        �                    �   �         }ddd||dz   z  z   z  z
  |d|� �<   ��dD ]�\  }}	}
|d         �                    |��  �        �                    �   �         }|d         �                    |	��  �        �                    �   �         }d|� d|	� �}
||z
  ||
<   ||
         �                    |
��  �        �                    �   �         ||
� d�<   ||
         ||
� d�         z
  ||
� d�<   ��dD ]�\  }}|d         �                    |�	�  �        �                    �   �         }|d         �                    |�	�  �        �
                    �   �         }|||z  z   |d|� �<   |||z  z
  |d|� �<   |d         |d|� �         z
  |d|� �         |d|� �         z
  z  |d|� �<   ��dD ]7}|d         �                    |�	�  �        �
                    �   �         |d|� d�<   �8d |j        v r�d!D ]P}|d          �                    |�	�  �        �                    �   �         |d"|� �<   |d          |d"|� �         z  |d#|� �<   �Q|d         |d          z  |d$<   |d$         �                    d%�	�  �        �                    �   �         |d          �                    d%�	�  �        �                    �   �         z  |d&<   d'D ]/}|d         |d         �                    |�  �        z  dz
  |d(|� �<   �0t          j        d)t          j        z  |j        j        z  d*z  �  �        |d+<   t          j        d)t          j        z  |j        j        z  d*z  �  �        |d,<   t          j        d)t          j        z  |j        j        z  d-z  �  �        |d.<   t          j        d)t          j        z  |j        j        z  d-z  �  �        |d/<   |j        j        d0k    �	                    t          �  �        |d1<   d2D ]D}|d         �                    |�	�  �        �                    �   �         }|d         |z
  |z  |d3|� �<   �E|�                    d4�5�  �        �                    d6�5�  �        �                    d�  �        }|�                    t          j        t          j         gd�  �        }t3          d7� |j        D �   �         �  �        }t          d8|� d9��  �         |S ):z3Erweiterte Feature-Engineering mit 221+ Indikatorenu7   🔬 Erstelle erweiterte Features (221+ Indikatoren)...)rX   r�   rr   �   �   r   �   �   r�   �$   �0   r�   )�periods�ret_�h�log_ret_)	rr   r�   �	   r�   r�   r�   r�   r�   �H   )�window�sma_)�span�ema_�
above_sma_�
above_ema_)�   �   �   �   r   �d   rX   g�����|�=�rsi_))r�   �   r�   )r   r�   �   �macd_�_�_signal�
_histogram))�   r�   )r�   r'   �	bb_upper_�	bb_lower_�bb_position_)r�   r�   r�   r�   �vol_r�   )r�   r�   r�   �volume_sma_�
volume_ratio_�price_volumer�   �vwap_12)rr   r�   r�   r�   r�   r�   �	momentum_r�   r�   �hour_sin�hour_cosr�   �day_sin�day_cosr�   �
is_weekend)r�   r�   r�   �trend_strength_�ffill)�method�bfillc                 �   � g | ]}|d v�|��	S �)
r�   r�   r�   r^   r�   r�   r�   r�   r�   r�   r�   r�   s     rS   r�   zKUltimateCompleteBitcoinTrading.create_advanced_features.<locals>.<listcomp>`  sB   � � #X� #X� #X�3�%(�  1W�  &W�  &W� $'� &W�  &W�  &WrU   u   ✅ Erweiterte Features: z Features erstellt)rP   �copy�
pct_changer�   �logr�   �rolling�mean�ewmr�   rF   �diff�where�stdr�   �sumr�   r�   r�   �hour�cos�	dayofweek�fillnar�   �infrb   )rR   r�   �	result_dfr�   r�   �delta�gain�loss�fast�slow�signal�ema_fast�ema_slow�	macd_name�std_mult�smar  �final_feature_counts                     rS   �create_advanced_featuresz7UltimateCompleteBitcoinTrading.create_advanced_features  s�  � �
�G�H�H�H��G�G�I�I�	� =� 	^� 	^�F�*,�W�+�*@�*@��*@�*P�*P�I�&�V�&�&�&�'�.0�f�R��[�2�g�;�CT�CT�U[�C\�C\�5\�.]�.]�I�*��*�*�*�+�+� 8� 	h� 	h�F�)+�G��)<�)<�F�)<�)K�)K�)P�)P�)R�)R�I�o�V�o�o�&�)+�G����f��)E�)E�)J�)J�)L�)L�I�o�V�o�o�&�02�7��i��v���>X�0X�/`�/`�af�/g�/g�I�+�6�+�+�,�02�7��i��v���>X�0X�/`�/`�af�/g�/g�I�+�6�+�+�,�,� &� 	S� 	S�F��w�K�$�$�&�&�E��K�K���	�1�-�-�6�6�f�6�E�E�J�J�L�L�D��[�[����A�.�.�.�7�7�v�7�F�F�K�K�M�M�D�),��q�4�4�%�<�;P�7P�0Q�)R�I�o�V�o�o�&�&� #<� 	j� 	j��D�$���'�{���D��1�1�6�6�8�8�H��'�{���D��1�1�6�6�8�8�H�-��-�-�t�-�-�I�#+�h�#6�I�i� �/8��/C�/G�/G�V�/G�/T�/T�/Y�/Y�/[�/[�I��+�+�+�,�2;�I�2F��V_�Sh�Sh�Sh�Ii�2i�I��.�.�.�/�/� !5� 	g� 	g��F�H��W�+�%�%�V�%�4�4�9�9�;�;�C��W�+�%�%�V�%�4�4�8�8�:�:�C�.1�X��^�.D�I�*�&�*�*�+�.1�X��^�.D�I�*�&�*�*�+�24�W�+�	�J^�V\�J^�J^�@_�2_�dm�  oC�  {A�  oC�  oC�  eD�  GP�  Qe�  ]c�  Qe�  Qe�  Gf�  ef�  2g�I�-�V�-�-�.�.� &� 	S� 	S�F�*,�W�+�*=�*=�V�*=�*L�*L�*P�*P�*R�*R�I�&�V�&�&�&�'�'� �r�z�!�!�%� 
g� 
g��46�x�L�4H�4H�PV�4H�4W�4W�4\�4\�4^�4^�	�0��0�0�1�68��l�Y�Oe�]c�Oe�Oe�Ef�6f�	�2�&�2�2�3�3�(*�7��b��l�(B�I�n�%�$-�n�$=�$E�$E�R�$E�$P�$P�$T�$T�$V�$V�#%�h�<�#7�#7�r�#7�#B�#B�#F�#F�#H�#H�%I�I�i� � ,� 	Z� 	Z�F�.0��k�B�w�K�<M�<M�f�<U�<U�.U�XY�.Y�I�*�&�*�*�+�+� !#��q�2�5�y�2�8�=�'@�2�'E� F� F�	�*�� "��q�2�5�y�2�8�=�'@�2�'E� F� F�	�*��!�v�a�"�%�i�"�(�2D�&D�q�&H�I�I�	�)��!�v�a�"�%�i�"�(�2D�&D�q�&H�I�I�	�)��#%�8�#5��#:�"B�"B�5�"I�"I�	�,�� #� 	N� 	N�F��W�+�%�%�V�%�4�4�9�9�;�;�C�57��[�3�5F�#�4M�I�0��0�0�1�1� �$�$�G�$�4�4�;�;�7�;�K�K�R�R�ST�U�U�	��%�%�r�v���w�&7��;�;�	�!� #X� #X�)�2C� #X� #X� #X� Y� Y�� 	�Q�*=�Q�Q�Q�R�R�R��rU   c                 ��  � t          d�  �         | �                    |�  �        }|�                    d�  �        }t          dt          |�  �        � d��  �         |j        D �]K}|j        |         }|t
          |d         �  �        t
          |�                    d|d         �  �        �  �        t
          |�                    d|d         �  �        �  �        t
          |�                    dd	�  �        �  �        | j        d
�}| j	        �
                    |�  �         d� |j        D �   �         }i }|D ]N}	t          j
        ||	         �  �        s2t          j        ||	         �  �        st
          ||	         �  �        ||	<   �O|r+||| j        | j        d�}
| j        �
                    |
�  �         ��Mt          d
t          | j	        �  �        � dt          | j        �  �        � d��  �         dS )z7Erweiterte Memory-Update mit intelligenter Datenauswahlu&   🧠 Aktualisiere erweiterte Memory...r�   u   📊 Ausgewählt: z hochwertige Datenpunkter�   r�   r�   r�   r   )rh   �pricer�   r�   r�   �sessionc                 �   � g | ]}|d v�|��	S r  r�   r�   s     rS   r�   zIUltimateCompleteBitcoinTrading.update_enhanced_memory.<locals>.<listcomp>~  sB   � � Q� Q� Q�C�!�  *P�  P�  P�  � P�  P�  PrU   )rh   �featuresr0  �learning_weightu   💾 Enhanced Memory: z	 Preise, z	 FeaturesN)rP   r-  �tailrb   r�   r�   rF   ra   rM   r>   �appendr�   r�   �isnan�isinfrJ   r?   )rR   r�   �df_features�recent_data�idx�row�
price_data�feature_colsr2  r�   �feature_datas              rS   �update_enhanced_memoryz5UltimateCompleteBitcoinTrading.update_enhanced_memoryf  s  � �
�6�7�7�7��3�3�B�7�7��!�&�&�s�+�+��
�M�3�{�#3�#3�M�M�M�N�N�N��$� 	9� 	9�C��/�#�&�C� !��s�7�|�,�,��c�g�g�f�c�'�l�;�;�<�<��S�W�W�U�C��L�9�9�:�:������!� 4� 4�5�5��-�
� �J� 
��$�$�Z�0�0�0�Q� Q�;�+>� Q� Q� Q�L� �H�#� 
4� 
4���x��C��)�)� 4�"�(�3�s�8�2D�2D� 4�$)�#�c�(�O�O�H�S�M��� 
9�!$� (�#�1�'+�'=�	 �  �� �#�*�*�<�8�8�8��
�k�s�4�+<�'=�'=�k�k��D�L_�H`�H`�k�k�k�l�l�l�l�lrU   c           
      �~  � t          | j        �  �        | j        k     r&t          dt          | j        �  �        � ��  �         dS t          d�  �         t          dt          | j        �  �        � d��  �         g }t	          | j        �  �        D ]U}d|d         i}|�                    |d         �  �         |�                    dd	�  �        |d<   |�                    |�  �         �Vt          j	        |�  �        �
                    d�  �        �                    �   �         }d
� | j        D �   �         }|j
        �                    |�  �        |d<   |�                    dg��  �        }t          |�  �        | j        k     rdS g d
�}i }|D ]�}	 | �                    ||�  �        \  }	}
}|	rH|	| j        |� d�<   |
| j        |� d�<   |||� d�<   t          d|� dt          |	�  �        � d|d���  �         �h# t&          $ r}t          d|� d|� ��  �         Y d}~��d}~ww xY w|�r|t)          j        t	          |�                    �   �         �  �        �  �        }
|
| j        k    rT|
| j        z
  }| xj        |dz  z
  c_        |
| _        t          d|
d�d|d�d��  �         t          d| j        d���  �         | j        �                    | j        |
t7          j        �   �         || j        | j        d��  �         t          | j        �  �        dk    r�| j        d         d         }|
|z
  }|d	k    r:t=          d | j        d!z  �  �        | _        t          d"|d�d#| j        d���  �         n9t?          d$| j        d%z  �  �        | _        t          d&|d�d#| j        d���  �         | xj        dz
  c_        t          | j        �  �        d	k    S )'z+Erweiterte Ensemble-Training mit 4 Modellenu   ⚠️ Zu wenig Memory-Daten: Fu+   🤖 Starte erweiterte Ensemble-Training...u   📚 Training mit z hochwertigen Datenpunktenrh   r2  r0  r   c                 �,   � i | ]}|d          |d         ��S )rh   r/  r�   )r�   �items     rS   �
<dictcomp>zJUltimateCompleteBitcoinTrading.train_advanced_ensemble.<locals>.<dictcomp>�  s#   � �U�U�U�4�d�;�'��g��U�U�UrU   r/  )�subset)rX   r�   r�   r�   u
   ✅ Ensemble �h: z Modelle, Genauigkeit: �.3fu
   ❌ Training zh fehlgeschlagen: Nr�   u%   🏆 NEUE BESTLEISTUNG! Genauigkeit: z (+�)�   🎁 Belohnungs-Score: rY   )r0  �accuracyrh   �ensemble_resultsrJ   rO   rX   �����rI  g       @g�������?u   📈 Verbesserung: +z, Momentum: �      �?r�   u   📉 Rückgang: ) rb   r?   r7   rP   �list�updatera   r5  r�   r�   �	set_index�
sort_indexr>   r�   �mapr�   �_train_horizon_ensemblerB   rC   rc   r�   r  �valuesrN   rO   rA   rM   r   rk   rJ   r9   �max)rR   �memory_datarB  r;  �	df_memory�
price_dict�horizonsrJ  �horizon�models�scalersrI  rf   �avg_accuracy�improvement�
prev_accuracys                   rS   �train_advanced_ensemblez6UltimateCompleteBitcoinTrading.train_advanced_ensemble�  sg  � ��t�"�#�#�d�&<�<�<��M�3�t�7J�3K�3K�M�M�N�N�N��5�
�;�<�<�<�
�W�3�t�':�#;�#;�W�W�W�X�X�X� ����,�-�-� 	$� 	$�D���[� 1�2�C��J�J�t�J�'�(�(�(�!�X�X�i��3�3�C�	�N����s�#�#�#�#��L��-�-�7�7��D�D�O�O�Q�Q�	� V�U�4�CT�U�U�U�
�&�_�0�0��<�<�	�'���$�$�W�I�$�6�6�	��y�>�>�D�2�2�2��5� �:�:����� 		F� 		F�G�
F�,0�,H�,H��T[�,\�,\�)����� j�:@�D�(�G����7�;B�D�)�W�-�-�-�8�6>�$��]�]�]�3��h�'�h�h�c�&�k�k�h�h�Zb�h�h�h�i�i�i���� 
F� 
F� 
F��D�g�D�D��D�D�E�E�E�E�E�E�E�E�����
F���� � 	h��7�4�(8�(?�(?�(A�(A�#B�#B�C�C�L� �d�0�0�0�*�T�-?�?���!�!�[�2�%5�5�!�!�%1��"��e�l�e�e�e�S^�e�e�e�e�f�f�f��G��0A�G�G�G�H�H�H��$�+�+��-�(�%�\�^�^�$4�%)�%;� $� 1�
-� -� 
� 
� 
� �4�+�,�,�q�0�0� $� 8�� <�Z� H�
�*�]�:����?�?�-0��d�6L�s�6R�-S�-S�D�*��j��j�j�j�d�Nd�j�j�j�k�k�k�k�-0��d�6L�t�6S�-T�-T�D�*��f�[�f�f�f�$�J`�f�f�f�g�g�g����a�����4�'�(�(�1�,�,s   �A$G,�,
H�6H�HrV  rY  c                 �&  � |d         �                     | �  �        }|d         }||z  dz
  �                    d�  �        }d|z  }||k    �                    t          �  �        }d� |j        D �   �         }g }	|D ]|}
|
|j        v rqt          j        t          j        ||
         �                    d�  �        |�  �        d         �  �        }t          j        |�  �        s|	�	                    |
|f�  �         �}|	�
                    d� d�	�  �         d
� |	dd�         D �   �         }t          |�  �        d
k     r
|dd�         }||         j        }
|j        }t          j        |
�  �        �
                    d��  �        t          j        |�  �        z   }|
|         ||         }}
t          |
�  �        dk     s|
j        d         dk    ri i dfS t          d
t          t          |
�  �        dz  �  �        �  �        }|
|d�         |
d|�         }}||d�         |d|�         }}t          |�  �        d
k     st          |�  �        dk     ri i dfS i }i }i }	 t!          �   �         }|�                    |�  �        }|�                    |�  �        }t'          dddddd��  �        }|�                    ||�  �         |�                    |�  �        }t-          ||�  �        }||d<   ||d<   ||d<   n,# t.          $ r}t1          d|� d|� ��  �         Y d}~nd}~ww xY w	 t!          �   �         }|�                    |�  �        } |�                    |�  �        }!t3          dddd��  �        }"|"�                    | |�  �         |"�                    |!�  �        }#t-          ||#�  �        }$|"|d <   ||d <   |$|d <   n,# t.          $ r}t1          d!|� d|� ��  �         Y d}~nd}~ww xY w|r3t          j        t7          |�                    �   �         �  �        �  �        nd}%|||%fS )"u.   Trainiere Ensemble-Modelle für einen Horizontr/  rX   r   g����Mb�?c                 �   � g | ]}|d v�|��	S ))r/  r0  r�   r�   s     rS   r�   zJUltimateCompleteBitcoinTrading._train_horizon_ensemble.<locals>.<listcomp>�  s-   � � ;� ;� ;���%9�9�9� �9�9�9rU   �r   rX   c                 �   � | d         S )NrX   r�   )�xs    rS   �<lambda>zHUltimateCompleteBitcoinTrading._train_horizon_ensemble.<locals>.<lambda>�  s
   � ��!�� rU   T)�key�reversec                 �   � g | ]\  }}|��S r�   r�   )r�   r�   �corrs      rS   r�   zJUltimateCompleteBitcoinTrading._train_horizon_ensemble.<locals>.<listcomp>�  s   � �L�L�L�Y�S�$�S�L�L�LrU   N�(   r�   r�   )�axisr5   g      �?r   r   r�   r�   r   ������balanced)�n_estimators�	max_depth�min_samples_split�random_state�n_jobs�class_weightr#   u       ⚠️ RandomForest rE  皙�����?r�   )rn  �
learning_ratero  rq  r$   u       ⚠️ GradientBoosting )r�   r  r�   r�   r�   r�   r�   �corrcoefr6  r5  �sortrb   rS  �any�shaperT  r
   �
fit_transform�	transformr   �fit�predictr   rc   rP   r   r  rM  )&rR   rV  rY  �
future_prices�current_prices�returns�base_threshold�labelsr=  �feature_correlationsr�   ri  �selected_features�X�y�
valid_mask�	split_idx�X_train�X_test�y_train�y_testrZ  r[  �
accuracies�	scaler_rf�
X_train_rf�	X_test_rf�model_rf�	y_pred_rf�acc_rfrf   �	scaler_gb�
X_train_gb�	X_test_gb�model_gb�	y_pred_gb�acc_gb�ensemble_accuracys&                                         rS   rR  z6UltimateCompleteBitcoinTrading._train_horizon_ensemble�  s�  � � "�'�*�0�0�'��:�:�
�"�7�+�� �>�1�A�5�=�=�a�@�@�� �����N�*�2�2�3�7�7��;� ;�y�'8� ;� ;� ;��  "��� 	=� 	=�C��i�'�'�'��v�b�k�)�C�.�*?�*?��*B�*B�F�K�K�D�Q�R�R���x��~�~� =�(�/�/��d��<�<�<���!�!�n�n�d�!�C�C�C�L�L�2F�s��s�2K�L�L�L��� �!�!�B�&�&� ,�S�b�S� 1���'�(�/���M�� �x��{�{���A��.�.���!���<�=�
���}�a�
�m�1���q�6�6�B�;�;�!�'�!�*��/�/��r�3�;�� ��C��A����
�.�.�/�/�	��I�J�J�-��:�I�:�����I�J�J�-��:�I�:�����w�<�<�"����F���a����r�3�;�� �����
�	>�$���I�"�0�0��9�9�J�!�+�+�F�3�3�I�-���"#���'�
� � �H� 
�L�L��W�-�-�-� �(�(��3�3�I�#�F�I�6�6�F�#�F�4�L�%�G�D�M�%�J�t����� 	>� 	>� 	>��<�W�<�<��<�<�=�=�=�=�=�=�=�=�����	>����	B�$���I�"�0�0��9�9�J�!�+�+�F�3�3�I�1��!���	� � �H� 
�L�L��W�-�-�-� �(�(��3�3�I�#�F�I�6�6�F�#�F�4�L�%�G�D�M�%�J�t����� 	B� 	B� 	B��@��@�@�Q�@�@�A�A�A�A�A�A�A�A�����	B���� CM�U�B�G�D��):�):�)<�)<�$=�$=�>�>�>�RU���w� 1�1�1s2   �BK( �(
L�2L�L�BN+ �+
O�5O�Or|   c                 �  � t          d�  �         | �                    |�  �        }|�                    di �  �        }t          j        ddd��  �        \  }}|�                    dddd	d
��  �         |�                    d�  �        }|j        }|d
         �                    ||d         ddd��  �         d|j	        v r&|d
         �                    ||d         ddd��  �         |d
         �
                    dddd	��  �         |d
         �                    �   �          |d
         �                    dd��  �         d|j	        v rd|d         �                    ||d         ddd��  �         |d         �
                    dd d!d"�#�  �         |d         �
                    d$d%d!d"�#�  �         |d         �
                    d&ddd	��  �         |d         �                    �   �          |d         �                    dd��  �         g }	g }
|�                    �   �         D ];\  }}|d'k    r0|	�                    |�  �         |
�                    |d(         �  �         �<|	rY|d)         �                    |	|
dd*�+�  �         |d)         �
                    d,ddd	��  �         |d)         �                    d-�  �         g d.�}
g d/�}t%          |�  �        D ]:\  }}||j	        v r,|d0         �                    |||         |
|         d|��  �         �;|d0         �
                    d1ddd	��  �         |d0         �                    �   �          |d0         �                    dd��  �         d2|j	        v rE|d3         �                    ||d2         d4d5d6�7�  �         |d3         �
                    d8ddd	��  �         d9|j	        v rU|d:         �                    ||d9         ddd;��  �         d<|j	        v r&|d:         �                    ||d<         ddd=��  �         |d:         �
                    d>ddd	��  �         |d:         �                    �   �          |d:         �                    dd��  �         d?|j	        v r�d@|j	        v r�|dA         �                    ||d         dddB��  �         |dA         �                    ||d?         d4dCd5�D�  �         |dA         �                    ||d@         d4dCd5�D�  �         |dA         �                    ||d?         |d@         d4dE�+�  �         |dA         �
                    dFddd	��  �         |dA         �                    �   �          |dA         �                    dd��  �         g d.�}g dG�}t%          |�  �        D ]:\  }}||j	        v r,|dH         �                    |||         ||         d|��  �         �;|dH         �
                    dIddd	��  �         |dH         �                    �   �          |dH         �                    dd��  �         |dJ         �                    dK�  �         |�                    dLdM�  �        }|�                    dNi �  �        }d'|v r!|d'         }|dO         }|d(         }|dP         }ndQ}dM}dM}dR|dS�dT|�                    dUdM�  �        dCz   � dV|�                    dWdM�  �        � dX|�                    dYdC�  �        dZ�d[| j        d\�d]| j        dZ�d^|� d_|d`�da|d`�db�}|dJ         �                    dcdc||dJ         j        ddddeded	t3          dfdgd*dd�h�  �        �i�
  �
         t          j        g dj��k�  �         t          j        �   �          t          dl�  �         dmS )nz1Erstelle umfassende 3x3 Visualisierung (9 Charts)u.   🎨 Erstelle umfassende 3x3 Visualisierung...�predictionsrr   )r�   rt   )�figsizeu4   🎨 ULTIMATE KOMPLETTES BITCOIN TRADING SYSTEM 🎨r�   �white�boldg\���(\�?)�fontsize�color�weightr�  r�   )r   r   r�   z
Bitcoin Preis)r�  �	linewidth�label�sma_24�#00ff88r�   zSMA 24hu   📈 PREISANALYSEr�   )r�  r�  r�  Tr"   )�alpha�rsi_14rb  �#ff6b35zRSI (14)�F   �redz--r�   )r�  r�  �	linestyler�  r�   �greenu   📊 RSI INDIKATOR�GESAMT�probability)r   r�   g�������?)r�  r�  u   🤖 ENSEMBLE PERFORMANCE�Wahrscheinlichkeit)r�  r�  z#8e44ad)�vol_6h�vol_12h�vol_24h)rX   r   u   📈 VOLATILITÄTr�   )rX   rX   �bluer�   �{�G�z�?)r�  r�  �widthu   📊 VOLUME�
macd_12_26)rX   r�   �MACD�macd_12_26_signal�Signalu	   📊 MACD�bb_upper_20�bb_lower_20)r�   r   �PreisrX   )r�  r�  r�  rt  u   📊 BOLLINGER BANDS)�
momentum_6�momentum_12�momentum_24)r�   rX   u
   📈 MOMENTUM)r�   r�   �offr/  r   �system_statsr&  �
confidencezN/Au#   🎨 SYSTEM STATS:

💰 Bitcoin: $�,.2fu   
🔄 Session: #rM   u   
🤖 Modelle: rB   u   
⚡ Momentum: rJ   rY   u   
🏆 Beste Genauigkeit: rZ   u   
🎁 Belohnungs-Score: u   

🎯 HAUPTSIGNAL:
z
Wahrscheinlichkeit: �.1%z
Konfidenz: u\   

⚡ PERFORMANCE:
Kontinuierliches Training: ✅
Adaptive Learning: ✅
Visualisierung: ✅rL  �   �centerzround,pad=1�black)�boxstyle�	facecolorr�  �	edgecolorr�  )r{  r�  r�  �ha�va�
fontweight�bbox)r   g���Q��?rX   r�   )�rectu,   ✅ Umfassende 3x3 Visualisierung angezeigt!N)rP   r-  ra   �plt�subplots�suptitler4  r�   �plotr�   �	set_title�legend�grid�axhline�itemsr5  �bar�
set_ylabel�	enumerate�fill_betweenrk  rN   rO   �text�	transAxesrj   �tight_layout�show)rR   r|   r�   r8  r�  �fig�axes�	recent_df�timesrX  �
probabilitiesrf  �pred�
vol_colors�vol_windowsr�   �vol_col�momentum_colors�momentum_windows�mom_col�
current_pricer�  �gesamt�main_signal�	main_prob�	main_conf�
stats_texts                              rS   �create_ultimate_visualizationz<UltimateCompleteBitcoinTrading.create_ultimate_visualizationK  s�  � �
�>�?�?�?��3�3�B�7�7���j�j���3�3�� �L��A�x�8�8�8�	��T����K��w�v�� 	� 	G� 	G� 	G�  �$�$�R�(�(�	�����T�
����y��1��A�Ud��e�e�e��y�(�(�(���J�O�O�E�9�X�#6�i�ST�\e�O�f�f�f��T�
���0�2�W�U[��\�\�\��T�
�������T�
����C��(�(�(� �y�(�(�(���J�O�O�E�9�X�#6�i�ST�\f�O�g�g�g���J����5�D���L�L�L���J����7�d�#��N�N�N��T�
���1�B�g�V\��]�]�]��T�
�������T�
����C��(�(�(� ���
�$�*�*�,�,� 	:� 	:�I�C���h�������$�$�$��$�$�T�-�%8�9�9�9��� 	8���J�N�N�8�]�)�3�N�O�O�O���J� � �!<�r�QX�ag� �h�h�h���J�!�!�"6�7�7�7� 7�6�6�
�6�6�6��#�K�0�0� 	l� 	l�J�A�w��)�+�+�+��T�
����y��'9��A��Z[�cj��k�k�k���T�
���0�2�W�U[��\�\�\��T�
�������T�
����C��(�(�(� �y�(�(�(���J�N�N�5�)�H�"5�V�3�VZ�N�[�[�[���J� � ���7�SY� �Z�Z�Z� �9�,�,�,���J�O�O�E�9�\�#:�)�WX�`f�O�g�g�g�"�i�&7�7�7��T�
����y�1D�'E�Y�bc�ks��t�t�t��T�
���[�2�W�V��T�T�T��T�
�������T�
����C��(�(�(� �I�-�-�-�-�9�CT�2T�2T���J�O�O�E�9�W�#5�W�PQ�Y`�O�a�a�a���J�O�O�E�9�]�#;�6�UV�^a�O�b�b�b���J�O�O�E�9�]�#;�6�UV�^a�O�b�b�b���J�#�#�E�9�]�+C�Y�}�E]�)/�s� 
$� 
<� 
<� 
<��T�
���3�b��X^��_�_�_��T�
�������T�
����C��(�(�(� <�;�;��G�G�G��#�$4�5�5� 	q� 	q�J�A�w��)�+�+�+��T�
����y��'9��QR�AS�_`�ho��p�p�p���T�
���_�r��QW��X�X�X��T�
�������T�
����C��(�(�(� 	
�T�
��������
�
�7�A�.�.�
��z�z�.�"�5�5���{�"�"� ��*�F� ��*�K��}�-�I��|�,�I�I��K��I��I���� � � � � ��!�4�4�q�8�� � ��� 1�1�5�5�	� �
 ��� 3�Q�7�7�� � � �+�
� � � �)�� � � 
�� � �� � � 
�� � � �
�& 	
�T�
����S�*��T�
�8L�!#�7�x�H�Y_�!�=�G�SV�+2�a�A� A� A� 	� 	B� 	B� 	B� 	��0�0�0�1�1�1�1� 	��
�
�
�
�<�=�=�=�=�=rU   c                 ��  � | j         st          d�  �         dS t          d�  �         | �                    |�  �        }t          |d         j        d         �  �        }|j        d         }i }| j         �                    �   �         D ]+}dddd	t          | j         |         �  �        | j        d
�||<   �,dddd	d| j        dz  z   d
d�|d<   |||| �	                    |d�  �        | �
                    |�  �        | j        t          d� | j         �                    �   �         D �   �         �  �        | j
        | j        t          d� |j        D �   �         �  �        | j        | j        dd�d�S )z Vereinfachte Vorhersage-Funktionu%   ❌ Keine Ensemble-Modelle verfügbarNu,   🔮 Erstelle erweiterte Ensemble-Signale...r�   rl  u
   HALTEN ⚖️u   ⚖️ POSITION HALTENrL  r�   )r&  �actionr�  r�  �
ensemble_size�session_evolutionr3   r�  r5   )r&  r�  r�  r�  �improvement_factor�adaptive_thresholdr�  rt  c              3   �4   K  � | ]}t          |�  �        V � �d S )N)rb   )r�   rZ  s     rS   �	<genexpr>zJUltimateCompleteBitcoinTrading.predict_advanced_signals.<locals>.<genexpr>�  s(   � � � �&_�&_�v�s�6�{�{�&_�&_�&_�&_�&_�&_rU   c                 �   � g | ]}|d v�|��	S r  r�   r�   s     rS   r�   zKUltimateCompleteBitcoinTrading.predict_advanced_signals.<locals>.<listcomp>   sB   � � &Z� &Z� &Z�c�'*�  3Y�  (Y�  (Y� '*� (Y�  (Y�  (YrU   T)rM   rB   rJ   rL   �
feature_countrN   rO   �adaptive_learning)r�   r/  r�  rH   �
market_regimer�  )rB   rP   r-  rF   �ilocr�   �keysrb   rM   �_calculate_risk_metrics�_detect_market_regimer  rS  rJ   rL   r�   rN   rO   )rR   r�   r8  r�  �current_time�ensemble_predictions�horizon_keys          rS   �predict_advanced_signalsz7UltimateCompleteBitcoinTrading.predict_advanced_signals�  s�  � ��#� 	��9�:�:�:��4�
�<�=�=�=��3�3�B�7�7���k�'�2�7��;�<�<�
�"�(��,��  "���/�4�4�6�6� 	� 	�K�)�2�"�!�!$�T�%9�+�%F�!G�!G�%)�%7�
1� 1� ��-�-� &�.���"%��);�d�)B�"C�"%�
*
� *
��X�&� !�"�/� �8�8���L�L�!�7�7��D�D�!%�!3�#&�&_�&_��AU�A\�A\�A^�A^�&_�&_�&_�#_�#_�%)�%;�(,�(A�!$� &Z� &Z�[�5H� &Z� &Z� &Z� "[� "[�!%�!3� $� 1�%)�
� 
�

� 
� 	
rU   r�  �
position_sizec                 ��   � 	 ||z  }|d| j         d         z
  z  }|d| j         d         z   z  }|| j         d         z  }|| j         d         z  }|dk    r||z  nd}||||||| j         d         d�S #  i cY S xY w)zRisk Management BerechnungrX   r)   r*   r   r(   )�position_value�stop_loss_price�take_profit_price�max_loss�potential_profit�risk_reward_ratio�optimal_position_size)rH   )	rR   r�  r	  r  r  r
  r  r  r  s	            rS   r  z6UltimateCompleteBitcoinTrading._calculate_risk_metrics  s�   � �	�*�]�:�N�+�q�4�3D�[�3Q�/Q�R�O� -��T�5F�}�5U�1U� V��%��(9�+�(F�F�H�-��0A�-�0P�P��?G�!�|�|� 0�8� ;� ;�QR�� #1�#2�%6�$�$4�%6�)-�):�;N�)O�� � 
��	��I�I�I���s   �A,A/ �/A5r8  c                 ��   � 	 |�                     d�  �        }d|j        v r|d         j        d         }nd}t          |�  �        dk    rd}nd}||t	          dt          |�  �        d	z  �  �        d
�S #  ddicY S xY w)
zMarktregime-Erkennungr�   �trend_strength_24rl  r   r�  �trendingr0   r3   r�   )r2   �trend_strength�regime_confidencer2   r.   )r4  r�   r  r�   r9   )rR   r8  r9  r  �regimes        rS   r  z4UltimateCompleteBitcoinTrading._detect_market_regime  s�   � �	1�%�*�*�2�.�.�K�"�k�&9�9�9�!,�-@�!A�!F�r�!J���!"���>�"�"�T�)�)�#���#�� #)�"0�%(��c�.�.A�.A�B�.F�%G�%G�� � 
��
	1�$�i�0�0�0�0���s   �A/A2 �2A:N)�__name__�
__module__�__qualname__�__doc__rT   rQ   ro   r�   r�   r�   rz   r{   r}   r-  r?  �boolr_  r�   r   r   rF   rR  r�  r   r  r  r  r�   rU   rS   r   r   3   s   � � � � � �
� 
�9'� 9'� 9'�v4� 4� 4�.8� 8� 8�*6�2�<� 6� 6� 6� 6�4-�b�l� -� -� -� -�0�R�\� 0� 0� 0� 0�d�b�l� �r�|� � � � �.P�2�<� P�B�L� P� P� P� P�d)m��� )m� )m� )m� )m�VL-�� L-� L-� L-� L-�\j2��� j2�� j2�PU�VZ�\`�bg�Vg�Ph� j2� j2� j2� j2�XF>�D� F>�b�l� F>� F>� F>� F>�P3
�2�<� 3
�H�T�N� 3
� 3
� 3
� 3
�j�U� �5� �UY� � � � �,1��� 1�$� 1� 1� 1� 1� 1� 1rU   r   c                  ��  � t          d�  �         t          d�  �         t          �   �         } 	 t          j        �   �         }t          dd� ��  �         t          d| j        dz   � dt	          j        �   �         �                    d�  �        � ��  �         t          d� �  �         | �                    �   �         }| �                    |�  �         | �	                    �   �         }|r�| �
                    |�  �        }|rrt          |�  �         | �                    ||�  �         | �
                    �   �          t          j        �   �         |z
  }t          d	|d
�d��  �         |||||d         d
d�S t          d�  �         nt          d�  �         t          j        �   �         |z
  }t          d|d
�d��  �         ||d�S # t          $ r5}t          d|� ��  �         ddl}|�                    �   �          Y d}~dS d}~ww xY w)zQHAUPTFUNKTION - Ultimate Komplettes Bitcoin Trading mit kontinuierlichem Trainingu9   🎨 STARTE ULTIMATE KOMPLETTES BITCOIN TRADING SYSTEM...uL   🚀 VOLLSTÄNDIGER SCRIPT MIT KONTINUIERLICHEM TRAINING UND VISUALISIERUNG!�
zn==============================================================================================================u+   🎨 ULTIMATE KOMPLETTE ANALYSE - SESSION #rX   z - z%H:%M:%Su:   
⚡ ULTIMATE KOMPLETTES BITCOIN TRADING abgeschlossen in �.1f�sr�  T)r|   r�   �elapsed_time�training_successfulr�  �visualization_createdu&   ❌ Advanced Prediction fehlgeschlagenu$   ❌ Advanced Training fehlgeschlagenu#   
⚡ System-Check abgeschlossen in )r"  r!  u'   ❌ ULTIMATE KOMPLETTES SYSTEM FEHLER: r   N)rP   r   r�   rM   r   rk   �strftimer�   r?  r_  r  �#display_ultimate_complete_dashboardr�  ro   rc   �	traceback�	print_exc)�ucbtr�   r�   �training_successr|   r!  rf   r&  s           rS   �%run_ultimate_complete_bitcoin_tradingr*  5  s_  � � 
�
E�F�F�F�	�
X�Y�Y�Y�)�+�+�D�;��Y�[�[�
�
�n�7�n�n����
�|�D�<N�QR�<R�|�|�W_�Wc�We�We�Wn�Wn�oy�Wz�Wz�|�|�}�}�}�
��l���� �
+�
+�
-�
-�� 	
�#�#�B�'�'�'�  �7�7�9�9��� 	:��2�2�2�6�6�F�� 
@�3�F�;�;�;� �2�2�6�2�>�>�>� �,�,�.�.�.�  $�y�{�{�Z�7���g�T`�g�g�g�g�h�h�h� %��$0�+;�$*�>�$:�-1�
� � � �>�?�?�?�?��8�9�9�9��y�{�{�Z�/��
�H�\�H�H�H�H�I�I�I� $4�(�
� 
� 	
��
 � � � �
�;��;�;�<�<�<������������t�t�t�t�t�����	���s   �D=F: �,A
F: �:
G9�*G4�4G9r|   c                 �h
  � t          d�  �         t          d�  �         t          d�  �         | �r�| �                    d�  �        �r�| d         }| �                    di �  �        }| �                    di �  �        }| �                    di �  �        }t          d�  �         t          d	| d
         �                    d�  �        � ��  �         t          d| d
         d���  �         t          d|�                    dd�  �        �                    �   �         � ��  �         t          d|�                    dd�  �        d���  �         t          d�  �         t          d|�                    dd�  �        dz   � ��  �         t          d|�                    dd�  �        � ��  �         t          d|�                    dd�  �        d���  �         t          d|�                    d d!�  �        d"���  �         t          d#|�                    d$d�  �        � ��  �         t          d%|�                    d&d�  �        d"���  �         t          d'|�                    d(d�  �        d���  �         d)|v r�|d)         }t          d*|d+         � ��  �         t          d,|d-         � ��  �         t          d.|d/         d"���  �         t          d0|d1         d"���  �         t          d2|�                    d3d�  �        d�d4��  �         t          d5�  �         t          d6d7�d8d9d:�d8d;d<�d8d=d<�d8d>d7�d8d?d@���  �         t          dA�  �         |�                    �   �         D ]�\  }}|d)k    r�|}t          |d+         �  �        dBk    r|d+         dCdB�         dDz   n|d+         }	|d/         dE�}
|d1         dE�}|�                    dFd�  �        }dG|�                    dHd�  �        � �}
t          |d7�d8|	d:�d8|
d<�d8|d<�d8|d7�d8|
d@���  �         ��|�r t          dI�  �         t          dJ|�                    dKd�  �        d���  �         t          dL|�                    dMd�  �        d���  �         t          dN|�                    dOd�  �        d���  �         t          dP|�                    dQd�  �        d���  �         t          dR|�                    dSd�  �        d���  �         t          dT|�                    dUd�  �        d���  �         t          dV|�                    dWd�  �        dE���  �         t          d�  �         dCS dCS dCS )Xz;Ultimate Komplettes Dashboard mit erweiterten Informationenz�
============================================================================================================================================uE   🎨 ULTIMATE KOMPLETTES BITCOIN TRADING SYSTEM - LIVE DASHBOARD 🎨z�============================================================================================================================================r�  rH   r   r�  u   
📊 LIVE STATUS:u   🕐 Zeit: r�   z%Y-%m-%d %H:%M:%Su   💰 Bitcoin: $r/  r�  u   🎯 Marktregime: r2   r.   u   📈 Trend-Stärke: r  r   rF  u'   
🧠 KONTINUIERLICHES LEARNING SYSTEM:u   🔄 Session: #rM   rX   u   🤖 Ensemble-Modelle: rB   �   ⚡ Lern-Momentum: rJ   rY   u   🎯 Konfidenz-Schwelle: rL   r4   rZ   u   🔬 Features: r�  �   🏆 Beste Genauigkeit: rN   rH  rO   r�  u   
🎯 ULTIMATE HAUPTSIGNAL: r&  u   💡 EMPFEHLUNG: r�  u   📈 Wahrscheinlichkeit: r�  u   🎪 Konfidenz: r�  u   🚀 Verbesserungs-Faktor: r�  rd  u"   
🔮 ERWEITERTE HORIZONT-SIGNALE:�Horizontz<8� r�  z<25zWahrsch.z<12�	Konfidenz�Modelle�	Evolutionz<10zs-------------------------------------------------------------------------------------------------------------------r�   Nz...r�  r�  �Sr�  u#   
⚖️ ERWEITERTE RISK MANAGEMENT:u   💼 Position-Wert: $r  u   🛑 Stop Loss: $r  u   🎯 Take Profit: $r
  u   📉 Max. Verlust: $r  u   📈 Pot. Gewinn: $r  u   ⚖️ Risk/Reward: r  u   🎯 Optimale Position: r  )rP   ra   r$  �upperr�  rb   )r|   r�  rH   r   r�  r�  rf  r�  rY  r&  r�  r�  r�  �	evolutions                 rS   r%  r%  z  s
  � � 
�.����	�
Q�R�R�R�	�'�N�N�N�
� 7�&�*�*�]�+�+� 7��]�+���z�z�.�"�5�5���
�
�?�B�7�7�
��z�z�.�"�5�5��
�$�%�%�%�
�J�F�6�N�3�3�4G�H�H�J�J�K�K�K�
�6��w��6�6�6�7�7�7�
�[�=�#4�#4�5E�y�#Q�#Q�#W�#W�#Y�#Y�[�[�\�\�\�
�Q�]�%6�%6�7G��%K�%K�Q�Q�Q�R�R�R�
�9�:�:�:�
�J�� 0� 0��!� D� D�q� H�J�J�K�K�K�
�P��(8�(8�9J�A�(N�(N�P�P�Q�Q�Q�
�R�L�$4�$4�5H�!�$L�$L�R�R�R�S�S�S�
�^�,�*:�*:�;Q�SW�*X�*X�^�^�^�_�_�_�
�F�� 0� 0��!� D� D�F�F�G�G�G�
�S��)9�)9�/�1�)M�)M�S�S�S�T�T�T�
�Q��(8�(8���(K�(K�Q�Q�Q�R�R�R��{�"�"� ��*�F��D�&��2B�D�D�E�E�E��8�f�X�&6�8�8�9�9�9��I�f�]�.C�I�I�I�J�J�J��?�V�L�%9�?�?�?�@�@�@��Z��
�
�;O�QR�0S�0S�Z�Z�Z�Z�[�[�[�
�4�5�5�5�
��s�s�s��s�s�s�
�s�s�s��s�s�s�S\�s�s�s�bm�s�s�s�t�t�t�
�i����$�*�*�,�,� 		z� 		z�I�C���h�����8;�D��N�8K�8K�b�8P�8P��h�����,�u�4�4�VZ�[c�Vd��!%�m�!4�:�:�� $�\� 2�8�8�
� $����!� <� <�
�B����)<�a� @� @�B�B�	���x�x�x�f�x�x�x�;�x�x�x�Z�x�x�x�Vc�x�x�x�ir�x�x�x�y�y�y�� � 	a��9�:�:�:��V�,�*:�*:�;K�Q�*O�*O�V�V�V�W�W�W��S�l�&6�&6�7H�!�&L�&L�S�S�S�T�T�T��W��(8�(8�9L�a�(P�(P�W�W�W�X�X�X��O��)9�)9�*�a�)H�)H�O�O�O�P�P�P��V��(8�(8�9K�Q�(O�(O�V�V�V�W�W�W��W��)9�)9�:M�q�)Q�)Q�W�W�W�X�X�X��_�\�-=�-=�>U�WX�-Y�-Y�_�_�_�`�`�`�
�g������o7� 7� 7� 7rU   �__main__u6   
🎉 ULTIMATE KOMPLETTES BITCOIN TRADING erfolgreich!u   ⚡ Laufzeit: r!  r  r   r"  r�  u   🤖 Training: ✅ ERFOLGREICHu   🔮 Vorhersagen: ✅ GENERIERTu%   ⚖️ Risk Management: ✅ ERWEITERTu!   🧠 Adaptive Learning: ✅ AKTIVu   🎯 Session: #rM   rX   r,  rJ   rY   u   🤖 Ensemble: rB   z Modeller-  rN   rZ   rH  rO   r#  u7   🎨 Visualisierung: ✅ UMFASSEND ANGEZEIGT (3x3 Grid)uF   
🏆 ULTIMATE KOMPLETTES BITCOIN TRADING SYSTEM - REVOLUTIONÄR! 🏆uY   💡 4 Ensemble-Modelle + Kontinuierliches Training + 221+ Features + 3x3 Visualisierung!u1   🎨 VOLLSTÄNDIGER SCRIPT - NICHT ABGESCHNITTEN!u>   
❌ ULTIMATE KOMPLETTES BITCOIN TRADING SYSTEM fehlgeschlagen);r  �warningsr�   �numpyr�   �pandasr�   �matplotlib.pyplot�pyplotr�  r   r   �sklearn.ensembler   r   �sklearn.svmr   �sklearn.linear_modelr   �sklearn.preprocessingr	   r
   �sklearn.metricsr   �yfinancer�   �collectionsr   r
   �typingr   r   r   r   r   �	threading�concurrent.futuresrv   �multiprocessingr:   r_   r[   �json�scipyr   �scipy.signalr   �filterwarnings�style�user�   r�   r   r*  r%  r  r|   rP   ra   r�   rU   rS   �<module>rM     s<  ��� �, ���� ���� � � � � � � � � � � � � � � (� (� (� (� (� (� (� (� O� O� O� O� O� O� O� O� � � � � � � .� .� .� .� .� .� >� >� >� >� >� >� >� >� *� *� *� *� *� *� � � � � *� *� *� *� *� *� *� *� 5� 5� 5� 5� 5� 5� 5� 5� 5� 5� 5� 5� 5� 5� � � � � � � � � � � � � 
�
�
�
� 	�	�	�	� ���� � � � � � � #� #� #� #� #� #� �� �� !� !� !� �	�
�
��  �  �  � �	���r� � � �@1� @1� @1� @1� @1� @1� @1� @1�DC� C� C�J>�� >� >� >� >�@ �z���
2�
2�
4�
4�F�
� R�
��H�I�I�I�
��C�v�z�z�.�!�<�<�C�C�C�C�D�D�D��:�:�+�,�,� 
	P��J�J�~�r�2�2�E��E�3�4�4�4��E�4�5�5�5��E�:�;�;�;��E�6�7�7�7��E�G�I�E�I�o�q�$A�$A�A�$E�G�G�H�H�H��E�O�	��	�2E�q�(I�(I�O�O�O�P�P�P��E�M�I�E�I�.?��$C�$C�M�M�M�N�N�N��E�P�Y�U�Y���-J�-J�P�P�P�Q�Q�Q��E�N�I�E�I�n�a�,H�,H�N�N�N�O�O�O��:�:�-�.�.� 	N��E�L�M�M�M�
��X�Y�Y�Y�
��j�k�k�k�
��B�C�C�C�C�C�
��P�Q�Q�Q�Q�Q�5 �rU   