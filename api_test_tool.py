#!/usr/bin/env python3
"""
🔧 API KOMPATIBILITÄTS-TEST TOOL
===============================
Testet verschiedene Bitcoin-APIs auf Funktionalität und Zuverlässigkeit
"""

import requests
import time
from datetime import datetime

def test_bitcoin_apis():
    """🧪 Teste Bitcoin-APIs"""
    apis = {
        'coinbase': 'https://api.coinbase.com/v2/exchange-rates?currency=BTC',
        'coingecko': 'https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd',
        'binance': 'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT'
    }

    print("🚀 TESTE BITCOIN-APIs")
    print("=" * 30)

    working_apis = []

    for name, url in apis.items():
        try:
            print(f"🔍 Teste {name}...")
            start_time = time.time()
            response = requests.get(url, timeout=10)
            response_time = time.time() - start_time

            if response.status_code == 200:
                data = response.json()
                print(f"✅ {name}: OK ({response_time:.2f}s)")
                working_apis.append(name)
            else:
                print(f"❌ {name}: HTTP {response.status_code}")

        except Exception as e:
            print(f"❌ {name}: {str(e)}")

        time.sleep(1)

    print(f"\n✅ Funktionierende APIs: {len(working_apis)}")
    return working_apis

if __name__ == "__main__":
    test_bitcoin_apis()