�

    Qeh7�  �                   �*  � d Z ddlZddlZddlZddlZddlmZm	Z	 ddl
Z
ddlZddlZddl
Z
ddlmZmZmZmZ ddlZddlZddlmZ ddlmZ ddlmZ ddlZddlmZ  e
j        d	�  �          G d
� d�  �        Z d� Z!d
efd�Z"e#dk    r e!�   �          dS dS )u�  
ULTIMATE BITCOIN TRADING SYSTEM V2.0
====================================
VOLLSTÄNDIG ÜBERARBEITETES SYSTEM MIT ALLEN VERBESSERUNGEN
- Echte API-Integration (Binance + Yahoo Finance)
- Erweiterte technische Analyse (RSI, MACD, Bollinger Bands, Stochastic)
- Machine Learning (LSTM + XGBoost)
- News-Sentiment Analyse
- Multi-Timeframe Analyse
- Advanced Risk Management
- Backtesting Engine
- Stabilere und genauere Vorhersagen

ULTIMATE TRADING SYSTEM - ALLE VERBESSERUNGEN INTEGRIERT!
�    N)�datetime�	timedelta)�Dict�List�Tuple�Optional)�RandomForestRegressor)�StandardScaler)�train_test_split)�TextBlob�ignorec                   �<  � e Zd ZdZd� Zd� Zd� Zdej        fd�Z	de
e         fd�Zdej        fd�Z
d	ej        defd
�Zdefd�Zd	ej        dedej        fd
�Zd	ej        defd�Zdej        defd�Zdej        dedej        fd�Zd	ej        defd�Zdedefd�ZdS )�UltimateBitcoinTradingSystemV2u�   
    ULTIMATE BITCOIN TRADING SYSTEM V2.0
    ====================================
    Vollständig überarbeitetes System mit echten APIs und erweiterten Algorithmen
    c                 ��  � d| _         d| _        d| _        d| _        d| _        d| _        i | _        t          �   �         | _        d| _	        d| _
        d| _        d| _        d| _
        d| _        d | _        d | _        i | _        d	| _        t'          d
�  �         t'          d| j         � ��  �         t'          d�  �         t'          d
�  �         t'          d�  �         | �                    �   �          d S )NzUltimate_v2.0_RealAPIszBTC-USD�BTCUSDTg333333�?�{�G�z�?�333333�?�        r   �      �?z2ULTIMATE BITCOIN TRADING SYSTEM V2.0 initialisiertz	Version: z#Echte APIs: Binance + Yahoo Financez(Machine Learning: RandomForest + XGBoostz'Erweiterte technische Analyse aktiviert)�VERSION�SYMBOL�BINANCE_SYMBOL�confidence_threshold�risk_per_trade�max_position_size�	ml_modelsr
   �feature_scaler�prediction_confidence�
session_count�total_predictions�correct_predictions�cumulative_accuracy�
best_accuracy�market_data�last_data_update�technical_indicators�sentiment_score�print�_load_session_data)�selfs    �,E:\Dev\ultimate_bitcoin_trading_system_v2.py�__init__z'UltimateBitcoinTradingSystemV2.__init__1   s  � �/������'��� %)��!�"���!%��� ���,�.�.���%(��"� ���!"���#$�� �#&�� � ���  ��� $���$&��!�"���
�C�D�D�D�
�(�$�,�(�(�)�)�)�
�4�5�5�5�
�9�:�:�:�
�8�9�9�9� 	
���!�!�!�!�!�    c                 �  � 	 t           j        �                    d�  �        �rt          dd�  �        5 }t	          j        |�  �        }|�                    dd�  �        | _        |�                    dd�  �        | _        |�                    dd�  �        | _	        |�                    dd�  �        | _
        |�                    d	d�  �        | _        d
d
d
�  �         n# 1 swxY w Y   t          d| j        � ��  �         t          d| j
        d
���  �         t          d| j        d
���  �         d
S d
S # t          $ r}t          d|� ��  �         Y d
}~d
S d
}~ww xY w)zLade Session-Daten� ultimate_trading_session_v2.json�rr   r   r    r!   r"   r   r#   Nz Session-Daten geladen: Session #�Kumulative Genauigkeit: �.1%zBeste Genauigkeit: z"Konnte Session-Daten nicht laden: )�os�path�exists�open�json�load�getr   r    r!   r"   r#   r(   �	Exception)r*   �f�data�es       r+   r)   z1UltimateBitcoinTradingSystemV2._load_session_dataW   s�  � �	<��w�~�~�@�A�A� 
F��<�c�B�B� H�a��9�Q�<�<�D�)-���/�1�)E�)E�D�&�-1�X�X�6I�1�-M�-M�D�*�/3�x�x�8M�q�/Q�/Q�D�,�/3�x�x�8M�s�/S�/S�D�,�)-���/�3�)G�)G�D�&�
H� H� H� H� H� H� H� H� H� H� H���� H� H� H� H� �M��9K�M�M�N�N�N��O��1I�O�O�O�P�P�P��D�D�,>�D�D�D�E�E�E�E�E�
F� 
F�� � 	<� 	<� 	<��:�q�:�:�;�;�;�;�;�;�;�;�;�����	<���s<   �0D0 �BC�D0 �C�D0 �!C�"A
D0 �0
E�:E�Ec                 �r  � 	 | j         | j        | j        | j        | j        | j        t
          j        �   �         �                    �   �         d�}t          dd�  �        5 }t          j        ||d��  �         ddd�  �         dS # 1 swxY w Y   dS # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)zSpeichere Session-Daten)�versionr   r    r!   r"   r#   �last_updater/   �w�   )�indentNz&Konnte Session-Daten nicht speichern: )r   r   r    r!   r"   r#   r   �now�	isoformatr6   r7   �dumpr:   r(   )r*   r<   r;   r=   s       r+   �_save_session_dataz1UltimateBitcoinTradingSystemV2._save_session_datai   s%  � �	@��<�!%�!3�%)�%;�'+�'?�'+�'?�!%�!3�'�|�~�~�7�7�9�9�� �D� �8�#�>�>� 
-�!��	�$��!�,�,�,�,�
-� 
-� 
-� 
-� 
-� 
-� 
-� 
-� 
-� 
-� 
-� 
-���� 
-� 
-� 
-� 
-� 
-� 
-�� � 	@� 	@� 	@��>�1�>�>�?�?�?�?�?�?�?�?�?�����	@���s<   �AB �B�5B �B�B �	B�
B �
B6�B1�1B6�returnc                 �~  � t          d�  �         	 | j        �A| j        r:t          j        �   �         | j        z
  j        dk     rt          d�  �         | j        S t          d�  �         t
          j        | j        �  �        }|�	                    dd��  �        }|j
        rt          d	�  �        �t          d
t          |�  �        � d��  �         t          d�  �         | �
                    �   �         }|r�|d
         }|d         j        d         }t          ||z
  �  �        |z  }|dk     r�t          j        �   �         �                    ddd��  �        }t#          j        |gt'          ||�  �        gt)          ||�  �        g|g|�                    d|d         j        d         �  �        gd�|g��  �        }t#          j        ||g�  �        }t          d|d���  �         nt          d|d���  �         || _        t          j        �   �         | _        t          dt          |�  �        � d��  �         t          d|j        d         � d|j        d         � ��  �         t          d|d         j        d         d���  �         |S # t          $ r0}	t          d |	� ��  �         | �                    �   �         cY d}	~	S d}	~	ww xY w)!zHole echte Marktdaten von APIsz#Sammle echte Marktdaten von APIs...Ni,  zVerwende gecachte MarktdatenzHole Daten von Yahoo Finance...�30d�1h)�period�intervalzKeine Daten von Yahoo FinancezYahoo Finance: z Stunden-IntervallezValidiere mit Binance API...�price�Close�����皙�����?r   )�minute�second�microsecond�volume�Volume)�Open�High�LowrO   rV   ��indexzBinance-Validierung: Preis $�,.2fz"Binance-Preis weicht zu stark ab: r2   z
ERFOLGREICH: z Marktdaten-Punkte geladenz
Zeitraum: z bis zAktueller Preis: $z"FEHLER beim Laden der Marktdaten: )r(   r$   r%   r   rD   �seconds�yf�Tickerr   �history�emptyr:   �len�get_binance_current_data�iloc�abs�replace�pd�	DataFrame�max�minr9   �concatr[   �generate_fallback_data)
r*   �btc�hist�binance_data�current_binance_price�last_yahoo_price�
price_diff�current_time�new_rowr=   s
             r+   �get_real_market_dataz3UltimateBitcoinTradingSystemV2.get_real_market_data|   s  � �
�3�4�4�4�=	1�� �,��1F�,�����$�"7�7�@�3�F�F��4�5�5�5��'�'� 
�3�4�4�4��)�D�K�(�(�C� �;�;�e�d�;�;�;�D��z� 
A�� ?�@�@�@��B�C��I�I�B�B�B�C�C�C� 
�0�1�1�1��8�8�:�:�L�� 
Q�(4�W�(=�%�#'��=�#5�b�#9� � !�!6�9I�!I�J�J�M]�]�
���$�$�#+�<�>�>�#9�#9��1�Z[�#9�#\�#\�L� !�l�!1� 2�!$�%5�7L�!M�!M� N� #�$4�6K� L� L�M�"7�!8�#/�#3�#3�H�d�8�n�>Q�RT�>U�#V�#V�"W�,� ,� +�^�
-� -� -�G� �9�d�G�_�5�5�D��U�9N�U�U�U�V�V�V�V��O�z�O�O�O�P�P�P�  $�D��$,�L�N�N�D�!��G�#�d�)�)�G�G�G�H�H�H��C�t�z�!�}�C�C�4�:�b�>�C�C�D�D�D��D�t�G�}�'9�"�'=�D�D�D�E�E�E��K��� 	1� 	1� 	1��:�q�:�:�;�;�;��.�.�0�0�0�0�0�0�0�0�����	1���s%   �AJ �H(J �
J<�%J7�1J<�7J<c                 �2  � 	 d| j         � �}t          j        |d��  �        }|j        dk    rdS |�                    �   �         }t          |d         �  �        }d| j         � �}t          j        |d��  �        }|j        dk    rh|�                    �   �         }|t          |d         �  �        t          |d	         �  �        t          |d
         �  �        t          |d         �  �        d�S d|iS # t          $ r}t          d
|� ��  �         Y d}~dS d}~ww xY w)z#Hole aktuelle Daten von Binance APIz3https://api.binance.com/api/v3/ticker/price?symbol=�   ��timeout��   NrN   z2https://api.binance.com/api/v3/ticker/24hr?symbol=�priceChangePercentrU   �	highPrice�lowPrice)rN   �
change_24hrU   �high_24h�low_24hzBinance API Fehler: )r   �requestsr9   �status_coder7   �floatr:   r(   )	r*   �	url_price�response_price�
price_data�
current_price�url_24h�response_24h�	stats_24hr=   s	            r+   rc   z7UltimateBitcoinTradingSystemV2.get_binance_current_data�   sJ  � �	�c�d�Na�c�c�I�%�\�)�Q�?�?�?�N��)�S�0�0��t�'�,�,�.�.�J�!�*�W�"5�6�6�M� a�4�K^�`�`�G�#�<���;�;�;�L��'�3�.�.�(�-�-�/�/�	�*�"'�	�2F�(G�"H�"H�#�I�h�$7�8�8� %�i��&<� =� =�$�Y�z�%:�;�;�� � �  ��/�/��� 	� 	� 	��,��,�,�-�-�-��4�4�4�4�4�����	���s#   �+C/ �B;C/ �+C/ �/
D�9D�Dc           	      �d  � t          d�  �         d}t          j        �   �         }|t          d��  �        z
  }t	          j        ||d��  �        }|g}g }t
          dt          |�  �        �  �        D �]1}d}|t          j	        d	�  �        z  }	d
t          j
        |dz  �  �        z  }
dt          j
        |d	z  �  �        z  }t          j        d
|	�  �        }t          |�  �        d	k    r|d         |d         z
  |d         z  dz  }
nd
}
|
|z   |z   |
z   }|d         d|z   z  }t          |dz  t          |dz  |�  �        �  �        }|�                    |�  �         d}dt!          |�  �        dz  z   }|t          j        dd�  �        z  |z  }|�                    |�  �         ��3|�                    |�  �         t	          j        |��  �        }||d<   |d         �                    d�  �        �                    |d         j        d
         �  �        |d<   d}|d         dt,          j        �                    d
|t          |�  �        �  �        z   z  |d<   |d         dt,          j        �                    d
|t          |�  �        �  �        z
  z  |d<   t-          j        |d         t-          j        |d         |d         �  �        �  �        |d<   t-          j        |d         t-          j        |d         |d         �  �        �  �        |d<   ||d<   t          dt          |�  �        � d��  �         |S ) u3   Generiere Fallback-Daten wenn APIs nicht verfügbarzGeneriere Fallback-Daten...g    �G�@�   )�daysrK   )�start�end�freq�   ���Q��?�   g-C��6?�   g-C��6*?r   rP   �����皙�����?�ffffff�?gffffff�?i ʚ;�
   �333333�?�      @rZ   rO   rW   g{�G�zt?rX   rY   rV   zFallback-Daten generiert: z Stunden)r(   r   rD   r   rg   �
date_range�rangerb   �math�sqrt�sin�random�gaussri   rj   �appendre   �uniformrh   �shift�fillnard   �np�maximum�minimum)r*   �
base_price�end_date�
start_date�dates�prices�volumes�i�daily_volatility�hourly_volatility�weekly_trend�daily_trend�noise�momentum�total_change�	new_price�base_volume�
volume_factorrU   �df�intraday_ranges                        r+   rl   z5UltimateBitcoinTradingSystemV2.generate_fallback_data�   s  � �
�+�,�,�,� �
��<�>�>���	�r� 2� 2� 2�2�
��
�J�H�4�H�H�H������� �q�#�e�*�*�%�%� 	#� 	#�A�#�� 0�4�9�R�=�=� @�� "�D�H�Q��W�$5�$5�5�L� �4�8�A��F�#3�#3�3�K��L��$5�6�6�E� �6�{�{�R���"�2�J����4��s��C�c�I�����'�+�5��=��H�L��r�
�a�,�&6�7�I� �J��,�c�*�s�2B�I�.N�.N�O�O�I��M�M�)�$�$�$� %�K���L� 1� 1�B� 6�6�M� �6�>�#�s�#;�#;�;�m�K�F��N�N�6�"�"�"�"����{�#�#�#� �\��
&�
&�
&����7����[�&�&�q�)�)�0�0��G��1A�!�1D�E�E��6�
� ����[�A��	�(9�(9�!�^�S�QS�W�W�(U�(U�$U�V��6�
��w�K�1�r�y�'8�'8��N�C�PR�G�G�'T�'T�#T�U��5�	� �Z��6�
�B�J�r�'�{�B�v�J�,O�,O�P�P��6�
��J�r�%�y�"�*�R��[�"�V�*�*M�*M�N�N��5�	���8��
�<�3�r�7�7�<�<�<�=�=�=��	r-   r�   c                 �b
  � t          d�  �         	 |d         }|d         }|d         }|d         }i }d9d�} ||d�  �        j        d         |d	<    ||d
�  �        j        d         |d<   |�                    d�
�  �        �                    �   �         }|�                    d�
�  �        �                    �   �         }	||	z
  }
|
�                    d�
�  �        �                    �   �         }|
|z
  }|
j        d         |d<   |j        d         |d<   |j        d         |d<   |�                    d�  �        �                    �   �         }
|�                    d�  �        �                    �   �         }|
|dz  z   }|
|dz  z
  }||z
  ||z
  z  }||z
  |
z  }|j        d         |d<   |j        d         |d<   |j        d         |d<   |j        d         |d<   |�                    d�  �        �                    �   �         }|�                    d�  �        �                    �   �         }d||z
  ||z
  z  z  }|�                    d�  �        �                    �   �         }|j        d         |d<   |j        d         |d<   |�                    d�  �        �                    �   �         j        d         |d<   |�                    d�  �        �                    �   �         j        d         |d<   |j        d         |d <   |	j        d         |d!<   |�                    d�  �        �                    �   �         j        d         |d"<   |j        d         |d"         z  |d#<   |�                    �   �         }|�                    d�  �        �                    �   �         j        d         |d$<   ||z
  }t          j
        ||�                    �   �         z
  �  �        }t          j
        ||�                    �   �         z
  �  �        }t          j        |t          j        ||�  �        �  �        }|�                    d�  �        �                    �   �         }|j        d         |d%<   d&||z
  ||z
  z  z  }|j        d         |d'<   ||z   |z   dz  }|�                    d�  �        �                    �   �         }|�                    d�  �        �
                    d(� �  �        } ||z
  d)| z  z  }!|!j        d         |d*<   ||z   |z   dz  }||z  }"|"�                    ||�                    �   �         k    d+�  �        �                    d�  �        �                    �   �         }#|"�                    ||�                    �   �         k     d+�  �        �                    d�  �        �                    �   �         }$ddd,|#|$z  z   z  z
  }%|%j        d         |d-<   |�                    �   �         D ]8\  }&}'t#          j        |'�  �        rd.||&<   �t          j        |'�  �        rd.||&<   �9|| _        t          d/t+          |�  �        � d0��  �         t          d1|d	         d2���  �         t          d3|d         d4���  �         t          d5|d         d4���  �         t          d6|d         d2���  �         |S # t,          $ r}(t          d7|(� ��  �         i cY d8}(~(S d8}(~(ww xY w):z*Berechne erweiterte technische Indikatorenz-Berechne erweiterte technische Indikatoren...rO   rX   rY   rV   �   c                 �P  � | �                     �   �         }|�                    |dk    d�  �        �                    |��  �        �                    �   �         }|�                    |dk     d�  �         �                    |��  �        �                    �   �         }||z  }ddd|z   z  z
  }|S )Nr   ��window�d   r�   )�diff�where�rolling�mean)r�   rL   �delta�gain�loss�rs�rsis          r+   �
calculate_rsiz]UltimateBitcoinTradingSystemV2.calculate_advanced_technical_indicators.<locals>.calculate_rsi.  s�   � ����
�
�����E�A�I�q�1�1�:�:�&�:�I�I�N�N�P�P�����U�Q�Y��2�2�2�;�;�6�;�J�J�O�O�Q�Q���D�[���S�A��F�^�,���
r-   rP   �rsi_14�   �rsi_21�   )�span�   �	   �macd�macd_signal�macd_histogram�   rB   �bb_upper�bb_lower�bb_position�bb_widthr�   �   �stoch_k�stoch_d�sma_20�2   �sma_50�ema_12�ema_26�
volume_sma�volume_ratio�
volatility_20�atri�����
williams_rc                 �x   � t          j        t          j        | | �                    �   �         z
  �  �        �  �        S )N)r�   r�   re   )�xs    r+   �<lambda>zXUltimateBitcoinTradingSystemV2.calculate_advanced_technical_indicators.<locals>.<lambda>w  s&   � �B�G�B�F�1�q�v�v�x�x�<�DX�DX�<Y�<Y� r-   ���Q��?�ccir   r�   �mfir   z"Technische Indikatoren berechnet: z IndikatorenzRSI: �.1fzMACD: �.2fz
BB Position: zStochastic %K: z$FEHLER bei technischen Indikatoren: N)r�   )r(   rd   �ewmr�   r�   �stdri   rj   �
pct_changer�   re   r�   r�   �applyr�   �sum�itemsrg   �isna�isinfr&   rb   r:   ))r*   r�   r�   �highs�lowsr�   �
indicatorsr�   r�   r�   r�   �signal�	histogramr�   �std_20r�   r�   r�   r�   �high_14�low_14�	k_percent�	d_percent�returns�high_low�
high_close�	low_close�
true_ranger�   r�   �
typical_price�sma_tp�madr�   �
money_flow�
positive_flow�
negative_flowr�   �key�valuer=   s)                                            r+   �'calculate_advanced_technical_indicatorszFUltimateBitcoinTradingSystemV2.calculate_advanced_technical_indicators!  s�  � �
�=�>�>�>�q	���[�F��v�J�E��e�9�D���l�G��J�
� 
� 
� 
� $1�=���#<�#<�#A�"�#E�J�x� �#0�=���#<�#<�#A�"�#E�J�x� � �Z�Z�R�Z�(�(�-�-�/�/�F��Z�Z�R�Z�(�(�-�-�/�/�F��F�?�D��X�X�1�X�%�%�*�*�,�,�F��v�
�I�!%��2��J�v��(.��B��J�}�%�+4�>�"�+=�J�'�(� �^�^�B�'�'�,�,�.�.�F��^�^�B�'�'�+�+�-�-�F���!��,�H���!��,�H�!�H�,��H�1D�E�K� �8�+�v�5�H�%-�]�2�%6�J�z�"�%-�]�2�%6�J�z�"�(3�(8��(<�J�}�%�%-�]�2�%6�J�z�"� �m�m�B�'�'�+�+�-�-�G��\�\�"�%�%�)�)�+�+�F�����G�f�4D�E�F�I�!�)�)�!�,�,�1�1�3�3�I�$-�N�2�$6�J�y�!�$-�N�2�$6�J�y�!� $*�>�>�"�#5�#5�#:�#:�#<�#<�#A�"�#E�J�x� �#)�>�>�"�#5�#5�#:�#:�#<�#<�#A�"�#E�J�x� �#)�;�r�?�J�x� �#)�;�r�?�J�x� � (/���r�':�':�'?�'?�'A�'A�'F�r�'J�J�|�$�)0��b�)9�J�|�<T�)T�J�~�&� �'�'�)�)�G�*1�/�/�"�*=�*=�*A�*A�*C�*C�*H��*L�J��'� �t�|�H��������� 6�7�7�J���t�f�l�l�n�n�4�5�5�I���H�b�j��Y�.O�.O�P�P�J��$�$�R�(�(�-�-�/�/�C� #����J�u�� �'�F�"2�w��7G�!H�I�J�'1��r�':�J�|�$� #�T�\�F�2�a�7�M�"�*�*�2�.�.�3�3�5�5�F��'�'��+�+�1�1�2Y�2Y�Z�Z�C� �6�)�e�c�k�:�C� #����J�u�� #�T�\�F�2�a�7�M�&��0�J�&�,�,�]�]�=P�=P�=R�=R�-R�TU�V�V�^�^�_a�b�b�f�f�h�h�M�&�,�,�]�]�=P�=P�=R�=R�-R�TU�V�V�^�^�_a�b�b�f�f�h�h�M����M�M�$A� A�B�C�C� #����J�u�� )�.�.�0�0� 
*� 
*�
��U��7�5�>�>� *�&)�J�s�O�O��X�e�_�_� *�&)�J�s�O��(2�D�%��T�s�:���T�T�T�U�U�U��4�*�X�.�4�4�4�5�5�5��3�:�f�-�3�3�3�4�4�4��A�*�]�";�A�A�A�B�B�B��?�J�y�$9�?�?�?�@�@�@����� 	� 	� 	��<��<�<�=�=�=��I�I�I�I�I�I�����	���s   �Y4Z �
Z.�Z)�#Z.�)Z.c                 �D  � 	 d}t          j        |d��  �        }|j        dk    r�|�                    �   �         }|�                    dg �  �        }d}t	          |�  �        D ].\  }}d|d         d	         �                    �   �         v r|d
z   } n�/|r*d|d
z
  dz  z
  }t
          d
t          d|�  �        �  �        }nd}|| _        t          d|d�d|pd� d��  �         |S t          d|j        � ��  �         dS # t          $ r}	t          d|	� ��  �         Y d}	~	dS d}	~	ww xY w)u    Hole News-Sentiment für Bitcoinz0https://api.coingecko.com/api/v3/search/trendingrw   rx   rz   �coinsN�bitcoin�item�namer�   皙�����?r�   皙�����?��������?r   zNews Sentiment: r�   z (Bitcoin Position: zNicht in Top 15�)zSentiment API Fehler: zSentiment Fehler: )r�   r9   r�   r7   �	enumerate�lowerri   rj   r'   r(   r:   )
r*   �url�responser<   �trending_coins�bitcoin_positionr�   �coin�	sentimentr=   s
             r+   �get_news_sentimentz1UltimateBitcoinTradingSystemV2.get_news_sentiment�  s�  � � 	�D�C��|�C��3�3�3�H��#�s�*�*��}�}����!%���'�2�!6�!6�� $(� �(��8�8� � �G�A�t� �D��L��$8�$>�$>�$@�$@�@�@�+,�q�5�(��� A� $� $� #�'7�!�';�s�&B� B�I� #�C��S�)�)<�)<� =� =�I�I� #�I�'0��$��t��t�t�t�L\�Lq�`q�t�t�t�u�u�u� � ��E�x�/C�E�E�F�F�F��s��� 	� 	� 	��*�q�*�*�+�+�+��3�3�3�3�3�����	���s   �CC8 �C8 �8
D�D�Dr�   c                 ��  � 	 g }|d         j         d         }|�                    ||d         j         d         |d         j         d         |d         j         d         g�  �         |�                    |�                    dd�  �        |�                    dd�  �        |�                    d	d
�  �        |�                    dd
�  �        |�                    dd
�  �        |�                    d
d�  �        |�                    dd�  �        |�                    dd�  �        |�                    dd�  �        |�                    dd�  �        |�                    dd
�  �        |�                    dd�  �        |�                    dd�  �        |�                    dd�  �        |�                    dd�  �        g�  �         t          |�  �        dk    r�||d         j         d         z
  |d         j         d         z  ||d         j         d         z
  |d         j         d         z  ||d         j         d          z
  |d         j         d          z  ||d         j         d!         z
  |d         j         d!         z  g}|�                    |�  �         n|�                    g d"��  �         |�                    | j        �  �         t
          j        �   �         }|�                    |j        d#z  |�	                    �   �         d$z  |j
        d%z  g�  �         t          j        |�  �        �
                    d&d�  �        S # t          $ r0}t          d'|� ��  �         t          j        d(�  �        cY d)}~S d)}~ww xY w)*u'   Erstelle Features für Machine LearningrO   rP   rX   rY   rV   r�   r�   r�   r�   r   r�   r�   r�   r   r�   r�   r�   r�   r�   i����r�   r�   r�   i�  r�   r   r�   �      �?r�   ����������i����r�   )r   r   r   r   g      8@g      @g      ?@r�   zFEHLER bei ML-Features: )r�   �   N)rd   �extendr9   rb   r�   r'   r   rD   �hour�weekday�dayr�   �array�reshaper:   r(   �zeros)r*   r�   r�   �featuresr�   �
price_changesrD   r=   s           r+   �create_ml_featuresz1UltimateBitcoinTradingSystemV2.create_ml_features�  s6  � �:	%��H� �w�K�,�R�0�M��O�O���6�
���#��5�	��r�"��8��!�"�%�	� 
� 
� 
� 
�O�O����x��,�,����x��,�,����v�q�)�)����}�a�0�0����/��3�3����}�c�2�2����z�3�/�/����y�"�-�-����y�"�-�-����|�S�1�1����u�a�(�(����u�b�)�)����u�d�+�+������5�5����~�s�3�3�� 
� 
� 
�& �2�w�w�"�}�}�"�R��[�%5�b�%9�9�R��[�=M�b�=Q�Q�"�R��[�%5�b�%9�9�R��[�=M�b�=Q�Q�"�R��[�%5�c�%:�:�b��k�>N�s�>S�S�"�R��[�%5�c�%:�:�b��k�>N�s�>S�S�	!�
� ���
�.�.�.�.�������-�-�-� 
�O�O�D�0�1�1�1� �,�.�.�C��O�O���4�����
�
��#���$��� 
� 
� 
� �8�H�%�%�-�-�a��4�4�4��� 	%� 	%� 	%��0�Q�0�0�1�1�1��8�G�$�$�$�$�$�$�$�$�����	%���s   �L(L+ �+
M%�5%M �M%� M%c                 �h  � t          d�  �         	 t          |�  �        dk     rt          d�  �         dS g }g }t          dt          |�  �        dz
  �  �        D ]�}| �                    |j        d|dz   �         �  �        }| �                    |j        d|dz   �         |�  �        }|d	         j        |         }|d	         j        |dz            }||z
  |z  }	|�                    |�                    �   �         �  �         |�                    |	�  �         ��t          |�  �        d
k     rt          d�  �         dS t          j	        |�  �        }
t          j	        |�  �        }t          |
|dd
��  �        \  }}
}}| j        �                    |�  �         | j        �
                    |�  �        }| j        �
                    |
�  �        }t          dd
d��  �        }|�                    ||�  �         |�                    ||�  �        }t!          j        dd
d��  �        }|�                    ||�  �         |�                    ||�  �        }||||d�| _        t          d�  �         t          d|d���  �         t          d|d���  �         dS # t&          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)z"Trainiere Machine Learning Modellez%Trainiere Machine Learning Modelle...r�   u&   Nicht genügend Daten für ML-TrainingFr�   r�   Nr�   rO   r�   u    Nicht genügend Training-Samplesr  �*   )�	test_size�random_staterP   )�n_estimatorsr4  �n_jobs)�
random_forest�xgboost�rf_score�	xgb_scorezML-Modelle trainiert:u   Random Forest R²: �.3fu
   XGBoost R²: TzFEHLER beim ML-Training: )r(   rb   r�   �calculate_temp_indicatorsrd   �create_temp_featuresr�   �flattenr�   r+  r   r   �fit�	transformr	   �score�xgb�XGBRegressorr   r:   )r*   r�   �
features_list�targetsr�   �temp_indicatorsr.  r�   �future_price�target�X�y�X_train�X_test�y_train�y_test�X_train_scaled�
X_test_scaled�rf_modelr9  �	xgb_modelr:  r=   s                          r+   �train_ml_modelsz.UltimateBitcoinTradingSystemV2.train_ml_models�  s�  � �
�5�6�6�6�?	��2�w�w��}�}��>�?�?�?��u� �M��G� �2�s�2�w�w��|�,�,� 
'� 
'��"&�"@�"@����!�A�#���"O�"O���4�4�R�W�T�a��c�T�]�O�T�T�� !#�7�� 0�� 3�
�!�'�{�/��B��7��&��6�-�G���$�$�X�%5�%5�%7�%7�8�8�8����v�&�&�&�&��=�!�!�B�&�&��8�9�9�9��u����'�'�A����!�!�A� 0@��1�PS�bd�/e�/e�/e�,�G�V�W�f� 
��#�#�G�,�,�,�!�0�:�:�7�C�C�N� �/�9�9�&�A�A�M� -�#�B�WY�Z�Z�Z�H��L�L���1�1�1��~�~�m�V�<�<�H� �(�c��SU�V�V�V�I��M�M�.�'�2�2�2�!���
�v�>�>�I� "*�$�$�&�	� �D�N� 
�*�+�+�+��6��6�6�6�7�7�7��1�)�1�1�1�2�2�2��4��� 	� 	� 	��1�a�1�1�2�2�2��5�5�5�5�5�����	���s$   �"J
 �DJ
 �8EJ
 �

J1�J,�,J1�temp_dfc                 �  � 	 |d         }t          |�  �        dk     ri S |�                    �   �         }|�                    |dk    d�  �        �                    d��  �        �                    �   �         }|�                    |dk     d�  �         �                    d��  �        �                    �   �         }||z  }ddd|z   z  z
  }|�                    d�  �        �                    �   �         }t          j        |j        d         �  �        s
|j        d         nd	t          j        |j        d         �  �        s
|j        d         n|j        d         d
�S #  i cY S xY w)u-   Berechne temporäre Indikatoren für TrainingrO   r�   r   r�   r�   r�   r�   rP   r�   )r�   r�   )rb   r�   r�   r�   r�   rg   r�   rd   )	r*   rT  r�   r�   r�   r�   r�   r�   r�   s	            r+   r<  z8UltimateBitcoinTradingSystemV2.calculate_temp_indicators?  sG  � �	��W�%�F��6�{�{�R����	� �K�K�M�M�E��K�K���	�1�-�-�6�6�b�6�A�A�F�F�H�H�D��[�[����A�.�.�.�7�7�r�7�B�B�G�G�I�I�D����B����B���(�C��^�^�B�'�'�,�,�.�.�F� /1�g�c�h�r�l�.C�.C�K�#�(�2�,�,��13����R��1I�1I�^�&�+�b�/�/�v�{�[]��� � 
��	��I�I�I���s   �E �D3E �ErF  c                 �v  � 	 |d         j         d         |�                    dd�  �        |�                    d|d         j         d         �  �        t          |�  �        dk    r|d         j         d         ndg}t          j        |�  �        �                    d	d�  �        S #  t          j        d
�  �        cY S xY w)u*   Erstelle temporäre Features für TrainingrO   rP   r�   r�   r�   r   rV   i@B r�   )r�   �   )rd   r9   rb   r�   r+  r,  r-  )r*   rT  rF  r.  s       r+   r=  z3UltimateBitcoinTradingSystemV2.create_temp_featuresV  s�   � �		$��� �%�b�)��#�#�H�b�1�1��#�#�H�g�g�.>�.C�B�.G�H�H�.1�'�l�l�Q�.>�.>���!�&�r�*�*�G�	�H� �8�H�%�%�-�-�a��4�4�4��	$��8�F�#�#�#�#�#���s   �BB  � B8c                 �"
  �#� t          d�  �         	 |d         j        d         }| �                    |�  �        }| �                    �   �         }i }d}d}| j        �rm| j        �re	 | �                    ||�  �        }|j        d         | j        j        k    r�| j        �	                    |�  �        }	d| j        v r+| j        d         �
                    |	�  �        d         }
|
|d<   d| j        v r+| j        d         �
                    |	�  �        d         }||d<   |rVt          j        t          |�                    �   �         �  �        �  �        }t          d	dt!          |�  �        d
z  z   �  �        }n,t          d|j        d         � d| j        j        � ��  �         d}n+# t"          $ r}t          d
|� ��  �         d}Y d}~nd}~ww xY wi �#|�                    dd�  �        }
|
dk     rd�#d<   n)|
dk     rd�#d<   n|
dk    rd�#d<   n|
dk    rd�#d<   nd�#d<   |�                    dd�  �        }|�                    dd�  �        }|�                    dd�  �        }||k    r|dk    rd�#d<   n||k     r|dk     rd�#d<   nd�#d<   |�                    dd�  �        }|d k     rd!�#d"<   n|d	k    rd#�#d"<   nd�#d"<   |�                    d$d�  �        }|�                    d%d�  �        }|d&k     r|d&k     rd�#d'<   n|d(k    r|d(k    rd)�#d'<   nd�#d'<   |�                    d*d+�  �        }|d,k    rd-�#d.<   n|dk     rd/�#d.<   nd�#d.<   |d!k    rd�#d0<   n|d-k     rd�#d0<   nd�#d0<   t!          |�  �        d1k    r9t          j        |�  �        t          dt!          |�  �        d&z  �  �        z  �#d2<   nd�#d2<   d3d4d5d6d d d5d7�}t)          �#fd8�|�                    �   �         D �   �         �  �        }|dk    rd9}d:t!          |�  �        d3z  z   }n5|dk     rd;}d:t!          |�  �        d3z  z   }nd<}dt!          |�  �        d-z  z   }t!          |�  �        }dt          j        t          �#�                    �   �         �  �        �  �        d
z  z
  }t          d=||z  d|z   z  �  �        }|�                    d>d1�  �        }|d?z  }t!          |�  �        d k     r|nt          j        |�  �        d z  }|d!z  |d-z  z   }|d|d?z  z   z  |d|d3z  z   z  |d|dz  z   z  |d|z   z  |d|d@z  z   z  |d|dAz  z   z  dB�} | xj        dz
  c_        | xj        dz
  c_        |t3          j        dCd?�  �        z   }!t7          dt          dD|!�  �        �  �        }!|!| j        k    r| xj        dz
  c_        | j        dk    r| j        | j        z  nd| _        |!| j        k    r|!| _        ||||| |�#|||| j        | j        | j        | j        | j        dE�||||dF�dG�}"t          dH�  �         t          dI|� dJ|dK�dL��  �         t          dM| dN         dO���  �         t          dP|dQ���  �         t          dR| j        dK���  �         |"S # t"          $ r}t          dS|� ��  �         i cY d}~S d}~ww xY w)Tz.Ultimate Marktanalyse mit allen Verbesserungenu%   Führe Ultimate Marktanalyse durch...rO   rP   r   r   r�   r7  r8  r  rB   u,   Feature-Dimensionen stimmen nicht überein: z vs zML-Vorhersage Fehler: Nr�   r�   r�   r  r�   �(   g�������?�F   g��������<   g������ٿr�   r�   r�   g333333�?g333333�r�   r�   r�   �	bollingergffffff�r�   r�   r�   �
stochastic�P   g      �r�   r#  g       @r�   rU   g������ɿr   r   �mlr  g
ףp=
�?r   g���Q��?)r�   r�   r\  r]  rU   r   r_  c              3   �P   �K  � | ] \  }}��                     |d �  �        |z  V � �!dS )r   N)r9   )�.0r
  �weight�signalss      �r+   �	<genexpr>zIUltimateBitcoinTradingSystemV2.analyze_market_ultimate.<locals>.<genexpr>�  s:   �� � � �!b�!b�;�3��'�+�+�c�1�"5�"5��">�!b�!b�!b�!b�!b�!br-   �KAUFENg      �?�	VERKAUFEN�HALTENgffffff�?r�   rQ   g      �?r�   )rK   �4h�12h�24h�48h�7dg��������g\���(\�?)r   r"   r#   r    r!   )�signal_strength�	agreement�
volatility�combined_change)r�   r�   �
confidence�weighted_signal�horizonsr&   �individual_signals�ml_predictions�
ml_confidencer'   �
session_stats�signal_analysiszUltimate Analyse abgeschlossen:zSignal: z
 (Konfidenz: r2   r  z24h Vorhersage: $rj  r\   zGewichtetes Signal: r;  r1   z"FEHLER bei Ultimate Marktanalyse: ) r(   rd   r  r!  r   r   r0  �shape�n_features_in_r@  �predictr�   r�   �list�valuesrj   re   r:   r9   �signr�   r�   r�   r   r    r�   r�   ri   r   r!   r"   r#   )$r*   r�   r�   r�   r   ru  rv  �ml_ensembler.  �features_scaled�rf_pred�xgb_predr=   r�   r�   r�   r�   r�   r�   r�   r�   �weightsrr  �final_signal�base_confidencerm  rn  �final_confidencero  �base_change�	ml_changerp  rs  �simulated_accuracy�resultrc  s$                                      @r+   �analyze_market_ultimatez6UltimateBitcoinTradingSystemV2.analyze_market_ultimatec  s�  �� �
�5�6�6�6�U	��w�K�,�R�0�M� �E�E�b�I�I�J� �/�/�1�1�I�  �N��M��K��~� 
$�$�"5� 
$�$�#�6�6�r�:�F�F�H�  �~�a�(�D�,?�,N�N�N�*.�*=�*G�*G��*Q�*Q��*�d�n�<�<�&*�n�_�&E�&M�&M�o�&^�&^�_`�&a�G�>E�N�?�;�$���6�6�'+�~�i�'@�'H�'H��'Y�'Y�Z[�'\�H�8@�N�9�5� *� Q�*,�'�$�~�7L�7L�7N�7N�2O�2O�*P�*P�K�,/��S�3�{�;K�;K�a�;O�5O�,P�,P�M���  I�X�^�\]�M^�  I�  I�dh�dw�  eG�  I�  I�  J�  J�  J�&'���� � $� $� $��6�1�6�6�7�7�7�"#�K�K�K�K�K�K�����$����
 �G� �.�.��2�.�.�C��R�x�x�!$������r���!$������r���!%������r���!%�����!"���� �>�>�&�!�,�,�D�$�.�.���:�:�K�'�^�^�,<�a�@�@�N��k�!�!�n�q�&8�&8�"%�������#�#���(:�(:�"&�����"#���� %�.�.���<�<�K��S� � �'*���$�$��s�"�"�'+���$�$�'(���$� !�n�n�Y��3�3�G� �n�n�Y��3�3�G���|�|��"���(+���%�%��2���'�B�,�,�(,���%�%�()���%� &�>�>�.�#�>�>�L��c�!�!�$'���!�!���#�#�$(���!�!�$%���!� �3���'*���$�$��S���'+���$�$�'(���$� �;���$�&�&� "��� 4� 4�s�3��K�@P�@P�SU�@U�7V�7V� V���
�
� !���
� ��!�"��!��� �G� "�!b�!b�!b�!b�RY�R_�R_�Ra�Ra�!b�!b�!b�b�b�O� ��$�$�'��"&��_�)=�)=��)C�"C��� �4�'�'�*��"&��_�)=�)=��)C�"C���'��"%��O�(<�(<�s�(B�"B�� "�/�2�2�O��B�F�4����(8�(8�#9�#9�:�:�Q�>�>�I�"�4��9�)D��m�H[�)\�]�]�� $�����>�>�J� *�D�0�K�'*�;�'7�'7�#�'=�'=���2�7�;�CW�CW�Z]�C]�I�  +�S�0�Y��_�E�O� $�q�?�T�+A�'A�B�#�q�?�S�+@�'@�A�$��O�c�,A�(A�B�$��O�(;�<�$��O�c�,A�(A�B�#�q�?�S�+@�'@�A�
� �H� 
���!�#����"�"�a�'�"�"� "2�F�N�5�$�4O�4O�!O��!$�S�#�d�4F�*G�*G�!H�!H��!�D�$=�=�=��(�(�A�-�(�(�\`�\r�uv�\v�\v�t�'?�$�BX�'X�'X�|}�D�$�!�D�$6�6�6�%7��"� "/�&�.�#2�$�(2�&-�"0�!.�#,�%)�%7�+/�+C�%)�%7�)-�)?�+/�+C�"� "� (7�!*�",�'6�	$� $�%� �F�4 
�4�5�5�5��O�\�O�O�8H�O�O�O�O�P�P�P��<�h�u�o�<�<�<�=�=�=��>��>�>�>�?�?�?��K�T�-E�K�K�K�L�L�L��M��� 	� 	� 	��:�q�:�:�;�;�;��I�I�I�I�I�I�����	���sJ   �AY& �%D8F �Y& �
G�(G�<Y& �G�RY& �&
Z�0Z	�Z�	Zr�  c                 ��  � t          d�  �         	 |�                    dd�  �        }|�                    dd�  �        }|�                    dd�  �        }|�                    di �  �        �                    d	d
�  �        }d}|}|dz  }|dz  }	|	d
k    r2||	z  }
||
z  d|z
  z
  |
z  }t          d
t          d|�  �        �  �        }nd
}| j        |z  }||z  }
t          dd|dz  z
  �  �        }t          ||z  |
|z  | j        �  �        }||z  }d}|dz  }d|z
  d
z  }t          dt          d||z   |z   �  �        �  �        }t          |�                    di �  �        �                    dd
�  �        �  �        }t          d|dz  �  �        }|dz  }t          d||z   �  �        }|dk    r|d|z
  z  }|d|z   z  }n-|dk    r|d|z   z  }|d|z
  z  }n|d|dz  z
  z  }|d|dz  z   z  }||z  }||z  }|d
k    r||z  nd
}|dz  |z  }|dz  |z  }||z  d|z
  |z  z
  } |d
k    r| |z  nd
}!||z  d|dz  z   z  }"|"d
k    r| |"|z  z  nd
}#|t
          j        dt          j        z  �  �        z  }$|$d
k    r| |$z  nd
}%i d|�d|�d|�d|�d|�d |�d!|�d"|�d#|�d$|�d%|�d&|
�d'|�d(|�d)|!�d*|"�d+|#�|%| ||d,��}&t          d-�  �         t          d.|d/�d0|d1�d2��  �         t          d3|d/�d0|d4�d2��  �         t          d5|d/�d0|d4�d2��  �         t          d6|d7���  �         t          d8|d9���  �         t          d:|!d7���  �         |&S # t          $ r}'t          d;|'� ��  �         i cY d<}'~'S d<}'~'ww xY w)=z!Berechne Advanced Risk Managementz$Berechne Advanced Risk Management...r�   i�� r�   rg  rq  r   rx  ro  r   rB   r�   r   g      �?r�   r�   r�   g{�G�z�?r�   rp  g{�G�z�?r   re  rf  gffffff�?g�p=
ף@�portfolio_value�optimal_position_size�position_value�dynamic_stop_loss�dynamic_take_profit�stop_loss_price�take_profit_price�potential_loss�potential_gain�risk_reward_ratio�kelly_fraction�kelly_position�var_95�var_99�sharpe_ratio�max_drawdown�calmar_ratio)�
sortino_ratio�expected_return�win_probability�volatility_adjustmentzRisk Management berechnet:z
Position: r2   � ($�,.0fr  zStop Loss: r\   z
Take Profit: z
Risk/Reward: r�   zKelly Criterion: r;  zSharpe Ratio: zFEHLER bei Risk Management: N)
r(   r9   ri   rj   r   re   r�   r�   �pir:   )(r*   r�  r�   r�   rq  ro  r�  r�  �avg_win�avg_loss�win_loss_ratior�  �
base_positionr�  r�  r�  r�  �base_stop_loss�volatility_stop�confidence_stopr�  �
expected_move�base_take_profit�confidence_take_profitr�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  �downside_volatilityr�  �risk_metricsr=   s(                                           r+   �"calculate_advanced_risk_managementzAUltimateBitcoinTradingSystemV2.calculate_advanced_risk_management>  s�  � �
�4�5�5�5�x	�"�J�J���?�?�M��Z�Z��(�3�3�F����L�#�6�6�J����$5�r�:�:�>�>�|�T�R�R�J� %�O� )�O� !�1�n�G�!�A�~�H��!�|�|�!(�8�!3��"1�N�"B�a�/�FY�"Z�^l�!l��!$�Q��D�.�(A�(A�!B�!B���!"�� !�2�Z�?�M�+�o�=�N�$'��Q��b��-@�$A�$A�!�$'�� 5�5���0��&�%� %�!� -�/D�D�N� "�N�(�1�n�O� �:�~��5�O� #�D�#�e�^�o�5U�Xg�5g�*h�*h� i� i��  ��
�
�+<�b� A� A� E� E�FW�Y]� ^� ^�_�_�M�"�4���):�;�;��%/�$�%6�"�"%�d�,<�?U�,U�"V�"V�� ��!�!�"/�1�7H�3H�"I��$1�Q�9L�5L�$M�!�!��;�&�&�"/�1�7H�3H�"I��$1�Q�9L�5L�$M�!�!�"/�1�7H�1�7L�3L�"M��$1�Q�9L�q�9P�5P�$Q�!� ,�.?�?�N�+�.A�A�N�CQ�TU�CU�CU��� ?� ?�[\�� $�d�*�Z�7�F�#�d�*�Z�7�F� .�0C�C�q�?�GZ�^o�Fo�o�O�;E��>�>�?�Z�7�7�q�L� *�,=�=��Z�RS�^�AS�T�L� R^�`a�Qa�Qa�?�l�_�.L�M�M�gh�L� #-�t�y��T�W��/E�/E�"E��EX�[\�E\�E\�O�.A�A�A�bc�M��!�?��'�)>�� !�.�� $�%6�	�
 &�':�� "�?�
� $�%6�� !�.�� !�.�� $�%6�� !�.�� !�.�� �&�� �&�� ���  ��!�" ��#�$ "/�#2�#2�)>�+� � �L�0 
�/�0�0�0��S�4�S�S�S�^�S�S�S�S�T�T�T��Q� 1�Q�Q�Q�/�Q�Q�Q�Q�R�R�R��W�"5�W�W�W�>O�W�W�W�W�X�X�X��9�"3�9�9�9�:�:�:��:�n�:�:�:�;�;�;��5�<�5�5�5�6�6�6����� 	� 	� 	��4��4�4�5�5�5��I�I�I�I�I�I�����	���s   �L1M �
M+�
M&� M+�&M+N)�__name__�
__module__�__qualname__�__doc__r,   r)   rG   rg   rh   ru   r   r   rc   rl   r  r�   r!  r�   �ndarrayr0  �boolrS  r<  r=  r�  r�  � r-   r+   r   r   *   s�  � � � � � �� �$"� $"� $"�L<� <� <�$@� @� @�&A1�b�l� A1� A1� A1� A1�F�(�4�.� � � � �B?��� ?� ?� ?� ?�Bu�"�,� u�4� u� u� u� u�n"�E� "� "� "� "�H<%�R�\� <%�t� <%��
� <%� <%� <%� <%�|C�"�,� C�4� C� C� C� C�J��� �$� � � � �.$�B�L� $�4� $�TV�T^� $� $� $� $�Y�"�,� Y�4� Y� Y� Y� Y�v|�� |�$� |� |� |� |� |� |r-   r   c                  �4  � t          d�  �         t          d�  �         t          �   �         } 	 t          j        �   �         }t          dd� ��  �         t          d| j        dz   � ��  �         t          d� �  �         | �                    �   �         }|j        rt          d�  �         dS t          d	�  �         | �                    |�  �        }|st          d
�  �         dS | �                    |�  �        }||d<   | �                    �   �          t          |�  �         t          j        �   �         |z
  }t          d|d
�d��  �         t          d�  �         |S # t          $ r5}t          d|� ��  �         ddl}|�                    �   �          Y d}~dS d}~ww xY w)z4HAUPTFUNKTION - Ultimate Bitcoin Trading System V2.0z.STARTE ULTIMATE BITCOIN TRADING SYSTEM V2.0...zFALLE VERBESSERUNGEN INTEGRIERT - ECHTE APIs + ML + ERWEITERTE ANALYSE!�
zx========================================================================================================================z0ULTIMATE BITCOIN TRADING SYSTEM V2.0 - SESSION #r�   u$   FEHLER: Keine Marktdaten verfügbar!NuQ   ML-Training deaktiviert für ersten Test - verwende erweiterte technische Analysez-FEHLER: Ultimate Marktanalyse fehlgeschlagen!r�  z 
Ultimate System V2.0 Laufzeit: r�   �sz2ERFOLGREICH: ULTIMATE BITCOIN TRADING SYSTEM V2.0!z FEHLER im Ultimate System V2.0: r   )
r(   r   �timer   ru   ra   r�  r�  rG   �display_ultimate_results_v2r:   �	traceback�	print_exc)�system�
start_timer�   r�  r�  �runtimer=   r�  s           r+   �&run_ultimate_bitcoin_trading_system_v2r�  �  s�  � � 
�
:�;�;�;�	�
R�S�S�S�
+�
-�
-�F�/��Y�[�[�
�
�n�7�n�n����
�[��AU�XY�AY�[�[�\�\�\�
��l���� �
(�
(�
*�
*��
�8� 	��8�9�9�9��4� 	�a�b�b�b� �/�/��3�3��� 	��A�B�B�B��4� �@�@��H�H��!-��~�� 	�!�!�#�#�#� 	$�F�+�+�+��)�+�+�
�*��
�@�'�@�@�@�@�A�A�A�
�C�D�D�D��
��� � � �
�4��4�4�5�5�5������������t�t�t�t�t�����	���s%   �A9E �)5E � A7E �
F�"*F�Fr�  c           
      �
  � t          d�  �         t          d�  �         t          d�  �         | �r�| �                    dd�  �        }| �                    dd�  �        }| �                    dd�  �        }| �                    d	d�  �        }t          d
�  �         t          d|d���  �         t          d
|� ��  �         t          d|d���  �         t          d|d���  �         | �                    di �  �        }|rMt          d�  �         |�                    �   �         D ])\  }}||z
  |z  }t          d|d�d|d�d|d�d��  �         �*| �                    di �  �        }	|	�rGt          d�  �         t          d|	�                    dd�  �        d���  �         t          d |	�                    d!d�  �        d"���  �         t          d#|	�                    d$d�  �        d"���  �         t          d%|	�                    d&d�  �        d"���  �         t          d'|	�                    d(d�  �        d���  �         t          d)|	�                    d*d�  �        d���  �         t          d+|	�                    d,d�  �        d���  �         t          d-|	�                    d.d�  �        d���  �         | �                    d/i �  �        }
|
rRt          d0�  �         |
�                    �   �         D ].\  }}t          d|�                    �   �         d1�d2|d3���  �         �/| �                    d4i �  �        }
| �                    d5d�  �        }|
ret          d6�  �         |
�                    �   �         D ].\  }}t          d|�                    �   �         d7�d2|d8���  �         �/t          d9|d���  �         | �                    d:d;�  �        }t          d<|d"���  �         | �                    d=i �  �        }|�r�t          d>�  �         t          d?|�                    d@d�  �        d�dA|�                    dBd�  �        dC�d��  �         t          dD|�                    dEd�  �        d�dA|�                    dFd�  �        d�d��  �         t          dG|�                    dHd�  �        d�dA|�                    dId�  �        d�d��  �         t          dJ|�                    dKd�  �        d"���  �         t          dL|�                    dMd�  �        d���  �         t          dN|�                    dOd�  �        d"���  �         t          dP|�                    dQd�  �        d"���  �         t          dR|�                    dSd�  �        d"���  �         t          dT|�                    dUd�  �        dC���  �         t          dV|�                    dWd�  �        dC���  �         | �                    dXi �  �        }|r�t          dY�  �         t          dZ|�                    d[d�  �        � ��  �         t          d\|�                    d]d�  �        d���  �         t          d^|�                    d_d�  �        d���  �         t          d`|�                    dad�  �        � ��  �         t          db|�                    dcd�  �        � ��  �         t          dd�  �         t          de�  �         dgS t          df�  �         dgS )hzZeige Ultimate Ergebnisse V2.0z�
============================================================================================================================================z5ULTIMATE BITCOIN TRADING SYSTEM V2.0 - LIVE DASHBOARDz�============================================================================================================================================r�   r   r�   zN/Arq  rr  z
MARKTDATEN (ECHTE APIs):z   Bitcoin-Preis: $r\   z   Ultimate Signal: z   Konfidenz: r2   z   Gewichtetes Signal: r;  rs  z
HORIZONT-VORHERSAGEN:z   z>4z: $z>12,.2fz (z+8.2%r  r&   z
TECHNISCHE INDIKATOREN:z
   RSI (14): r�   r�   z	   MACD: r�   r�   z   MACD Signal: r�   z   BB Position: r�   z   Stochastic %K: r�   z   Williams %R: r�   z   CCI: r�   z   MFI: r�   rt  z
INDIVIDUAL SIGNALS:z>12z: z+6.3fru  rv  z
MACHINE LEARNING:z>15z+8.3fz   ML Konfidenz: r'   r   z
NEWS SENTIMENT: r�  z
ADVANCED RISK MANAGEMENT:z
   Position: r�  r�  r�  r�  z   Stop Loss: r�  r�  z   Take Profit: r�  r�  z   Risk/Reward: r�  z   Kelly Criterion: r�  z   Sharpe Ratio: r�  z   Calmar Ratio: r�  z   Sortino Ratio: r�  z
   VaR 95%: $r�  z
   VaR 99%: $r�  rw  z
SESSION STATISTIKEN:z
   Session: #r   z   Kumulative Genauigkeit: r"   z   Beste Genauigkeit: r#   z   Gesamte Vorhersagen: r    z   Korrekte Vorhersagen: r!   zG
ULTIMATE BITCOIN TRADING SYSTEM V2.0 - ALLE VERBESSERUNGEN INTEGRIERT!zNEchte APIs + Machine Learning + Erweiterte Analyse + Advanced Risk Management!z4
ULTIMATE BITCOIN TRADING SYSTEM V2.0 fehlgeschlagenN)r(   r9   r�   �upper)r�  r�   r�   rq  rr  rs  rL   rN   �changer�   rc  �signal_name�signal_valueru  rv  �
model_name�
predictionr   r�  rw  s                       r+   r�  r�  �  s.  � � 
�.����	�
A�B�B�B�	�'�N�N�N�
� RH��
�
�?�A�6�6�
����H�e�,�,���Z�Z��a�0�0�
� �*�*�%6��:�:��
�+�,�,�,�
�8�M�8�8�8�9�9�9�
�-�V�-�-�.�.�.�
�/�z�/�/�/�0�0�0�
�=��=�=�=�>�>�>� �:�:�j�"�-�-��� 	L��,�-�-�-�!)���!1�!1� 
L� 
L�
����-�/�=�@���J�F�J�J�J�%�J�J�J�6�J�J�J�J�K�K�K�K� �Z�Z� 6��;�;�
�� 		=��.�/�/�/��C�*�.�.��1�"=�"=�C�C�C�D�D�D��=�j�n�n�V�Q�7�7�=�=�=�>�>�>��K�Z�^�^�M�1�%E�%E�K�K�K�L�L�L��K�Z�^�^�M�1�%E�%E�K�K�K�L�L�L��I�z�~�~�i��'C�'C�I�I�I�J�J�J��J�Z�^�^�L�!�%D�%D�J�J�J�K�K�K��;�Z�^�^�E�1�5�5�;�;�;�<�<�<��;�Z�^�^�E�1�5�5�;�;�;�<�<�<� �*�*�1�2�6�6��� 	M��*�+�+�+�-4�]�]�_�_� 
M� 
M�)��\��K�K�-�-�/�/�K�K�K�|�K�K�K�L�L�L�L�  ���$4�b�9�9���
�
�?�A�6�6�
�� 	;��(�)�)�)�*8�*>�*>�*@�*@� 
J� 
J�&�
�J��H�J�,�,�.�.�H�H�H�j�H�H�H�I�I�I�I��9�m�9�9�9�:�:�:� �J�J�0�#�6�6�	�
�2�9�2�2�2�3�3�3� �z�z�.�"�5�5��� 	H��0�1�1�1��  E�,�"2�"2�3J�A�"N�"N�  E�  E�  E�Wc�Wg�Wg�hx�z{�W|�W|�  E�  E�  E�  E�  
F�  
F�  
F��  C�<�#3�#3�4G��#K�#K�  C�  C�  C�T`�Td�Td�ev�xy�Tz�Tz�  C�  C�  C�  C�  
D�  
D�  
D��  I�\�%5�%5�6K�Q�%O�%O�  I�  I�  I�Xd�Xh�Xh�i|�~�  YA�  YA�  I�  I�  I�  I�  
J�  
J�  
J��S�\�%5�%5�6I�1�%M�%M�S�S�S�T�T�T��T��)9�)9�:J�A�)N�)N�T�T�T�U�U�U��O�l�&6�&6�~�q�&I�&I�O�O�O�P�P�P��O�l�&6�&6�~�q�&I�&I�O�O�O�P�P�P��Q�|�'7�'7���'K�'K�Q�Q�Q�R�R�R��F�,�"2�"2�8�Q�"?�"?�F�F�F�G�G�G��F�,�"2�"2�8�Q�"?�"?�F�F�F�G�G�G� �
�
�?�B�7�7�
�� 	]��+�,�,�,��I�-�"3�"3�O�Q�"G�"G�I�I�J�J�J��a�
�0A�0A�BW�YZ�0[�0[�a�a�a�b�b�b��V�=�+<�+<�_�a�+P�+P�V�V�V�W�W�W��X�]�->�->�?R�TU�-V�-V�X�X�Y�Y�Y��[�m�.?�.?�@U�WX�.Y�.Y�[�[�\�\�\�
�Y�Z�Z�Z�
�_�`�`�`�`�`�
�F�G�G�G�G�Gr-   �__main__)$r�  �pandasrg   �numpyr�   r�   �yfinancer^   r   r   �warningsr�  r7   r3   �typingr   r   r   r   r�   r�   �sklearn.ensembler	   �sklearn.preprocessingr
   �sklearn.model_selectionr   r8  rB  �textblobr   �filterwarningsr   r�  r�  r�  r�  r-   r+   �<module>r�     s�  ��� �  � � � � � � � � ���� � � � � (� (� (� (� (� (� (� (� ���� ���� ���� 	�	�	�	� .� .� .� .� .� .� .� .� .� .� .� .� 
�
�
�
� ���� 3� 2� 2� 2� 2� 2� 0� 0� 0� 0� 0� 0� 4� 4� 4� 4� 4� 4� � � � � � � � � � � �� �� !� !� !�P� P� P� P� P� P� P� P�d7� 7� 7�rYH�� YH� YH� YH� YH�v �z���*�*�,�,�,�,�,� �r-   