@echo off
title Bitcoin Trading GUI - FINAL VERSION
color 0B

echo ================================================================================
echo BITCOIN TRADING GUI - FINAL VERSION STARTER
echo ================================================================================
echo.
echo Starte Bitcoin Trading GUI...
echo.

cd /d "e:\Dev"

echo Pruefe Python Installation...
python --version
if errorlevel 1 (
    echo FEHLER: Python ist nicht installiert oder nicht im PATH!
    echo Bitte installieren Sie Python 3.8+ von https://python.org
    pause
    exit /b 1
)

echo.
echo Pruefe erforderliche Pakete...
python -c "import tkinter, matplotlib, yfinance, pandas, numpy, sklearn" 2>nul
if errorlevel 1 (
    echo Installiere erforderliche Pakete...
    pip install yfinance pandas numpy scikit-learn requests matplotlib seaborn tkinter
)

echo.
echo ================================================================================
echo STARTE BITCOIN TRADING GUI - FINAL VERSION
echo ================================================================================
echo.

python bitcoin_gui_final.py

echo.
echo ================================================================================
echo Bitcoin Trading GUI beendet.
echo ================================================================================
pause
