#!/usr/bin/env python3
"""
PERFORMANCE-TEST - Bitcoin Prediction Model
Testet verschiedene Konfigurationen und misst Performance
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers.legacy import Adam
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, r2_score
import time
import os
import warnings
warnings.filterwarnings('ignore')

print("⚡ PERFORMANCE-TEST - Bitcoin Prediction Model")
print("=" * 60)

# TEST-KONFIGURATIONEN
TEST_CONFIGS = [
    {
        'name': 'Schnell',
        'look_back': 12,
        'epochs': 10,
        'batch_size': 64,
        'lstm_units': 32,
        'features': 'basic'
    },
    {
        'name': 'Standard',
        'look_back': 24,
        'epochs': 30,
        'batch_size': 32,
        'lstm_units': 64,
        'features': 'extended'
    },
    {
        'name': 'Intensiv',
        'look_back': 48,
        'epochs': 50,
        'batch_size': 16,
        'lstm_units': 128,
        'features': 'full'
    }
]

def load_and_prepare_data():
    """Lade und bereite Daten vor"""
    print("📊 Lade Daten...")
    df = pd.read_csv('crypto_data.csv')
    df['time'] = pd.to_datetime(df['time'])
    df.set_index('time', inplace=True)
    print(f"   ✅ {len(df)} Datenpunkte geladen")
    return df

def create_features(df, feature_level='basic'):
    """Erstelle Features basierend auf Level"""
    features = df[['open', 'high', 'low', 'close', 'volume']].copy()
    
    if feature_level in ['basic', 'extended', 'full']:
        # Basis Features
        features['sma_10'] = df['close'].rolling(10).mean()
        features['sma_20'] = df['close'].rolling(20).mean()
        features['rsi'] = calculate_rsi(df['close'], 14)
        features['volatility'] = df['close'].pct_change().rolling(10).std()
    
    if feature_level in ['extended', 'full']:
        # Erweiterte Features
        features['sma_50'] = df['close'].rolling(50).mean()
        features['ema_12'] = df['close'].ewm(span=12).mean()
        features['ema_26'] = df['close'].ewm(span=26).mean()
        features['macd'] = features['ema_12'] - features['ema_26']
        features['bb_width'] = calculate_bb_width(df['close'], 20)
        features['volume_sma'] = df['volume'].rolling(20).mean()
        features['volume_ratio'] = df['volume'] / features['volume_sma']
    
    if feature_level == 'full':
        # Vollständige Features
        for period in [5, 14, 21]:
            features[f'sma_{period}'] = df['close'].rolling(period).mean()
            features[f'rsi_{period}'] = calculate_rsi(df['close'], period)
        
        features['atr'] = calculate_atr(df)
        features['momentum'] = df['close'] - df['close'].shift(10)
        features['roc'] = df['close'].pct_change(periods=10) * 100
        
        # Zeitbasierte Features
        features['hour_sin'] = np.sin(2 * np.pi * df.index.hour / 24)
        features['hour_cos'] = np.cos(2 * np.pi * df.index.hour / 24)
        features['dow_sin'] = np.sin(2 * np.pi * df.index.dayofweek / 7)
        features['dow_cos'] = np.cos(2 * np.pi * df.index.dayofweek / 7)
    
    return features.dropna()

def calculate_rsi(prices, period=14):
    """RSI Berechnung"""
    delta = prices.diff()
    gain = delta.where(delta > 0, 0).rolling(period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def calculate_bb_width(prices, period=20):
    """Bollinger Band Width"""
    sma = prices.rolling(period).mean()
    std = prices.rolling(period).std()
    return (std * 2) / sma

def calculate_atr(df, period=14):
    """Average True Range"""
    high_low = df['high'] - df['low']
    high_close = (df['high'] - df['close'].shift()).abs()
    low_close = (df['low'] - df['close'].shift()).abs()
    true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    return true_range.rolling(period).mean()

def create_sequences(data, target, look_back):
    """Erstelle Sequenzen"""
    X, y = [], []
    for i in range(look_back, len(data)):
        X.append(data[i-look_back:i])
        y.append(target[i])
    return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)

def build_model(input_shape, config):
    """Erstelle Modell basierend auf Konfiguration"""
    model = Sequential([
        LSTM(config['lstm_units'], return_sequences=True, dropout=0.2, input_shape=input_shape),
        LSTM(config['lstm_units']//2, return_sequences=False, dropout=0.2),
        Dense(config['lstm_units']//4, activation='relu'),
        Dropout(0.3),
        Dense(1)
    ])
    
    model.compile(optimizer=Adam(0.001), loss='mse', metrics=['mae'])
    return model

def run_performance_test(config, df):
    """Führe Performance-Test für eine Konfiguration durch"""
    print(f"\n🔥 Teste Konfiguration: {config['name']}")
    print("-" * 40)
    
    start_time = time.time()
    
    try:
        # Features erstellen
        print(f"   📊 Erstelle {config['features']} Features...")
        features = create_features(df, config['features'])
        print(f"      ✅ {len(features.columns)} Features erstellt")
        
        # Daten vorbereiten
        X = features.drop('close', axis=1)
        y = features['close'].values
        
        scaler_X = MinMaxScaler()
        scaler_y = MinMaxScaler()
        
        X_scaled = scaler_X.fit_transform(X)
        y_scaled = scaler_y.fit_transform(y.reshape(-1, 1)).flatten()
        
        # Sequenzen erstellen
        X_seq, y_seq = create_sequences(X_scaled, y_scaled, config['look_back'])
        
        # Train-Test Split
        train_size = int(len(X_seq) * 0.8)
        X_train, X_test = X_seq[:train_size], X_seq[train_size:]
        y_train, y_test = y_seq[:train_size], y_seq[train_size:]
        
        print(f"   📦 Sequenzen: {len(X_seq)} (Train: {len(X_train)}, Test: {len(X_test)})")
        
        # Modell erstellen und trainieren
        print(f"   🤖 Trainiere Modell ({config['epochs']} Epochen)...")
        model = build_model((X_train.shape[1], X_train.shape[2]), config)
        
        train_start = time.time()
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=config['epochs'],
            batch_size=config['batch_size'],
            verbose=0
        )
        train_time = time.time() - train_start
        
        # Evaluation
        print(f"   📊 Evaluiere Performance...")
        y_pred = model.predict(X_test, verbose=0)
        
        # Skalierung rückgängig
        y_test_orig = scaler_y.inverse_transform(y_test.reshape(-1, 1)).flatten()
        y_pred_orig = scaler_y.inverse_transform(y_pred).flatten()
        
        # Metriken berechnen
        r2 = r2_score(y_test_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
        mae = np.mean(np.abs(y_test_orig - y_pred_orig))
        mape = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig)) * 100
        
        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(y_pred_orig) > 0
            direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            direction_acc = 0
        
        total_time = time.time() - start_time
        
        # Ergebnisse
        results = {
            'config': config['name'],
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'mape': mape,
            'direction_accuracy': direction_acc,
            'train_time': train_time,
            'total_time': total_time,
            'features_count': len(features.columns),
            'test_samples': len(y_test_orig),
            'model_params': model.count_params()
        }
        
        print(f"   ✅ ERGEBNISSE:")
        print(f"      R²: {r2:.4f} ({r2*100:.1f}%)")
        print(f"      RMSE: ${rmse:.2f}")
        print(f"      MAE: ${mae:.2f}")
        print(f"      MAPE: {mape:.2f}%")
        print(f"      Direction Acc: {direction_acc:.1f}%")
        print(f"      Training Zeit: {train_time:.1f}s")
        print(f"      Gesamt Zeit: {total_time:.1f}s")
        print(f"      Parameter: {model.count_params():,}")
        
        return True, results
        
    except Exception as e:
        print(f"   ❌ FEHLER: {e}")
        return False, None

def compare_results(all_results):
    """Vergleiche alle Testergebnisse"""
    print("\n" + "=" * 60)
    print("📊 PERFORMANCE-VERGLEICH")
    print("=" * 60)
    
    # Tabelle erstellen
    df_results = pd.DataFrame(all_results)
    
    print("\n🏆 RANKING nach R²:")
    df_sorted = df_results.sort_values('r2', ascending=False)
    for i, (_, row) in enumerate(df_sorted.iterrows()):
        medal = "🥇" if i == 0 else "🥈" if i == 1 else "🥉"
        print(f"   {medal} {row['config']}: R² = {row['r2']:.4f} ({row['r2']*100:.1f}%)")
    
    print(f"\n⚡ GESCHWINDIGKEITS-RANKING:")
    df_speed = df_results.sort_values('total_time')
    for i, (_, row) in enumerate(df_speed.iterrows()):
        medal = "🥇" if i == 0 else "🥈" if i == 1 else "🥉"
        print(f"   {medal} {row['config']}: {row['total_time']:.1f}s")
    
    print(f"\n🎯 EFFIZIENZ-RANKING (R²/Zeit):")
    df_results['efficiency'] = df_results['r2'] / df_results['total_time']
    df_eff = df_results.sort_values('efficiency', ascending=False)
    for i, (_, row) in enumerate(df_eff.iterrows()):
        medal = "🥇" if i == 0 else "🥈" if i == 1 else "🥉"
        print(f"   {medal} {row['config']}: {row['efficiency']:.6f}")
    
    # Visualisierung
    plt.figure(figsize=(15, 10))
    
    # R² Vergleich
    plt.subplot(2, 3, 1)
    plt.bar(df_results['config'], df_results['r2'], color=['gold', 'silver', '#CD7F32'])
    plt.title('R² Score Vergleich')
    plt.ylabel('R² Score')
    plt.ylim(0, 1)
    
    # Zeit Vergleich
    plt.subplot(2, 3, 2)
    plt.bar(df_results['config'], df_results['total_time'], color=['lightblue', 'lightgreen', 'lightcoral'])
    plt.title('Ausführungszeit')
    plt.ylabel('Zeit (Sekunden)')
    
    # RMSE Vergleich
    plt.subplot(2, 3, 3)
    plt.bar(df_results['config'], df_results['rmse'], color=['lightpink', 'lightyellow', 'lightgray'])
    plt.title('RMSE Vergleich')
    plt.ylabel('RMSE ($)')
    
    # Effizienz
    plt.subplot(2, 3, 4)
    plt.bar(df_results['config'], df_results['efficiency'], color=['purple', 'orange', 'green'])
    plt.title('Effizienz (R²/Zeit)')
    plt.ylabel('Effizienz')
    
    # Parameter Count
    plt.subplot(2, 3, 5)
    plt.bar(df_results['config'], df_results['model_params'], color=['red', 'blue', 'green'])
    plt.title('Modell-Parameter')
    plt.ylabel('Anzahl Parameter')
    
    # Direction Accuracy
    plt.subplot(2, 3, 6)
    plt.bar(df_results['config'], df_results['direction_accuracy'], color=['cyan', 'magenta', 'yellow'])
    plt.title('Richtungsgenauigkeit')
    plt.ylabel('Genauigkeit (%)')
    
    plt.tight_layout()
    plt.show()
    
    # Empfehlung
    best_overall = df_sorted.iloc[0]
    best_speed = df_speed.iloc[0]
    best_efficiency = df_eff.iloc[0]
    
    print(f"\n💡 EMPFEHLUNGEN:")
    print(f"   🎯 Beste Genauigkeit: {best_overall['config']} (R² = {best_overall['r2']:.4f})")
    print(f"   ⚡ Schnellste: {best_speed['config']} ({best_speed['total_time']:.1f}s)")
    print(f"   🏆 Beste Effizienz: {best_efficiency['config']} (Effizienz = {best_efficiency['efficiency']:.6f})")

def main():
    """Hauptfunktion für Performance-Test"""
    print(f"💻 CPU-Kerne: {os.cpu_count()}")
    print(f"🎮 GPU verfügbar: {'Ja' if len(tf.config.experimental.list_physical_devices('GPU')) > 0 else 'Nein'}")
    
    # Daten laden
    df = load_and_prepare_data()
    
    # Alle Konfigurationen testen
    all_results = []
    
    for config in TEST_CONFIGS:
        success, results = run_performance_test(config, df)
        if success:
            all_results.append(results)
    
    # Ergebnisse vergleichen
    if all_results:
        compare_results(all_results)
        
        print(f"\n✅ PERFORMANCE-TEST ABGESCHLOSSEN!")
        print(f"   {len(all_results)} Konfigurationen getestet")
    else:
        print(f"\n❌ PERFORMANCE-TEST FEHLGESCHLAGEN!")

if __name__ == "__main__":
    main()
