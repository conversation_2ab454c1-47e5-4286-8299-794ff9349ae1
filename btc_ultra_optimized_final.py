#!/usr/bin/env python3
"""
🎯 ULTRA OPTIMIZED BITCOIN PREDICTION - FINAL VERSION
Radikal optimiert für kleine Datensätze mit maximaler Genauigkeit
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, <PERSON><PERSON>, Dropout, BatchNormalization
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers import Adam
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor
import warnings
import time
import os
warnings.filterwarnings('ignore')

print("🎯 ULTRA OPTIMIZED BITCOIN PREDICTION - FINAL VERSION")
print("=" * 60)
print("🚀 Radikal optimiert für MAXIMALE Genauigkeit")
print("💡 Speziell für kleine Datensätze entwickelt")
print("=" * 60)

# ULTRA OPTIMIERTE KONFIGURATION (Radikal angepasst)
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.70,           # Mehr Test-Daten
    'validation_split': 0.20,      # Ausreichend Validation
    'test_split': 0.10,            # Genug Test-Daten
    'look_back': 12,               # VIEL kürzer für kleine Datensätze
    'batch_size': 32,              # Kleinere Batches
    'epochs': 50,                  # Weniger Epochen gegen Overfitting
    'patience': 15,                # Früh stoppen
    'learning_rate': 0.001,        # Standard Learning Rate
    'dropout_rate': 0.5,           # HOHE Regularisierung
    'n_best_features': 8,          # NUR die allerbesten Features
    'target_accuracy': 0.85        # Realistisches Ziel
}

# Hardware-Optimierung
tf.config.threading.set_intra_op_parallelism_threads(0)
tf.config.threading.set_inter_op_parallelism_threads(0)
os.environ['OMP_NUM_THREADS'] = str(os.cpu_count())

print(f"💻 CPU-Kerne: {os.cpu_count()}")

class UltraOptimizedPredictor:
    """Ultra optimiert für kleine Datensätze"""
    
    def __init__(self):
        self.scalers = {}
        self.feature_selector = None
        self.selected_features = None
    
    def create_essential_features(self):
        """NUR die essentiellsten Features - Qualität über Quantität"""
        print("\n📊 ESSENTIAL FEATURE ENGINEERING")
        print("-" * 40)
        
        df = pd.read_csv(CONFIG['data_file'])
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        print(f"📈 Daten: {len(df)} Punkte")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
        
        # Basis OHLCV
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # === NUR DIE BESTEN FEATURES ===
        print("   🎯 Erstelle nur die BESTEN Features...")
        
        # 1. Returns (wichtigster Indikator)
        features['returns'] = df['close'].pct_change()
        features['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # 2. Einfache Moving Averages (nur die wichtigsten)
        for period in [5, 10, 20]:
            features[f'sma_{period}'] = df['close'].rolling(period).mean()
            features[f'price_sma_{period}_ratio'] = df['close'] / features[f'sma_{period}']
        
        # 3. RSI (Standard 14)
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # 4. MACD (Standard)
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        features['macd'] = ema_12 - ema_26
        features['macd_signal'] = features['macd'].ewm(span=9).mean()
        
        # 5. Bollinger Bands
        sma_20 = df['close'].rolling(20).mean()
        std_20 = df['close'].rolling(20).std()
        features['bb_upper'] = sma_20 + (std_20 * 2)
        features['bb_lower'] = sma_20 - (std_20 * 2)
        features['bb_position'] = (df['close'] - features['bb_lower']) / (features['bb_upper'] - features['bb_lower'])
        
        # 6. Volumen
        features['volume_sma'] = df['volume'].rolling(10).mean()
        features['volume_ratio'] = df['volume'] / features['volume_sma']
        
        # 7. Preis-Ratios
        features['hl_ratio'] = df['high'] / df['low']
        features['co_ratio'] = df['close'] / df['open']
        
        # 8. Zeit-Features (nur die wichtigsten)
        features['hour'] = df.index.hour
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        
        # Cleanup
        features = features.dropna()
        
        print(f"   ✅ {len(features.columns)} Essential Features erstellt")
        print(f"   📊 {len(features)} saubere Datenpunkte")
        
        return features
    
    def ultra_feature_selection(self, features):
        """Ultra aggressive Feature Selection"""
        print("\n🎯 ULTRA FEATURE SELECTION")
        print("-" * 30)
        
        X = features.drop('close', axis=1)
        y = features['close']
        
        print(f"📊 Vor Selektion: {X.shape[1]} Features")
        
        # Nur die allerbesten Features
        self.feature_selector = SelectKBest(score_func=f_regression, k=CONFIG['n_best_features'])
        X_selected = self.feature_selector.fit_transform(X, y)
        
        # Feature Namen
        selected_mask = self.feature_selector.get_support()
        self.selected_features = X.columns[selected_mask].tolist()
        
        print(f"✅ Nach Selektion: {len(self.selected_features)} Features")
        
        # Top Features anzeigen
        feature_scores = self.feature_selector.scores_[selected_mask]
        top_features = sorted(zip(self.selected_features, feature_scores), key=lambda x: x[1], reverse=True)
        
        print(f"\n🏆 TOP {len(self.selected_features)} FEATURES:")
        for i, (feature, score) in enumerate(top_features):
            print(f"   {i+1}. {feature}: {score:.2f}")
        
        # Finales DataFrame
        selected_df = X[self.selected_features].copy()
        selected_df['close'] = features['close']
        
        return selected_df
    
    def create_sequences_optimized(self, data, target, look_back):
        """Optimierte Sequenz-Erstellung"""
        X, y = [], []
        for i in range(look_back, len(data)):
            X.append(data[i-look_back:i])
            y.append(target[i])
        return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)
    
    def prepare_data_ultra(self, features):
        """Ultra optimierte Datenaufbereitung"""
        print("\n🔄 ULTRA DATENAUFBEREITUNG")
        print("-" * 30)
        
        # Feature Selection
        features_selected = self.ultra_feature_selection(features)
        
        # Features und Target
        X = features_selected.drop('close', axis=1)
        y = features_selected['close'].values
        
        print(f"📊 Finale Features: {X.shape[1]}")
        print(f"📊 Samples: {len(y)}")
        
        # Optimale Skalierung
        print("   🔧 Skaliere Daten...")
        feature_scaler = StandardScaler()
        target_scaler = MinMaxScaler()
        
        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        
        # Sequenzen erstellen
        print(f"   📦 Erstelle Sequenzen (Look-back: {CONFIG['look_back']})...")
        X_seq, y_seq = self.create_sequences_optimized(X_scaled, y_scaled, CONFIG['look_back'])
        
        print(f"   ✅ {len(X_seq)} Sequenzen erstellt")
        print(f"   📐 Sequenz-Shape: {X_seq.shape}")
        
        # Speichere Scaler
        self.scalers['feature'] = feature_scaler
        self.scalers['target'] = target_scaler
        
        return X_seq, y_seq
    
    def split_data_optimized(self, X, y):
        """Optimierte Datenaufteilung"""
        print("\n✂️  OPTIMIERTE DATENAUFTEILUNG")
        print("-" * 25)
        
        total_size = len(X)
        train_size = int(total_size * CONFIG['train_split'])
        val_size = int(total_size * CONFIG['validation_split'])
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        X_val = X[train_size:train_size+val_size]
        y_val = y[train_size:train_size+val_size]
        X_test = X[train_size+val_size:]
        y_test = y[train_size+val_size:]
        
        print(f"📊 Training: {len(X_train)} ({len(X_train)/total_size*100:.1f}%)")
        print(f"📊 Validation: {len(X_val)} ({len(X_val)/total_size*100:.1f}%)")
        print(f"📊 Test: {len(X_test)} ({len(X_test)/total_size*100:.1f}%)")
        
        return (X_train, y_train), (X_val, y_val), (X_test, y_test)
    
    def build_ultra_model(self, input_shape):
        """Ultra optimiertes Modell - Einfach aber effektiv"""
        print("🎯 Baue Ultra Optimiertes Modell...")
        
        model = Sequential(name='UltraOptimizedPredictor')
        
        # Einfache aber effektive Architektur
        model.add(LSTM(32, return_sequences=True, dropout=CONFIG['dropout_rate'], 
                      recurrent_dropout=CONFIG['dropout_rate'], input_shape=input_shape))
        model.add(BatchNormalization())
        
        model.add(LSTM(16, return_sequences=False, dropout=CONFIG['dropout_rate'], 
                      recurrent_dropout=CONFIG['dropout_rate']))
        model.add(BatchNormalization())
        
        # Einfache Dense Layers
        model.add(Dense(16, activation='relu'))
        model.add(Dropout(CONFIG['dropout_rate']))
        
        model.add(Dense(8, activation='relu'))
        model.add(Dropout(CONFIG['dropout_rate'] * 0.5))
        
        model.add(Dense(1))
        
        # Optimierter Optimizer
        optimizer = Adam(learning_rate=CONFIG['learning_rate'])
        model.compile(optimizer=optimizer, loss='mse', metrics=['mae'])
        
        print(f"   ✅ Ultra Model: {model.count_params():,} Parameter")
        return model
    
    def train_ultra_model(self, model, X_train, y_train, X_val, y_val):
        """Ultra optimiertes Training"""
        print(f"\n🎯 ULTRA TRAINING")
        print("-" * 20)
        
        # Ultra Callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=CONFIG['patience'],
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=CONFIG['patience']//2,
                min_lr=1e-6,
                verbose=1
            )
        ]
        
        start_time = time.time()
        
        # Training
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            callbacks=callbacks,
            verbose=1
        )
        
        training_time = time.time() - start_time
        
        print(f"   ✅ Training: {training_time:.1f}s")
        
        return model, history
    
    def evaluate_ultra_model(self, model, X_test, y_test):
        """Ultra Evaluation"""
        print(f"\n📊 ULTRA EVALUATION")
        print("-" * 20)
        
        # Vorhersagen
        y_pred = model.predict(X_test, verbose=0)
        
        # Skalierung rückgängig
        y_test_orig = self.scalers['target'].inverse_transform(y_test.reshape(-1, 1)).flatten()
        y_pred_orig = self.scalers['target'].inverse_transform(y_pred).flatten()
        
        # Metriken
        r2 = r2_score(y_test_orig, y_pred_orig)
        rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
        mae = mean_absolute_error(y_test_orig, y_pred_orig)
        mape = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig)) * 100
        
        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(y_pred_orig) > 0
            direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            direction_acc = 0
        
        # Accuracy Bands
        accuracy_5pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.05) * 100
        accuracy_10pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.10) * 100
        
        results = {
            'r2': r2,
            'rmse': rmse,
            'mae': mae,
            'mape': mape,
            'direction_accuracy': direction_acc,
            'accuracy_5pct': accuracy_5pct,
            'accuracy_10pct': accuracy_10pct,
            'y_test_orig': y_test_orig,
            'y_pred_orig': y_pred_orig
        }
        
        print(f"   📈 R²: {r2:.4f} ({r2*100:.1f}%)")
        print(f"   💰 RMSE: ${rmse:.2f}")
        print(f"   📊 MAPE: {mape:.2f}%")
        print(f"   🎯 Direction: {direction_acc:.1f}%")
        print(f"   ✅ 5% Accuracy: {accuracy_5pct:.1f}%")
        print(f"   ✅ 10% Accuracy: {accuracy_10pct:.1f}%")
        
        return results

    def plot_ultra_results(self, results):
        """Ultra Visualisierung"""
        print(f"\n📈 ULTRA VISUALISIERUNG")
        print("-" * 25)

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('🎯 ULTRA OPTIMIZED BITCOIN PREDICTION - RESULTS', fontsize=16, fontweight='bold')

        y_test = results['y_test_orig']
        y_pred = results['y_pred_orig']

        # 1. Vorhersage vs Actual
        axes[0, 0].plot(y_test, 'g-', label='Actual', linewidth=2, alpha=0.8)
        axes[0, 0].plot(y_pred, 'r--', label='Predicted', linewidth=2)
        axes[0, 0].set_title('Vorhersage vs Actual')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 2. Scatter Plot
        axes[0, 1].scatter(y_test, y_pred, alpha=0.6, c='blue')
        min_val = min(y_test.min(), y_pred.min())
        max_val = max(y_test.max(), y_pred.max())
        axes[0, 1].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
        axes[0, 1].set_title('Scatter Plot')
        axes[0, 1].set_xlabel('Actual')
        axes[0, 1].set_ylabel('Predicted')
        axes[0, 1].grid(True, alpha=0.3)

        # 3. Error Distribution
        residuals = y_test - y_pred
        axes[1, 0].hist(residuals, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[1, 0].set_title('Error Distribution')
        axes[1, 0].set_xlabel('Prediction Error')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].grid(True, alpha=0.3)

        # 4. Performance Metrics
        axes[1, 1].axis('off')

        target_reached = "✅ JA" if results['r2'] >= CONFIG['target_accuracy'] else "❌ NEIN"

        summary_text = f"""
🎯 ULTRA OPTIMIZED RESULTS

📈 PERFORMANCE:
   R²: {results['r2']:.4f} ({results['r2']*100:.1f}%)
   RMSE: ${results['rmse']:.2f}
   MAPE: {results['mape']:.2f}%
   Direction: {results['direction_accuracy']:.1f}%

✅ ACCURACY BANDS:
   5% Band: {results['accuracy_5pct']:.1f}%
   10% Band: {results['accuracy_10pct']:.1f}%

🎯 ZIEL ERREICHT: {target_reached}
   Target: {CONFIG['target_accuracy']*100:.0f}%
   Erreicht: {results['r2']*100:.1f}%

📊 SYSTEM:
   Features: {len(self.selected_features)}
   Test Samples: {len(y_test)}
        """

        axes[1, 1].text(0.1, 0.5, summary_text, fontsize=11, verticalalignment='center',
                        bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

        plt.tight_layout()
        plt.show()

        print("   ✅ Visualisierung abgeschlossen")

def main():
    """ULTRA OPTIMIZED HAUPTFUNKTION"""
    print("🎯 STARTE ULTRA OPTIMIZED SYSTEM")
    print("=" * 50)

    start_time = time.time()

    # System initialisieren
    predictor = UltraOptimizedPredictor()

    # === DATENAUFBEREITUNG ===
    print(f"\n📊 DATENAUFBEREITUNG")
    print("=" * 25)

    # Essential Features erstellen
    features = predictor.create_essential_features()

    # Daten vorbereiten
    X, y = predictor.prepare_data_ultra(features)

    # Daten aufteilen
    (X_train, y_train), (X_val, y_val), (X_test, y_test) = predictor.split_data_optimized(X, y)

    print(f"\n✅ DATENAUFBEREITUNG ABGESCHLOSSEN!")
    print(f"   📊 Features: {len(predictor.selected_features)}")
    print(f"   📦 Sequenzen: {len(X)}")

    # === MODELL-TRAINING ===
    print(f"\n🤖 ULTRA MODELL-TRAINING")
    print("=" * 30)

    input_shape = (X_train.shape[1], X_train.shape[2])

    # Ultra Model
    model = predictor.build_ultra_model(input_shape)
    model, history = predictor.train_ultra_model(model, X_train, y_train, X_val, y_val)

    # === EVALUATION ===
    results = predictor.evaluate_ultra_model(model, X_test, y_test)

    # === FINALE ANALYSE ===
    print(f"\n🏆 FINALE ULTRA ANALYSE")
    print("=" * 35)

    total_time = time.time() - start_time

    # Ziel-Check
    if results['r2'] >= CONFIG['target_accuracy']:
        print(f"\n🎉🎉🎉 ZIEL ERREICHT! 🎉🎉🎉")
        print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {results['r2']*100:.1f}%")
        improvement = (results['r2'] - CONFIG['target_accuracy']) * 100
        print(f"🚀 Übererfüllung: +{improvement:.1f} Prozentpunkte!")
    elif results['r2'] >= 0.70:
        print(f"\n🔥🔥 SEHR GUT! 🔥🔥")
        print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {results['r2']*100:.1f}%")
        gap = (CONFIG['target_accuracy'] - results['r2']) * 100
        print(f"Nur noch {gap:.1f} Prozentpunkte bis zum Ziel!")
    elif results['r2'] >= 0.50:
        print(f"\n💪💪 GUT! 💪💪")
        print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {results['r2']*100:.1f}%")
        gap = (CONFIG['target_accuracy'] - results['r2']) * 100
        print(f"Noch {gap:.1f} Prozentpunkte bis zum Ziel")
    elif results['r2'] >= 0:
        print(f"\n✅ POSITIVE ERGEBNISSE!")
        print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {results['r2']*100:.1f}%")
        print(f"Modell lernt - weitere Optimierung möglich!")
    else:
        print(f"\n🔧 OPTIMIERUNG ERFORDERLICH")
        print(f"R²: {results['r2']*100:.1f}% - Modell braucht Anpassungen")

    # Performance Details
    print(f"\n📈 DETAILLIERTE PERFORMANCE:")
    print(f"   R²: {results['r2']:.4f} ({results['r2']*100:.1f}%)")
    print(f"   RMSE: ${results['rmse']:.2f}")
    print(f"   MAPE: {results['mape']:.2f}%")
    print(f"   Direction: {results['direction_accuracy']:.1f}%")
    print(f"   5% Accuracy: {results['accuracy_5pct']:.1f}%")
    print(f"   10% Accuracy: {results['accuracy_10pct']:.1f}%")

    # System Stats
    print(f"\n⚡ SYSTEM-STATISTIKEN:")
    print(f"   Zeit: {total_time:.1f}s")
    print(f"   Features: {len(predictor.selected_features)}")
    print(f"   Test Samples: {len(results['y_test_orig'])}")
    print(f"   Model Parameter: {model.count_params():,}")
    print(f"   CPU-Kerne: {os.cpu_count()}")

    # Visualisierung
    predictor.plot_ultra_results(results)

    print(f"\n✅ ULTRA OPTIMIZED SYSTEM ABGESCHLOSSEN!")

    return results

if __name__ == "__main__":
    result = main()

    if result['r2'] >= CONFIG['target_accuracy']:
        print(f"\n🎯 MISSION ACCOMPLISHED! 🎯")
        print(f"Ultra System erreicht das Ziel von {CONFIG['target_accuracy']*100:.0f}%!")
    elif result['r2'] >= 0:
        print(f"\n💪 GUTE BASIS GESCHAFFEN!")
        print(f"Positive R² erreicht - System funktioniert!")
    else:
        print(f"\n🔧 WEITERE OPTIMIERUNG NÖTIG")
        print(f"System braucht weitere Anpassungen.")
