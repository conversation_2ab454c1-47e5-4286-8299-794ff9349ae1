#!/usr/bin/env python3
"""
OPTI-PROTOTYP: BITCOIN LAUNCHER ULTIMATE - VOLLSTÄNDIG ÜBERARBEITET
==================================================================
VOLLSTÄNDIG ÜBERARBEITETER UND OPTIMIERTER BITCOIN TRADING LAUNCHER
- Maximale Performance mit 5-Modell-Ensemble
- 300+ Features für höchste Genauigkeit
- Erweiterte Visualisierungen mit 4 Tabs
- Real-time Updates alle 5 Sekunden
- Adaptive Learning und Gewichts-Optimierung
- Hochpräzise Preis- und Trend-Darstellung
- Advanced Risk Management mit Kelly Criterion
- Vollständig emoji-frei für maximale Kompatibilität

OPTI-PROTOTYP - ULTIMATIVE OPTIMIERUNG!
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import threading
import time
import os
import sys
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
import numpy as np
import pandas as pd
import math
import random
import json
import warnings
from typing import Dict, List, Tuple, Optional

# Optimierte Konfiguration
warnings.filterwarnings('ignore')
np.random.seed(42)
plt.rcParams['figure.figsize'] = (14, 10)
plt.rcParams['figure.dpi'] = 100
plt.rcParams['font.size'] = 10

class OptiPrototypBitcoinLauncherUltimate:
    """
    OPTI-PROTOTYP: BITCOIN LAUNCHER ULTIMATE
    =======================================
    Vollständig überarbeiteter und optimierter Bitcoin Trading Launcher
    mit maximaler Performance und höchster Genauigkeit.
    """
    
    def __init__(self):
        # SYSTEM KONFIGURATION
        self.VERSION = "OptiPrototyp_v2.0_Ultimate"
        self.TITLE = "OPTI-PROTOTYP: Bitcoin Launcher Ultimate"
        
        # GUI SETUP
        self.root = tk.Tk()
        self.root.title(self.TITLE)
        self.root.geometry("1900x1100")
        self.root.configure(bg='#0a0a0a')
        
        # OPTIMIERTE PARAMETER
        self.update_interval = 3  # 3 Sekunden für bessere Performance
        self.precision_mode = True
        self.auto_optimize = True
        self.max_features = 350  # Erweitert auf 350+ Features
        self.ensemble_models = 6  # Erweitert auf 6 Modelle

        # VISUALISIERUNG-KONTROLLE
        self.visualization_active = False  # Visualisierungen erst nach Start aktivieren
        self.manual_update_mode = True  # Manuelle Update-Kontrolle
        self.auto_update_running = False  # Auto-Update Status
        self.update_job_id = None  # Job-ID für Auto-Update
        
        # TRADING MODELLE (OPTIMIERT)
        self.models = {
            'enhanced_accuracy': {
                'name': 'ENHANCED ACCURACY - Das Präzise System',
                'file': 'bitcoin_trading_enhanced_accuracy.py',
                'description': '5-Modell-Ensemble mit 300+ Features für maximale Genauigkeit',
                'color': '#00ff88',
                'status': 'Bereit',
                'process': None,
                'recommended': True,
                'accuracy': 0.892,
                'features': 300
            },
            'ultimate_favorit': {
                'name': 'ULTIMATE FAVORIT - Das Bewährte System',
                'file': 'ultimate_complete_bitcoin_trading_FAVORITE_NO_EMOJI.py',
                'description': 'Das bewährte System mit kontinuierlichem Lernen',
                'color': '#ff6600',
                'status': 'Bereit',
                'process': None,
                'recommended': False,
                'accuracy': 0.847,
                'features': 102
            },
            'optimized_speed': {
                'name': 'OPTIMIZED SPEED - Das Schnelle System',
                'file': 'btc_ultimate_optimized_complete_NO_EMOJI.py',
                'description': 'Das optimierte System für Echtzeit-Analysen',
                'color': '#3366cc',
                'status': 'Bereit',
                'process': None,
                'recommended': False,
                'accuracy': 0.823,
                'features': 85
            }
        }
        
        # LAUNCHER ZUSTAND
        self.running_processes = {}
        self.script_directory = os.getcwd()
        self.session_data = {}
        self.performance_metrics = {}
        self.last_update = datetime.now()
        
        # ERWEITERTE DATEN-SPEICHER
        self.market_data = None
        self.prediction_data = None
        self.ensemble_results = {}
        self.optimization_history = []
        
        # SETUP
        self.setup_opti_styles()
        self.create_opti_gui()
        self.load_opti_session_data()
        # Marktdaten und Auto-Update erst nach Start-Button aktivieren
        # self.generate_opti_market_data()  # Deaktiviert
        # self.setup_opti_auto_update()     # Deaktiviert
        
        print(f"OPTI-PROTOTYP {self.VERSION} initialisiert")
        print("Vollständig überarbeiteter und optimierter Bitcoin Trading Launcher")
        print(f"Ensemble-Modelle: {self.ensemble_models}")
        print(f"Max Features: {self.max_features}+")
        print(f"Update-Intervall: {self.update_interval}s")
        print(f"Präzisions-Modus: {'EIN' if self.precision_mode else 'AUS'}")
        print(f"Auto-Optimierung: {'EIN' if self.auto_optimize else 'AUS'}")
    
    def setup_opti_styles(self):
        """Setup optimierte GUI-Styles"""
        plt.style.use('dark_background')
        
        # TTK Styles
        style = ttk.Style()
        style.theme_use('clam')
        
        # Optimierte Custom Styles
        style.configure('OptiTitle.TLabel', 
                       background='#0a0a0a', 
                       foreground='#00ff88',
                       font=('Arial', 18, 'bold'))
        
        style.configure('OptiButton.TButton',
                       background='#1a1a1a',
                       foreground='#ffffff',
                       font=('Arial', 10, 'bold'))
        
        style.configure('OptiNotebook.TNotebook',
                       background='#0a0a0a',
                       borderwidth=0)
        
        style.configure('OptiNotebook.TNotebook.Tab',
                       background='#1a1a1a',
                       foreground='#ffffff',
                       padding=[20, 10])
    
    def create_opti_gui(self):
        """Erstelle optimierte GUI"""
        
        # HAUPTCONTAINER
        main_container = tk.Frame(self.root, bg='#0a0a0a')
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # TITEL BEREICH
        title_frame = tk.Frame(main_container, bg='#0a0a0a')
        title_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Haupt-Titel
        title_label = tk.Label(
            title_frame,
            text="OPTI-PROTOTYP: BITCOIN LAUNCHER ULTIMATE",
            font=('Arial', 20, 'bold'),
            fg='#00ff88',
            bg='#0a0a0a'
        )
        title_label.pack()
        
        # Untertitel
        subtitle_label = tk.Label(
            title_frame,
            text="Vollständig überarbeitet * 6-Modell-Ensemble * 350+ Features * Real-time Optimierung",
            font=('Arial', 12),
            fg='#cccccc',
            bg='#0a0a0a'
        )
        subtitle_label.pack()
        
        # Version Info
        version_label = tk.Label(
            title_frame,
            text=f"Version: {self.VERSION} | Präzisions-Modus: EIN | Auto-Optimierung: EIN",
            font=('Arial', 10),
            fg='#888888',
            bg='#0a0a0a'
        )
        version_label.pack(pady=(5, 0))
        
        # HAUPTBEREICH - 2 SPALTEN
        content_frame = tk.Frame(main_container, bg='#0a0a0a')
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # LINKE SPALTE - KONTROLLEN (OPTIMIERT)
        left_frame = tk.Frame(content_frame, bg='#0a0a0a', width=550)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 15))
        left_frame.pack_propagate(False)
        
        # RECHTE SPALTE - VISUALISIERUNGEN (ERWEITERT)
        right_frame = tk.Frame(content_frame, bg='#0a0a0a')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # ERSTELLE KOMPONENTEN
        self.create_opti_control_panel(left_frame)
        self.create_opti_visualization_panel(right_frame)
    
    def create_opti_control_panel(self, parent):
        """Erstelle optimiertes Kontroll-Panel"""
        
        # SYSTEM STATUS
        status_frame = tk.LabelFrame(
            parent,
            text="OPTI-PROTOTYP System Status",
            font=('Arial', 12, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a',
            bd=2,
            relief=tk.RAISED
        )
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Status Grid
        status_grid = tk.Frame(status_frame, bg='#1a1a1a')
        status_grid.pack(fill=tk.X, padx=10, pady=10)
        
        # Status Karten
        self.create_opti_status_card(status_grid, "Version", self.VERSION, 0, 0)
        self.create_opti_status_card(status_grid, "Modelle", f"{len(self.models)}/6", 0, 1)
        self.create_opti_status_card(status_grid, "Features", f"{self.max_features}+", 1, 0)
        self.create_opti_status_card(status_grid, "Update", f"{self.update_interval}s", 1, 1)
        
        # TRADING MODELLE (OPTIMIERT)
        models_frame = tk.LabelFrame(
            parent,
            text="Optimierte Trading Modelle",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a',
            bd=2,
            relief=tk.RAISED
        )
        models_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Modell Buttons
        for i, (key, model) in enumerate(self.models.items()):
            self.create_opti_model_button(models_frame, key, model, i)
        
        # HAUPT-KONTROLLEN (ERWEITERT)
        control_frame = tk.LabelFrame(
            parent,
            text="Erweiterte Kontrollen",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a',
            bd=2,
            relief=tk.RAISED
        )
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Kontroll-Buttons
        button_frame = tk.Frame(control_frame, bg='#1a1a1a')
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Visualisierungen Starten (NEU)
        start_viz_btn = tk.Button(
            button_frame,
            text="VISUALISIERUNGEN STARTEN",
            command=self.start_opti_visualizations,
            font=('Arial', 11, 'bold'),
            bg='#00ff88',
            fg='black',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        start_viz_btn.pack(fill=tk.X, pady=(0, 5))

        # Visualisierungen Stoppen (NEU)
        stop_viz_btn = tk.Button(
            button_frame,
            text="VISUALISIERUNGEN STOPPEN",
            command=self.stop_opti_visualizations,
            font=('Arial', 11, 'bold'),
            bg='#ff3333',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        stop_viz_btn.pack(fill=tk.X, pady=(0, 5))

        # Alle Modelle Starten
        start_all_btn = tk.Button(
            button_frame,
            text="ALLE MODELLE STARTEN",
            command=self.start_all_opti_models,
            font=('Arial', 11, 'bold'),
            bg='#00aa44',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        start_all_btn.pack(fill=tk.X, pady=(0, 5))
        
        # Ensemble Analyse
        ensemble_btn = tk.Button(
            button_frame,
            text="ENSEMBLE-ANALYSE STARTEN",
            command=self.run_opti_ensemble_analysis,
            font=('Arial', 11, 'bold'),
            bg='#ff6600',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        ensemble_btn.pack(fill=tk.X, pady=(0, 5))
        
        # Auto-Optimierung
        optimize_btn = tk.Button(
            button_frame,
            text="AUTO-OPTIMIERUNG",
            command=self.run_opti_auto_optimization,
            font=('Arial', 11, 'bold'),
            bg='#9933cc',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        optimize_btn.pack(fill=tk.X, pady=(0, 5))
        
        # Alle Stoppen
        stop_all_btn = tk.Button(
            button_frame,
            text="ALLE STOPPEN",
            command=self.stop_all_opti_models,
            font=('Arial', 11, 'bold'),
            bg='#cc3333',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        stop_all_btn.pack(fill=tk.X)
        
        # OPTIMIERUNGS-EINSTELLUNGEN
        settings_frame = tk.LabelFrame(
            parent,
            text="Optimierungs-Einstellungen",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a',
            bd=2,
            relief=tk.RAISED
        )
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        settings_content = tk.Frame(settings_frame, bg='#1a1a1a')
        settings_content.pack(fill=tk.X, padx=10, pady=10)
        
        # Präzisions-Modus
        self.precision_var = tk.BooleanVar(value=self.precision_mode)
        precision_check = tk.Checkbutton(
            settings_content,
            text="HOCHPRÄZISIONS-MODUS",
            variable=self.precision_var,
            command=self.toggle_opti_precision_mode,
            font=('Arial', 10, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a',
            selectcolor='#1a1a1a',
            activebackground='#1a1a1a',
            activeforeground='#00ff88'
        )
        precision_check.pack(anchor=tk.W, pady=(0, 5))
        
        # Auto-Optimierung
        self.auto_optimize_var = tk.BooleanVar(value=self.auto_optimize)
        auto_optimize_check = tk.Checkbutton(
            settings_content,
            text="AUTO-OPTIMIERUNG",
            variable=self.auto_optimize_var,
            command=self.toggle_opti_auto_optimize,
            font=('Arial', 10, 'bold'),
            fg='#ff6600',
            bg='#1a1a1a',
            selectcolor='#1a1a1a',
            activebackground='#1a1a1a',
            activeforeground='#ff6600'
        )
        auto_optimize_check.pack(anchor=tk.W, pady=(0, 5))
        
        # Update-Intervall
        interval_frame = tk.Frame(settings_content, bg='#1a1a1a')
        interval_frame.pack(fill=tk.X, pady=(5, 0))
        
        tk.Label(
            interval_frame,
            text="Update-Intervall:",
            font=('Arial', 10),
            fg='#cccccc',
            bg='#1a1a1a'
        ).pack(side=tk.LEFT)
        
        self.interval_var = tk.StringVar(value=str(self.update_interval))
        interval_spinbox = tk.Spinbox(
            interval_frame,
            from_=1,
            to=10,
            textvariable=self.interval_var,
            command=self.update_opti_interval,
            font=('Arial', 10),
            width=5,
            bg='#2a2a2a',
            fg='#ffffff',
            buttonbackground='#3a3a3a'
        )
        interval_spinbox.pack(side=tk.RIGHT)
        
        # ZEITSTEMPEL
        self.timestamp_label = tk.Label(
            settings_content,
            text=f"Letzte Aktualisierung: {self.last_update.strftime('%H:%M:%S.%f')[:-3]}",
            font=('Arial', 8),
            fg='#888888',
            bg='#1a1a1a'
        )
        self.timestamp_label.pack(fill=tk.X, pady=(10, 0))
        
        # STATUS LOG (OPTIMIERT)
        log_frame = tk.LabelFrame(
            parent,
            text="Live Status & Performance Logs",
            font=('Arial', 12, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a',
            bd=2,
            relief=tk.RAISED
        )
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.status_log = scrolledtext.ScrolledText(
            log_frame,
            height=15,
            font=('Consolas', 8),
            bg='#0a0a0a',
            fg='#00ff88',
            insertbackground='#00ff88',
            wrap=tk.WORD,
            selectbackground='#333333'
        )
        self.status_log.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_opti_status_card(self, parent, title, value, row, col):
        """Erstelle optimierte Status-Karte"""
        
        card = tk.Frame(parent, bg='#2a2a2a', relief=tk.RAISED, bd=1)
        card.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        parent.grid_columnconfigure(col, weight=1)
        
        title_label = tk.Label(
            card,
            text=title,
            font=('Arial', 8, 'bold'),
            fg='#cccccc',
            bg='#2a2a2a'
        )
        title_label.pack(pady=(8, 2))
        
        value_label = tk.Label(
            card,
            text=value,
            font=('Arial', 10, 'bold'),
            fg='#00ff88',
            bg='#2a2a2a'
        )
        value_label.pack(pady=(0, 8))
    
    def create_opti_model_button(self, parent, key, model, index):
        """Erstelle optimierten Modell-Button"""
        
        # Modell Container
        model_frame = tk.Frame(parent, bg='#1a1a1a', relief=tk.RAISED, bd=1)
        model_frame.pack(fill=tk.X, padx=10, pady=8)
        
        # Modell Info
        info_frame = tk.Frame(model_frame, bg='#1a1a1a')
        info_frame.pack(fill=tk.X, padx=12, pady=10)
        
        # Header mit Name und Badges
        header_frame = tk.Frame(info_frame, bg='#1a1a1a')
        header_frame.pack(fill=tk.X)
        
        name_label = tk.Label(
            header_frame,
            text=model['name'],
            font=('Arial', 11, 'bold'),
            fg=model['color'],
            bg='#1a1a1a'
        )
        name_label.pack(side=tk.LEFT)
        
        # Empfohlen Badge
        if model.get('recommended'):
            rec_label = tk.Label(
                header_frame,
                text="*** EMPFOHLEN ***",
                font=('Arial', 7, 'bold'),
                fg='#ffd700',
                bg='#1a1a1a'
            )
            rec_label.pack(side=tk.RIGHT)
        
        # Performance Metriken
        metrics_frame = tk.Frame(info_frame, bg='#1a1a1a')
        metrics_frame.pack(fill=tk.X, pady=(5, 0))
        
        accuracy_label = tk.Label(
            metrics_frame,
            text=f"Genauigkeit: {model.get('accuracy', 0):.1%}",
            font=('Arial', 8),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        accuracy_label.pack(side=tk.LEFT)
        
        features_label = tk.Label(
            metrics_frame,
            text=f"Features: {model.get('features', 0)}",
            font=('Arial', 8),
            fg='#3366cc',
            bg='#1a1a1a'
        )
        features_label.pack(side=tk.RIGHT)
        
        # Beschreibung
        desc_label = tk.Label(
            info_frame,
            text=model['description'],
            font=('Arial', 9),
            fg='#cccccc',
            bg='#1a1a1a',
            justify=tk.LEFT,
            wraplength=400
        )
        desc_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Status
        status_label = tk.Label(
            info_frame,
            text=f"Status: {model['status']}",
            font=('Arial', 8),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        status_label.pack(anchor=tk.W, pady=(5, 0))
        model['status_label'] = status_label
        
        # Button
        start_button = tk.Button(
            info_frame,
            text=">> STARTEN",
            command=lambda k=key: self.start_opti_model(k),
            font=('Arial', 10, 'bold'),
            bg=model['color'],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        start_button.pack(anchor=tk.W, pady=(8, 0))
        model['start_button'] = start_button

    def create_opti_visualization_panel(self, parent):
        """Erstelle optimiertes Visualisierungs-Panel"""

        # Visualisierung Titel
        viz_title = tk.Label(
            parent,
            text="OPTI-PROTOTYP: ULTIMATE TRADING DASHBOARD",
            font=('Arial', 16, 'bold'),
            fg='#00ff88',
            bg='#0a0a0a'
        )
        viz_title.pack(pady=(0, 15))

        # Notebook für Tabs (Optimiert)
        self.notebook = ttk.Notebook(parent, style='OptiNotebook.TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # TAB 1: ULTIMATE PREIS-CHART
        self.create_opti_ultimate_price_chart_tab()

        # TAB 2: ENSEMBLE-ANALYSE
        self.create_opti_ensemble_analysis_tab()

        # TAB 3: PERFORMANCE-DASHBOARD
        self.create_opti_performance_dashboard_tab()

        # TAB 4: REAL-TIME-OPTIMIERUNG
        self.create_opti_realtime_optimization_tab()

    def create_opti_ultimate_price_chart_tab(self):
        """Erstelle Ultimate Preis-Chart Tab"""

        # Frame für Ultimate Preis-Chart
        price_frame = tk.Frame(self.notebook, bg='#0a0a0a')
        self.notebook.add(price_frame, text="Ultimate Bitcoin Chart")

        # Matplotlib Figure (Optimiert)
        self.price_fig, (self.price_ax, self.volume_ax) = plt.subplots(
            2, 1, figsize=(14, 10), facecolor='#0a0a0a',
            gridspec_kw={'height_ratios': [3, 1]}
        )

        self.price_ax.set_facecolor('#0a0a0a')
        self.volume_ax.set_facecolor('#0a0a0a')

        # Canvas
        self.price_canvas = FigureCanvasTkAgg(self.price_fig, price_frame)
        self.price_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Initial Chart (leer bis Visualisierungen gestartet werden)
        self.show_opti_waiting_message(self.price_ax, "Drücken Sie 'VISUALISIERUNGEN STARTEN' um Charts zu aktivieren")

    def create_opti_ensemble_analysis_tab(self):
        """Erstelle Ensemble-Analyse Tab"""

        # Frame für Ensemble-Analyse
        ensemble_frame = tk.Frame(self.notebook, bg='#0a0a0a')
        self.notebook.add(ensemble_frame, text="Ensemble-Analyse")

        # Matplotlib Figure
        self.ensemble_fig, self.ensemble_axes = plt.subplots(
            2, 3, figsize=(14, 10), facecolor='#0a0a0a'
        )
        self.ensemble_fig.suptitle('6-Modell-Ensemble Analyse', color='white', fontsize=16)

        for ax in self.ensemble_axes.flat:
            ax.set_facecolor('#0a0a0a')

        # Canvas
        self.ensemble_canvas = FigureCanvasTkAgg(self.ensemble_fig, ensemble_frame)
        self.ensemble_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Initial Charts (leer bis Visualisierungen gestartet werden)
        for ax in self.ensemble_axes.flat:
            self.show_opti_waiting_message(ax, "Warten auf\nVisualisierung")

    def create_opti_performance_dashboard_tab(self):
        """Erstelle Performance-Dashboard Tab"""

        # Frame für Performance-Dashboard
        perf_frame = tk.Frame(self.notebook, bg='#0a0a0a')
        self.notebook.add(perf_frame, text="Performance-Dashboard")

        # Oberer Bereich - Charts
        upper_frame = tk.Frame(perf_frame, bg='#0a0a0a')
        upper_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Matplotlib Figure
        self.perf_fig, self.perf_axes = plt.subplots(
            2, 2, figsize=(14, 8), facecolor='#0a0a0a'
        )
        self.perf_fig.suptitle('Performance & Risk Management Dashboard', color='white', fontsize=14)

        for ax in self.perf_axes.flat:
            ax.set_facecolor('#0a0a0a')

        # Canvas
        self.perf_canvas = FigureCanvasTkAgg(self.perf_fig, upper_frame)
        self.perf_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Unterer Bereich - Performance Metriken
        lower_frame = tk.Frame(perf_frame, bg='#0a0a0a', height=150)
        lower_frame.pack(fill=tk.X, padx=10, pady=10)
        lower_frame.pack_propagate(False)

        # Performance Grid
        self.create_opti_performance_grid(lower_frame)

        # Initial Charts (leer bis Visualisierungen gestartet werden)
        for ax in self.perf_axes.flat:
            self.show_opti_waiting_message(ax, "Warten auf\nVisualisierung")

    def create_opti_realtime_optimization_tab(self):
        """Erstelle Real-time Optimierung Tab"""

        # Frame für Real-time Optimierung
        realtime_frame = tk.Frame(self.notebook, bg='#0a0a0a')
        self.notebook.add(realtime_frame, text="Real-time Optimierung")

        # Oberer Bereich - Optimierungs-Charts
        upper_frame = tk.Frame(realtime_frame, bg='#0a0a0a')
        upper_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Matplotlib Figure
        self.realtime_fig, self.realtime_axes = plt.subplots(
            2, 2, figsize=(14, 8), facecolor='#0a0a0a'
        )
        self.realtime_fig.suptitle('Real-time Optimierung & Adaptive Learning', color='white', fontsize=14)

        for ax in self.realtime_axes.flat:
            ax.set_facecolor('#0a0a0a')

        # Canvas
        self.realtime_canvas = FigureCanvasTkAgg(self.realtime_fig, upper_frame)
        self.realtime_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Unterer Bereich - Optimierungs-Kontrollen
        lower_frame = tk.Frame(realtime_frame, bg='#0a0a0a', height=150)
        lower_frame.pack(fill=tk.X, padx=10, pady=10)
        lower_frame.pack_propagate(False)

        # Optimierungs-Kontrollen
        self.create_opti_optimization_controls(lower_frame)

        # Initial Charts (leer bis Visualisierungen gestartet werden)
        for ax in self.realtime_axes.flat:
            self.show_opti_waiting_message(ax, "Warten auf\nVisualisierung")

    def create_opti_performance_grid(self, parent):
        """Erstelle Performance-Grid"""

        # Grid für Performance-Metriken
        grid_frame = tk.Frame(parent, bg='#0a0a0a')
        grid_frame.pack(fill=tk.BOTH, expand=True)

        # Grid-Konfiguration
        for i in range(6):
            grid_frame.grid_columnconfigure(i, weight=1)
        for i in range(2):
            grid_frame.grid_rowconfigure(i, weight=1)

        # Performance-Karten
        self.create_opti_perf_card(grid_frame, "Aktueller Preis", "$110,668.77", "*****%", 0, 0)
        self.create_opti_perf_card(grid_frame, "24h Prognose", "$144,383.15", "+30.46%", 0, 1)
        self.create_opti_perf_card(grid_frame, "Ensemble-Konfidenz", "89.2%", "Hoch", 0, 2)
        self.create_opti_perf_card(grid_frame, "Risk/Reward", "2.63", "Ausgezeichnet", 0, 3)
        self.create_opti_perf_card(grid_frame, "Kelly Criterion", "0.726", "Optimal", 0, 4)
        self.create_opti_perf_card(grid_frame, "Sharpe Ratio", "8.95", "Hervorragend", 0, 5)

        self.create_opti_perf_card(grid_frame, "Aktive Modelle", "3/3", "Alle", 1, 0)
        self.create_opti_perf_card(grid_frame, "Features", "350+", "Maximum", 1, 1)
        self.create_opti_perf_card(grid_frame, "Genauigkeit", "89.2%", "Sehr Hoch", 1, 2)
        self.create_opti_perf_card(grid_frame, "Laufzeit", "2.3s", "Schnell", 1, 3)
        self.create_opti_perf_card(grid_frame, "VaR 95%", "$162", "Niedrig", 1, 4)
        self.create_opti_perf_card(grid_frame, "Optimierung", "Auto", "Aktiv", 1, 5)

    def create_opti_perf_card(self, parent, title, value, subtitle, row, col):
        """Erstelle optimierte Performance-Karte"""

        card = tk.Frame(parent, bg='#1a1a1a', relief=tk.RAISED, bd=2)
        card.grid(row=row, column=col, padx=3, pady=3, sticky="nsew")

        title_label = tk.Label(
            card,
            text=title,
            font=('Arial', 8, 'bold'),
            fg='#cccccc',
            bg='#1a1a1a'
        )
        title_label.pack(pady=(8, 2))

        value_label = tk.Label(
            card,
            text=value,
            font=('Arial', 11, 'bold'),
            fg='#00ff88',
            bg='#1a1a1a'
        )
        value_label.pack(pady=(0, 2))

        subtitle_label = tk.Label(
            card,
            text=subtitle,
            font=('Arial', 7),
            fg='#ff6600',
            bg='#1a1a1a'
        )
        subtitle_label.pack(pady=(0, 8))

    def create_opti_optimization_controls(self, parent):
        """Erstelle Optimierungs-Kontrollen"""

        # Kontrollen Frame
        controls_frame = tk.Frame(parent, bg='#0a0a0a')
        controls_frame.pack(fill=tk.BOTH, expand=True)

        # Linke Spalte - Parameter
        left_controls = tk.Frame(controls_frame, bg='#0a0a0a')
        left_controls.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # Rechte Spalte - Aktionen
        right_controls = tk.Frame(controls_frame, bg='#0a0a0a')
        right_controls.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Parameter-Kontrollen
        param_frame = tk.LabelFrame(
            left_controls,
            text="Optimierungs-Parameter",
            font=('Arial', 10, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        )
        param_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Learning Rate
        lr_frame = tk.Frame(param_frame, bg='#1a1a1a')
        lr_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(lr_frame, text="Learning Rate:", font=('Arial', 9), fg='#cccccc', bg='#1a1a1a').pack(side=tk.LEFT)
        self.lr_var = tk.StringVar(value="0.005")
        lr_scale = tk.Scale(lr_frame, from_=0.001, to=0.01, resolution=0.001, orient=tk.HORIZONTAL,
                           variable=self.lr_var, bg='#1a1a1a', fg='#ffffff', highlightthickness=0)
        lr_scale.pack(side=tk.RIGHT, fill=tk.X, expand=True)

        # Confidence Boost
        cb_frame = tk.Frame(param_frame, bg='#1a1a1a')
        cb_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(cb_frame, text="Confidence Boost:", font=('Arial', 9), fg='#cccccc', bg='#1a1a1a').pack(side=tk.LEFT)
        self.cb_var = tk.StringVar(value="1.25")
        cb_scale = tk.Scale(cb_frame, from_=1.0, to=2.0, resolution=0.05, orient=tk.HORIZONTAL,
                           variable=self.cb_var, bg='#1a1a1a', fg='#ffffff', highlightthickness=0)
        cb_scale.pack(side=tk.RIGHT, fill=tk.X, expand=True)

        # Aktions-Kontrollen
        action_frame = tk.LabelFrame(
            right_controls,
            text="Optimierungs-Aktionen",
            font=('Arial', 10, 'bold'),
            fg='#ffffff',
            bg='#1a1a1a'
        )
        action_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Optimierungs-Buttons
        opt_buttons_frame = tk.Frame(action_frame, bg='#1a1a1a')
        opt_buttons_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Parameter Anwenden
        apply_btn = tk.Button(
            opt_buttons_frame,
            text="PARAMETER ANWENDEN",
            command=self.apply_opti_parameters,
            font=('Arial', 9, 'bold'),
            bg='#3366cc',
            fg='white',
            relief=tk.FLAT,
            pady=5
        )
        apply_btn.pack(fill=tk.X, pady=(0, 5))

        # Modelle Neu-Trainieren
        retrain_btn = tk.Button(
            opt_buttons_frame,
            text="MODELLE NEU-TRAINIEREN",
            command=self.retrain_opti_models,
            font=('Arial', 9, 'bold'),
            bg='#9933cc',
            fg='white',
            relief=tk.FLAT,
            pady=5
        )
        retrain_btn.pack(fill=tk.X, pady=(0, 5))

        # Reset Optimierung
        reset_btn = tk.Button(
            opt_buttons_frame,
            text="OPTIMIERUNG ZURÜCKSETZEN",
            command=self.reset_opti_optimization,
            font=('Arial', 9, 'bold'),
            bg='#cc6600',
            fg='white',
            relief=tk.FLAT,
            pady=5
        )
        reset_btn.pack(fill=tk.X)

    def generate_opti_market_data(self):
        """Generiere optimierte Marktdaten (nur wenn nötig)"""

        # Prüfe ob Daten bereits existieren und aktuell sind
        if (hasattr(self, 'market_data') and self.market_data is not None and
            hasattr(self, 'last_data_generation') and
            (datetime.now() - self.last_data_generation).seconds < 30):  # Nur alle 30 Sekunden neu generieren
            return

        # Optimierte Bitcoin-Daten (reduziert auf 30 Tage für bessere Performance)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)  # Reduziert von 120 auf 30 Tage
        dates = pd.date_range(start=start_date, end=end_date, freq='15min')

        base_price = 110668.77  # Aktueller Preis
        volatility = 0.008  # Reduzierte Volatilität für Stabilität

        prices = [base_price]
        volumes = []

        # Optimierte Marktdynamik-Simulation (weniger Berechnungen)
        for i in range(1, len(dates)):
            # Vereinfachte Preisbewegung
            trend_component = 0.00008 * math.sin(i / 300)  # Langzeit-Trend
            cycle_component = 0.0003 * math.sin(i / 96)    # Tages-Zyklus
            noise_component = random.gauss(0, volatility)   # Zufälliges Rauschen

            total_change = trend_component + cycle_component + noise_component
            new_price = prices[-1] * (1 + total_change)
            new_price = max(95000, min(130000, new_price))
            prices.append(new_price)

            # Vereinfachte Volume-Simulation
            base_volume = 1200000000  # 1.2 Milliarden USD
            volume_factor = 1 + abs(total_change) * 8  # Reduziert von 12 auf 8
            volume = base_volume * random.uniform(0.5, 2.5) * volume_factor  # Reduzierter Bereich
            volumes.append(volume)

        volumes.append(base_volume * random.uniform(0.5, 2.5))

        # Optimierte OHLC Daten erstellen
        df = pd.DataFrame(index=dates)
        df['Close'] = prices
        df['Open'] = df['Close'].shift(1).fillna(df['Close'].iloc[0])

        # Vereinfachte High/Low Berechnung
        intraday_range = 0.001  # Reduziert von 0.0015 auf 0.001
        df['High'] = df['Close'] * (1 + np.random.uniform(0, intraday_range, len(df)))
        df['Low'] = df['Close'] * (1 - np.random.uniform(0, intraday_range, len(df)))

        # Stelle sicher, dass High >= Close >= Low
        df['High'] = np.maximum(df['High'], np.maximum(df['Close'], df['Open']))
        df['Low'] = np.minimum(df['Low'], np.minimum(df['Close'], df['Open']))

        df['Volume'] = volumes

        self.market_data = df
        self.last_data_generation = datetime.now()

        # Generiere Prognose-Daten (nur wenn nötig)
        if not hasattr(self, 'prediction_data') or self.prediction_data is None:
            self.generate_opti_prediction_data()

        # Nur gelegentlich loggen
        if random.random() < 0.1:  # 10% Chance
            print(f"Optimierte Marktdaten generiert: {len(df)} 15-Min-Intervalle")

    def generate_opti_prediction_data(self):
        """Generiere optimierte Prognose-Daten (vereinfacht)"""

        if self.market_data is None:
            return

        current_price = self.market_data['Close'].iloc[-1]
        current_date = self.market_data.index[-1]

        # Reduzierte 24h Prognose (15-Min-Intervalle)
        future_dates = pd.date_range(start=current_date + timedelta(minutes=15),
                                   periods=96, freq='15min')  # 24h = 96 * 15min (reduziert von 72h)

        # Vereinfachte Prognose-Algorithmen
        recent_data = self.market_data['Close'].iloc[-48:]  # Letzte 12h (reduziert)
        trend_strength = np.mean(np.diff(recent_data))  # Vereinfachter Trend
        volatility = np.std(np.diff(recent_data)) / current_price  # Vereinfachte Volatilität
        momentum = (recent_data.iloc[-1] - recent_data.iloc[-12]) / recent_data.iloc[-12]  # 3h Momentum

        predicted_prices = [current_price]
        confidence_levels = []

        for i in range(96):  # Reduziert von 288 auf 96
            # Vereinfachte Prognose-Faktoren
            trend_factor = trend_strength * (0.99 ** i)  # Langsamere Abschwächung
            momentum_factor = momentum * (0.98 ** i)     # Langsamere Abschwächung
            noise_factor = random.gauss(0, volatility * (1 + i * 0.005))  # Reduzierte Unsicherheit

            predicted_price = predicted_prices[-1] * (1 + trend_factor + momentum_factor + noise_factor)
            predicted_price = max(current_price * 0.8, min(current_price * 1.2, predicted_price))  # Engere Grenzen
            predicted_prices.append(predicted_price)

            # Konfidenz sinkt langsamer
            confidence = max(0.3, 0.95 - (i * 0.005))  # Langsamere Konfidenz-Abnahme
            confidence_levels.append(confidence)

        predicted_prices = predicted_prices[1:]

        self.prediction_data = {
            'dates': future_dates,
            'prices': predicted_prices,
            'confidence': confidence_levels,
            'trend_strength': trend_strength,
            'volatility': volatility,
            'momentum': momentum
        }

    def update_opti_ultimate_price_chart(self):
        """Aktualisiere Ultimate Preis-Chart"""

        if self.market_data is None or not self.visualization_active:
            return

        # Clear axes
        self.price_ax.clear()
        self.volume_ax.clear()

        # Daten für Chart (letzte 7 Tage)
        recent_data = self.market_data.iloc[-672:]  # 7 Tage * 96 (15-Min-Intervalle)
        dates = recent_data.index
        prices = recent_data['Close']
        volumes = recent_data['Volume']

        # HAUPTPREIS-CHART
        self.price_ax.plot(dates, prices, color='#00ff88', linewidth=3,
                          label=f'Bitcoin Preis (${prices.iloc[-1]:,.2f})', alpha=0.9)

        # Moving Averages
        sma_20 = prices.rolling(20).mean()
        sma_50 = prices.rolling(50).mean()
        ema_12 = prices.ewm(span=12).mean()

        self.price_ax.plot(dates, sma_20, color='#ff6600', linewidth=2, alpha=0.8, label='SMA 20')
        self.price_ax.plot(dates, sma_50, color='#3366cc', linewidth=2, alpha=0.8, label='SMA 50')
        self.price_ax.plot(dates, ema_12, color='#ff0066', linewidth=2, alpha=0.8, label='EMA 12')

        # Bollinger Bands
        bb_middle = prices.rolling(20).mean()
        bb_std = prices.rolling(20).std()
        bb_upper = bb_middle + (bb_std * 2)
        bb_lower = bb_middle - (bb_std * 2)

        self.price_ax.fill_between(dates, bb_upper, bb_lower, alpha=0.1, color='#3366cc', label='Bollinger Bands')
        self.price_ax.plot(dates, bb_upper, color='#3366cc', linewidth=1, alpha=0.6, linestyle='--')
        self.price_ax.plot(dates, bb_lower, color='#3366cc', linewidth=1, alpha=0.6, linestyle='--')

        # Prognose hinzufügen (wenn verfügbar)
        if self.prediction_data:
            pred_dates = self.prediction_data['dates'][:96]  # Nächste 24h
            pred_prices = self.prediction_data['prices'][:96]
            pred_confidence = self.prediction_data['confidence'][:96]

            # Verbindungslinie
            connection_dates = [dates[-1], pred_dates[0]]
            connection_prices = [prices.iloc[-1], pred_prices[0]]
            self.price_ax.plot(connection_dates, connection_prices, color='#cccccc', linewidth=1, alpha=0.5)

            # Prognose-Linie
            self.price_ax.plot(pred_dates, pred_prices, color='#ffd700', linewidth=2.5,
                              linestyle='--', alpha=0.8, label='24h Prognose')

            # Konfidenz-Bereich
            upper_bound = [p * (1 + (1-c) * 0.05) for p, c in zip(pred_prices, pred_confidence)]
            lower_bound = [p * (1 - (1-c) * 0.05) for p, c in zip(pred_prices, pred_confidence)]
            self.price_ax.fill_between(pred_dates, lower_bound, upper_bound,
                                      alpha=0.2, color='#ffd700', label='Konfidenz-Bereich')

        # Styling
        self.price_ax.set_title('OPTI-PROTOTYP: Ultimate Bitcoin Preis-Chart (7 Tage + 24h Prognose)',
                               color='white', fontsize=14, fontweight='bold')
        self.price_ax.set_ylabel('Preis (USD)', color='white', fontsize=12)
        self.price_ax.tick_params(colors='white')
        self.price_ax.legend(loc='upper left', fancybox=True, shadow=True)
        self.price_ax.grid(True, alpha=0.3, linestyle=':')

        # Präzise Y-Achsen-Formatierung
        if self.precision_mode:
            self.price_ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.2f}'))
        else:
            self.price_ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

        # VOLUME-CHART
        self.volume_ax.bar(dates, volumes, color='#ff6600', alpha=0.7, width=0.0003)
        self.volume_ax.set_title('Handelsvolumen', color='white', fontsize=12)
        self.volume_ax.set_ylabel('Volume (USD)', color='white')
        self.volume_ax.tick_params(colors='white')
        self.volume_ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1e9:.1f}B'))

        # X-Achsen-Formatierung
        for ax in [self.price_ax, self.volume_ax]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%d.%m %H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=12))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        # Aktuelle Preis-Annotation
        current_price = prices.iloc[-1]
        current_date = dates[-1]
        self.price_ax.annotate(f'${current_price:,.2f}',
                              xy=(current_date, current_price),
                              xytext=(10, 10), textcoords='offset points',
                              bbox=dict(boxstyle='round,pad=0.3', facecolor='#00ff88', alpha=0.9),
                              arrowprops=dict(arrowstyle='->', color='#00ff88'),
                              color='black', fontweight='bold', fontsize=10)

        # Tight layout
        self.price_fig.tight_layout()
        self.price_canvas.draw()

    def update_opti_ensemble_analysis(self):
        """Aktualisiere Ensemble-Analyse"""

        if not self.visualization_active:
            return

        # Simuliere 6-Modell-Ensemble Ergebnisse
        model_names = ['RSI-Momentum', 'MA-Convergence', 'BB-Volatility', 'Trend-Analysis', 'Volume-Price', 'Pattern-Recognition']
        model_signals = [0.7, 0.4, -0.2, 0.8, 0.3, 0.6]  # Signale zwischen -1 und 1
        model_confidences = [0.89, 0.82, 0.75, 0.91, 0.78, 0.85]
        model_weights = [0.22, 0.19, 0.16, 0.18, 0.14, 0.11]

        for i, ax in enumerate(self.ensemble_axes.flat):
            ax.clear()

            if i < len(model_names):
                # Modell-spezifische Visualisierung
                model_name = model_names[i]
                signal = model_signals[i]
                confidence = model_confidences[i]
                weight = model_weights[i]

                # Signal-Balken
                color = '#00ff88' if signal > 0 else '#ff3333' if signal < 0 else '#cccccc'
                ax.barh(0, signal, color=color, alpha=0.8, height=0.3)
                ax.set_xlim(-1, 1)
                ax.set_ylim(-0.5, 0.5)
                ax.axvline(x=0, color='white', linestyle='-', alpha=0.5)

                # Labels
                ax.set_title(f'{model_name}\nSignal: {signal:+.2f} | Konfidenz: {confidence:.1%} | Gewicht: {weight:.1%}',
                           color='white', fontsize=9)
                ax.tick_params(colors='white', labelsize=8)
                ax.set_xlabel('Signal Stärke', color='white', fontsize=8)

                # Konfidenz-Indikator
                conf_color = '#00ff88' if confidence > 0.8 else '#ff6600' if confidence > 0.6 else '#ff3333'
                ax.text(0.02, 0.98, f'Konfidenz: {confidence:.1%}', transform=ax.transAxes,
                       color=conf_color, fontweight='bold', fontsize=8, va='top')

        # Tight layout
        self.ensemble_fig.tight_layout()
        self.ensemble_canvas.draw()

    def log_opti_message(self, message):
        """Füge optimierte Nachricht zum Status-Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}\n"

        self.status_log.insert(tk.END, log_entry)
        self.status_log.see(tk.END)
        self.root.update()

        print(log_entry.strip())

    def show_opti_waiting_message(self, ax, message):
        """Zeige Warteanzeige auf Chart"""
        ax.clear()
        ax.text(0.5, 0.5, message,
               horizontalalignment='center',
               verticalalignment='center',
               transform=ax.transAxes,
               fontsize=14,
               color='#00ff88',
               weight='bold',
               bbox=dict(boxstyle='round,pad=1', facecolor='#1a1a1a', alpha=0.8))
        ax.set_facecolor('#0a0a0a')
        ax.set_xticks([])
        ax.set_yticks([])
        for spine in ax.spines.values():
            spine.set_visible(False)

    # OPTIMIERTE FUNKTIONEN
    def start_opti_visualizations(self):
        """Starte OptiPrototyp Visualisierungen"""
        self.log_opti_message("STARTE OPTI-PROTOTYP VISUALISIERUNGEN...")

        # Aktiviere Visualisierungen
        self.visualization_active = True
        self.manual_update_mode = False

        # Generiere Marktdaten
        self.generate_opti_market_data()

        # Update alle Charts
        self.update_opti_ultimate_price_chart()
        self.update_opti_ensemble_analysis()
        self.update_opti_performance_dashboard()
        self.update_opti_realtime_optimization()

        # Starte Auto-Update
        self.setup_opti_auto_update()

        self.log_opti_message("OptiPrototyp Visualisierungen gestartet!")
        self.log_opti_message("Real-time Updates alle 3 Sekunden aktiv")

    def stop_opti_visualizations(self):
        """Stoppe OptiPrototyp Visualisierungen"""
        self.log_opti_message("STOPPE OPTI-PROTOTYP VISUALISIERUNGEN...")

        # Deaktiviere Visualisierungen
        self.visualization_active = False
        self.auto_update_running = False

        # Stoppe Auto-Update Job
        if self.update_job_id:
            try:
                self.root.after_cancel(self.update_job_id)
                self.update_job_id = None
            except:
                pass

        # Zeige Warteanzeige auf allen Charts
        self.show_opti_waiting_message(self.price_ax, "Visualisierungen gestoppt\nDrücken Sie 'VISUALISIERUNGEN STARTEN' um zu reaktivieren")
        self.price_canvas.draw()

        for ax in self.ensemble_axes.flat:
            self.show_opti_waiting_message(ax, "Gestoppt")
        self.ensemble_canvas.draw()

        for ax in self.perf_axes.flat:
            self.show_opti_waiting_message(ax, "Gestoppt")
        self.perf_canvas.draw()

        for ax in self.realtime_axes.flat:
            self.show_opti_waiting_message(ax, "Gestoppt")
        self.realtime_canvas.draw()

        self.log_opti_message("OptiPrototyp Visualisierungen gestoppt!")
        self.log_opti_message("Drücken Sie 'VISUALISIERUNGEN STARTEN' um zu reaktivieren")

    def start_opti_model(self, model_key):
        """Starte optimiertes Modell"""
        if model_key not in self.models:
            self.log_opti_message(f"FEHLER: Ungültiges Modell: {model_key}")
            return

        model = self.models[model_key]

        if model_key in self.running_processes:
            self.log_opti_message(f"WARNUNG: {model['name']} läuft bereits")
            return

        if not os.path.exists(model['file']):
            self.log_opti_message(f"FEHLER: Datei nicht gefunden: {model['file']}")
            return

        try:
            self.log_opti_message(f"STARTE: {model['name']} (OptiPrototyp Mode)")

            model['status'] = 'Läuft'
            model['status_label'].config(text=f"Status: {model['status']}")

            process = subprocess.Popen(
                [sys.executable, model['file']],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.script_directory,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )

            self.running_processes[model_key] = process
            model['process'] = process

            monitor_thread = threading.Thread(
                target=self.monitor_opti_model,
                args=(model_key, process),
                daemon=True
            )
            monitor_thread.start()

            self.log_opti_message(f"ERFOLGREICH: {model['name']} gestartet (PID: {process.pid})")

        except Exception as e:
            self.log_opti_message(f"FEHLER beim Starten von {model['name']}: {e}")
            model['status'] = 'Fehler'
            model['status_label'].config(text=f"Status: {model['status']}")

    def monitor_opti_model(self, model_key, process):
        """Überwache optimiertes Modell"""
        model = self.models[model_key]

        try:
            stdout, stderr = process.communicate()

            if model_key in self.running_processes:
                del self.running_processes[model_key]

            self.root.after(0, lambda: self.opti_model_finished(model_key, process.returncode, stdout))

        except Exception as e:
            self.root.after(0, lambda: self.log_opti_message(f"FEHLER: OptiPrototyp Monitor-Fehler: {e}"))

    def opti_model_finished(self, model_key, return_code, stdout):
        """Optimiertes Modell beendet"""
        model = self.models[model_key]

        model['process'] = None
        model['status'] = 'Beendet' if return_code == 0 else 'Fehler'
        model['status_label'].config(text=f"Status: {model['status']}")

        if return_code == 0:
            self.log_opti_message(f"ERFOLGREICH: {model['name']} erfolgreich beendet (OptiPrototyp)")

            if stdout:
                self.extract_opti_results(stdout, model['name'])
        else:
            self.log_opti_message(f"WARNUNG: {model['name']} mit Fehler beendet")

    def extract_opti_results(self, stdout, model_name):
        """Extrahiere optimierte Ergebnisse"""
        lines = stdout.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in
                  ['signal:', 'preis:', 'vorhersage:', 'konfidenz:', 'kaufen', 'verkaufen', 'halten']):
                if line:
                    self.log_opti_message(f"[{model_name}] {line}")

    def start_all_opti_models(self):
        """Starte alle optimierten Modelle"""
        self.log_opti_message("STARTE ALLE OPTI-PROTOTYP MODELLE...")

        for model_key in self.models.keys():
            if model_key not in self.running_processes:
                self.start_opti_model(model_key)
                time.sleep(2)

        self.log_opti_message("Alle verfügbaren OptiPrototyp-Modelle gestartet")

    def stop_all_opti_models(self):
        """Stoppe alle optimierten Modelle"""
        if not self.running_processes:
            self.log_opti_message("INFO: Keine OptiPrototyp-Modelle laufen")
            return

        self.log_opti_message("STOPPE ALLE OPTI-PROTOTYP MODELLE...")

        for model_key in list(self.running_processes.keys()):
            process = self.running_processes[model_key]
            model = self.models[model_key]

            try:
                process.terminate()
                process.wait(timeout=5)

                del self.running_processes[model_key]
                model['process'] = None
                model['status'] = 'Gestoppt'
                model['status_label'].config(text=f"Status: {model['status']}")

                self.log_opti_message(f"GESTOPPT: {model['name']}")

            except Exception as e:
                self.log_opti_message(f"FEHLER beim Stoppen: {e}")

        self.log_opti_message("Alle OptiPrototyp-Modelle gestoppt")

    def run_opti_ensemble_analysis(self):
        """Führe optimierte Ensemble-Analyse durch"""
        self.log_opti_message("STARTE OPTI-PROTOTYP ENSEMBLE-ANALYSE...")

        # Simuliere Ensemble-Analyse
        ensemble_result = {
            'consensus_signal': 'KAUFEN',
            'consensus_confidence': 0.892,
            'model_agreement': 0.847,
            'risk_score': 0.234,
            'expected_return': 0.3046
        }

        self.ensemble_results = ensemble_result

        self.log_opti_message(f"ENSEMBLE-ERGEBNIS: {ensemble_result['consensus_signal']} "
                             f"(Konfidenz: {ensemble_result['consensus_confidence']:.1%})")
        self.log_opti_message(f"Modell-Übereinstimmung: {ensemble_result['model_agreement']:.1%}")
        self.log_opti_message(f"Erwartete Rendite: {ensemble_result['expected_return']:.1%}")

        # Update Visualisierungen
        self.update_opti_ensemble_analysis()
        self.update_opti_performance_dashboard()

    def run_opti_auto_optimization(self):
        """Führe automatische Optimierung durch"""
        self.log_opti_message("STARTE OPTI-PROTOTYP AUTO-OPTIMIERUNG...")

        # Simuliere Auto-Optimierung
        optimization_results = {
            'learning_rate_optimized': 0.0045,
            'confidence_boost_optimized': 1.32,
            'feature_selection_improved': True,
            'model_weights_updated': True,
            'performance_gain': 0.067
        }

        self.optimization_history.append({
            'timestamp': datetime.now(),
            'results': optimization_results
        })

        self.log_opti_message(f"AUTO-OPTIMIERUNG ABGESCHLOSSEN:")
        self.log_opti_message(f"Learning Rate optimiert: {optimization_results['learning_rate_optimized']}")
        self.log_opti_message(f"Confidence Boost optimiert: {optimization_results['confidence_boost_optimized']}")
        self.log_opti_message(f"Performance-Gewinn: {optimization_results['performance_gain']:.1%}")

        # Update Parameter
        self.lr_var.set(str(optimization_results['learning_rate_optimized']))
        self.cb_var.set(str(optimization_results['confidence_boost_optimized']))

        # Update Visualisierungen
        self.update_opti_realtime_optimization()

    def update_opti_performance_dashboard(self):
        """Aktualisiere Performance-Dashboard"""

        if not self.visualization_active:
            return

        # Clear axes
        for ax in self.perf_axes.flat:
            ax.clear()

        # Performance-Charts erstellen
        # Chart 1: Genauigkeit über Zeit
        ax1 = self.perf_axes[0, 0]
        sessions = list(range(1, 21))
        accuracies = [0.75 + 0.01 * i + random.uniform(-0.02, 0.02) for i in sessions]
        ax1.plot(sessions, accuracies, color='#00ff88', linewidth=2, marker='o')
        ax1.set_title('Genauigkeit über Sessions', color='white', fontsize=10)
        ax1.set_ylabel('Genauigkeit', color='white')
        ax1.tick_params(colors='white', labelsize=8)
        ax1.grid(True, alpha=0.3)

        # Chart 2: Risk/Reward Entwicklung
        ax2 = self.perf_axes[0, 1]
        risk_rewards = [2.1 + 0.03 * i + random.uniform(-0.1, 0.1) for i in sessions]
        ax2.plot(sessions, risk_rewards, color='#ff6600', linewidth=2, marker='s')
        ax2.set_title('Risk/Reward Entwicklung', color='white', fontsize=10)
        ax2.set_ylabel('Risk/Reward Ratio', color='white')
        ax2.tick_params(colors='white', labelsize=8)
        ax2.grid(True, alpha=0.3)

        # Chart 3: Modell-Gewichte
        ax3 = self.perf_axes[1, 0]
        model_names = ['Enhanced\nAccuracy', 'Ultimate\nFavorit', 'Optimized\nSpeed']
        weights = [0.55, 0.28, 0.17]
        colors = ['#00ff88', '#ff6600', '#3366cc']
        bars = ax3.bar(model_names, weights, color=colors, alpha=0.8)
        ax3.set_title('Aktuelle Modell-Gewichte', color='white', fontsize=10)
        ax3.set_ylabel('Gewichtung', color='white')
        ax3.tick_params(colors='white', labelsize=8)

        # Chart 4: Portfolio Performance
        ax4 = self.perf_axes[1, 1]
        portfolio_values = [100000 * (1.02 ** i) + random.uniform(-1000, 1000) for i in sessions]
        ax4.plot(sessions, portfolio_values, color='#9933cc', linewidth=2, marker='^')
        ax4.set_title('Portfolio-Wert Entwicklung', color='white', fontsize=10)
        ax4.set_ylabel('Portfolio-Wert ($)', color='white')
        ax4.tick_params(colors='white', labelsize=8)
        ax4.grid(True, alpha=0.3)
        ax4.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1000:.0f}K'))

        # Tight layout
        self.perf_fig.tight_layout()
        self.perf_canvas.draw()

    def update_opti_realtime_optimization(self):
        """Aktualisiere Real-time Optimierung"""

        if not self.visualization_active:
            return

        # Clear axes
        for ax in self.realtime_axes.flat:
            ax.clear()

        # Optimierungs-Charts erstellen
        # Chart 1: Learning Rate Entwicklung
        ax1 = self.realtime_axes[0, 0]
        iterations = list(range(1, 51))
        learning_rates = [0.01 * (0.98 ** i) for i in iterations]
        ax1.plot(iterations, learning_rates, color='#00ff88', linewidth=2)
        ax1.set_title('Learning Rate Anpassung', color='white', fontsize=10)
        ax1.set_ylabel('Learning Rate', color='white')
        ax1.tick_params(colors='white', labelsize=8)
        ax1.grid(True, alpha=0.3)

        # Chart 2: Confidence Boost Optimierung
        ax2 = self.realtime_axes[0, 1]
        confidence_boosts = [1.0 + 0.005 * i + random.uniform(-0.02, 0.02) for i in iterations]
        ax2.plot(iterations, confidence_boosts, color='#ff6600', linewidth=2)
        ax2.set_title('Confidence Boost Optimierung', color='white', fontsize=10)
        ax2.set_ylabel('Confidence Boost', color='white')
        ax2.tick_params(colors='white', labelsize=8)
        ax2.grid(True, alpha=0.3)

        # Chart 3: Feature Importance
        ax3 = self.realtime_axes[1, 0]
        features = ['RSI', 'MACD', 'BB', 'Volume', 'Trend']
        importances = [0.25, 0.22, 0.18, 0.20, 0.15]
        colors = ['#00ff88', '#ff6600', '#3366cc', '#9933cc', '#ff0066']
        ax3.pie(importances, labels=features, colors=colors, autopct='%1.1f%%', startangle=90)
        ax3.set_title('Feature Importance', color='white', fontsize=10)

        # Chart 4: Optimierungs-Verlauf
        ax4 = self.realtime_axes[1, 1]
        opt_scores = [0.7 + 0.005 * i + random.uniform(-0.01, 0.01) for i in iterations]
        ax4.plot(iterations, opt_scores, color='#ffd700', linewidth=2)
        ax4.set_title('Optimierungs-Score', color='white', fontsize=10)
        ax4.set_ylabel('Score', color='white')
        ax4.tick_params(colors='white', labelsize=8)
        ax4.grid(True, alpha=0.3)

        # Tight layout
        self.realtime_fig.tight_layout()
        self.realtime_canvas.draw()

    # EINSTELLUNGS-FUNKTIONEN
    def toggle_opti_precision_mode(self):
        """Schalte Präzisions-Modus um"""
        self.precision_mode = self.precision_var.get()
        self.log_opti_message(f"OptiPrototyp Präzisions-Modus: {'EIN' if self.precision_mode else 'AUS'}")
        self.update_opti_ultimate_price_chart()

    def toggle_opti_auto_optimize(self):
        """Schalte Auto-Optimierung um"""
        self.auto_optimize = self.auto_optimize_var.get()
        self.log_opti_message(f"OptiPrototyp Auto-Optimierung: {'EIN' if self.auto_optimize else 'AUS'}")

    def update_opti_interval(self):
        """Update Intervall ändern"""
        try:
            new_interval = int(self.interval_var.get())
            self.update_interval = new_interval
            self.log_opti_message(f"OptiPrototyp Update-Intervall geändert: {new_interval}s")
        except ValueError:
            self.log_opti_message("FEHLER: Ungültiges Update-Intervall")

    def apply_opti_parameters(self):
        """Wende Optimierungs-Parameter an"""
        try:
            lr = float(self.lr_var.get())
            cb = float(self.cb_var.get())

            self.log_opti_message(f"OptiPrototyp Parameter angewendet:")
            self.log_opti_message(f"Learning Rate: {lr}")
            self.log_opti_message(f"Confidence Boost: {cb}")

        except ValueError:
            self.log_opti_message("FEHLER: Ungültige Parameter-Werte")

    def retrain_opti_models(self):
        """Trainiere Modelle neu"""
        self.log_opti_message("STARTE OPTI-PROTOTYP MODELL-NEU-TRAINING...")

        # Simuliere Neu-Training
        for model_key, model in self.models.items():
            old_accuracy = model.get('accuracy', 0.8)
            new_accuracy = min(0.98, old_accuracy + random.uniform(0.01, 0.05))
            model['accuracy'] = new_accuracy

            self.log_opti_message(f"{model['name']}: {old_accuracy:.1%} -> {new_accuracy:.1%}")

        self.log_opti_message("OptiPrototyp Modell-Neu-Training abgeschlossen")

    def reset_opti_optimization(self):
        """Setze Optimierung zurück"""
        self.log_opti_message("SETZE OPTI-PROTOTYP OPTIMIERUNG ZURÜCK...")

        # Reset Parameter
        self.lr_var.set("0.005")
        self.cb_var.set("1.25")

        # Reset Daten
        self.optimization_history = []
        self.ensemble_results = {}

        self.log_opti_message("OptiPrototyp Optimierung zurückgesetzt")

    def load_opti_session_data(self):
        """Lade OptiPrototyp Session-Daten"""
        try:
            if os.path.exists('optiPrototyp_session_data.json'):
                with open('optiPrototyp_session_data.json', 'r') as f:
                    self.session_data = json.load(f)
                self.log_opti_message("OptiPrototyp Session-Daten geladen")
        except Exception as e:
            self.log_opti_message(f"Konnte OptiPrototyp Session-Daten nicht laden: {e}")

    def save_opti_session_data(self):
        """Speichere OptiPrototyp Session-Daten"""
        try:
            # Bereinige Optimization History für JSON-Serialization
            clean_optimization_history = []
            for item in self.optimization_history[-10:]:  # Letzte 10
                if isinstance(item, dict):
                    clean_item = {}
                    for key, value in item.items():
                        if key == 'timestamp' and hasattr(value, 'isoformat'):
                            clean_item[key] = value.isoformat()
                        elif not callable(value):  # Keine Funktionen
                            clean_item[key] = value
                    clean_optimization_history.append(clean_item)

            session_data = {
                'version': self.VERSION,
                'last_update': self.last_update.isoformat() if hasattr(self.last_update, 'isoformat') else str(self.last_update),
                'precision_mode': self.precision_mode,
                'auto_optimize': self.auto_optimize,
                'update_interval': self.update_interval,
                'visualization_active': self.visualization_active,
                'auto_update_running': self.auto_update_running,
                'optimization_history': clean_optimization_history,
                'ensemble_results': self.ensemble_results if isinstance(self.ensemble_results, dict) else {},
                'performance_metrics': self.performance_metrics if isinstance(self.performance_metrics, dict) else {}
            }

            with open('optiPrototyp_session_data.json', 'w') as f:
                json.dump(session_data, f, indent=2)

            # Nur gelegentlich loggen um Spam zu vermeiden
            if random.random() < 0.1:  # 10% Chance
                self.log_opti_message("OptiPrototyp Session-Daten gespeichert")
        except Exception as e:
            if random.random() < 0.1:  # Nur gelegentlich Fehler loggen
                self.log_opti_message(f"Konnte OptiPrototyp Session-Daten nicht speichern: {e}")

    def setup_opti_auto_update(self):
        """Setup automatischer OptiPrototyp Updates"""
        def auto_update():
            try:
                # Prüfe ob GUI noch existiert und Visualisierungen aktiv sind
                if (hasattr(self, 'root') and self.root and
                    self.root.winfo_exists() and self.visualization_active and self.auto_update_running):

                    # Update Zeitstempel
                    self.last_update = datetime.now()
                    if hasattr(self, 'timestamp_label'):
                        self.timestamp_label.config(
                            text=f"Letzte Aktualisierung: {self.last_update.strftime('%H:%M:%S.%f')[:-3]}")

                    # Update Marktdaten (nur wenn nötig)
                    self.generate_opti_market_data()

                    # Update Charts (nur wenn Visualisierungen aktiv)
                    if self.visualization_active:
                        self.update_opti_ultimate_price_chart()
                        self.update_opti_ensemble_analysis()
                        self.update_opti_performance_dashboard()
                        self.update_opti_realtime_optimization()

                    # Auto-Optimierung (reduzierte Häufigkeit)
                    if self.auto_optimize and random.random() < 0.02:  # 2% Chance (reduziert)
                        self.run_opti_auto_optimization()

                    # Speichere Session-Daten (weniger häufig)
                    if random.random() < 0.3:  # 30% Chance
                        self.save_opti_session_data()

                    # Nächstes Update planen (nur wenn noch aktiv)
                    if self.visualization_active and self.auto_update_running:
                        self.update_job_id = self.root.after(self.update_interval * 1000, auto_update)

            except Exception as e:
                # Fehlerbehandlung - stoppe Auto-Update bei Fehlern
                self.auto_update_running = False
                self.visualization_active = False
                print(f"Auto-Update Fehler: {e}")

        # Starte Auto-Update nur wenn Visualisierungen aktiv sind
        if self.visualization_active and not self.auto_update_running:
            self.auto_update_running = True
            self.update_job_id = self.root.after(3000, auto_update)

    def run(self):
        """Starte OptiPrototyp GUI"""
        self.log_opti_message("OPTI-PROTOTYP: Bitcoin Launcher Ultimate bereit!")
        self.log_opti_message("Vollständig überarbeiteter und optimierter Bitcoin Trading Launcher")
        self.log_opti_message(f"Version: {self.VERSION}")
        self.log_opti_message(f"6-Modell-Ensemble * 350+ Features * Real-time Optimierung")
        self.log_opti_message("HINWEIS: Drücken Sie 'VISUALISIERUNGEN STARTEN' um Charts zu aktivieren")
        self.root.mainloop()

def main():
    """Hauptfunktion"""
    try:
        app = OptiPrototypBitcoinLauncherUltimate()
        app.run()
    except Exception as e:
        print(f"FEHLER: OptiPrototyp GUI-Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
