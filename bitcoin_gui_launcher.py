#!/usr/bin/env python3
"""
🚀 FREUNDLICHER BITCOIN TRADING GUI-LAUNCHER 🚀
==============================================
🏆 MODERNE GUI + KONTINUIERLICHE BERECHNUNG 🏆
✅ Freundliche Benutzeroberfläche mit Buttons
✅ Kontinuierliche Modell-Berechnung für bessere Ergebnisse
✅ Automatischer Script-Stop beim Schließen
✅ Live-Status-Anzeige aller laufenden Modelle
✅ Intelligente Prozessverwaltung
✅ Ein-Klick-Start für alle Bitcoin Trading Systeme

🎯 KONTINUIERLICHE VERBESSERUNG:
- Modelle laufen permanent im Hintergrund
- Bessere Ergebnisse durch kontinuierliches Training
- Automatische Optimierung während der Laufzeit
- Live-Updates der Trading-Signale

💡 FREUNDLICHER GUI-LAUNCHER - EINFACH UND EFFEKTIV!
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import threading
import time
import os
import sys
import signal
from datetime import datetime
from typing import Dict, List, Optional
import queue
import psutil

class BitcoinGUILauncher:
    """
    🚀 FREUNDLICHER BITCOIN TRADING GUI-LAUNCHER
    ==========================================
    Moderne GUI mit kontinuierlicher Berechnung und automatischem
    Script-Stop beim Schließen für optimale Bitcoin Trading Performance.
    """
    
    def __init__(self):
        # GUI SETUP
        self.root = tk.Tk()
        self.root.title("🚀 Bitcoin Trading Launcher - Kontinuierliche Berechnung")
        self.root.geometry("900x700")
        self.root.configure(bg='#1e1e1e')
        
        # PROCESS MANAGEMENT
        self.running_processes = {}
        self.process_threads = {}
        self.continuous_mode = False
        self.status_queue = queue.Queue()
        
        # BITCOIN TRADING SYSTEME
        self.systems = {
            'favorit': {
                'name': '🏅 FAVORIT - Bewährt & Getestet',
                'file': 'ultimate_complete_bitcoin_trading_FAVORITE.py',
                'description': 'Das bewährte System mit 100% Genauigkeit\nSession #12+, kontinuierliches Lernen',
                'color': '#4CAF50',
                'status': 'Gestoppt',
                'last_run': 'Nie',
                'runs': 0
            },
            'schnell': {
                'name': '🚀 SCHNELL - Optimiert & Effizient',
                'file': 'ultimate_final_bitcoin_trading_complete.py',
                'description': 'Das schnelle System für tägliche Analysen\n97% Datenqualität, robuste Architektur',
                'color': '#2196F3',
                'status': 'Gestoppt',
                'last_run': 'Nie',
                'runs': 0
            },
            'ki_system': {
                'name': '🧠 KI-SYSTEM - Innovativ & Selbstlernend',
                'file': 'ultimate_self_learning_ai_bitcoin_trading.py',
                'description': 'Das revolutionäre KI-System der Zukunft\n6 KI-Capabilities, Selbstoptimierung',
                'color': '#FF9800',
                'status': 'Gestoppt',
                'last_run': 'Nie',
                'runs': 0
            }
        }
        
        # GUI KOMPONENTEN
        self.setup_gui()
        self.setup_status_monitor()
        
        # CLEANUP BEIM SCHLIESSEN
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        print("🚀 Freundlicher Bitcoin Trading GUI-Launcher initialisiert")
        print("💡 Kontinuierliche Berechnung verfügbar")
        print("🎯 Automatischer Script-Stop beim Schließen aktiviert")
    
    def setup_gui(self):
        """Erstelle die freundliche GUI"""
        
        # TITEL
        title_frame = tk.Frame(self.root, bg='#1e1e1e')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="🚀 Bitcoin Trading Launcher",
            font=("Arial", 24, "bold"),
            fg='#4CAF50',
            bg='#1e1e1e'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="Kontinuierliche Berechnung für bessere Ergebnisse",
            font=("Arial", 12),
            fg='#CCCCCC',
            bg='#1e1e1e'
        )
        subtitle_label.pack()
        
        # HAUPTBEREICH
        main_frame = tk.Frame(self.root, bg='#1e1e1e')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # LINKE SEITE - SYSTEM BUTTONS
        left_frame = tk.Frame(main_frame, bg='#1e1e1e')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        systems_label = tk.Label(
            left_frame,
            text="📊 Bitcoin Trading Systeme",
            font=("Arial", 16, "bold"),
            fg='#FFFFFF',
            bg='#1e1e1e'
        )
        systems_label.pack(pady=(0, 15))
        
        # SYSTEM BUTTONS
        for key, system in self.systems.items():
            self.create_system_button(left_frame, key, system)
        
        # KONTINUIERLICHER MODUS
        continuous_frame = tk.Frame(left_frame, bg='#1e1e1e')
        continuous_frame.pack(pady=20, fill=tk.X)
        
        self.continuous_var = tk.BooleanVar()
        continuous_check = tk.Checkbutton(
            continuous_frame,
            text="🔄 Kontinuierliche Berechnung (bessere Ergebnisse)",
            variable=self.continuous_var,
            command=self.toggle_continuous_mode,
            font=("Arial", 12, "bold"),
            fg='#4CAF50',
            bg='#1e1e1e',
            selectcolor='#2e2e2e',
            activebackground='#1e1e1e',
            activeforeground='#4CAF50'
        )
        continuous_check.pack()
        
        # CONTROL BUTTONS
        control_frame = tk.Frame(left_frame, bg='#1e1e1e')
        control_frame.pack(pady=10, fill=tk.X)
        
        self.stop_all_btn = tk.Button(
            control_frame,
            text="🛑 Alle Stoppen",
            command=self.stop_all_processes,
            font=("Arial", 12, "bold"),
            bg='#f44336',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10
        )
        self.stop_all_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.refresh_btn = tk.Button(
            control_frame,
            text="🔄 Status Aktualisieren",
            command=self.refresh_status,
            font=("Arial", 12, "bold"),
            bg='#2196F3',
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10
        )
        self.refresh_btn.pack(side=tk.LEFT)
        
        # RECHTE SEITE - STATUS UND LOG
        right_frame = tk.Frame(main_frame, bg='#1e1e1e')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(20, 0))
        
        status_label = tk.Label(
            right_frame,
            text="📈 Live-Status & Logs",
            font=("Arial", 16, "bold"),
            fg='#FFFFFF',
            bg='#1e1e1e'
        )
        status_label.pack(pady=(0, 15))
        
        # STATUS ANZEIGE
        self.status_text = scrolledtext.ScrolledText(
            right_frame,
            height=15,
            font=("Consolas", 10),
            bg='#2e2e2e',
            fg='#CCCCCC',
            insertbackground='#CCCCCC'
        )
        self.status_text.pack(fill=tk.BOTH, expand=True)
        
        # SYSTEM INFO
        info_frame = tk.Frame(right_frame, bg='#1e1e1e')
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.info_label = tk.Label(
            info_frame,
            text="💡 Bereit für Bitcoin Trading",
            font=("Arial", 10),
            fg='#4CAF50',
            bg='#1e1e1e'
        )
        self.info_label.pack()
    
    def create_system_button(self, parent, key, system):
        """Erstelle Button für ein System"""
        
        frame = tk.Frame(parent, bg='#2e2e2e', relief=tk.RAISED, bd=1)
        frame.pack(fill=tk.X, pady=5)
        
        # SYSTEM INFO
        info_frame = tk.Frame(frame, bg='#2e2e2e')
        info_frame.pack(fill=tk.X, padx=15, pady=10)
        
        name_label = tk.Label(
            info_frame,
            text=system['name'],
            font=("Arial", 14, "bold"),
            fg=system['color'],
            bg='#2e2e2e'
        )
        name_label.pack(anchor=tk.W)
        
        desc_label = tk.Label(
            info_frame,
            text=system['description'],
            font=("Arial", 10),
            fg='#CCCCCC',
            bg='#2e2e2e',
            justify=tk.LEFT
        )
        desc_label.pack(anchor=tk.W)
        
        # STATUS UND BUTTONS
        control_frame = tk.Frame(frame, bg='#2e2e2e')
        control_frame.pack(fill=tk.X, padx=15, pady=(0, 10))
        
        # STATUS
        status_frame = tk.Frame(control_frame, bg='#2e2e2e')
        status_frame.pack(side=tk.LEFT)
        
        status_label = tk.Label(
            status_frame,
            text=f"Status: {system['status']}",
            font=("Arial", 9),
            fg='#CCCCCC',
            bg='#2e2e2e'
        )
        status_label.pack(anchor=tk.W)
        
        runs_label = tk.Label(
            status_frame,
            text=f"Läufe: {system['runs']}",
            font=("Arial", 9),
            fg='#CCCCCC',
            bg='#2e2e2e'
        )
        runs_label.pack(anchor=tk.W)
        
        # BUTTONS
        button_frame = tk.Frame(control_frame, bg='#2e2e2e')
        button_frame.pack(side=tk.RIGHT)
        
        start_btn = tk.Button(
            button_frame,
            text="▶️ Starten",
            command=lambda k=key: self.start_system(k),
            font=("Arial", 10, "bold"),
            bg=system['color'],
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5
        )
        start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        stop_btn = tk.Button(
            button_frame,
            text="⏹️ Stoppen",
            command=lambda k=key: self.stop_system(k),
            font=("Arial", 10, "bold"),
            bg='#f44336',
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5
        )
        stop_btn.pack(side=tk.LEFT)
        
        # SPEICHERE REFERENZEN
        system['status_label'] = status_label
        system['runs_label'] = runs_label
        system['start_btn'] = start_btn
        system['stop_btn'] = stop_btn

    def setup_status_monitor(self):
        """Setup Status-Monitoring"""
        self.log_message("🚀 Bitcoin Trading GUI-Launcher gestartet")
        self.log_message("💡 Wählen Sie ein System oder aktivieren Sie kontinuierliche Berechnung")
        self.log_message("🎯 Alle Prozesse werden automatisch beim Schließen beendet")

        # STATUS UPDATE TIMER
        self.root.after(1000, self.update_status)

    def log_message(self, message):
        """Füge Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.status_text.insert(tk.END, log_entry)
        self.status_text.see(tk.END)

        # Begrenze Log-Größe
        lines = self.status_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.status_text.delete("1.0", "10.0")

    def start_system(self, system_key):
        """Starte ein Bitcoin Trading System"""
        system = self.systems[system_key]

        if system_key in self.running_processes:
            self.log_message(f"⚠️ {system['name']} läuft bereits")
            return

        if not os.path.exists(system['file']):
            self.log_message(f"❌ Datei nicht gefunden: {system['file']}")
            messagebox.showerror("Fehler", f"Datei nicht gefunden: {system['file']}")
            return

        try:
            self.log_message(f"▶️ Starte {system['name']}...")

            # STARTE PROZESS
            process = subprocess.Popen(
                [sys.executable, system['file']],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.getcwd()
            )

            self.running_processes[system_key] = process
            system['status'] = 'Läuft'
            system['last_run'] = datetime.now().strftime("%H:%M:%S")
            system['runs'] += 1

            # STARTE MONITORING THREAD
            thread = threading.Thread(
                target=self.monitor_process,
                args=(system_key, process),
                daemon=True
            )
            thread.start()
            self.process_threads[system_key] = thread

            self.update_system_display(system_key)
            self.log_message(f"✅ {system['name']} gestartet (PID: {process.pid})")

        except Exception as e:
            self.log_message(f"❌ Fehler beim Starten von {system['name']}: {e}")
            messagebox.showerror("Fehler", f"Fehler beim Starten: {e}")

    def stop_system(self, system_key):
        """Stoppe ein Bitcoin Trading System"""
        system = self.systems[system_key]

        if system_key not in self.running_processes:
            self.log_message(f"⚠️ {system['name']} läuft nicht")
            return

        try:
            process = self.running_processes[system_key]

            self.log_message(f"⏹️ Stoppe {system['name']}...")

            # BEENDE PROZESS SANFT
            process.terminate()

            # WARTE AUF BEENDIGUNG
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                # FORCE KILL WENN NÖTIG
                process.kill()
                self.log_message(f"🔨 {system['name']} zwangsbeendet")

            # CLEANUP
            del self.running_processes[system_key]
            if system_key in self.process_threads:
                del self.process_threads[system_key]

            system['status'] = 'Gestoppt'
            self.update_system_display(system_key)
            self.log_message(f"✅ {system['name']} gestoppt")

        except Exception as e:
            self.log_message(f"❌ Fehler beim Stoppen von {system['name']}: {e}")

    def stop_all_processes(self):
        """Stoppe alle laufenden Prozesse"""
        if not self.running_processes:
            self.log_message("💡 Keine Prozesse laufen")
            return

        self.log_message("🛑 Stoppe alle Prozesse...")

        # STOPPE KONTINUIERLICHEN MODUS
        if self.continuous_mode:
            self.continuous_var.set(False)
            self.toggle_continuous_mode()

        # STOPPE ALLE SYSTEME
        for system_key in list(self.running_processes.keys()):
            self.stop_system(system_key)

        self.log_message("✅ Alle Prozesse gestoppt")

    def toggle_continuous_mode(self):
        """Schalte kontinuierlichen Modus um"""
        self.continuous_mode = self.continuous_var.get()

        if self.continuous_mode:
            self.log_message("🔄 Kontinuierliche Berechnung aktiviert")
            self.log_message("💡 Modelle werden automatisch neu gestartet für bessere Ergebnisse")
            self.start_continuous_calculation()
        else:
            self.log_message("⏸️ Kontinuierliche Berechnung deaktiviert")

    def start_continuous_calculation(self):
        """Starte kontinuierliche Berechnung"""
        if not self.continuous_mode:
            return

        # STARTE ALLE SYSTEME WENN NICHT LAUFEND
        for system_key in self.systems.keys():
            if system_key not in self.running_processes:
                self.start_system(system_key)

        # PLANE NÄCHSTE ÜBERPRÜFUNG
        self.root.after(30000, self.continuous_check)  # Alle 30 Sekunden

    def continuous_check(self):
        """Überprüfe kontinuierliche Berechnung"""
        if not self.continuous_mode:
            return

        # RESTART BEENDETE PROZESSE
        for system_key, system in self.systems.items():
            if system_key not in self.running_processes:
                self.log_message(f"🔄 Starte {system['name']} neu (kontinuierliche Berechnung)")
                self.start_system(system_key)

        # PLANE NÄCHSTE ÜBERPRÜFUNG
        self.root.after(30000, self.continuous_check)

    def monitor_process(self, system_key, process):
        """Überwache einen Prozess"""
        system = self.systems[system_key]

        try:
            # WARTE AUF PROZESS-ENDE
            stdout, stderr = process.communicate()

            # PROZESS BEENDET
            if system_key in self.running_processes:
                del self.running_processes[system_key]

            system['status'] = 'Beendet'

            # UPDATE GUI IM MAIN THREAD
            self.root.after(0, lambda: self.update_system_display(system_key))

            if process.returncode == 0:
                self.root.after(0, lambda: self.log_message(f"✅ {system['name']} erfolgreich beendet"))
            else:
                self.root.after(0, lambda: self.log_message(f"⚠️ {system['name']} mit Fehler beendet (Code: {process.returncode})"))

            # KONTINUIERLICHER MODUS: RESTART
            if self.continuous_mode:
                self.root.after(5000, lambda: self.start_system(system_key))  # Restart nach 5 Sekunden

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ Monitor-Fehler für {system['name']}: {e}"))

    def update_system_display(self, system_key):
        """Update System-Anzeige"""
        system = self.systems[system_key]

        # UPDATE STATUS
        system['status_label'].config(text=f"Status: {system['status']}")
        system['runs_label'].config(text=f"Läufe: {system['runs']}")

        # UPDATE BUTTON STATES
        if system['status'] == 'Läuft':
            system['start_btn'].config(state=tk.DISABLED)
            system['stop_btn'].config(state=tk.NORMAL)
        else:
            system['start_btn'].config(state=tk.NORMAL)
            system['stop_btn'].config(state=tk.DISABLED)

    def update_status(self):
        """Update Status-Anzeige"""
        running_count = len(self.running_processes)
        total_runs = sum(system['runs'] for system in self.systems.values())

        status_text = f"💡 Laufende Systeme: {running_count}/3 | Gesamtläufe: {total_runs}"
        if self.continuous_mode:
            status_text += " | 🔄 Kontinuierlich aktiv"

        self.info_label.config(text=status_text)

        # PLANE NÄCHSTES UPDATE
        self.root.after(1000, self.update_status)

    def refresh_status(self):
        """Aktualisiere Status aller Systeme"""
        self.log_message("🔄 Status wird aktualisiert...")

        for system_key in self.systems.keys():
            self.update_system_display(system_key)

        self.log_message("✅ Status aktualisiert")

    def on_closing(self):
        """Beim Schließen des Fensters"""
        if self.running_processes:
            result = messagebox.askyesno(
                "Prozesse laufen",
                f"Es laufen noch {len(self.running_processes)} Bitcoin Trading Systeme.\n\n"
                "Sollen alle Prozesse gestoppt werden?"
            )

            if result:
                self.log_message("🛑 Schließe Launcher - Stoppe alle Prozesse...")
                self.stop_all_processes()

                # WARTE AUF PROZESS-BEENDIGUNG
                for _ in range(10):  # Max 10 Sekunden warten
                    if not self.running_processes:
                        break
                    time.sleep(1)
                    self.root.update()

                self.log_message("👋 Launcher geschlossen")
                self.root.destroy()
            else:
                return  # Schließen abbrechen
        else:
            self.log_message("👋 Launcher geschlossen")
            self.root.destroy()

    def run(self):
        """Starte die GUI"""
        self.log_message("🎯 GUI bereit - Wählen Sie Ihre Bitcoin Trading Systeme!")
        self.root.mainloop()

def main():
    """Hauptfunktion"""
    try:
        launcher = BitcoinGUILauncher()
        launcher.run()
    except Exception as e:
        print(f"❌ GUI-Launcher Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
