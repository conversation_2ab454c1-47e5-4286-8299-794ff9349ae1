#!/usr/bin/env python3
"""
🚀 WORKING BITCOIN TRADING DASHBOARD
===================================
Funktionierende Version ohne problematische Chart-Komponenten
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import requests
from datetime import datetime, timedelta
import numpy as np
from collections import deque

class SimpleBitcoinDataProvider:
    """📡 Einfacher Bitcoin-Datenlieferant"""

    def __init__(self):
        self.apis = {
            'coinbase': {
                'url': 'https://api.coinbase.com/v2/exchange-rates?currency=BTC',
                'extract': lambda data: float(data['data']['rates']['USD'])
            },
            'coingecko': {
                'url': 'https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd',
                'extract': lambda data: float(data['bitcoin']['usd'])
            }
        }

        self.price_history = deque(maxlen=100)
        self.last_price = 0
        self.last_update = None

    def get_current_price(self):
        """💰 Aktuellen Bitcoin-Preis abrufen"""
        for api_name, api_config in self.apis.items():
            try:
                response = requests.get(api_config['url'], timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    price = api_config['extract'](data)

                    if 1000 <= price <= 1000000:
                        entry = {
                            'price': price,
                            'timestamp': datetime.now(),
                            'source': api_name
                        }

                        self.price_history.append(entry)
                        self.last_price = price
                        self.last_update = datetime.now()

                        return price

            except Exception as e:
                print(f"❌ API {api_name} Fehler: {e}")
                continue

        return self.last_price if self.last_price > 0 else 50000.0

class TechnicalAnalysisModule:
    """📊 Technical Analysis Modul"""

    def __init__(self, data_provider):
        self.data_provider = data_provider
        self.is_running = False
        self.results = {
            'rsi': 50,
            'sma_20': 0,
            'overall_signal': 'HALTEN',
            'confidence': 0.5,
            'last_update': None
        }

    def calculate_rsi(self, prices, period=14):
        """📈 RSI berechnen"""
        if len(prices) < period + 1:
            return 50

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def analyze(self):
        """🔍 Technische Analyse durchführen"""
        try:
            history = self.data_provider.price_history
            if len(history) < 10:
                return self.results

            prices = [entry['price'] for entry in history]

            # RSI berechnen
            rsi = self.calculate_rsi(prices)

            # SMA berechnen
            sma_20 = np.mean(prices[-20:]) if len(prices) >= 20 else prices[-1]

            # Signal bestimmen
            if rsi > 70:
                signal = 'VERKAUFEN'
                confidence = 0.7
            elif rsi < 30:
                signal = 'KAUFEN'
                confidence = 0.7
            else:
                signal = 'HALTEN'
                confidence = 0.5

            self.results = {
                'rsi': round(rsi, 2),
                'sma_20': round(sma_20, 2),
                'overall_signal': signal,
                'confidence': confidence,
                'last_update': datetime.now().strftime('%H:%M:%S')
            }

        except Exception as e:
            print(f"❌ Technical Analysis Fehler: {e}")

        return self.results

class WorkingTradingDashboard:
    """🚀 Funktionierendes Trading Dashboard"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 Working Bitcoin Trading Dashboard")
        self.root.geometry("1000x700")
        self.root.configure(bg='#1a1a1a')

        # Core-Systeme
        self.data_provider = SimpleBitcoinDataProvider()
        self.technical_module = TechnicalAnalysisModule(self.data_provider)

        # Update-System
        self.update_interval = 30
        self.is_running = False
        self.update_thread = None

        # GUI erstellen
        self.create_gui()

        # Erste Daten laden
        self.update_price_display()

    def create_gui(self):
        """🎨 GUI erstellen"""
        # Header
        self.create_header()

        # Hauptbereich
        self.create_main_area()

        # Footer
        self.create_footer()

    def create_header(self):
        """📊 Header erstellen"""
        header_frame = tk.Frame(self.root, bg='#2d2d2d', height=80)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)

        # Bitcoin-Preis
        self.price_label = tk.Label(header_frame,
                                   text="Bitcoin: $0.00",
                                   font=('Arial', 24, 'bold'),
                                   bg='#2d2d2d', fg='#00ff88')
        self.price_label.pack(side='left', padx=20, pady=20)

        # Status
        self.status_label = tk.Label(header_frame,
                                    text="🔴 Gestoppt",
                                    font=('Arial', 12, 'bold'),
                                    bg='#2d2d2d', fg='#ff6b6b')
        self.status_label.pack(side='right', padx=20, pady=20)

    def create_main_area(self):
        """📊 Hauptbereich erstellen"""
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Technical Analysis Bereich
        tech_frame = tk.LabelFrame(main_frame, text="📊 Technical Analysis",
                                  font=('Arial', 14, 'bold'))
        tech_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # RSI Anzeige
        self.rsi_label = tk.Label(tech_frame, text="RSI: --",
                                 font=('Arial', 16, 'bold'))
        self.rsi_label.pack(pady=10)

        # SMA Anzeige
        self.sma_label = tk.Label(tech_frame, text="SMA(20): --",
                                 font=('Arial', 16, 'bold'))
        self.sma_label.pack(pady=10)

        # Signal Anzeige
        self.signal_label = tk.Label(tech_frame, text="Signal: HALTEN",
                                    font=('Arial', 20, 'bold'),
                                    fg='#666')
        self.signal_label.pack(pady=20)

        # Konfidenz Anzeige
        self.confidence_label = tk.Label(tech_frame, text="Konfidenz: 50%",
                                        font=('Arial', 14))
        self.confidence_label.pack(pady=10)

        # Letztes Update
        self.update_label = tk.Label(tech_frame, text="Letztes Update: --",
                                    font=('Arial', 10),
                                    fg='#666')
        self.update_label.pack(pady=5)

    def create_footer(self):
        """🎮 Footer erstellen"""
        footer_frame = tk.Frame(self.root, bg='#2d2d2d', height=60)
        footer_frame.pack(fill='x', padx=10, pady=5)
        footer_frame.pack_propagate(False)

        # Start/Stop Button
        self.main_button = tk.Button(footer_frame,
                                    text="🚀 SYSTEM STARTEN",
                                    font=('Arial', 14, 'bold'),
                                    bg='#4CAF50', fg='white',
                                    command=self.toggle_system)
        self.main_button.pack(side='left', padx=20, pady=10)

        # Intervall-Einstellung
        tk.Label(footer_frame, text="Update-Intervall:",
                font=('Arial', 10), bg='#2d2d2d', fg='white').pack(side='right', padx=5)

        self.interval_var = tk.StringVar(value="30")
        interval_combo = ttk.Combobox(footer_frame, textvariable=self.interval_var,
                                     values=["10", "30", "60"], width=5)
        interval_combo.pack(side='right', padx=5)
        interval_combo.bind('<<ComboboxSelected>>', self.update_interval_changed)

    def toggle_system(self):
        """🔄 System starten/stoppen"""
        if self.is_running:
            self.stop_system()
        else:
            self.start_system()

    def start_system(self):
        """▶️ System starten"""
        self.is_running = True
        self.technical_module.is_running = True

        # Update-Thread starten
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()

        # GUI aktualisieren
        self.main_button.config(text="🛑 SYSTEM STOPPEN", bg='#f44336')
        self.status_label.config(text="🟢 Läuft", fg='#4CAF50')

        print("🚀 System gestartet")

    def stop_system(self):
        """⏹️ System stoppen"""
        self.is_running = False
        self.technical_module.is_running = False

        # GUI aktualisieren
        self.main_button.config(text="🚀 SYSTEM STARTEN", bg='#4CAF50')
        self.status_label.config(text="🔴 Gestoppt", fg='#ff6b6b')

        print("🛑 System gestoppt")

    def update_interval_changed(self, event):
        """⏰ Update-Intervall geändert"""
        try:
            self.update_interval = int(self.interval_var.get())
            print(f"⏰ Update-Intervall: {self.update_interval}s")
        except ValueError:
            self.interval_var.set("30")
            self.update_interval = 30

    def update_loop(self):
        """🔄 Update-Schleife"""
        while self.is_running:
            try:
                # Preis aktualisieren
                self.update_price_display()

                # Technical Analysis aktualisieren
                if self.technical_module.is_running:
                    results = self.technical_module.analyze()
                    self.update_technical_display(results)

                # Warten
                time.sleep(self.update_interval)

            except Exception as e:
                print(f"❌ Update-Loop Fehler: {e}")
                time.sleep(5)

    def update_price_display(self):
        """💰 Preis-Anzeige aktualisieren"""
        try:
            price = self.data_provider.get_current_price()
            self.price_label.config(text=f"Bitcoin: ${price:,.2f}")
        except Exception as e:
            print(f"❌ Preis-Update Fehler: {e}")

    def update_technical_display(self, results):
        """📊 Technical Analysis Anzeige aktualisieren"""
        try:
            # RSI
            rsi = results.get('rsi', 0)
            rsi_color = '#ff6b6b' if rsi > 70 else '#4CAF50' if rsi < 30 else '#666'
            self.rsi_label.config(text=f"RSI: {rsi:.1f}", fg=rsi_color)

            # SMA
            sma = results.get('sma_20', 0)
            self.sma_label.config(text=f"SMA(20): ${sma:,.2f}")

            # Signal
            signal = results.get('overall_signal', 'HALTEN')
            signal_color = '#4CAF50' if signal == 'KAUFEN' else '#ff6b6b' if signal == 'VERKAUFEN' else '#666'
            self.signal_label.config(text=f"Signal: {signal}", fg=signal_color)

            # Konfidenz
            confidence = results.get('confidence', 0.5)
            self.confidence_label.config(text=f"Konfidenz: {confidence*100:.0f}%")

            # Letztes Update
            last_update = results.get('last_update', '--')
            self.update_label.config(text=f"Letztes Update: {last_update}")

        except Exception as e:
            print(f"❌ Display-Update Fehler: {e}")

    def on_closing(self):
        """🚪 Programm beenden"""
        if self.is_running:
            self.stop_system()
            time.sleep(1)

        self.root.destroy()
        print("👋 Dashboard beendet")

    def run(self):
        """🚀 Dashboard starten"""
        print("🚀 Working Bitcoin Trading Dashboard gestartet")

        # Schließen-Event binden
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # GUI starten
        self.root.mainloop()

def main():
    """🚀 Hauptfunktion"""
    try:
        print("🚀 WORKING BITCOIN TRADING DASHBOARD")
        print("=" * 40)

        dashboard = WorkingTradingDashboard()
        dashboard.run()

    except Exception as e:
        print(f"❌ Dashboard-Start Fehler: {e}")
        messagebox.showerror("Fehler", f"Dashboard konnte nicht gestartet werden:\n{e}")

if __name__ == "__main__":
    main()