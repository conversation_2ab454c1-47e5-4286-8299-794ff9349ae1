#!/usr/bin/env python3
"""
🚀 BEDIENUNGSFREUNDLICHER BITCOIN TRADING LAUNCHER 🚀
===================================================
🏆 EINFACH - SCHNELL - INTELLIGENT 🏆
✅ Super einfache Bedienung mit Menü-Navigation
✅ Automatische Empfehlungen für beste Performance
✅ Alle 3 Bitcoin Trading Systeme in einem Launcher
✅ Desktop-Verknüpfung für Ein-Klick-Start
✅ Benutzerfreundliche Oberfläche ohne Komplexität

🏅 VERFÜGBARE SYSTEME:
1. FAVORIT - Bewährt & Getestet (für Profis)
2. SCHNELL - Optimiert & Effizient (für Alltag)  
3. KI-SYSTEM - Innovativ & Selbstlernend (für Experimente)

💡 EINFACH NUMMER WÄHLEN UND LOSLEGEN!
"""

import os
import sys
import time
import subprocess
from datetime import datetime

class BitcoinLauncher:
    """
    🚀 BEDIENUNGSFREUNDLICHER BITCOIN TRADING LAUNCHER
    ================================================
    Super einfacher Launcher für alle Bitcoin Trading Systeme.
    Einfach Nummer wählen und das System startet automatisch!
    """
    
    def __init__(self):
        self.systems = {
            '1': {
                'name': '🏅 FAVORIT - Bewährt & Getestet',
                'file': 'ultimate_complete_bitcoin_trading_FAVORITE.py',
                'description': 'Das bewährte System mit 100% Genauigkeit',
                'time': '~10 Minuten',
                'best_for': 'Live-Trading, Professionelle Nutzung',
                'features': 'Session #12+, 102 Features, Kontinuierliches Lernen'
            },
            '2': {
                'name': '🚀 SCHNELL - Optimiert & Effizient', 
                'file': 'ultimate_final_bitcoin_trading_complete.py',
                'description': 'Das schnelle System für tägliche Analysen',
                'time': '~3 Minuten',
                'best_for': 'Schnelle Marktchecks, Tägliche Nutzung',
                'features': '97% Datenqualität, 53 Features, Robuste Architektur'
            },
            '3': {
                'name': '🧠 KI-SYSTEM - Innovativ & Selbstlernend',
                'file': 'ultimate_self_learning_ai_bitcoin_trading.py', 
                'description': 'Das revolutionäre KI-System der Zukunft',
                'time': 'Sofort',
                'best_for': 'Experimente, KI-Innovation, Forschung',
                'features': '6 KI-Capabilities, Selbstoptimierung, Predictive AI'
            }
        }
        
        print("🚀 Bitcoin Trading Launcher gestartet!")
        print("💡 Wählen Sie einfach Ihr gewünschtes System aus!")
    
    def show_welcome(self):
        """Zeige Willkommens-Bildschirm"""
        print(f"\n{'='*80}")
        print(f"🚀 BITCOIN TRADING LAUNCHER")
        print(f"{'='*80}")
        print(f"🏆 EINFACH - SCHNELL - INTELLIGENT")
        print(f"")
        print(f"💡 Wählen Sie Ihr Bitcoin Trading System:")
        print(f"")
        
        for key, system in self.systems.items():
            print(f"{key}. {system['name']}")
            print(f"   📝 {system['description']}")
            print(f"   ⏱️ Laufzeit: {system['time']}")
            print(f"   🎯 Ideal für: {system['best_for']}")
            print(f"   ✨ Features: {system['features']}")
            print(f"")
        
        print(f"🤖 EMPFEHLUNG:")
        print(f"   🏅 Für Profis: Wählen Sie 1 (FAVORIT)")
        print(f"   🚀 Für Alltag: Wählen Sie 2 (SCHNELL)")
        print(f"   🧠 Für Experimente: Wählen Sie 3 (KI-SYSTEM)")
        print(f"")
        print(f"{'='*80}")
    
    def get_user_choice(self):
        """Hole Benutzer-Auswahl"""
        while True:
            try:
                print(f"\n🎯 Ihre Wahl (1-3) oder 'q' zum Beenden:")
                choice = input("➤ ").strip().lower()
                
                if choice == 'q':
                    print("👋 Auf Wiedersehen!")
                    return None
                
                if choice in self.systems:
                    return choice
                
                print("❌ Ungültige Eingabe! Bitte wählen Sie 1, 2, 3 oder 'q'")
                
            except KeyboardInterrupt:
                print("\n👋 Auf Wiedersehen!")
                return None
            except Exception as e:
                print(f"❌ Fehler: {e}")
    
    def launch_system(self, choice: str):
        """Starte gewähltes System"""
        system = self.systems[choice]
        
        print(f"\n🚀 Starte {system['name']}...")
        print(f"📝 {system['description']}")
        print(f"⏱️ Erwartete Laufzeit: {system['time']}")
        print(f"")
        print(f"💡 Das System wird jetzt gestartet...")
        print(f"📊 Bitte warten Sie auf die Ergebnisse...")
        print(f"")
        
        # Prüfe ob Datei existiert
        if not os.path.exists(system['file']):
            print(f"❌ FEHLER: Datei '{system['file']}' nicht gefunden!")
            print(f"💡 Stellen Sie sicher, dass alle Bitcoin Trading Systeme im gleichen Ordner sind.")
            return False
        
        try:
            # Starte das gewählte System
            print(f"▶️ Führe aus: python {system['file']}")
            print(f"{'='*80}")
            
            # Führe das System aus
            result = subprocess.run([sys.executable, system['file']], 
                                  capture_output=False, 
                                  text=True)
            
            print(f"{'='*80}")
            
            if result.returncode == 0:
                print(f"✅ {system['name']} erfolgreich abgeschlossen!")
                print(f"🎉 Das System hat alle Analysen durchgeführt!")
            else:
                print(f"⚠️ {system['name']} mit Warnungen beendet (Code: {result.returncode})")
                print(f"💡 Das System hat möglicherweise trotzdem Ergebnisse geliefert.")
            
            return True
            
        except FileNotFoundError:
            print(f"❌ FEHLER: Python nicht gefunden!")
            print(f"💡 Stellen Sie sicher, dass Python installiert ist.")
            return False
        except Exception as e:
            print(f"❌ FEHLER beim Starten: {e}")
            return False
    
    def show_post_launch_menu(self):
        """Zeige Menü nach System-Ausführung"""
        print(f"\n🎯 Was möchten Sie als nächstes tun?")
        print(f"")
        print(f"1. 🔄 Anderes System starten")
        print(f"2. 🚀 Gleiches System nochmal starten") 
        print(f"3. 👋 Launcher beenden")
        print(f"")
        
        while True:
            try:
                choice = input("➤ Ihre Wahl (1-3): ").strip()
                
                if choice in ['1', '2', '3']:
                    return choice
                
                print("❌ Ungültige Eingabe! Bitte wählen Sie 1, 2 oder 3")
                
            except KeyboardInterrupt:
                return '3'
            except Exception:
                print("❌ Eingabefehler! Bitte versuchen Sie es erneut.")
    
    def run(self):
        """Hauptschleife des Launchers"""
        last_choice = None
        
        while True:
            try:
                # Zeige Willkommens-Bildschirm
                self.show_welcome()
                
                # Hole Benutzer-Auswahl
                choice = self.get_user_choice()
                
                if choice is None:
                    break
                
                # Starte gewähltes System
                success = self.launch_system(choice)
                last_choice = choice
                
                if success:
                    # Zeige Post-Launch-Menü
                    post_choice = self.show_post_launch_menu()
                    
                    if post_choice == '1':
                        continue  # Zurück zur Hauptauswahl
                    elif post_choice == '2' and last_choice:
                        # Gleiches System nochmal starten
                        self.launch_system(last_choice)
                        continue
                    else:
                        break  # Launcher beenden
                else:
                    # Bei Fehler fragen ob weitermachen
                    print(f"\n🤔 Möchten Sie es nochmal versuchen? (j/n)")
                    retry = input("➤ ").strip().lower()
                    if retry not in ['j', 'ja', 'y', 'yes']:
                        break
                
            except KeyboardInterrupt:
                print("\n👋 Launcher beendet!")
                break
            except Exception as e:
                print(f"❌ Unerwarteter Fehler: {e}")
                print(f"💡 Launcher wird neu gestartet...")
                time.sleep(2)

def main():
    """Hauptfunktion"""
    print("🚀 Starte Bitcoin Trading Launcher...")
    
    launcher = BitcoinLauncher()
    launcher.run()
    
    print("\n🎉 Vielen Dank für die Nutzung des Bitcoin Trading Launchers!")
    print("💡 Bis zum nächsten Mal!")

if __name__ == "__main__":
    main()
