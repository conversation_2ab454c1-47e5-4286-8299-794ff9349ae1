�

    Heh��  �                   �  � d Z ddlZddlZddlZddlZddlmZm	Z	 ddl
Z
ddlZddlZddl
Z
ddlZddlZddlmZmZmZmZ ddlZddlZddlmZmZ ddlmZmZ ddlmZmZ ddl m!Z!m"Z"m#Z# ddl$Z%dd	l&m'Z' ddl(Z)dd
l(m*Z*m+Z+ ddl,m-Z. ddl/m0Z0 ddl1m2Z3  e
j4        d�  �          G d
� d�  �        Z5d� Z6e7dk    r e6�   �          dS dS )u�  
ULTIMATE BITCOIN TRADING SYSTEM V3.0
====================================
KOMPLETT ÜBERARBEITET UND OPTIMIERT VON GRUND AUF
- Selbstlernendes ML-Modell mit kontinuierlichem Training
- 60%+ Minimum Genauigkeit durch optimierte Algorithmen
- Streamlined Code ohne unnötige Schritte
- Vollständig funktionsfähige moderne GUI
- Alle Features: APIs + ML + Technische Analyse + Risk Management
- Performance-optimiert für maximale Geschwindigkeit

ULTIMATE TRADING SYSTEM V3.0 - NEUGESTALTUNG FÜR PERFEKTION!
�    N)�datetime�	timedelta)�Dict�List�Tuple�Optional)�RandomForestRegressor�GradientBoostingRegressor)�StandardScaler�RobustScaler)�train_test_split�cross_val_score)�accuracy_score�mean_squared_error�r2_score)�MLPRegressor)�ttk�
messagebox)�FigureCanvasTkAgg�ignorec                   �$  � e Zd ZdZd� Zd� Zd� Zdej        fd�Z	dej        de
fd�Zdej        d	e
dej
        fd
�Zddej        dedefd
�Zdej        d	e
de
fd�Zdedefd�Zd	e
de
fd�Zdej
        ded	e
fd�Zde
fd�Zde
dede
fd�Zde
fd�ZdS )�UltimateBitcoinTradingSystemV3u�   
    ULTIMATE BITCOIN TRADING SYSTEM V3.0
    ====================================
    Komplett überarbeitet und optimiert für maximale Genauigkeit und Performance
    c                 ��  � d| _         d| _        d| _        d| _        d| _        d| _        i | _        i | _        t          �   �         | _	        g | _
        g | _        g | _        d | _
        i | _        d | _        d | _        ddddddd	�| _        t%          d
�  �         t%          d| j         � ��  �         t%          d| j        d
���  �         t%          d�  �         | �                    �   �          d S )NzUltimate_v3.0_SelfLearningzBTC-USD�BTCUSDT�333333�?g      �?���Q��?r   �        )�total_predictions�correct_predictions�current_accuracy�
best_accuracy�model_improvements�training_cyclesz2ULTIMATE BITCOIN TRADING SYSTEM V3.0 initialisiertz	Version: zZiel-Genauigkeit: �.1%z#Selbstlernendes ML-System aktiviert)�VERSION�SYMBOL�BINANCE_SYMBOL�min_accuracy_target�confidence_threshold�risk_per_trade�	ml_models�model_performancer   �feature_scaler�
training_data�prediction_history�accuracy_history�market_data�technical_indicators�current_prediction�last_update�
session_stats�print�_load_persistent_data)�selfs    �,E:\Dev\ultimate_bitcoin_trading_system_v3.py�__init__z'UltimateBitcoinTradingSystemV3.__init__7   s  � �3������'��� $(�� �$(��!�#��� ���!#���*�n�n������"$��� "���  ���$&��!�"&������ "#�#$� #� �"#� �

� 
��� 	�C�D�D�D�
�(�$�,�(�(�)�)�)�
�A�4�#;�A�A�A�B�B�B�
�4�5�5�5� 	
�"�"�$�$�$�$�$�    c                 ��  � 	 t           j        �                    d�  �        r�t          dd�  �        5 }t	          j        |�  �        }|�                    di �  �        | _        |�                    di �  �        | _        |�                    dt          �   �         �  �        | _
        t          dt          | j        �  �        � d��  �         ddd�  �         n# 1 swxY w Y   t           j        �                    d	�  �        r�t          d	d
�  �        5 }t          j        |�  �        }|�                    dg �  �        | _        |�                    dg �  �        | _        |�                    d
g �  �        | _        |�                    d| j        �  �        | _        t          dt          | j        �  �        � d��  �         ddd�  �         dS # 1 swxY w Y   dS dS # t$          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)z.Lade persistente ML-Modelle und Trainingsdaten�ml_models_v3.pkl�rb�models�performance�scalerzML-Modelle geladen: z ModelleN�training_data_v3.json�rr.   r/   r0   r5   zTrainingsdaten geladen: � Samplesz&Konnte persistente Daten nicht laden: )�os�path�exists�open�pickle�load�getr+   r,   r   r-   r6   �len�jsonr.   r/   r0   r5   �	Exception)r8   �f�
saved_data�data�es        r9   r7   z4UltimateBitcoinTradingSystemV3._load_persistent_datab   s�  � �	@��w�~�~�0�1�1� 
P��,�d�3�3� P�q�!'��Q���J�%/�^�^�H�b�%A�%A�D�N�-7�^�^�M�2�-N�-N�D�*�*4�.�.��<�>�>�*R�*R�D�'��N��T�^�1D�1D�N�N�N�O�O�O�P� P� P� P� P� P� P� P� P� P� P���� P� P� P� P� �w�~�~�5�6�6� 
X��1�3�7�7� X�1��9�Q�<�<�D�)-���/�2�)F�)F�D�&�.2�h�h�7K�R�.P�.P�D�+�,0�H�H�5G��,L�,L�D�)�)-���/�4�CU�)V�)V�D�&��V�S��9K�5L�5L�V�V�V�W�W�W�
X� X� X� X� X� X� X� X� X� X� X� X���� X� X� X� X� X� X�
X� 
X�� � 	@� 	@� 	@��>�1�>�>�?�?�?�?�?�?�?�?�?�����	@���s`   �/G �BC�G �C�G �C�2G �B+G�9G �G
�
G �
G
�G �
G<�G7�7G<c                 �   � 	 | j         | j        | j        | j        t	          j        �   �         �                    �   �         d�}t          dd�  �        5 }t          j	        ||�  �         ddd�  �         n# 1 swxY w Y   | j
        dd�         | j        dd�         | j        dd�         | j
        | j        t	          j        �   �         �                    �   �         d�}t          d	d
�  �        5 }t          j	        ||d��  �         ddd�  �         n# 1 swxY w Y   t          d
t!          | j         �  �        � dt!          | j
        �  �        � d��  �         dS # t"          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)z'Speichere ML-Modelle und Trainingsdaten)r?   r@   rA   �version�	timestampr=   �wbN��������i����)r.   r/   r0   r5   rT   rU   rB   �w�   )�indentzPersistente Daten gespeichert: z
 Modelle, rD   z*Konnte persistente Daten nicht speichern: )r+   r,   r-   r%   r   �now�	isoformatrH   rI   �dumpr.   r/   r0   r5   rM   r6   rL   rN   )r8   �
model_datarO   r.   rR   s        r9   �_save_persistent_dataz4UltimateBitcoinTradingSystemV3._save_persistent_data{   s  � �	D� �.�#�5��-��<�%�\�^�^�5�5�7�7�� �J� �(�$�/�/� 
+�1���J��*�*�*�
+� 
+� 
+� 
+� 
+� 
+� 
+� 
+� 
+� 
+� 
+���� 
+� 
+� 
+� 
+�
 "&�!3�E�F�F�!;�&*�&=�d�e�e�&D�$(�$9�$�%�%�$@�!%�!3��<�%�\�^�^�5�5�7�7�
� �M� �-�s�3�3� 
6�q��	�-��1�5�5�5�5�
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6� 
6���� 
6� 
6� 
6� 
6� 
�t�C���4G�4G�t�t�SV�W[�Wi�Sj�Sj�t�t�t�u�u�u�u�u��� 	D� 	D� 	D��B�q�B�B�C�C�C�C�C�C�C�C�C�����	D���sa   �AE �A3�'E �3A7�7E �:A7�;A0E �+D�E �D�E �D�=E �
E=� E8�8E=�returnc                 �  � 	 | j         �2| j        r+t          j        �   �         | j        z
  j        dk     r| j         S t          d�  �         t
          j        | j        �  �        }|�	                    dd��  �        }|j
        rt          d�  �        �	 d| j        � �}t          j        |d	�
�  �        }|j        dk    r�t!          |�                    �   �         d         �  �        }|d
         j        d         }t'          ||z
  �  �        |z  dk     r�t          j        �   �         �                    ddd��  �        }t+          j        |gt/          ||�  �        gt1          ||�  �        g|g|d         j        d         gd�|g��  �        }t+          j        ||g�  �        }n#  Y nxY w|�                    �   �         }||d         dk             }|| _         t          j        �   �         | _        t          dt7          |�  �        � d��  �         |S # t          $ r/}	t          d|	� ��  �         t+          j        �   �         cY d}	~	S d}	~	ww xY w)z>Hole optimierte Marktdaten mit intelligenter Caching-StrategieN�   zSammle optimierte Marktdaten...�7d�1h)�period�intervalzKeine Yahoo Finance Datenz3https://api.binance.com/api/v3/ticker/price?symbol=�   )�timeout��   �price�Close�����皙�����?r   )�minute�second�microsecond�Volume)�Open�High�Lowrl   rr   )�indexzMarktdaten optimiert: z DatenpunktezFEHLER bei Marktdaten: )r1   r4   r   r\   �secondsr6   �yf�Tickerr&   �history�emptyrN   r'   �requestsrK   �status_code�floatrM   �iloc�abs�replace�pd�	DataFrame�max�min�concat�dropnarL   )
r8   �btc�hist�url�response�
binance_price�
last_price�current_time�new_rowrR   s
             r9   �get_optimized_market_dataz8UltimateBitcoinTradingSystemV3.get_optimized_market_data�   sf  � �3	"�� �,��1A�,�����$�"2�2�;�c�A�A��'�'��3�4�4�4� �)�D�K�(�(�C��;�;�d�T�;�:�:�D��z� 
=�� ;�<�<�<�
�a�D�L_�a�a��#�<��Q�7�7�7���'�3�.�.�$)�(�-�-�/�/�'�*B�$C�$C�M� "&�g��!3�B�!7�J��=�:�5�6�6��C�d�J�J�'/�|�~�~�'=�'=�Q�q�^_�'=�'`�'`��"$�,�%/�L�%(��]�%C�%C�$D�$'�
�M�$B�$B�#C�&3�_�'+�H�~�':�2�'>�&?�0� 0� #/��
#1� #1� #1��  "�y�$���9�9����
������ �;�;�=�=�D���X���*�+�D�  $�D��'�|�~�~�D���B�3�t�9�9�B�B�B�C�C�C��K��� 	"� 	"� 	"��/�A�/�/�0�0�0��<�>�>�!�!�!�!�!�!�����	"���s=   �8H �AH �DF �H �F"� A+H �
I�$I �:I� I�dfc                 �  � 	 t          |�  �        dk     ri S |d         }|d         }|d         }|d         }i }dD ]�}|�                    �   �         }|�                    |dk    d�  �        �                    |��  �        �                    �   �         }	|�                    |dk     d�  �         �                    |��  �        �                    �   �         }
|	|
z  }d	d	d
|z   z  z
  }|j        d         |d|� �<   ��|�                    d
��  �        �                    �   �         }
|�                    d��  �        �                    �   �         }|
|z
  }|�                    d��  �        �                    �   �         }||z
  }|j        d         |d<   |j        d         |d<   |j        d         |d<   |�                    d�  �        �                    �   �         }|�                    d�  �        �                    �   �         }||dz  z   }||dz  z
  }||z
  ||z
  z  }||z
  |z  }|j        d         |d<   |j        d         |d<   ||�                    d�  �        �                    �   �         k     j        d         |d<   |�                    d�  �        �                    �   �         }|j        d         |j        d         z  |d<   |t          j	        |�                    �   �         �  �        z  �
                    �   �         }|j        d         |�                    d�  �        �                    �   �         j        d         k    |d<   |�                    �   �         }|�                    d�  �        �                    �   �         j        d         |d<   |�                    d�  �        �                    �   �         j        d         |d<   |j        d         |j        d         z  d
z
  |d<   |j        d         |j        d          z  d
z
  |d!<   |�                    d�  �        �                    �   �         j        d         }|�                    d�  �        �
                    �   �         j        d         }|j        d         |z
  ||z
  z  |d"<   |�                    d�  �        �                    �   �         }|j        d         |j        d         z
  |j        d         z  |d#<   |�                    �   �         D ]Z\  }}t          j        |�  �        st          j        |�  �        rd$||<   �3t%          |t&          �  �        rt)          |�  �        ||<   �[|| _        t-          d%t          |�  �        � d&��  �         |S # t.          $ r} t-          d'| � ��  �         i cY d(} ~ S d(} ~ ww xY w))uD   Berechne optimierte technische Indikatoren für maximale Genauigkeit�2   rl   rt   ru   rr   )�   �   r   )�window�d   �   rm   �rsi_�   )�span�   �	   �macd�macd_signal�macd_histogram�   rZ   �bb_position�bb_width�
bb_squeeze�volume_ratio�
   �	obv_trend�
volatility_10�
volatility_20i�����momentum_10i�����momentum_20�price_position�trend_strengthr   z"Technische Indikatoren berechnet: z Indikatorenz$FEHLER bei technischen Indikatoren: N)rL   �diff�where�rolling�meanr   �ewm�std�np�sign�cumsum�
pct_changer�   r�   �itemsr�   �isna�isinf�
isinstance�boolr~   r2   r6   rN   )!r8   r�   �prices�highs�lows�volumes�
indicatorsrf   �delta�gain�loss�rs�rsi�ema_12�ema_26r�   �signal�	histogram�sma_20�std_20�bb_upper�bb_lowerr�   r�   �
volume_sma�obv�returns�recent_high�
recent_low�sma_50�key�valuerR   s!                                    r9   �(calculate_optimized_technical_indicatorszGUltimateBitcoinTradingSystemV3.calculate_optimized_technical_indicators�   sQ  � �U	��2�w�w��|�|��	���[�F��v�J�E��e�9�D���l�G��J�
 #� 
;� 
;�����
�
�����E�A�I�q�1�1�:�:�&�:�I�I�N�N�P�P�����U�Q�Y��2�2�2�;�;�6�;�J�J�O�O�Q�Q���D�[���S�A��F�^�,��.1�h�r�l�
�?�&�?�?�+�+� �Z�Z�R�Z�(�(�-�-�/�/�F��Z�Z�R�Z�(�(�-�-�/�/�F��F�?�D��X�X�1�X�%�%�*�*�,�,�F��v�
�I�!%��2��J�v��(.��B��J�}�%�+4�>�"�+=�J�'�(� �^�^�B�'�'�,�,�.�.�F��^�^�B�'�'�+�+�-�-�F���!��,�H���!��,�H�!�H�,��H�1D�E�K� �8�+�v�5�H�(3�(8��(<�J�}�%�%-�]�2�%6�J�z�"�(0�8�3C�3C�B�3G�3G�3L�3L�3N�3N�(N�'T�UW�'X�J�|�$� !����,�,�1�1�3�3�J�)0��b�)9�J�O�B�<O�)O�J�~�&� �R�W�V�[�[�]�]�3�3�3�;�;�=�=�C�'*�x��|�c�k�k�"�o�o�6J�6J�6L�6L�6Q�RT�6U�'U�J�{�#� �'�'�)�)�G�*1�/�/�"�*=�*=�*A�*A�*C�*C�*H��*L�J��'�*1�/�/�"�*=�*=�*A�*A�*C�*C�*H��*L�J��'� *0��R��6�;�s�;K�)K�a�)O�J�}�%�)/��R��6�;�s�;K�)K�a�)O�J�}�%�  �-�-��+�+�/�/�1�1�6�r�:�K����b�)�)�-�-�/�/�4�R�8�J�,2�K��O�j�,H�[�[e�Me�+f�J�'�(� �^�^�B�'�'�,�,�.�.�F�,2�K��O�f�k�"�o�,M�QW�Q\�]_�Q`�+`�J�'�(� )�.�.�0�0� 
3� 
3�
��U��7�5�>�>� 3�R�X�e�_�_� 3�&)�J�s�O�O���t�,�,� 3�&+�E�l�l�J�s�O��(2�D�%��T�s�:���T�T�T�U�U�U����� 	� 	� 	��<��<�<�=�=�=��I�I�I�I�I�I�����	���s#   �U �UU �
V�(V�;V�Vr�   c                 �$  � 	 g }|d         j         d         }|d         j         d         }|d         j         d         }|d         j         d         }|dz  ||z  ||z  ||z
  |z  |dz  ||k    r||z
  ||z
  z  ndg}|�                    |�  �         |�                    d	d
�  �        dz  |�                    dd
�  �        dz  |�                    d	d
�  �        |�                    dd
�  �        z
  dz  t          j        |�                    d
d�  �        dz  �  �        t          j        |�                    dd�  �        dz  �  �        |�                    dd�  �        |�                    dd�  �        t          |�                    dd�  �        �  �        t
          |�                    dd�  �        d�  �        dz  t          |�                    dd�  �        �  �        |�                    dd�  �        d
z  |�                    dd�  �        d
z  |�                    dd�  �        t          |�                    dd�  �        d�  �        z  t          j        |�                    dd�  �        dz  �  �        t          j        |�                    d d�  �        dz  �  �        |�                    d!d�  �        t          j        |�                    d"d�  �        dz  �  �        g}	|�                    |	�  �         t          j	        �   �         }
|
j
        d#z  |
�                    �   �         d$z  |
j        dz
  d%z  t          j        d&t          j        z  |
j
        z  d'z  �  �        t          j        d&t          j        z  |
j
        z  d'z  �  �        t          j        d&t          j        z  |
�                    �   �         z  d(z  �  �        t          j        d&t          j        z  |
�                    �   �         z  d(z  �  �        d)|
j
        cxk    rd*k    rn nd+nd,d-|
j
        k    s|
j
        d&k    rd+nd,g	}|�                    |�  �         t#          |�  �        d.k    �r,g }|d         �                    �   �         �                    d�  �        }
d/D ]�}t#          |�  �        |k    r�||d         j         | dz
           z
  |d         j         | dz
           z  }|�                    t          j        |d0z  �  �        �  �         |t#          |
�  �        k    rH|
j         | d1�         �                    �   �         }|�                    t
          |dz  d+�  �        �  �         ��|�                    ddg�  �         ��|�                    |�  �         n|�                    dgd2z  �  �         t#          |�  �        dk    r�|d         j         d3d1�         }|d         j         d3d1�         }|�                    �   �         |�                    �   �         z  |�                    �   �         |�                    �   �         z  t#          |�  �        dk    rt          j        ||�  �        d4         ndg}|�                    |�  �         n|�                    g d5��  �         t          j        |�  �        }t          j        |d,d+d6�7�  �        }|�                    dd�  �        S # t6          $ rH}t9          d8|� ��  �         dd1l}|�                    �   �          t          j        d9�  �        cY d1}~S d1}~ww xY w):u�   
        ULTIMATIVE ML-FEATURE ENGINEERING V2.0
        ======================================
        Drastisch verbesserte Features für 70%+ Genauigkeit
        rl   rm   rt   ru   rr   i�� g    e��A�      �?�rsi_14r�   r�   �rsi_21r�   r   ��  r�   r�   r�   皙�����?r�   Fr�   r�   �   r�   r�   �{�G�z�?r�   �����MbP?r�   r�   r�   r�   r�   �      8@g      @g      >@rZ   �   �   �   �   �      �?r   �   �0   )r�   rZ   �   �   r�   r�   r�   r�   Nr�   i����)r   r�   )r   r   r   g      �)�nan�posinf�neginfz$FEHLER bei ultimativen ML-Features: )r�   �(   ) r   �extendrK   r�   �tanhr~   r�   r�   r   r\   �hour�weekday�day�math�sin�pi�cosrL   r�   �fillna�appendr�   r�   �corrcoef�array�
nan_to_num�reshaperN   r6   �	traceback�	print_exc�zeros)r8   r�   r�   �features�
current_price�
high_price�	low_price�volume�price_features�
tech_featuresr\   �
time_features�
price_changesr�   �lookback�change�
period_vol�
recent_closes�recent_volumes�microstructure_featuresrR   r�   s                         r9   �create_ml_featuresz1UltimateBitcoinTradingSystemV3.create_ml_features,  s�  � �h	%��H� �w�K�,�R�0�M��F����,�J��5�	��r�*�I���\�&�r�*�F� ��&��]�*��M�)��i�'�=�8����JT�Xa�Ja�Ja���*�z�I�/E�F�F�gj�
�N� 
�O�O�N�+�+�+� ���x��,�,�s�2����x��,�,�s�2�����"�-�-�
���x��0L�0L�L�PS�S���
���v�q�1�1�D�8�9�9���
���'7��;�;�d�B�C�C����}�c�2�2����z�3�/�/��j�n�n�\�5�9�9�:�:��J�N�N�>�1�5�5�q�9�9�A�=��j�n�n�[�%�8�8�9�9������5�5��:������5�5��:������5�5��J�N�N�?�\`�<a�<a�ch�8i�8i�i���
���}�a�8�8�2�=�>�>���
���}�a�8�8�1�<�=�=����/��5�5���
���'7��;�;�b�@�A�A�#�M�& 
�O�O�M�*�*�*� �,�.�.�C���4�����
�
��#���1���$����T�W��s�x�/�"�4�5�5����T�W��s�x�/�"�4�5�5����T�W��s�{�{�}�}�4�q�8�9�9����T�W��s�{�{�}�}�4�q�8�9�9��C�H�*�*�*�*��*�*�*�*�*�����S�X�~�~���Q�����C�
�M� 
�O�O�M�*�*�*� �2�w�w�"�}�}� "�
��W�+�0�0�2�2�9�9�!�<�<�� !9� 5� 5�H��2�w�w��)�)�"/�"�W�+�2B�H�9�Q�;�2O�"O�SU�V]�S^�Sc�em�dm�no�do�Sp�!p��%�,�,�R�W�V�b�[�-A�-A�B�B�B� $�s�7�|�|�3�3�)0��x�i�j�j�)A�)E�)E�)G�)G�J�)�0�0��Z�#�5E�s�1K�1K�L�L�L��%�,�,�a��V�4�4�4�4����
�.�.�.�.� �����b��)�)�)� �2�w�w�"�}�}� "�7�� 0���� 6�
�!#�H��!2�3�4�4�!8�� "�%�%�'�'�-�*<�*<�*>�*>�>�"�&�&�(�(�>�+>�+>�+@�+@�@�HK�M�HZ�HZ�]^�H^�H^�B�K�
�~�>�>�t�D�D�de�+�'�
 ��� 7�8�8�8�8����	�	�	�*�*�*� �x��)�)�H��}�X�3�s�4�P�P�P�H��#�#�A�r�*�*�*��� 	%� 	%� 	%��<��<�<�=�=�=��������!�!�!��8�G�$�$�$�$�$�$�$�$�����		%���s   �Z:Z= �=
\�=\
�\�
\F�
force_retrainc                 �z  � 	 t          d�  �         t          |�  �        dk     r"t          dt          |�  �        � d��  �         dS t          dt          |�  �        � d��  �         t          d�  �         g }g }g d	�}t          |�  �        d
k    r|�                    d�  �         |D �]H}t          dt          |�  �        d
z  �  �        }t	          |t          |�  �        |z
  �  �        D �]}	 |j        d|dz   �         }	| �                    |	�  �        }
| �                    |	|
�  �        }|d         j        |         }|d         j        ||z            }
|
|z
  |z  }|dk    rd}n|dk    rd}n|dk     rd}n|dk     rd}nd}t          j        |�	                    �   �         |dz  g�  �        }|�                    |�  �         |�                    |�  �         ��# t          $ r
}Y d}~��d}~ww xY w��J| j        dd�         D ]�}	 |�                    dd�  �        }t          |t          t          f�  �        r'|dk    rd}n |dk    rd}n|dk     rd}n|dk     rd}nd}nd}|d          }t          |�  �        d!k     r|d"gz   }|�                    |�  �         |�                    |�  �         ��#  Y ��xY wt          |�  �        d#k     r"t          d$t          |�  �        � d%��  �         dS t          j        |�  �        }t          j        |�  �        }t          dt          |�  �        � d&��  �         t#          ||dd'�(�  �        \  }}}}| j        �                    |�  �         | j        �                    |�  �        }| j        �                    |�  �        }t          d)�  �         t          j        |t          �*�  �        }t+          d+d,d-dd.d/d'd�0�  �        t-          d+d1d2dd-dd'�3�  �        t/          j        d+d1d2ddd4d4d'd�5�	  �	        t3          d6d7d8d9d:d;d/d4d#d'�<�
  �
        t/          j        d=d>d?d@d@dAd'd�B�  �        dC�}t          dDt          |�  �        � dE��  �         d}d} |�                    �   �         D �]�\  }!}"	 t          dD|!� dF��  �         |"�                    ||�  �         |"�                    ||�  �        }#|"�                    ||�  �        }$t9          |"||d>dG�H�  �        }%|%�                    �   �         }&|%�                    �   �         }'|"�                    |�  �        }(t          j         |(�  �        �!                    t          �  �        })t          j"        |)dd�  �        })tG          ||)�  �        }*t          j$        ||)z
  �  �        dk    }+t          j        |+�  �        },t          j%        |�  �        t          j%        |)�  �        k    }-t          j        |-�  �        }.|*dIz  |,dJz  z   |.dz  z   }/tM          ||(�  �        }0t          j'        |0�  �        }1tQ          ||(�  �        }2|/dz  tS          d|2�  �        dJz  z   tS          d|&�  �        dz  z   tS          dd|1dz  z
  �  �        d4z  z   }3|#|$|&|'|*|,|.|/|3|0|1|2tU          j+        �   �         �,                    �   �         dK�
| j-        |!<   t          |!� dL|3dM�dN|/dM�dO|2dM���  �         |3| k    r|3} |!}|"| j.        |!<   ��f# t          $ r8}t          dP|!� dQ|� ��  �         ddl/}4|4�0                    �   �          Y d}~���d}~ww xY w| j1        dRxx         dz
  cc<   | | j1        dS         k    r| | j1        dS<   | j1        dTxx         dz
  cc<   | �2                    �   �          t          dU�  �         t          dV| dM�dW|� dX��  �         t          dYt          | j.        �  �        � ��  �         t          dZt          |�  �        � ��  �         | dk    }5|5rt          d[| dM�d\��  �         nt          d]| dM�d^��  �         |5S # t          $ r}t          d_|� ��  �         Y d}~dS d}~ww xY w)`u�   
        ULTIMATIVES SELBSTLERNENDES ML-SYSTEM V2.0
        ==========================================
        Drastisch verbesserte ML-Training für 70%+ Genauigkeit
        z6Starte ULTIMATIVES selbstlernendes ML-Training V2.0...r�   u#   Nicht genügend historische Daten: z < 50Fz
Training mit z historischen Datenpunkten...z(Erstelle ultimatives Training-Dataset...)r�   r�   r�   �K   r�   �   rh   Nr�   rl   r�   rZ   g{�G�zt?g{�G�z�������g{�G�zt�rm   r   r�   rX   �target皙�����?r   皙�����?g�������?r  �   r�   r�   u'   Nicht genügend valide Trainingsdaten: z < 20z Samples...�*   )�	test_size�random_statez&Initialisiere ultimative ML-Modelle...)�dtyperj   �   r�   �sqrtT)�n_estimators�	max_depth�min_samples_split�min_samples_leaf�max_features�	bootstrapr  �n_jobsr�   rn   )r   r!  �
learning_rate�	subsampler"  r#  r  r�   )	r   r!  r'  r(  �colsample_bytree�	reg_alpha�
reg_lambdar  r&  )rj   r�   r�   �relu�adamr�   �adaptiver�   )
�hidden_layer_sizes�
activation�solver�alphar'  �max_iter�early_stopping�validation_fraction�n_iter_no_changer  �   r�   �{�G�z�?g�������?zreg:squarederror)r   r!  r'  r(  r)  �	objectiver  r&  )�random_forest_ultimate�gradient_boost_ultimate�xgboost_ultimate�neural_network_ultimate�extra_trees_ultimatez
Trainiere z ultimative ML-Modelle...z...�r2)�cv�scoringr�   �333333�?)
�train_score�
test_score�cv_score�cv_std�exact_accuracy�tolerant_accuracy�direction_accuracy�combined_accuracy�final_score�mse�rmser   rU   z: Final Score �.3fz, Combined Accuracy u   , R² zFEHLER beim Training von �: r#   r!   r"   z&ULTIMATIVES ML-Training abgeschlossen!zBeste Final Score: z (�)zTrainierte Modelle: zTraining-Samples: u    ✅ TRAINING ERFOLGREICH: Score z >= 0.6u"   ⚠️ Training suboptimal: Score z < 0.6zFEHLER beim ML-Training: )3r6   rL   r�   r�   �ranger   r�   r  r�   �flattenrN   r.   rK   r�   �intr~   r�   r
   r-   �fit�	transformr	   r
   �xgb�XGBRegressorr   r�   �scorer   r�   r�   �predict�round�astype�clipr   r�   r�   r   r  r   r�   r   r\   r]   r,   r+   r�   r�   r5   r`   )6r8   r�   r  �X_list�y_list�horizons�horizon�	start_idx�i�temp_df�temp_indicatorsr  r  �future_price�price_changer  �enhanced_featuresrR   �sample�
old_target�
new_target�X�y�X_train�X_test�y_train�y_test�X_train_scaled�
X_test_scaled�y_regressionr?   �
best_model�
best_score�name�modelrC  rD  �	cv_scores�cv_meanrF  �y_pred�y_pred_classesrG  �tolerant_correctrH  �direction_correctrI  rJ  rL  rM  r?  rK  r�   �successs6                                                         r9   �train_self_learning_modelsz9UltimateBitcoinTradingSystemV3.train_self_learning_models�  s!
  � �[	��J�K�K�K� �2�w�w��|�|��J�C��G�G�J�J�J�K�K�K��u��H�#�b�'�'�H�H�H�I�I�I� 
�<�=�=�=��F��F� "�z�z�H��2�w�w�"�}�}�����#�#�#�#� !
!� !
!����C��G�G�q�L�1�1�	��y�#�b�'�'�G�*;�<�<� !� !�A�!�"$�'�$�1�Q�3�$�-��*.�*W�*W�X_�*`�*`��#'�#:�#:�7�O�#T�#T�� )+�7��(8��(;�
�')�'�{�'7��G��'D��(4�}�(D�
�'U�� (�$�.�.�%&�F�F�)�E�1�1�%&�F�F�)�E�1�1�%'�F�F�)�F�2�2�%'�F�F�%&�F� -/�I�h�6F�6F�6H�6H�7�UY�>�JZ�,[�,[�)��
�
�&7�8�8�8��
�
�f�-�-�-�-��$� !� !� !� ���������!����=!�D �,�T�U�U�3� 
� 
���!'���H�a�!8�!8�J�!�*�s�E�l�;�;� '�%��+�+�)*�J�J�'�#�-�-�)*�J�J�'�#�-�-�)+�J�J�'�#�-�-�)+�J�J�)*�J�J�%&�
�  &�j�1�H��8�}�}�r�)�)�#+�s�e�#3���M�M�(�+�+�+��M�M�*�-�-�-�-����H�����6�{�{�R����R��F���R�R�R�S�S�S��u���� � �A���� � �A��5�#�a�&�&�5�5�5�6�6�6� 0@��1�PS�bd�/e�/e�/e�,�G�V�W�f� 
��#�#�G�,�,�,�!�0�:�:�7�C�C�N� �/�9�9�&�A�A�M� 
�:�;�;�;� �8�F�%�8�8�8�L� +@�!$� �&'�%&�!'�"�!#��	+� 	+� 	+� ,E�!$��"&�!�&'�%&�!#�,� ,� ,� %(�$4�!$��"&�!�%(�!�"�!#��
%� 
%� 
%� ,8�'5�%�!��",�!�#'�(+�%'�!#�,� ,� ,� ),�(8�!$� �"&�!�%(�0�!#��	)� 	)� 	)�W5� 5�F�p 
�E�s�6�{�{�E�E�E�F�F�F��J��J�%�|�|�~�~� X
*� X
*���e�W*��0�t�0�0�0�1�1�1� �I�I�n�g�6�6�6� #(�+�+�n�g�"F�"F�K�!&���]�F�!C�!C�J� !0��~�w�SU�_c� d� d� d�I�'�n�n�.�.�G�&�]�]�_�_�F� #�]�]�=�9�9�F� &(�X�f�%5�%5�%<�%<�S�%A�%A�N�%'�W�^�R��%C�%C�N� &4�F�N�%K�%K�N� (*�v�f�~�.E�'F�'F�!�'K�$�(*��0@�(A�(A�%� )+�����2�7�>�;R�;R�(R�%�)+��1B�)C�)C�&� '��,�)�C�/�0�*�S�0�1� &� -�V�V�<�<�C��7�3�<�<�D� "�&�&�1�1�B�
 *�C�/��A�r�
�
�S�(�)��A�w���#�-�.� �A��D��F�
�,�,�s�2�3�  � (3�&0�$+�"(�*8�->�.@�->�'2�"� $�$&�%-�\�^�^�%=�%=�%?�%?�4� 4�D�*�4�0�  �T�{�{��{�{�{�Vg�{�{�{�su�{�{�{�|�|�|� #�Z�/�/�%0�
�%)�
� ,1�D�N�4�(�(�� � *� *� *��A�d�A�A�a�A�A�B�B�B�$�$�$�$��'�'�)�)�)�)�)�)�)�)�����*���� 
��0�1�1�1�Q�6�1�1�1��D�.��?�?�?�6@��"�?�3��"�#7�8�8�8�A�=�8�8�8� 
�&�&�(�(�(��;�<�<�<��G�
�G�G�G�*�G�G�G�H�H�H��>��T�^�)<�)<�>�>�?�?�?��4�s�6�{�{�4�4�5�5�5� !�C�'�G�� 
S��P��P�P�P�P�Q�Q�Q�Q��Q�:�Q�Q�Q�Q�R�R�R��N��� 	� 	� 	��1�a�1�1�2�2�2��5�5�5�5�5�����	���s�   �A` �B'` �.C)G�` �
G,�"` �'G,�,` �B&J*�)` �*J.�,8` �&F` �:I[�` �
\�#-\�` �\�C7` �
`:�`5�5`:c                 �@	  ��� 	 | j         s$t          d�  �         | �                    |�  �        S t          dt          | j         �  �        � d��  �         | �                    ||�  �        }t          j        |�                    �   �         dg�  �        }|�                    dd�  �        }| j	        �
                    |�  �        }i }i }i �| j         �                    �   �         D ]�\  }}		 |	�                    |�  �        d         }
|
||<   | j
        �                    |i �  �        }|�                    dd	�  �        }|�                    d
d	�  �        }
|dz  |
dz  z   }|||<   |�|<   t          |� d
|
d�d|d���  �         ��# t          $ r}t          d|� d|� ��  �         Y d}~��d}~ww xY w|s$t          d�  �         | �                    |�  �        S t!          ��                    �   �         �  �        ��dk    r6t          j        t'          |�                    �   �         �  �        �  �        }d	}n�t!          ��fd�|�                    �   �         D �   �         �  �        }d� |�                    �   �         D �   �         }|rt          j        |�  �        }n3t          j        t'          |�                    �   �         �  �        �  �        }|dk    rd}t)          d|dz  �  �        }nr|d	k    rd}t)          d|dz  �  �        }nV|dk    r#d}t)          dt+          |�  �        dz  �  �        }n-|dk    r#d}t)          dt+          |�  �        dz  �  �        }nd}d	}||z  dz  }t-          dt)          d |�  �        �  �        }|||||�||t          d!� |�                    �   �         D �   �         �  �        t/          j        �   �         �                    �   �         t          |�  �        | �                    |�  �        d"�}t          d#|� d$|d%�d&��  �         t          d'|d�d(|d���  �         | �                    |�                    �   �         ||�  �         |S # t          $ rI}t          d)|� ��  �         ddl}|�                    �   �          | �                    |�  �        cY d}~S d}~ww xY w)*u�   
        ULTIMATIVE INTELLIGENTE VORHERSAGE V2.0
        =======================================
        Drastisch verbesserte Ensemble-Vorhersage für 70%+ Genauigkeit
        u9   Keine ML-Modelle verfügbar - verwende technische Analyseu!   Führe ultimative Vorhersage mit z Modellen durch...r�   r�   rm   r   rK  r�   rJ  �ffffff�?rB  z
: Prediction rN  z
, Confidence zVorhersage-Fehler bei rO  Nu2   Keine gültigen ML-Vorhersagen - verwende Fallbackc              3   �:   �K  � | ]\  }}|�|         z  �z  V � �d S )N� )�.0rv  �pred�final_scores�total_weights      ��r9   �	<genexpr>zMUltimateBitcoinTradingSystemV3.make_intelligent_prediction.<locals>.<genexpr>�  sR   �� � � � *O� *O�,6�D�$� +/��d�1C�*C�l�*R� *O� *O� *O� *O� *O� *Or;   c                 �   � g | ]
}|d k    �|��S �r   r�  )r�  �confs     r9   �
<listcomp>zNUltimateBitcoinTradingSystemV3.make_intelligent_prediction.<locals>.<listcomp>�  s   � �#X�#X�#X�T�T�TW�Z�Z�D�Z�Z�Zr;   �      �?�KAUFENg       @r  �      ���	VERKAUFEN�      ��HALTENg333333�?r�   �ffffff�?c                 �   � g | ]
}|d k    �|��S r�  r�  )r�  �cs     r9   r�  zNUltimateBitcoinTradingSystemV3.make_intelligent_prediction.<locals>.<listcomp>&  s   � �(T�(T�(T�q�A�PS�G�G��G�G�Gr;   )r�   �
confidence�
ml_prediction�individual_predictions�model_confidencesr�  �ensemble_confidence�signal_strength�best_model_countrU   �models_used�prediction_classzUltimative Vorhersage: �
 (Konfidenz: r$   rP  zML-Prediction: u   , Signal-Stärke: z#FEHLER bei ultimativer Vorhersage: )r+   r6   �_fallback_technical_predictionrL   r  r�   r�   rR  r�   r-   rU  r�   rY  r,   rK   rN   �sum�valuesr�   �listr�   r�   r�   r   r\   r]   �_get_prediction_class�_store_prediction_for_learningr�   r�   )r8   r�   r�   r  rg  �features_scaled�predictions�confidencesrv  rw  r�  r@   rK  rJ  r�  rR   �ensemble_predictionr�  �best_confidencesr�   r�  �final_confidence�
predictionr�   r�  r�  s                           @@r9   �make_intelligent_predictionz:UltimateBitcoinTradingSystemV3.make_intelligent_prediction�  s�  ��� �s	C��>� 
G��Q�R�R�R��:�:�:�F�F�F��]�c�$�.�6I�6I�]�]�]�^�^�^� �.�.�r�:�>�>�H� !#�	�(�*:�*:�*<�*<�s�e� D� D�� 1� 9� 9�!�R� @� @�� #�1�;�;�<M�N�N�O� �K��K��L�#�~�3�3�5�5� 
@� 
@���e�@� �=�=��9�9�!�<�D�(,�K��%� #'�"8�"<�"<�T�2�"F�"F�K�"-�/�/�-��"E�"E�K�(3���8K�S�(Q�(Q�%� #.��"3�6G�#�6M�"M�J�(2�K��%�)4�L��&��T�W�W��W�W�W�z�W�W�W�X�X�X�X�� � @� @� @��>�4�>�>�1�>�>�?�?�?�?�?�?�?�?�����@���� � 
G��J�K�K�K��:�:�:�F�F�F� �|�2�2�4�4�5�5�L��q� � �&(�g�d�;�3E�3E�3G�3G�.H�.H�&I�&I�#�&)�#�#� '*� *O� *O� *O� *O� *O�:E�:K�:K�:M�:M�*O� *O� *O� 'O� 'O�#� $Y�#X�[�5G�5G�5I�5I�#X�#X�#X� �#� N�*,�'�2B�*C�*C�'�'�*,�'�$�{�7I�7I�7K�7K�2L�2L�*M�*M�'� #�c�)�)�!��"%�c�+>��+D�"E�"E���$��+�+�!��"%�c�+>��+D�"E�"E���$��,�,�$��"%�c�3�/B�+C�+C�c�+I�"J�"J���$��,�,�$��"%�c�3�/B�+C�+C�c�+I�"J�"J���!��"%��  3�_�D�s�J��"�3��D�2B�(C�(C�D�D�� !�.�!4�*5�%0� ,�':�#2�$'�(T�(T�K�4F�4F�4H�4H�(T�(T�(T�$U�$U�%�\�^�^�5�5�7�7�"�;�/�/�$(�$>�$>�?R�$S�$S�
� 
�J� 
�X�F�X�X�AQ�X�X�X�X�Y�Y�Y��d�$7�d�d�d��d�d�d�e�e�e� 
�/�/�0A�0I�0I�0K�0K�M`�bl�m�m�m����� 	C� 	C� 	C��;��;�;�<�<�<��������!�!�!��6�6�z�B�B�B�B�B�B�B�B�����		C���sU   �*Q
 �B7Q
 �'BE=�<Q
 �=
F&�F!�Q
 �!F&�&(Q
 �I:Q
 �

R�>R�R�Rr�  c                 �F   � |dk    rdS |dk    rdS |dk    rdS |dk    rdS d	S )
z2Konvertiere numerische Vorhersage zu Klassen-Labelr�  �STARKER_ANSTIEGr�   �SCHWACHER_ANSTIEGr�  u   STARKER_RÜCKGANGr�  u   SCHWACHER_RÜCKGANGu
   SEITWÄRTSr�  )r8   r�  s     r9   r�  z4UltimateBitcoinTradingSystemV3._get_prediction_class:  sK   � �����$�$�
�3�
�
�&�&�
�4�
�
�&�&�
�4�
�
�(�(��<r;   c                 �  � 	 |�                     dd�  �        }|�                     dd�  �        }|�                     dd�  �        }g }|dk     r|�                    d�  �         n|d	k    r|�                    d
�  �         |dk    r|�                    d�  �         n|dk     r|�                    d�  �         |d
k     r|�                    d�  �         n|dk    r|�                    d�  �         |sd}nt          j        |�  �        }|dk    rd}dt	          |�  �        d
z  z   }n"|dk     rd}dt	          |�  �        d
z  z   }nd}d}|t          d|�  �        dd|t
          j        �   �         �                    �   �         d�S # t          $ rT}	t          d|	� ��  �         dddt          |	�  �        t
          j        �   �         �                    �   �         d�cY d}	~	S d}	~	ww xY w)u4   Fallback technische Analyse wenn ML nicht verfügbarr�   r�   r�   r   r�   r�   �   r  �F   g�������r   g333333�r  r�  gffffff�rB  r�  g333333ӿr�  r�  T)r�   r�  r�  �
fallback_mode�weighted_signalrU   z FEHLER bei Fallback-Vorhersage: )r�   r�  r�  �errorrU   N)rK   r�   r�   r�   r�   r�   r   r\   r]   rN   r6   �str)
r8   r�   r�   �	macd_histr�   �signalsr�  r�   r�  rR   s
             r9   r�  z=UltimateBitcoinTradingSystemV3._fallback_technical_predictionG  s9  � �8	��.�.��2�.�.�C�"���'7��;�;�I�$�.�.���<�<�K� �G��R�x�x����s�#�#�#�#��r������t�$�$�$��1�}�}����s�#�#�#�#��Q������t�$�$�$��S� � ����s�#�#�#�#��s�"�"����t�$�$�$�� 
3�"#���"$�'�'�"2�"2�� ��$�$�!�� �3��#7�#7�#�#=�=�
�
� �4�'�'�$�� �3��#7�#7�#�#=�=�
�
�!�� �
� !�!�#�z�2�2�!$�!%�#2�%�\�^�^�5�5�7�7�
� � 
�� � 	� 	� 	��8�Q�8�8�9�9�9�"�!�!$��Q���%�\�^�^�5�5�7�7�� � 
� 
� 
� 
� 
� 
�����	���s   �E;E> �>
G�A	G�G�Gr  c                 �  � 	 |�                     �   �         ||t          j        �   �         �                    �   �         | j        �| j        d         j        d         ndd�}| j        �                    |�  �         t          | j        �  �        dk    r| j        dd�         | _        dS dS # t          $ r}t          d|� ��  �         Y d}~dS d}~ww xY w)	u1   Speichere Vorhersage für kontinuierliches LernenNrl   rm   r   )r  r�  r�   rU   rk   r�   rW   u#   FEHLER beim Speichern für Lernen: )�tolistr   r\   r]   r1   r   r.   r�   rL   rN   r6   )r8   r  r�  r�   rh  rR   s         r9   r�  z=UltimateBitcoinTradingSystemV3._store_prediction_for_learning�  s�   � �	=� %�O�O�-�-�(�(�%�\�^�^�5�5�7�7�?C�?O�?[��)�'�2�7��;�;�ab�� �F� 
��%�%�f�-�-�-� �4�%�&�&��-�-�%)�%7����%?��"�"�"� .�-�� � 	=� 	=� 	=��;��;�;�<�<�<�<�<�<�<�<�<�����	=���s   �B"B( �(
C�2C
�
Cc                 ��  � 	 t          d�  �         t          j        �   �         }| �                    �   �         }|j        rt	          d�  �        �|d         j        d         }| �                    |�  �        }t          | j        �  �        dk    pt          |�  �        dk    }t          |�  �        dk    r8| �	                    ||��  �        }|rt          d	�  �         nt          d
�  �         | �
                    ||�  �        }| �                    ||�  �        }| �                    |�  �         t          j        �   �         |z
  }	t          j        �   �         �                    �   �         ||d         |d         |�                    d
d�  �        |||| j        |	t          |�  �        t          | j        �  �        | j        d�
}
|
| _        t          d|	d�d��  �         t          d|d         � d|d         d�d��  �         t          d| j        d         d���  �         |
S # t          $ rS}t          d|� ��  �         t)          |�  �        t          j        �   �         �                    �   �         ddd�cY d}~S d}~ww xY w)z�
        ULTIMATE MARKTANALYSE V3.0
        ==========================
        Komplett optimierte Analyse mit selbstlernendem ML-System
        z$Starte Ultimate Marktanalyse V3.0...u   Keine Marktdaten verfügbarrl   rm   r   r  r�   )r  u1   ✅ ML-Modelle erfolgreich trainiert/aktualisiertu5   ⚠️ ML-Training fehlgeschlagen - verwende Fallbackr�   r�  r�  r�   )
rU   r  r�   r�  r�  r2   �prediction_details�risk_metricsr5   �
analysis_time�data_points�models_availablerT   z"Ultimate Analyse abgeschlossen in �.2f�szSignal: r�  r$   rP  zAktuelle Genauigkeit: r    zFEHLER bei Ultimate Analyse: r�  )r�  rU   r�   r�  N)r6   �timer�   r{   rN   r   r�   rL   r+   r  r�  �#calculate_optimized_risk_management�_update_session_statsr   r\   r]   rK   r5   r%   r3   r�  )r8   �
start_timer�   r  r�   �force_training�
ml_trainedr�  r�  r�  �resultrR   s               r9   �run_ultimate_analysisz4UltimateBitcoinTradingSystemV3.run_ultimate_analysis�  s�  � �E	��8�9�9�9�����J� �/�/�1�1�B��x� 
?�� =�>�>�>��w�K�,�R�0�M� �F�F�r�J�J�J� !���0�0�A�5�F��R���B��N��2�w�w�"�}�}�!�<�<�R�~�<�^�^�
�� S��M�N�N�N�N��Q�R�R�R� �9�9�"�j�I�I�J�  �C�C�J�P]�^�^�L� 
�&�&�z�2�2�2� !�I�K�K�*�4�M� &�\�^�^�5�5�7�7�!.�$�X�.�(��6�!+�����!E�!E�(2�&0� ,�!%�!3�!.�"�2�w�w�$'���$7�$7��<�� �F�" '-�D�#��K�}�K�K�K�K�L�L�L��_�Z��1�_�_�
�<�@X�_�_�_�_�`�`�`��W�4�+=�>P�+Q�W�W�W�X�X�X��M��� 	� 	� 	��5�!�5�5�6�6�6��Q���%�\�^�^�5�5�7�7�"�!�	� � 
� 
� 
� 
� 
� 
�����	���s   �HH �
I:�'AI5�/I:�5I:r  c           
      �  � 	 |�                     dd�  �        }|�                     dd�  �        }d}|dz  }t          d||z  �  �        }| j        �                     dd	�  �        }d
}	|dz  }
t          dt          d
|	|
z   �  �        �  �        }|dz  }t          d|dz  �  �        }
|
|z  }|}|
}|}||z  d|z
  |z  z
  |z  }t          dt          d|�  �        �  �        }|dk    r|d|z
  z  }|d|
z   z  }n-|dk    r|d|z   z  }|d|
z
  z  }n|d|dz  z
  z  }|d|
dz  z   z  }|||
||||||
d�	S # t          $ r}t          d|� ��  �         i cY d}~S d}~ww xY w)z$Berechne optimiertes Risk Managementr�  r�   r�   r�  r�   r�  g      �?r�   r�   g���Q��?rZ   r8  r   rn   g{�G�z�?r�   r   r�  r�  )	�
position_size�stop_loss_percent�take_profit_percent�stop_loss_price�take_profit_price�risk_reward_ratio�kelly_fraction�confidence_multiplier�volatility_adjustmentzFEHLER bei Risk Management: N)rK   r�   r2   r�   rN   r6   )r8   r�  r  r�  r�   �
base_positionr�  �max_position�
volatility�	base_stop�volatility_stop�dynamic_stop�
expected_move�dynamic_take_profit�risk_reward�win_prob�avg_win�avg_lossr�  r�  r�  rR   s                         r9   r�  zBUltimateBitcoinTradingSystemV3.calculate_optimized_risk_management�  s
  � �6	�#����c�:�:�J��^�^�H�h�7�7�F�  �M�$.��$4�!��t�]�5J�%J�K�K�L� �2�6�6���M�M�J��I�(�1�n�O��t�S��	�O�0K�%L�%L�M�M�L� '��-�M�"%�d�M�A�,=�">�">�� .��<�K� "�H�)�G�#�H�&��0�A��L�H�3L�L�PW�W�N� ��C��n�$=�$=�>�>�N� ��!�!�"/�1�|�3C�"D��$1�Q�9L�5L�$M�!�!��;�&�&�"/�1�|�3C�"D��$1�Q�9L�5L�$M�!�!�"/�1�|�a�7G�3G�"H��$1�Q�9L�q�9P�5P�$Q�!� ".�%1�':�#2�%6�%0�"0�)>�)8�
� 
� 

�� � 	� 	� 	��4��4�4�5�5�5��I�I�I�I�I�I�����	���s   �D*D- �-
E�7E�
E�Ec                 �  � 	 | j         dxx         dz
  cc<   |�                    dd�  �        }t          d|t          j        dd�  �        z   �  �        }|dk    r| j         d	xx         dz
  cc<   | j         d         d
k    r#| j         d	         | j         d         z  | j         d<   | j         d         | j         d         k    r| j         d         | j         d<   dS dS # t
          $ r}t
          d
|� ��  �         Y d}~dS d}~ww xY w)zUpdate Session-Statistikenr   r�   r�  r�   r�  g��������r�   r   r   r   r    r!   z!FEHLER bei Session-Stats Update: N)r5   rK   r�   �random�uniformrN   r6   )r8   r�  r�  �simulated_accuracyrR   s        r9   r�  z4UltimateBitcoinTradingSystemV3._update_session_stats   sV  � �	;���2�3�3�3�q�8�3�3�3� $����c�:�:�J�!$�T�:���t�S�8Q�8Q�+Q�!R�!R��!�C�'�'��"�#8�9�9�9�Q�>�9�9�9� �!�"5�6��:�:��&�'<�=��&�':�;�<� �"�#5�6� �!�"4�5��8J�?�8[�[�[�6:�6H�I[�6\��"�?�3�3�3� \�[�� � 	;� 	;� 	;��9�a�9�9�:�:�:�:�:�:�:�:�:�����	;���s   �CC �
C>�!C9�9C>N)F)�__name__�
__module__�__qualname__�__doc__r:   r7   r`   r�   r�   r�   r   r�   r�   �ndarrayr  r�   r  r�  r~   r�  r�  r�  r�  r�  r�  r�  r�  r;   r9   r   r   0   s  � � � � � �� �)%� )%� )%�V@� @� @�2D� D� D�B5"�2�<� 5"� 5"� 5"� 5"�nW�2�<� W�D� W� W� W� W�rn%�R�\� n%�t� n%��
� n%� n%� n%� n%�`a� a�R�\� a�$� a�[_� a� a� a� a�F	yC�b�l� yC�� yC�QU� yC� yC� yC� yC�v ��  �#�  �  �  �  �:�� :�$� :� :� :� :�x=�r�z� =�u� =�bf� =� =� =� =�,K�t� K� K� K� K�Z8�d� 8�SX� 8�]a� 8� 8� 8� 8�t;�� ;� ;� ;� ;� ;� ;r;   r   c                  �  � t          d�  �         t          d�  �         	 t          �   �         } | �                    �   �         }d|v rt          d|d         � ��  �         dS t          d�  �         t          d�  �         t          d�  �         t          d	�  �         t          d
|d         d���  �         t          d
|d         � ��  �         t          d|d         d�d��  �         t          d�  �         t          d|d         � ��  �         t          d|d         d���  �         t          d|d         d���  �         t          d|d         � ��  �         t          d�  �         |d         }t          d |d!         d���  �         t          d"|d#         d���  �         t          d$|d%         � ��  �         t          d&|d'         � ��  �         t          d(|d)         � ��  �         t          d*|d+         � ��  �         t          d,�  �         |d-         }|r�t          d.|d/         d���  �         t          d0|d1         d�d2|d3         d�d4��  �         t          d5|d6         d�d2|d7         d�d4��  �         t          d8|d9         d���  �         t          d:|d;         d���  �         t          d<�  �         |d=         }|r�t          d>|�                    d?d@�  �        dA���  �         t          dB|�                    dCd@�  �        d���  �         t          dD|�                    dEd@�  �        d���  �         t          dF|�                    dGd@�  �        d���  �         t          dH|�                    dId@�  �        d���  �         t          dJ�  �         |S # t          $ r5}t          dK|� ��  �         d@dl}|�                    �   �          Y d}~dS d}~ww xY w)Lz4HAUPTFUNKTION - Ultimate Bitcoin Trading System V3.0z.STARTE ULTIMATE BITCOIN TRADING SYSTEM V3.0...u?   KOMPLETT ÜBERARBEITET UND OPTIMIERT FÜR MAXIMALE GENAUIGKEIT!r�  zFEHLER: NzQ
================================================================================z1ULTIMATE BITCOIN TRADING SYSTEM V3.0 - ERGEBNISSEzP================================================================================z
MARKTDATEN:z   Bitcoin-Preis: $r  z,.2fz   Datenpunkte: r�  z   Analysezeit: r�  r�  r�  z
ML-VORHERSAGE:z   Signal: r�   z   Konfidenz: r�  r$   z   ML-Prediction: r�  rN  u      Verfügbare Modelle: r�  z
SESSION-STATISTIKEN:r5   z   Aktuelle Genauigkeit: r    z   Beste Genauigkeit: r!   z   Gesamte Vorhersagen: r   z   Korrekte Vorhersagen: r   z   Training-Zyklen: r#   z   Model-Verbesserungen: r"   z
RISK MANAGEMENT:r�  z
   Position: r�  z   Stop Loss: r�  z ($r�  rP  z   Take Profit: r�  r�  z   Risk/Reward: r�  z   Kelly Criterion: r�  z
TECHNISCHE INDIKATOREN:r2   z
   RSI (14): r�   r   z.1fz	   MACD: r�   z   BB Position: r�   u      Volatilität: r�   z   Volume Ratio: r�   zD
ULTIMATE BITCOIN TRADING SYSTEM V3.0 - SELBSTLERNEND UND OPTIMIERT!z FEHLER im Ultimate System V3.0: )r6   r   r�  rK   rN   r�   r�   )�systemr�  �stats�riskr�   rR   r�   s          r9   �&run_ultimate_bitcoin_trading_system_v3r�  :  s�  � � 
�
:�;�;�;�	�
K�L�L�L�>�/�1�1�� �-�-�/�/���f����.�V�G�_�.�.�/�/�/��4� 	�m����
�A�B�B�B�
�f�
�
�
�
�����
�B�F�?�$;�B�B�B�C�C�C�
�8��
�!6�8�8�9�9�9�
�?���!8�?�?�?�?�@�@�@�
�!�"�"�"�
�.�F�8�,�.�.�/�/�/�
�9�v�l�3�9�9�9�:�:�:�
�@�6�/�#:�@�@�@�A�A�A�
�E��0B�)C�E�E�F�F�F�
�'�(�(�(���'��
�I�%�0B�*C�I�I�I�J�J�J�
�C�u�_�'=�C�C�C�D�D�D�
�E��/B�)C�E�E�F�F�F�
�H�%�0E�*F�H�H�I�I�I�
�?�U�+<�%=�?�?�@�@�@�
�G�%�0D�*E�G�G�H�H�H�
�#�$�$�$��n�%��� 	G��=�$��"7�=�=�=�>�>�>��d�4�(;�#<�d�d�d�T�J[�E\�d�d�d�d�e�e�e��j�T�*?�%@�j�j�j��Na�Ib�j�j�j�j�k�k�k��D�T�*=�%>�D�D�D�E�E�E��E��.>�)?�E�E�E�F�F�F�
�*�+�+�+��2�3�
�� 	O��C�*�.�.��1�"=�"=�C�C�C�D�D�D��=�j�n�n�V�Q�7�7�=�=�=�>�>�>��K�Z�^�^�M�1�%E�%E�K�K�K�L�L�L��N�j�n�n�_�a�&H�&H�N�N�N�O�O�O��M�j�n�n�^�Q�&G�&G�M�M�M�N�N�N�
�V�W�W�W��
��� � � �
�4��4�4�5�5�5������������t�t�t�t�t�����	���s   �>N � L9N �
O�$*O�O�__main__)8r�  �pandasr�   �numpyr�   r|   �yfinancerx   r   r   �warningsr�  rM   rE   rI   �	threading�typingr   r   r   r   r�   r�  �sklearn.ensembler	   r
   �sklearn.preprocessingr   r   �sklearn.model_selectionr
   r   �sklearn.metricsr   r   r   �xgboostrV  �sklearn.neural_networkr   �tkinter�tkr   r   �matplotlib.pyplot�pyplot�plt�!matplotlib.backends.backend_tkaggr   �matplotlib.dates�dates�mdates�filterwarningsr   r�  r�  r�  r;   r9   �<module>r     s)  ��� � � � � � � � � � ���� � � � � (� (� (� (� (� (� (� (� ���� ���� ���� 	�	�	�	� 
�
�
�
� � � � � .� .� .� .� .� .� .� .� .� .� .� .� ���� 
�
�
�
� N� M� M� M� M� M� M� M� >� >� >� >� >� >� >� >� E� E� E� E� E� E� E� E� H� H� H� H� H� H� H� H� H� H� � � � � /� /� /� /� /� /� � � � � #� #� #� #� #� #� #� #� � � � � � � ?� ?� ?� ?� ?� ?� !� !� !� !� !� !� �� �� !� !� !�H;� H;� H;� H;� H;� H;� H;� H;�T D� D� D�L �z���*�*�,�,�,�,�,� �r;   