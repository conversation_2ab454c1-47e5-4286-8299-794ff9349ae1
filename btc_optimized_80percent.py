#!/usr/bin/env python3
"""
OPTIMIZED BITCOIN PREDICTION MODEL - 80% TARGET
Speziell optimiert für 80%+ Genauigkeit mit robuster Datenaufteilung
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.optimizers import <PERSON>
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from datetime import datetime
import time
import os
import warnings
warnings.filterwarnings('ignore')

# CPU-Optimierung
print(f"🚀 Aktiviere alle {os.cpu_count()} CPU-Kerne...")
tf.config.threading.set_intra_op_parallelism_threads(0)
tf.config.threading.set_inter_op_parallelism_threads(0)

# OPTIMIERTE Konfiguration für 80% Genauigkeit
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.8,         # Ausgewogene Aufteilung
    'look_back': 30,            # Optimale Sequenzlänge
    'future_steps': 12,         # 12h Prognose
    'batch_size': 64,           # Optimale Batch-Größe
    'epochs': 100,              # Ausreichend Epochen
    'patience': 15,             # Geduld für Konvergenz
    'learning_rate': 0.001,     # Optimale Learning Rate
    'dropout_rate': 0.2,        # Regularisierung
    'n_features_select': 15     # Top Features auswählen
}

def smart_feature_engineering(df):
    """Intelligente Feature-Auswahl für 80% Genauigkeit"""
    print("🧠 Intelligente Feature-Engineering...")
    
    # Basis OHLCV
    features = df[['open', 'high', 'low', 'close', 'volume']].copy()
    
    # Top Moving Averages
    features['ema_12'] = df['close'].ewm(span=12).mean()
    features['ema_26'] = df['close'].ewm(span=26).mean()
    features['sma_20'] = df['close'].rolling(20).mean()
    
    # MACD (sehr wichtig für Trends)
    features['macd'] = features['ema_12'] - features['ema_26']
    features['macd_signal'] = features['macd'].ewm(span=9).mean()
    
    # RSI (Momentum)
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    features['rsi'] = 100 - (100 / (1 + rs))
    
    # Bollinger Bands (Volatilität)
    sma_20 = df['close'].rolling(20).mean()
    std_20 = df['close'].rolling(20).std()
    features['bb_upper'] = sma_20 + (std_20 * 2)
    features['bb_lower'] = sma_20 - (std_20 * 2)
    features['bb_position'] = (df['close'] - features['bb_lower']) / (features['bb_upper'] - features['bb_lower'])
    
    # Volumen-Indikatoren
    features['volume_sma'] = df['volume'].rolling(20).mean()
    features['volume_ratio'] = df['volume'] / features['volume_sma']
    
    # Preis-Momentum
    features['price_change'] = df['close'].pct_change()
    features['price_momentum'] = df['close'] - df['close'].shift(10)
    
    # Volatilität
    features['volatility'] = df['close'].pct_change().rolling(10).std()
    
    # Trend-Stärke
    features['trend'] = (df['close'] - features['sma_20']) / features['sma_20']
    
    return features.dropna()

def load_and_optimize_data():
    """Optimierte Datenvorbereitung"""
    print("📊 Lade und optimiere Daten...")
    
    df = pd.read_csv(CONFIG['data_file'])
    df['time'] = pd.to_datetime(df['time'])
    df.set_index('time', inplace=True)
    
    print(f"✅ {len(df)} Datenpunkte geladen")
    print(f"   Zeitraum: {df.index[0]} bis {df.index[-1]}")
    print(f"   Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
    
    # Feature Engineering
    df_features = smart_feature_engineering(df)
    
    print(f"📈 {len(df_features.columns)} Features erstellt")
    
    return df_features

def create_optimized_sequences(data, look_back):
    """Optimierte Sequenz-Erstellung"""
    X, y = [], []
    
    for i in range(len(data) - look_back):
        X.append(data[i:i + look_back])
        y.append(data[i + look_back, 3])  # close price index
    
    return np.array(X, dtype=np.float32), np.array(y, dtype=np.float32)

def build_optimized_model(input_shape):
    """Für 80% Genauigkeit optimiertes Modell"""
    model = Sequential([
        # Erste LSTM-Schicht
        LSTM(128, return_sequences=True, dropout=CONFIG['dropout_rate'], 
             recurrent_dropout=CONFIG['dropout_rate'], input_shape=input_shape),
        BatchNormalization(),
        
        # Zweite LSTM-Schicht
        LSTM(64, return_sequences=True, dropout=CONFIG['dropout_rate'],
             recurrent_dropout=CONFIG['dropout_rate']),
        BatchNormalization(),
        
        # Dritte LSTM-Schicht
        LSTM(32, return_sequences=False, dropout=CONFIG['dropout_rate']),
        BatchNormalization(),
        
        # Dense Layers
        Dense(64, activation='relu'),
        Dropout(CONFIG['dropout_rate']),
        Dense(32, activation='relu'),
        Dropout(CONFIG['dropout_rate']),
        Dense(16, activation='relu'),
        Dense(1, activation='linear')
    ])
    
    # Optimierter Optimizer
    optimizer = Adam(
        learning_rate=CONFIG['learning_rate'],
        beta_1=0.9,
        beta_2=0.999,
        epsilon=1e-8
    )
    
    model.compile(
        optimizer=optimizer,
        loss='mse',
        metrics=['mae']
    )
    
    return model

def calculate_comprehensive_metrics(y_true, y_pred):
    """Umfassende Metriken-Berechnung"""
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    # MAPE (Mean Absolute Percentage Error)
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    
    # Richtungsgenauigkeit
    true_direction = np.diff(y_true.flatten()) > 0
    pred_direction = np.diff(y_pred.flatten()) > 0
    direction_acc = np.mean(true_direction == pred_direction) * 100
    
    # Erklärte Varianz
    explained_var = 1 - (np.var(y_true - y_pred) / np.var(y_true))
    
    return {
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'mape': mape,
        'direction_accuracy': direction_acc,
        'explained_variance': explained_var
    }

def plot_optimized_results(df, train_size, y_test, y_pred, metrics, training_time):
    """Optimierte Visualisierung"""
    plt.figure(figsize=(16, 12))
    
    # Hauptchart
    plt.subplot(2, 3, 1)
    dates = df.index
    plt.plot(dates[:train_size], df['close'].iloc[:train_size], 'b-', label='Training', alpha=0.7)
    plt.plot(dates[train_size:train_size+len(y_test)], y_test, 'g-', label='Actual', linewidth=2)
    plt.plot(dates[train_size:train_size+len(y_pred)], y_pred, 'r--', label='Predicted', linewidth=2)
    
    plt.title('Bitcoin Price Prediction - 80% TARGET MODEL', fontsize=14, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Metriken
    plt.subplot(2, 3, 2)
    color = 'lightgreen' if metrics['r2'] >= 0.8 else 'lightyellow' if metrics['r2'] >= 0.6 else 'lightcoral'
    
    metrics_text = f"""
    80% TARGET MODEL
    
    R²: {metrics['r2']:.4f} ({metrics['r2']*100:.1f}%)
    RMSE: ${metrics['rmse']:.2f}
    MAE: ${metrics['mae']:.2f}
    MAPE: {metrics['mape']:.2f}%
    Direction Acc: {metrics['direction_accuracy']:.1f}%
    
    Training: {training_time:.1f}s
    Status: {'✅ TARGET REACHED!' if metrics['r2'] >= 0.8 else '🔥 CLOSE!' if metrics['r2'] >= 0.7 else '⚠️ NEEDS WORK'}
    """
    
    plt.text(0.1, 0.5, metrics_text, fontsize=11, verticalalignment='center',
             bbox=dict(boxstyle='round', facecolor=color, alpha=0.8))
    plt.axis('off')
    
    # Scatter Plot
    plt.subplot(2, 3, 3)
    plt.scatter(y_test, y_pred, alpha=0.6, s=30)
    min_val, max_val = min(y_test.min(), y_pred.min()), max(y_test.max(), y_pred.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
    plt.title('Predicted vs Actual')
    plt.xlabel('Actual Values')
    plt.ylabel('Predicted Values')
    plt.grid(True, alpha=0.3)
    
    # Residuals
    plt.subplot(2, 3, 4)
    residuals = y_test - y_pred
    plt.scatter(y_pred, residuals, alpha=0.6, s=20)
    plt.axhline(y=0, color='red', linestyle='--')
    plt.title('Residuals')
    plt.xlabel('Predicted Values')
    plt.ylabel('Residuals')
    plt.grid(True, alpha=0.3)
    
    # Error Distribution
    plt.subplot(2, 3, 5)
    plt.hist(residuals, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('Error Distribution')
    plt.xlabel('Prediction Error')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)
    
    # Performance Over Time
    plt.subplot(2, 3, 6)
    error_pct = np.abs((y_test - y_pred) / y_test) * 100
    plt.plot(error_pct, 'o-', alpha=0.7, markersize=4)
    plt.axhline(y=error_pct.mean(), color='red', linestyle='--', label=f'Mean: {error_pct.mean():.1f}%')
    plt.title('Prediction Error Over Time')
    plt.xlabel('Time Steps')
    plt.ylabel('Error %')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def main():
    """MAIN - 80% Genauigkeits-Ziel"""
    print("🚀 OPTIMIZED BITCOIN PREDICTION MODEL - 80% TARGET")
    print("=" * 60)
    print(f"🎯 Ziel: 80%+ Genauigkeit (R² ≥ 0.80)")
    
    start_time = time.time()
    
    try:
        # 1. Daten optimieren
        df = load_and_optimize_data()
        
        # 2. Skalierung
        print("🔄 Optimiere Skalierung...")
        scaler = MinMaxScaler(feature_range=(0, 1))
        scaled_data = scaler.fit_transform(df.values)
        
        # 3. Sequenzen
        print("📦 Erstelle optimierte Sequenzen...")
        X, y = create_optimized_sequences(scaled_data, CONFIG['look_back'])
        
        # Robuste Train-Test Aufteilung
        train_size = int(len(X) * CONFIG['train_split'])
        X_train, X_test = X[:train_size], X[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        print(f"✅ Train: {X_train.shape}, Test: {X_test.shape}")
        
        # 4. Optimiertes Modell
        print("🏗️  Erstelle 80%-Ziel-Modell...")
        model = build_optimized_model((X_train.shape[1], X_train.shape[2]))
        print(f"📋 Parameter: {model.count_params():,}")
        
        # 5. Optimiertes Training
        print("🎯 Starte optimiertes Training...")
        train_start = time.time()
        
        callbacks = [
            EarlyStopping(
                patience=CONFIG['patience'], 
                restore_best_weights=True, 
                verbose=1,
                monitor='val_loss',
                min_delta=0.0001
            ),
            ReduceLROnPlateau(
                factor=0.5, 
                patience=8, 
                min_lr=1e-7, 
                verbose=1,
                monitor='val_loss'
            )
        ]
        
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            callbacks=callbacks,
            verbose=1
        )
        
        training_time = time.time() - train_start
        print(f"⚡ Training abgeschlossen in {training_time:.1f} Sekunden!")
        
        # 6. Evaluation
        print("📊 Umfassende Evaluation...")
        y_pred = model.predict(X_test, verbose=0)
        
        # Skalierung rückgängig machen
        dummy_test = np.zeros((len(y_test), scaler.n_features_in_))
        dummy_test[:, 3] = y_test  # close price
        y_test_orig = scaler.inverse_transform(dummy_test)[:, 3]
        
        dummy_pred = np.zeros((len(y_pred), scaler.n_features_in_))
        dummy_pred[:, 3] = y_pred.flatten()
        y_pred_orig = scaler.inverse_transform(dummy_pred)[:, 3]
        
        # Umfassende Metriken
        metrics = calculate_comprehensive_metrics(y_test_orig, y_pred_orig)
        
        print(f"\n📊 FINALE PERFORMANCE:")
        print(f"R²: {metrics['r2']:.4f} ({metrics['r2']*100:.1f}%)")
        print(f"RMSE: ${metrics['rmse']:.2f}")
        print(f"MAE: ${metrics['mae']:.2f}")
        print(f"MAPE: {metrics['mape']:.2f}%")
        print(f"Direction Accuracy: {metrics['direction_accuracy']:.1f}%")
        print(f"Explained Variance: {metrics['explained_variance']:.4f}")
        
        # Ziel-Check
        if metrics['r2'] >= 0.80:
            print(f"\n🎉 🎉 🎉 ZIEL ERREICHT! 🎉 🎉 🎉")
            print(f"Genauigkeit: {metrics['r2']*100:.1f}% ≥ 80%")
        elif metrics['r2'] >= 0.75:
            print(f"\n🔥 SEHR NAH AM ZIEL!")
            print(f"Genauigkeit: {metrics['r2']*100:.1f}% (nur {(0.8-metrics['r2'])*100:.1f}% fehlen)")
        elif metrics['r2'] >= 0.60:
            print(f"\n💪 GUTE BASIS!")
            print(f"Genauigkeit: {metrics['r2']*100:.1f}% (solide Grundlage)")
        else:
            print(f"\n⚠️  VERBESSERUNG NÖTIG")
            print(f"Genauigkeit: {metrics['r2']*100:.1f}% (mehr Optimierung erforderlich)")
        
        # 7. Visualisierung
        print("📈 Erstelle optimierte Visualisierung...")
        plot_optimized_results(df, train_size, y_test_orig, y_pred_orig, metrics, training_time)
        
        total_time = time.time() - start_time
        print(f"\n✅ 80% TARGET MODEL FERTIG!")
        print(f"⚡ Gesamtzeit: {total_time:.1f} Sekunden")
        print(f"🎯 Finale Genauigkeit: {metrics['r2']*100:.1f}%")
        
    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
