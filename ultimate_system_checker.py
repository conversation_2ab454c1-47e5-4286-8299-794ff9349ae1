#!/usr/bin/env python3
"""
🔧 ULTIMATE SYSTEM CHECKER & OPTIMIZER
=====================================
Umfassende System-Überprüfung und Optimierung für das Trading Dashboard
"""

import sys
import os
import time
import subprocess
from pathlib import Path

def check_python_version():
    """🐍 Python-Version prüfen"""
    print("🐍 PYTHON-VERSION CHECK")
    print("-" * 25)

    version = sys.version_info
    print(f"Python Version: {version.major}.{version.minor}.{version.micro}")

    if version < (3, 7):
        print("❌ FEHLER: Python 3.7+ erforderlich!")
        return False
    elif version < (3, 9):
        print("⚠️ WARNUNG: Python 3.9+ empfohlen")
        return True
    else:
        print("✅ Python-Version optimal")
        return True

def check_dependencies_detailed():
    """🔍 Detaillierte Abhängigkeitsprüfung"""
    print("\n🔍 ABHÄNGIGKEITEN CHECK")
    print("-" * 25)

    dependencies = {
        'tkinter': 'GUI-Framework',
        'numpy': 'Numerische Berechnungen',
        'requests': 'HTTP-Requests für APIs',
        'matplotlib': 'Chart-Visualisierungen'
    }

    optional = {
        'pandas': 'Datenanalyse (optional)',
        'sklearn': 'Machine Learning (optional)',
        'psutil': 'System-Monitoring (optional)'
    }

    missing_required = []

    for module, desc in dependencies.items():
        try:
            __import__(module)
            print(f"✅ {module:<12} - {desc}")
        except ImportError:
            print(f"❌ {module:<12} - {desc} (FEHLT)")
            missing_required.append(module)

    print("\nOptionale Module:")
    for module, desc in optional.items():
        try:
            __import__(module)
            print(f"✅ {module:<12} - {desc}")
        except ImportError:
            print(f"⚠️ {module:<12} - {desc} (nicht verfügbar)")

    if missing_required:
        print(f"\n❌ FEHLENDE MODULE: {', '.join(missing_required)}")
        print(f"📦 INSTALLATION: pip install {' '.join(missing_required)}")
        return False

    print("\n✅ ALLE ERFORDERLICHEN MODULE VERFÜGBAR")
    return True

def check_file_integrity():
    """📁 Datei-Integrität prüfen"""
    print("\n📁 DATEI-INTEGRITÄT CHECK")
    print("-" * 28)

    required_files = [
        'ultimate_modular_trading_dashboard.py',
        'ultimate_launcher.py'
    ]

    all_good = True

    for file in required_files:
        if Path(file).exists():
            size = Path(file).stat().st_size
            print(f"✅ {file} ({size:,} bytes)")
        else:
            print(f"❌ {file} (FEHLT)")
            all_good = False

    return all_good

def run_performance_test():
    """⚡ Performance-Test"""
    print("\n⚡ PERFORMANCE-TEST")
    print("-" * 20)

    try:
        import numpy as np
        import time

        print("🧮 Teste numerische Berechnungen...")
        start = time.time()
        data = np.random.random(10000)
        result = np.mean(data)
        numpy_time = time.time() - start

        print(f"   Numpy-Test: {numpy_time:.3f}s")

        if numpy_time > 0.1:
            print("⚠️ Langsame numerische Berechnungen")
        else:
            print("✅ Numerische Berechnungen optimal")

        return True

    except Exception as e:
        print(f"❌ Performance-Test fehlgeschlagen: {e}")
        return False

def main():
    """🚀 Hauptfunktion"""
    print("🔧 ULTIMATE SYSTEM CHECKER & OPTIMIZER")
    print("=" * 50)
    print("🎯 Überprüfung für Ultimate Bitcoin Trading Dashboard")
    print("=" * 50)

    all_checks_passed = True

    # Alle Checks durchführen
    checks = [
        check_python_version,
        check_dependencies_detailed,
        check_file_integrity,
        run_performance_test
    ]

    for check_func in checks:
        try:
            result = check_func()
            if not result:
                all_checks_passed = False
        except Exception as e:
            print(f"❌ Fehler bei Check: {e}")
            all_checks_passed = False

    # Zusammenfassung
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("✅ SYSTEM-CHECK ERFOLGREICH")
        print("🚀 System bereit für Ultimate Trading Dashboard")
    else:
        print("⚠️ SYSTEM-CHECK MIT PROBLEMEN")
        print("🔧 Bitte beheben Sie die oben genannten Probleme")

    print("=" * 50)

    return all_checks_passed

if __name__ == "__main__":
    main()