import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
import time
import warnings
warnings.filterwarnings('ignore')

# Set dark background style for all plots
plt.style.use('dark_background')

def fetch_data():
    """Fetch and combine data from multiple sources"""
    # Placeholder for actual API calls
    # In production, use the functions from your existing scripts:
    # df_av = get_alpha_vantage_data()
    # df_cg = get_coingecko_data_hourly()
    # df_cc = get_crypto_compare_data()
    # df_binance = fetch_binance_data()
    
    # For demo, load from CSV
    df = pd.read_csv('bitcoin_data.csv', index_col=0, parse_dates=True)
    return df

def add_technical_indicators(df):
    """Add technical indicators to the dataframe"""
    # SMA
    df['SMA_50'] = df['close'].rolling(window=50).mean()
    df['SMA_200'] = df['close'].rolling(window=200).mean()
    
    # RSI
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0).rolling(window=14).mean()
    loss = -delta.where(delta < 0, 0).rolling(window=14).mean()
    rs = gain / loss
    df['RSI'] = 100 - (100 / (1 + rs))
    
    # MACD
    df['EMA_12'] = df['close'].ewm(span=12).mean()
    df['EMA_26'] = df['close'].ewm(span=26).mean()
    df['MACD'] = df['EMA_12'] - df['EMA_26']
    df['MACD_signal'] = df['MACD'].ewm(span=9).mean()
    
    # Bollinger Bands
    df['BB_middle'] = df['close'].rolling(window=20).mean()
    std = df['close'].rolling(window=20).std()
    df['BB_upper'] = df['BB_middle'] + 2 * std
    df['BB_lower'] = df['BB_middle'] - 2 * std
    
    return df.dropna()

def prepare_data(df, target_col='close', look_back=24):
    """Prepare data for LSTM model"""
    # Scale data
    scaler = MinMaxScaler(feature_range=(0, 1))
    scaled_data = scaler.fit_transform(df)
    
    # Create features and target
    X, y = [], []
    for i in range(look_back, len(scaled_data)):
        X.append(scaled_data[i-look_back:i])
        y.append(scaled_data[i, df.columns.get_loc(target_col)])
    
    X, y = np.array(X), np.array(y)
    
    # Split data
    train_size = int(len(X) * 0.8)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]
    
    return X_train, X_test, y_train, y_test, scaler, train_size, df.columns.get_loc(target_col)

def build_lstm_model(input_shape):
    """Build optimized LSTM model"""
    model = Sequential()
    model.add(LSTM(100, return_sequences=True, input_shape=input_shape))
    model.add(Dropout(0.2))
    model.add(LSTM(50, return_sequences=False))
    model.add(Dropout(0.2))
    model.add(Dense(25))
    model.add(Dense(1))
    model.compile(optimizer='adam', loss='mean_squared_error')
    return model

def predict_future(model, last_sequence, steps=72, scaler=None, df=None, target_idx=0):
    """Predict future values with confidence intervals"""
    future_predictions = []
    lower_bounds = []
    upper_bounds = []
    
    # Use the last sequence from our test data as the initial input
    curr_seq = last_sequence[-1:].copy()
    
    for i in range(steps):
        # Get prediction (scaled)
        pred = model.predict(curr_seq)
        
        # Add prediction to results
        future_predictions.append(pred[0, 0])
        
        # Create confidence bounds (simple approach - could be more sophisticated)
        lower_bounds.append(pred[0, 0] * 0.95)  # 5% lower
        upper_bounds.append(pred[0, 0] * 1.05)  # 5% higher
        
        # Update sequence for next prediction
        new_seq = curr_seq[0, 1:].copy()
        new_seq = np.append(new_seq, pred)
        curr_seq[0] = new_seq
    
    # If scaler provided, inverse transform predictions
    if scaler is not None and df is not None:
        # Create dummy arrays for inverse transform
        dummy = np.zeros((len(future_predictions), df.shape[1]))
        dummy[:, target_idx] = future_predictions
        future_predictions = scaler.inverse_transform(dummy)[:, target_idx]
        
        dummy[:, target_idx] = lower_bounds
        lower_bounds = scaler.inverse_transform(dummy)[:, target_idx]
        
        dummy[:, target_idx] = upper_bounds
        upper_bounds = scaler.inverse_transform(dummy)[:, target_idx]
    
    return future_predictions, lower_bounds, upper_bounds

def calculate_metrics(y_true, y_pred):
    """Calculate performance metrics"""
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    return {
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'r2': r2
    }

def plot_results(df, train_size, y_test, y_pred, future_dates, future_pred, 
                 lower_bound, upper_bound, metrics, training_time):
    """Plot comprehensive results with dark theme"""
    fig = plt.figure(figsize=(20, 12))
    fig.patch.set_facecolor('#121212')  # Dark background
    
    # Main prediction chart
    ax1 = plt.subplot2grid((3, 3), (0, 0), colspan=2, rowspan=2)
    
    # Training data
    ax1.plot(df.index[:train_size], df['close'].iloc[:train_size], 
             color='#4B5D67', label='Training Data', alpha=0.7)
    
    # Test data and predictions
    test_dates = df.index[train_size:train_size+len(y_test)]
    ax1.plot(test_dates, y_test, color='#58B368', label='Actual', linewidth=2)
    ax1.plot(test_dates, y_pred, color='#D64933', label='Predicted', linewidth=2, linestyle='--')
    
    # Future predictions with confidence interval
    ax1.plot(future_dates, future_pred, color='#FF9505', label='Future Forecast', linewidth=2)
    ax1.fill_between(future_dates, lower_bound, upper_bound, color='#FF9505', alpha=0.2, label='Confidence Interval')
    
    # Current time marker
    current_time = df.index[-1]
    ax1.axvline(x=current_time, color='#E5E5E5', linestyle='-', linewidth=1, label='Current Time')
    
    # Format chart
    ax1.set_title('Bitcoin Price Prediction Model', fontsize=16, color='white', fontweight='bold')
    ax1.set_ylabel('Price (USD)', color='white')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax1.xaxis.set_major_locator(mdates.DayLocator(interval=7))
    ax1.tick_params(colors='white')
    
    # Metrics panel
    ax2 = plt.subplot2grid((3, 3), (0, 2), rowspan=1)
    metrics_text = (
        f"MODEL PERFORMANCE\n\n"
        f"R² Score: {metrics['r2']:.4f}\n"
        f"RMSE: {metrics['rmse']:.2f}\n"
        f"MAE: {metrics['mae']:.2f}\n\n"
        f"Training Time: {training_time:.2f}s"
    )
    
    # Color based on R² score
    if metrics['r2'] >= 0.8:
        box_color = '#58B368'  # Green
    elif metrics['r2'] >= 0.6:
        box_color = '#FF9505'  # Yellow
    else:
        box_color = '#D64933'  # Red
    
    ax2.text(0.5, 0.5, metrics_text, ha='center', va='center', fontsize=12, 
             color='white', bbox=dict(facecolor=box_color, alpha=0.3, boxstyle='round,pad=1'))
    ax2.axis('off')
    
    # Technical indicators
    ax3 = plt.subplot2grid((3, 3), (2, 0), colspan=2)
    
    # Plot RSI
    ax3.plot(df.index[-90:], df['RSI'].iloc[-90:], color='#FF9505', label='RSI')
    ax3.axhline(y=70, color='#D64933', linestyle='--', alpha=0.5)
    ax3.axhline(y=30, color='#58B368', linestyle='--', alpha=0.5)
    ax3.set_ylim(0, 100)
    ax3.set_title('RSI (14)', color='white')
    ax3.set_ylabel('RSI Value', color='white')
    ax3.grid(True, alpha=0.3)
    ax3.tick_params(colors='white')
    
    # Price change prediction
    ax4 = plt.subplot2grid((3, 3), (2, 2))
    current_price = df['close'].iloc[-1]
    future_price = future_pred[-1]
    price_change = ((future_price / current_price) - 1) * 100
    
    if price_change > 0:
        direction = "BULLISH"
        color = '#58B368'  # Green
    else:
        direction = "BEARISH"
        color = '#D64933'  # Red
    
    forecast_text = (
        f"72-HOUR FORECAST\n\n"
        f"Current: ${current_price:.2f}\n"
        f"Forecast: ${future_price:.2f}\n\n"
        f"Change: {price_change:.2f}%\n"
        f"Outlook: {direction}"
    )
    
    ax4.text(0.5, 0.5, forecast_text, ha='center', va='center', fontsize=12, 
             color='white', bbox=dict(facecolor=color, alpha=0.3, boxstyle='round,pad=1'))
    ax4.axis('off')
    
    plt.tight_layout()
    plt.subplots_adjust(wspace=0.2, hspace=0.3)
    
    return fig

def main():
    # Start timer
    start_time = time.time()
    
    # Load and prepare data
    df = fetch_data()
    df = add_technical_indicators(df)
    
    # Prepare data for LSTM
    X_train, X_test, y_train, y_test, scaler, train_size, target_idx = prepare_data(df)
    
    # Build and train model
    model = build_lstm_model((X_train.shape[1], X_train.shape[2]))
    model.fit(X_train, y_train, epochs=50, batch_size=32, verbose=0)
    
    # Calculate training time
    training_time = time.time() - start_time
    
    # Make predictions on test data
    y_pred_scaled = model.predict(X_test)
    
    # Inverse transform predictions
    dummy_array = np.zeros((len(y_pred_scaled), df.shape[1]))
    dummy_array[:, target_idx] = y_pred_scaled.flatten()
    y_pred = scaler.inverse_transform(dummy_array)[:, target_idx]
    
    # Get actual test values
    dummy_array = np.zeros((len(y_test), df.shape[1]))
    dummy_array[:, target_idx] = y_test
    y_test_inv = scaler.inverse_transform(dummy_array)[:, target_idx]
    
    # Calculate metrics
    metrics = calculate_metrics(y_test_inv, y_pred)
    
    # Predict future 72 hours
    future_steps = 72
    future_pred, lower_bound, upper_bound = predict_future(
        model, X_test, steps=future_steps, scaler=scaler, df=df, target_idx=target_idx
    )
    
    # Create future dates
    last_date = df.index[-1]
    future_dates = pd.date_range(start=last_date, periods=future_steps+1, freq='H')[1:]
    
    # Plot results
    fig = plot_results(
        df, train_size, y_test_inv, y_pred, 
        future_dates, future_pred, lower_bound, upper_bound,
        metrics, training_time
    )
    
    # Save figure
    fig.savefig('bitcoin_prediction.png', facecolor='#121212', dpi=300)
    plt.show()
    
    print(f"Model Performance:")
    print(f"R² Score: {metrics['r2']:.4f}")
    print(f"RMSE: {metrics['rmse']:.2f}")
    print(f"MAE: {metrics['mae']:.2f}")
    print(f"Training Time: {training_time:.2f} seconds")
    
    # Forecast summary
    current_price = df['close'].iloc[-1]
    future_price = future_pred[-1]
    price_change = ((future_price / current_price) - 1) * 100
    print(f"\n72-Hour Forecast:")
    print(f"Current Price: ${current_price:.2f}")
    print(f"Forecasted Price: ${future_price:.2f}")
    print(f"Predicted Change: {price_change:.2f}%")

if __name__ == "__main__":
    main()