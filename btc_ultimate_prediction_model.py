import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import requests
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
from tensorflow.keras.optimizers import <PERSON>
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score, mean_absolute_percentage_error
from datetime import datetime, timedelta
import time
import os
import joblib
import yfinance as yf
import warnings
warnings.filterwarnings('ignore')

# MAXIMALE CPU-AUSLASTUNG AKTIVIEREN
tf.config.threading.set_intra_op_parallelism_threads(0)  # Alle verfügbaren Kerne
tf.config.threading.set_inter_op_parallelism_threads(0)  # Alle verfügbaren Kerne
os.environ['OMP_NUM_THREADS'] = str(os.cpu_count())      # OpenMP Threads
os.environ['TF_NUM_INTEROP_THREADS'] = str(os.cpu_count())
os.environ['TF_NUM_INTRAOP_THREADS'] = str(os.cpu_count())

# SPEED-OPTIMIERTE Konfiguration
CONFIG = {
    'symbol': 'BTC-USD',
    'data_period': '90d',   # Weniger Daten für Geschwindigkeit
    'data_interval': '1h',
    'train_split': 0.85,    # Mehr Training, weniger Validation
    'look_back': 24,        # Kürzere Sequenzen
    'future_steps': 24,     # 24h statt 72h
    'batch_size': 512,      # Große Batches für CPU-Effizienz
    'epochs': 50,           # Weniger Epochen
    'patience': 8,          # Früher stoppen
    'model_type': 'speed_optimized',
    'save_dir': 'saved_models',
    'use_multiple_sources': False,  # Nur lokale Daten
    'monte_carlo_simulations': 100  # Weniger Simulationen
}

class AdvancedDataSource:
    @staticmethod
    def fetch_yahoo_finance():
        """Optimierte Yahoo Finance Datenabfrage"""
        try:
            print("📊 Lade Yahoo Finance Daten...")
            data = yf.download(CONFIG['symbol'], period=CONFIG['data_period'], 
                             interval=CONFIG['data_interval'], progress=False)
            
            if data.empty:
                print("❌ Keine Yahoo Finance Daten erhalten")
                return None
            
            # MultiIndex Spalten behandeln (yfinance gibt manchmal MultiIndex zurück)
            if isinstance(data.columns, pd.MultiIndex):
                # Nur die erste Ebene der Spalten verwenden
                data.columns = [col[0] for col in data.columns]

            # Spalten standardisieren
            column_mapping = {
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Adj Close': 'adj_close',
                'Volume': 'volume'
            }

            # Spalten umbenennen falls vorhanden
            data = data.rename(columns=column_mapping)

            # Adj Close entfernen falls vorhanden
            if 'adj_close' in data.columns:
                data = data.drop('adj_close', axis=1)
            print(f"✅ Yahoo Finance: {len(data)} Datenpunkte")
            return data
        except Exception as e:
            print(f"❌ Yahoo Finance Fehler: {e}")
            return None
    
    @staticmethod
    def fetch_coingecko():
        """CoinGecko API mit Retry-Logik"""
        try:
            print("📊 Lade CoinGecko Daten...")
            url = "https://api.coingecko.com/api/v3/coins/bitcoin/market_chart"
            params = {
                "vs_currency": "usd",
                "days": int(CONFIG['data_period'].replace('d', '')),
                "interval": "hourly"
            }
            
            for attempt in range(3):  # Retry-Logik
                try:
                    response = requests.get(url, params=params, timeout=30)
                    response.raise_for_status()
                    data = response.json()
                    break
                except:
                    if attempt == 2:
                        raise
                    time.sleep(2)
            
            if not data or 'prices' not in data:
                return None
            
            # Daten verarbeiten
            df = pd.DataFrame(data['prices'], columns=['timestamp', 'close'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Volumen hinzufügen
            volumes = pd.DataFrame(data['total_volumes'], columns=['timestamp', 'volume'])
            volumes['timestamp'] = pd.to_datetime(volumes['timestamp'], unit='ms')
            volumes.set_index('timestamp', inplace=True)
            df = df.join(volumes)
            
            # OHLC approximieren mit verbesserter Logik
            df['open'] = df['close'].shift(1)
            df['high'] = df['close'] * np.random.uniform(1.001, 1.01, len(df))
            df['low'] = df['close'] * np.random.uniform(0.99, 0.999, len(df))
            
            df.dropna(inplace=True)
            print(f"✅ CoinGecko: {len(df)} Datenpunkte")
            return df
        except Exception as e:
            print(f"❌ CoinGecko Fehler: {e}")
            return None

class AdvancedTechnicalIndicators:
    @staticmethod
    def add_comprehensive_indicators(df):
        """Umfassende technische Indikatoren"""
        df = df.copy()
        
        # Trend-Indikatoren
        df = AdvancedTechnicalIndicators.add_moving_averages(df)
        df = AdvancedTechnicalIndicators.add_macd(df)
        df = AdvancedTechnicalIndicators.add_adx(df)
        
        # Momentum-Indikatoren
        df = AdvancedTechnicalIndicators.add_rsi(df)
        df = AdvancedTechnicalIndicators.add_stochastic(df)
        df = AdvancedTechnicalIndicators.add_williams_r(df)
        
        # Volatilitäts-Indikatoren
        df = AdvancedTechnicalIndicators.add_bollinger_bands(df)
        df = AdvancedTechnicalIndicators.add_atr(df)
        df = AdvancedTechnicalIndicators.add_keltner_channels(df)
        
        # Volumen-Indikatoren
        df = AdvancedTechnicalIndicators.add_volume_indicators(df)
        
        # Preis-Indikatoren
        df = AdvancedTechnicalIndicators.add_price_patterns(df)
        
        return df.dropna()
    
    @staticmethod
    def add_moving_averages(df):
        """Erweiterte Moving Averages"""
        # EMAs
        for period in [9, 12, 21, 26, 50]:
            df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
        
        # SMAs
        for period in [20, 50, 100, 200]:
            df[f'sma_{period}'] = df['close'].rolling(window=period).mean()
        
        # VWMA (Volume Weighted Moving Average)
        df['vwma_20'] = (df['close'] * df['volume']).rolling(20).sum() / df['volume'].rolling(20).sum()
        
        return df
    
    @staticmethod
    def add_macd(df):
        """MACD mit Histogram"""
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        return df
    
    @staticmethod
    def add_rsi(df):
        """RSI mit verschiedenen Perioden"""
        for period in [14, 21]:
            delta = df['close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=period).mean()
            avg_loss = loss.rolling(window=period).mean()
            rs = avg_gain / avg_loss
            df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        return df
    
    @staticmethod
    def add_bollinger_bands(df):
        """Bollinger Bands mit verschiedenen Perioden"""
        for period in [20, 50]:
            sma = df['close'].rolling(window=period).mean()
            std = df['close'].rolling(window=period).std()
            df[f'bb_upper_{period}'] = sma + (std * 2)
            df[f'bb_lower_{period}'] = sma - (std * 2)
            df[f'bb_width_{period}'] = (df[f'bb_upper_{period}'] - df[f'bb_lower_{period}']) / sma
            df[f'bb_position_{period}'] = (df['close'] - df[f'bb_lower_{period}']) / (df[f'bb_upper_{period}'] - df[f'bb_lower_{period}'])
        return df
    
    @staticmethod
    def add_atr(df):
        """Average True Range"""
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        df['atr'] = true_range.rolling(14).mean()
        df['atr_percent'] = df['atr'] / df['close'] * 100
        return df
    
    @staticmethod
    def add_stochastic(df):
        """Stochastic Oscillator"""
        low_14 = df['low'].rolling(14).min()
        high_14 = df['high'].rolling(14).max()
        df['stoch_k'] = 100 * ((df['close'] - low_14) / (high_14 - low_14))
        df['stoch_d'] = df['stoch_k'].rolling(3).mean()
        return df
    
    @staticmethod
    def add_williams_r(df):
        """Williams %R"""
        high_14 = df['high'].rolling(14).max()
        low_14 = df['low'].rolling(14).min()
        df['williams_r'] = -100 * ((high_14 - df['close']) / (high_14 - low_14))
        return df
    
    @staticmethod
    def add_adx(df):
        """Average Directional Index"""
        high_diff = df['high'].diff()
        low_diff = df['low'].diff()
        plus_dm = high_diff.where((high_diff > low_diff) & (high_diff > 0), 0)
        minus_dm = (-low_diff).where((low_diff > high_diff) & (low_diff > 0), 0)
        
        atr = AdvancedTechnicalIndicators.calculate_atr(df)
        plus_di = 100 * (plus_dm.rolling(14).mean() / atr)
        minus_di = 100 * (minus_dm.rolling(14).mean() / atr)
        
        dx = 100 * (plus_di - minus_di).abs() / (plus_di + minus_di)
        df['adx'] = dx.rolling(14).mean()
        return df
    
    @staticmethod
    def add_keltner_channels(df):
        """Keltner Channels"""
        ema_20 = df['close'].ewm(span=20).mean()
        atr = AdvancedTechnicalIndicators.calculate_atr(df)
        df['keltner_upper'] = ema_20 + (2 * atr)
        df['keltner_lower'] = ema_20 - (2 * atr)
        return df
    
    @staticmethod
    def add_volume_indicators(df):
        """Volumen-Indikatoren"""
        # OBV (On-Balance Volume)
        obv = [0]
        for i in range(1, len(df)):
            if df['close'].iloc[i] > df['close'].iloc[i-1]:
                obv.append(obv[-1] + df['volume'].iloc[i])
            elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                obv.append(obv[-1] - df['volume'].iloc[i])
            else:
                obv.append(obv[-1])
        df['obv'] = obv
        
        # Volume Rate of Change
        df['volume_roc'] = df['volume'].pct_change(periods=12) * 100
        
        # Accumulation/Distribution Line
        clv = ((df['close'] - df['low']) - (df['high'] - df['close'])) / (df['high'] - df['low'])
        df['ad_line'] = (clv * df['volume']).cumsum()
        
        return df
    
    @staticmethod
    def add_price_patterns(df):
        """Preis-Pattern Indikatoren"""
        # Price Rate of Change
        for period in [12, 24]:
            df[f'price_roc_{period}'] = df['close'].pct_change(periods=period) * 100
        
        # Momentum
        df['momentum'] = df['close'] - df['close'].shift(10)
        
        # Volatilität
        df['volatility'] = df['close'].pct_change().rolling(window=20).std() * 100
        
        # Höchst/Tiefst Verhältnisse
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        
        return df
    
    @staticmethod
    def calculate_atr(df):
        """ATR Berechnung Hilfsfunktion"""
        high_low = df['high'] - df['low']
        high_close = (df['high'] - df['close'].shift()).abs()
        low_close = (df['low'] - df['close'].shift()).abs()
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(14).mean()

class AdvancedDataProcessor:
    def __init__(self):
        self.feature_scaler = None
        self.target_scaler = None
        self.feature_columns = None
    
    def prepare_data(self):
        """Erweiterte Datenvorbereitung mit lokalen Daten"""
        print("🔄 Bereite Daten vor...")

        # Versuche zuerst APIs, dann lokale Daten
        df_yahoo = AdvancedDataSource.fetch_yahoo_finance()
        df_coingecko = AdvancedDataSource.fetch_coingecko()

        # Daten kombinieren oder lokale Daten verwenden
        if df_yahoo is not None and df_coingecko is not None:
            # Zeitindex synchronisieren
            common_index = df_yahoo.index.intersection(df_coingecko.index)
            df_yahoo_sync = df_yahoo.loc[common_index]
            df_coingecko_sync = df_coingecko.loc[common_index]

            # Gewichteter Durchschnitt
            df = pd.DataFrame(index=common_index)
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = 0.7 * df_yahoo_sync[col] + 0.3 * df_coingecko_sync[col]
        elif df_yahoo is not None:
            df = df_yahoo
        elif df_coingecko is not None:
            df = df_coingecko
        else:
            # Fallback auf lokale Daten
            print("⚠️  APIs nicht verfügbar, verwende lokale Daten...")
            df = self.load_local_data()
            if df is None:
                raise ValueError("❌ Keine Daten verfügbar!")
        
        # Technische Indikatoren hinzufügen
        print("📈 Berechne technische Indikatoren...")
        df = AdvancedTechnicalIndicators.add_comprehensive_indicators(df)
        
        # SPEED-OPTIMIERTE Feature-Auswahl (nur die wichtigsten)
        feature_columns = [
            'open', 'high', 'low', 'close', 'volume',
            'ema_9', 'ema_21', 'sma_20',
            'macd', 'macd_signal',
            'rsi_14', 'bb_width_20',
            'atr_percent', 'volatility'
        ]
        
        # Verfügbare Features filtern
        self.feature_columns = [col for col in feature_columns if col in df.columns]
        
        print(f"📊 Verwende {len(self.feature_columns)} Features")
        
        # Daten vorbereiten
        feature_data = df[self.feature_columns].values.astype('float32')
        target_data = df['close'].values.reshape(-1, 1).astype('float32')
        
        # Skalierung
        self.feature_scaler = RobustScaler()
        self.target_scaler = MinMaxScaler(feature_range=(0, 1))
        
        feature_scaled = self.feature_scaler.fit_transform(feature_data)
        target_scaled = self.target_scaler.fit_transform(target_data)
        
        # Train-Test Split
        train_size = int(len(feature_scaled) * CONFIG['train_split'])
        
        # Sequenzen erstellen
        X_train, y_train = self.create_sequences(
            feature_scaled[:train_size], target_scaled[:train_size]
        )
        X_test, y_test = self.create_sequences(
            feature_scaled[train_size:], target_scaled[train_size:]
        )
        
        print(f"✅ Daten vorbereitet: Train {X_train.shape}, Test {X_test.shape}")
        
        return X_train, y_train, X_test, y_test, df, train_size
    
    def create_sequences(self, features, targets):
        """Sequenzen für LSTM erstellen"""
        X, y = [], []
        for i in range(len(features) - CONFIG['look_back']):
            X.append(features[i:i + CONFIG['look_back']])
            y.append(targets[i + CONFIG['look_back']])
        return np.array(X), np.array(y)
    
    def load_local_data(self):
        """Lade lokale CSV-Daten als Fallback"""
        try:
            print("📊 Lade lokale Daten aus crypto_data.csv...")
            df = pd.read_csv('crypto_data.csv')

            # Zeitstempel konvertieren
            df['time'] = pd.to_datetime(df['time'])
            df.set_index('time', inplace=True)

            print(f"✅ Lokale Daten geladen: {len(df)} Datenpunkte")
            print(f"   Zeitraum: {df.index[0]} bis {df.index[-1]}")
            print(f"   Aktueller Preis: ${df['close'].iloc[-1]:.2f}")

            return df
        except Exception as e:
            print(f"❌ Fehler beim Laden lokaler Daten: {e}")
            return None

    def inverse_transform_target(self, data):
        """Target-Skalierung rückgängig machen"""
        return self.target_scaler.inverse_transform(data)

class AdvancedModelBuilder:
    def __init__(self):
        self.model = None

    def build_hybrid_advanced_model(self, input_shape):
        """Speed-optimiertes Modell"""
        model = Sequential([
            # Kompakte LSTM-Schichten
            LSTM(64, return_sequences=True, dropout=0.2, input_shape=input_shape),

            # Zweite LSTM-Schicht
            LSTM(32, return_sequences=False, dropout=0.2),

            # Kompakte Dense Layers
            Dense(32, activation='relu'),
            Dropout(0.3),
            Dense(16, activation='relu'),
            Dense(1, activation='linear')
        ])

        # Schneller Optimizer
        optimizer = Adam(learning_rate=0.002)  # Höhere Learning Rate

        model.compile(
            optimizer=optimizer,
            loss='mse',  # Schneller als Huber
            metrics=['mae']
        )

        return model

    def get_advanced_callbacks(self):
        """Erweiterte Callbacks"""
        os.makedirs(CONFIG['save_dir'], exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_path = os.path.join(CONFIG['save_dir'], f"btc_ultimate_{timestamp}.h5")

        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=CONFIG['patience'],
                restore_best_weights=True,
                verbose=1,
                min_delta=0.0001
            ),
            ModelCheckpoint(
                model_path,
                monitor='val_loss',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.7,
                patience=8,
                min_lr=0.00001,
                verbose=1,
                cooldown=3
            )
        ]

        return callbacks, model_path

class AdvancedPredictor:
    def __init__(self, model, processor):
        self.model = model
        self.processor = processor

    def predict_future_with_uncertainty(self, last_sequence):
        """Erweiterte Zukunftsprognose mit Unsicherheitsquantifizierung"""
        print("🔮 Erstelle Zukunftsprognosen...")

        n_simulations = CONFIG['monte_carlo_simulations']
        future_steps = CONFIG['future_steps']

        all_predictions = []

        # Monte Carlo Simulationen
        for sim in range(n_simulations):
            if sim % 100 == 0:
                print(f"   Simulation {sim+1}/{n_simulations}")

            current_seq = last_sequence.copy()
            sim_predictions = []

            for step in range(future_steps):
                # Vorhersage
                pred = self.model.predict(current_seq.reshape(1, *current_seq.shape), verbose=0)[0, 0]
                sim_predictions.append(pred)

                # Nächste Sequenz vorbereiten
                # Neue Features basierend auf Vorhersage generieren
                new_features = self.generate_next_features(current_seq, pred, step)

                # Sequenz aktualisieren
                current_seq = np.vstack([current_seq[1:], new_features.reshape(1, -1)])

            all_predictions.append(sim_predictions)

        # Statistiken berechnen
        all_predictions = np.array(all_predictions)
        mean_pred = np.mean(all_predictions, axis=0)
        std_pred = np.std(all_predictions, axis=0)

        # Konfidenzintervalle
        confidence_95_upper = np.percentile(all_predictions, 97.5, axis=0)
        confidence_95_lower = np.percentile(all_predictions, 2.5, axis=0)
        confidence_80_upper = np.percentile(all_predictions, 90, axis=0)
        confidence_80_lower = np.percentile(all_predictions, 10, axis=0)

        # Skalierung rückgängig machen
        mean_pred_rescaled = self.processor.inverse_transform_target(mean_pred.reshape(-1, 1)).flatten()
        upper_95_rescaled = self.processor.inverse_transform_target(confidence_95_upper.reshape(-1, 1)).flatten()
        lower_95_rescaled = self.processor.inverse_transform_target(confidence_95_lower.reshape(-1, 1)).flatten()
        upper_80_rescaled = self.processor.inverse_transform_target(confidence_80_upper.reshape(-1, 1)).flatten()
        lower_80_rescaled = self.processor.inverse_transform_target(confidence_80_lower.reshape(-1, 1)).flatten()

        return {
            'mean': mean_pred_rescaled,
            'upper_95': upper_95_rescaled,
            'lower_95': lower_95_rescaled,
            'upper_80': upper_80_rescaled,
            'lower_80': lower_80_rescaled,
            'std': std_pred
        }

    def generate_next_features(self, current_seq, predicted_close, step):
        """Generiere nächste Features basierend auf Vorhersage"""
        last_features = current_seq[-1].copy()

        # Simuliere realistische Änderungen
        volatility = 0.02 * (1 + step * 0.001)  # Zunehmende Unsicherheit

        # OHLC basierend auf predicted_close
        noise = np.random.normal(0, volatility)
        open_price = predicted_close * (1 + noise * 0.5)
        high_price = max(open_price, predicted_close) * (1 + abs(np.random.normal(0, volatility * 0.3)))
        low_price = min(open_price, predicted_close) * (1 - abs(np.random.normal(0, volatility * 0.3)))

        # Volume simulation
        volume_change = np.random.normal(0, 0.1)
        volume = last_features[4] * (1 + volume_change)  # Annahme: Volume ist Index 4

        # Neue Features erstellen (vereinfacht)
        new_features = last_features.copy()
        new_features[0] = open_price  # open
        new_features[1] = high_price  # high
        new_features[2] = low_price   # low
        new_features[3] = predicted_close  # close
        new_features[4] = volume      # volume

        # Technische Indikatoren approximieren (vereinfacht)
        # In einer vollständigen Implementierung würden diese korrekt berechnet
        for i in range(5, len(new_features)):
            change = np.random.normal(0, volatility * 0.5)
            new_features[i] = new_features[i] * (1 + change)

        return new_features

    def evaluate_model_comprehensive(self, X_train, y_train, X_test, y_test):
        """Umfassende Modellevaluation"""
        print("📊 Evaluiere Modell...")

        # Vorhersagen
        train_pred = self.model.predict(X_train, verbose=0)
        test_pred = self.model.predict(X_test, verbose=0)

        # Skalierung rückgängig machen
        y_train_orig = self.processor.inverse_transform_target(y_train)
        y_test_orig = self.processor.inverse_transform_target(y_test)
        train_pred_orig = self.processor.inverse_transform_target(train_pred)
        test_pred_orig = self.processor.inverse_transform_target(test_pred)

        # Metriken berechnen
        metrics = {
            'train_rmse': np.sqrt(mean_squared_error(y_train_orig, train_pred_orig)),
            'test_rmse': np.sqrt(mean_squared_error(y_test_orig, test_pred_orig)),
            'train_mae': mean_absolute_error(y_train_orig, train_pred_orig),
            'test_mae': mean_absolute_error(y_test_orig, test_pred_orig),
            'train_r2': r2_score(y_train_orig, train_pred_orig),
            'test_r2': r2_score(y_test_orig, test_pred_orig),
            'train_mape': mean_absolute_percentage_error(y_train_orig, train_pred_orig),
            'test_mape': mean_absolute_percentage_error(y_test_orig, test_pred_orig)
        }

        # Richtungsgenauigkeit
        train_direction_acc = self.calculate_direction_accuracy(y_train_orig, train_pred_orig)
        test_direction_acc = self.calculate_direction_accuracy(y_test_orig, test_pred_orig)

        metrics['train_direction_accuracy'] = train_direction_acc
        metrics['test_direction_accuracy'] = test_direction_acc

        return metrics, y_train_orig, y_test_orig, train_pred_orig, test_pred_orig

    def calculate_direction_accuracy(self, y_true, y_pred):
        """Berechne Richtungsgenauigkeit"""
        true_direction = np.diff(y_true.flatten()) > 0
        pred_direction = np.diff(y_pred.flatten()) > 0
        return np.mean(true_direction == pred_direction) * 100

class AdvancedVisualizer:
    def __init__(self):
        plt.style.use('default')
        plt.rcParams['figure.figsize'] = (15, 10)
        plt.rcParams['font.size'] = 10

    def plot_comprehensive_results(self, df, train_size, metrics, predictions, future_pred=None):
        """Umfassende Ergebnisvisualisierung"""
        plt.figure(figsize=(20, 16))

        # 1. Hauptpreis-Chart mit Vorhersagen
        ax1 = plt.subplot(3, 2, (1, 2))
        self.plot_price_predictions(ax1, df, train_size, predictions, future_pred)

        # 2. Trainingsverlauf
        ax2 = plt.subplot(3, 2, 3)
        self.plot_training_metrics(ax2, metrics)

        # 3. Residuals
        ax3 = plt.subplot(3, 2, 4)
        self.plot_residuals(ax3, predictions)

        # 4. Zukunftsprognose Detail
        ax4 = plt.subplot(3, 2, 5)
        if future_pred:
            self.plot_future_detail(ax4, future_pred)

        # 5. Metriken Übersicht
        ax5 = plt.subplot(3, 2, 6)
        self.plot_metrics_overview(ax5, metrics)

        plt.tight_layout()
        plt.show()

    def plot_price_predictions(self, ax, df, train_size, predictions, future_pred):
        """Hauptpreis-Chart"""
        # Daten vorbereiten
        dates = df.index
        prices = df['close'].values

        train_dates = dates[:train_size]
        test_dates = dates[train_size:]
        train_prices = prices[:train_size]

        y_test_orig, test_pred_orig = predictions['test_actual'], predictions['test_pred']

        # Plot
        ax.plot(train_dates, train_prices, label='Training', color='blue', alpha=0.7, linewidth=1)
        ax.plot(test_dates[-len(y_test_orig):], y_test_orig.flatten(),
                label='Test (Actual)', color='green', linewidth=2)
        ax.plot(test_dates[-len(test_pred_orig):], test_pred_orig.flatten(),
                label='Test (Predicted)', color='red', linestyle='--', linewidth=2)

        # Zukunftsprognose
        if future_pred:
            future_dates = pd.date_range(
                start=dates[-1] + pd.Timedelta(hours=1),
                periods=len(future_pred['mean']),
                freq='1H'
            )

            ax.plot(future_dates, future_pred['mean'],
                   label='Future Prediction', color='purple', linewidth=2)
            ax.fill_between(future_dates, future_pred['lower_95'], future_pred['upper_95'],
                           color='purple', alpha=0.2, label='95% Confidence')
            ax.fill_between(future_dates, future_pred['lower_80'], future_pred['upper_80'],
                           color='purple', alpha=0.3, label='80% Confidence')

        ax.set_title('Bitcoin Price Prediction - Ultimate Model', fontsize=14, fontweight='bold')
        ax.set_xlabel('Date')
        ax.set_ylabel('Price (USD)')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Zeitachse formatieren
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=7))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    def plot_training_metrics(self, ax, metrics):
        """Trainingsmetriken"""
        if 'history' in metrics:
            history = metrics['history']
            epochs = range(1, len(history.history['loss']) + 1)

            ax.plot(epochs, history.history['loss'], 'b-', label='Training Loss', linewidth=2)
            ax.plot(epochs, history.history['val_loss'], 'r-', label='Validation Loss', linewidth=2)

            ax.set_title('Training Progress', fontweight='bold')
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Loss')
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'Training History\nNot Available',
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            ax.set_title('Training Progress')

    def plot_residuals(self, ax, predictions):
        """Residuals Plot"""
        y_test = predictions['test_actual'].flatten()
        y_pred = predictions['test_pred'].flatten()
        residuals = y_test - y_pred

        ax.scatter(y_pred, residuals, alpha=0.6, s=20)
        ax.axhline(y=0, color='red', linestyle='--')
        ax.set_title('Residuals Plot', fontweight='bold')
        ax.set_xlabel('Predicted Values')
        ax.set_ylabel('Residuals')
        ax.grid(True, alpha=0.3)

    def plot_future_detail(self, ax, future_pred):
        """Detaillierte Zukunftsprognose"""
        hours = range(1, len(future_pred['mean']) + 1)

        ax.plot(hours, future_pred['mean'], 'purple', linewidth=2, label='Prediction')
        ax.fill_between(hours, future_pred['lower_95'], future_pred['upper_95'],
                       alpha=0.2, color='purple', label='95% CI')
        ax.fill_between(hours, future_pred['lower_80'], future_pred['upper_80'],
                       alpha=0.3, color='purple', label='80% CI')

        ax.set_title('72-Hour Future Prediction', fontweight='bold')
        ax.set_xlabel('Hours Ahead')
        ax.set_ylabel('Price (USD)')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def plot_metrics_overview(self, ax, metrics):
        """Metriken Übersicht"""
        ax.axis('off')

        # Metriken Text
        metrics_text = f"""
        MODEL PERFORMANCE METRICS

        Test RMSE: ${metrics.get('test_rmse', 0):.2f}
        Test MAE: ${metrics.get('test_mae', 0):.2f}
        Test R²: {metrics.get('test_r2', 0):.4f}
        Test MAPE: {metrics.get('test_mape', 0):.2f}%

        Direction Accuracy: {metrics.get('test_direction_accuracy', 0):.1f}%

        Train RMSE: ${metrics.get('train_rmse', 0):.2f}
        Train R²: {metrics.get('train_r2', 0):.4f}
        """

        ax.text(0.1, 0.9, metrics_text, transform=ax.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

def main():
    """Hauptfunktion - Ultimatives Bitcoin Prediction Model"""
    print("🚀 ULTIMATE BITCOIN PREDICTION MODEL")
    print("=" * 50)

    start_time = time.time()

    try:
        # 1. Daten vorbereiten
        processor = AdvancedDataProcessor()
        X_train, y_train, X_test, y_test, df, train_size = processor.prepare_data()

        # 2. Modell erstellen
        print("🏗️  Erstelle fortschrittliches Modell...")
        builder = AdvancedModelBuilder()
        model = builder.build_hybrid_advanced_model((X_train.shape[1], X_train.shape[2]))
        callbacks, model_path = builder.get_advanced_callbacks()

        print(f"📋 Modell-Architektur:")
        model.summary()

        # 3. Training
        print("🎯 Starte Training...")
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=CONFIG['epochs'],
            batch_size=CONFIG['batch_size'],
            callbacks=callbacks,
            verbose=1,
            shuffle=True
        )

        # 4. Evaluation
        predictor = AdvancedPredictor(model, processor)
        metrics, y_train_orig, y_test_orig, train_pred_orig, test_pred_orig = predictor.evaluate_model_comprehensive(
            X_train, y_train, X_test, y_test
        )

        # Training History zu Metriken hinzufügen
        metrics['history'] = history

        print("\n📊 MODELL-PERFORMANCE:")
        print(f"Test RMSE: ${metrics['test_rmse']:.2f}")
        print(f"Test MAE: ${metrics['test_mae']:.2f}")
        print(f"Test R²: {metrics['test_r2']:.4f}")
        print(f"Test MAPE: {metrics['test_mape']:.2f}%")
        print(f"Direction Accuracy: {metrics['test_direction_accuracy']:.1f}%")

        # 5. Zukunftsprognose
        print("\n🔮 Erstelle 72-Stunden Prognose...")
        last_sequence = X_test[-1]  # Letzte Sequenz für Prognose
        future_predictions = predictor.predict_future_with_uncertainty(last_sequence)

        # 6. Ergebnisse speichern
        print("💾 Speichere Modell und Scaler...")
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        joblib.dump(processor.feature_scaler, f"{CONFIG['save_dir']}/feature_scaler_{timestamp}.pkl")
        joblib.dump(processor.target_scaler, f"{CONFIG['save_dir']}/target_scaler_{timestamp}.pkl")

        # 7. Visualisierung
        print("📈 Erstelle Visualisierungen...")
        visualizer = AdvancedVisualizer()

        predictions_dict = {
            'test_actual': y_test_orig,
            'test_pred': test_pred_orig,
            'train_actual': y_train_orig,
            'train_pred': train_pred_orig
        }

        visualizer.plot_comprehensive_results(df, train_size, metrics, predictions_dict, future_predictions)

        # 8. Prognose-Zusammenfassung
        print("\n🎯 72-STUNDEN PROGNOSE:")
        current_price = df['close'].iloc[-1]

        for hours in [24, 48, 72]:
            if hours <= len(future_predictions['mean']):
                pred_price = future_predictions['mean'][hours-1]
                change_pct = ((pred_price / current_price) - 1) * 100
                lower_95 = future_predictions['lower_95'][hours-1]
                upper_95 = future_predictions['upper_95'][hours-1]

                print(f"In {hours}h: ${pred_price:.2f} ({change_pct:+.2f}%) "
                      f"[95% CI: ${lower_95:.2f} - ${upper_95:.2f}]")

        total_time = time.time() - start_time
        print(f"\n✅ Fertig! Gesamtzeit: {total_time:.1f} Sekunden")
        print(f"📁 Modell gespeichert: {model_path}")

    except Exception as e:
        print(f"❌ Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
