�

    ��eh��  �                   �  � d Z ddlZddlZddlZddlZddlZddlZddlZddlZddl	Z	ddl
Z
ddlZddlZddl
mZ ddlmZmZ ddlmZmZ ddlmZmZmZmZmZmZ ddlZddlmZmZ ddlZ ddl!Z"ddl#Z#ddl$Z%ddl&m'Z'm(Z(m)Z) dd	l*m+Z+m,Z, dd
l-m.Z. ddl/m0Z0 ddl1m2Z2m3Z3m4Z4 dd
l5m6Z6m7Z7m8Z8m9Z9 ddl:m;Z;m<Z<m=Z= ddl>m?Z?m@Z@mAZA 	 ddlBZCddlDmEZE ddlFmGZGmHZHmIZImJZJmKZK ddlLmMZM dZNn
# eO$ r dZNY nw xY w	 ddlPZQdZRn
# eO$ r dZRY nw xY w	 ddlSZSdZTn
# eO$ r dZTY nw xY wddlUZVddlUmWZWmXZXmYZY ddlZm[Z\ ddl]m^Z^m_Z_ ddl`maZb ddlcmdZdmeZemfZf ddlgmhZh ddlcmiZj ddlkmlZl ddlmmnZn ddlompZp ddlqZr ejs        d�  �         dejt        d<   e\ju        �v                    d�  �         d e\jw        d!<   d e\jw        d"<   d e\jw        d#<   d$d%d&d'd(d$d)d*d d+d,d-d.d/d0�Zx ejy        ejz        d1 ej{        d2 ej|        �   �         �}                    d3�  �        � d4��  �         ej~        �   �         g�5�  �          G d6� d7�  �        Z e�   �         Z� G d8� d9e�  �        Z� G d:� d;e�  �        Z� G d<� d=e�  �        Z� G d>� d?e�  �        Z� G d@� dAe��  �        Z� G dB� dCe��  �        Z� G dD� dEe��  �        Z� G dF� dGe��  �        Z� G dH� dIe��  �        Z� G dJ� dKe��  �        Z� G dL� dMe��  �        Z� G dN� dO�  �        Z� G dP� dQ�  �        Z�e�dRk    r e�dS�  �          e�dT�  �         dS dS )Uu0  
🚀 ULTIMATE MODULAR TRADING SYSTEM 🚀
=====================================
🏆 PERFEKT DURCHDACHTES, STRUKTURIERTES MODELL MIT ALLEN FEATURES 🏆

✨ MODULARE ARCHITEKTUR:
✅ Austauschbare ML-Modelle
✅ Modulare Datenquellen
✅ Separate Tabs für alle Features
✅ Plugin-System für Erweiterungen
✅ Umfassende API-Integration
✅ Professionelle Visualisierungen
✅ Automatische Modell-Optimierung
✅ Echtzeit-Performance-Monitoring

🧠 ADVANCED FEATURES:
✅ 15+ Datenquellen (APIs, yfinance, etc.)
✅ 50+ Technische Indikatoren
✅ 10+ ML-Modelle (austauschbar)
✅ Sentiment-Analyse Integration
✅ On-Chain-Daten Integration
✅ Portfolio-Management
✅ Risk-Management
✅ Backtesting-Engine
✅ Paper-Trading-Simulator

🎨 PROFESSIONAL UI/UX:
✅ 12 Spezialisierte Tabs
✅ Modular Dashboard System
✅ Plugin-Manager GUI
✅ Model-Comparison-Charts
✅ Real-time Performance-Monitoring
✅ Advanced Visualizations
✅ Export/Import-Funktionen

Version: ULTIMATE_MODULAR 1.0
Erstellt: 2025-07-03
Architektur: Modular, Erweiterbar, Professionell
�    N)�Path)�datetime�	timedelta)�deque�defaultdict)�Dict�List�Tuple�Optional�Any�Union)�ABC�abstractmethod)�RandomForestClassifier�GradientBoostingClassifier�ExtraTreesClassifier)�LogisticRegression�
SGDClassifier)�SVC)�
MLPClassifier)�StandardScaler�RobustScaler�MinMaxScaler)�accuracy_score�classification_report�confusion_matrix�
roc_auc_score)�cross_val_score�train_test_split�GridSearchCV)�SelectKBest�	f_classif�RFE)�
Sequential)�LSTM�Dense�Dropout�Conv1D�MaxPooling1D)�AdamTF)�ttk�
messagebox�
filedialog)�FigureCanvasTkAgg�NavigationToolbar2Tk)�	Rectangle�Circle�FancyBboxPatch)�LineCollection)�Cursor)�LinearSegmentedColormap)�Axes3D�ignore�2�TF_CPP_MIN_LOG_LEVEL�dark_backgroundz#1a1a1azfigure.facecolorzaxes.facecolorzsavefig.facecolorz#00ff88z#00aaffz#ff8800z#ffaa00z#ff4444z#00ccffz#9900ccz#2d2d2dz#3d3d3dz#ffffffz#888888z#666666)�primary�	secondary�accent�warning�danger�success�info�purple�
background�surface�card�text�text_secondary�
text_mutedz)%(asctime)s - %(levelname)s - %(message)s�ultimate_modular_trading_z%Y%m%dz.log)�level�format�handlersc                   �0   � e Zd ZdZd� Zd� Zd� Zd� Zd� ZdS )�ProcessManagerzRobuste Prozess-Verwaltungc                 �J   � g | _         d| _        | �                    �   �          d S )NF)�active_threads�cleanup_registered�_register_cleanup��selfs    �)E:\Dev\ULTIMATE_MODULAR_TRADING_SYSTEM.py�__init__zProcessManager.__init__�   s*   � � ���"'������ � � � � �    c                 ��   � | j         sst          j        | j        �  �         	 t	          j        t          j        | j        �  �         t	          j        t          j        | j        �  �         n#  Y nxY wd| _         d S d S )NT)rQ   �atexit�register�cleanup�signal�SIGINT�_signal_handler�SIGTERMrS   s    rU   rR   z ProcessManager._register_cleanup�   s{   � ��&� 	+��O�D�L�)�)�)�
��
�f�m�T�-A�B�B�B��
�f�n�d�.B�C�C�C�C��
������&*�D�#�#�#�	+� 	+s   �AA+ �+A/c                 �   � t          j        d|� d��  �         | �                    �   �          t          j        d�  �         d S )NzSignal z empfangen - beende System...r   )�loggingrA   r[   �sys�exit)rT   �signum�frames      rU   r^   zProcessManager._signal_handler�   s<   � ���D�v�D�D�D�E�E�E��������������rW   c                 �:   � | j         �                    |�  �         d S �N)rP   �append�rT   �threads     rU   �
add_threadzProcessManager.add_thread�   s   � ���"�"�6�*�*�*�*�*rW   c                 �   � t          j        d�  �         | j        D ],}|�                    �   �         r|�                    d��  �         �-t          j        d�  �         d S )NzBereinige System...�   ��timeoutzSystem Cleanup abgeschlossen)ra   rA   rP   �is_alive�joinri   s     rU   r[   zProcessManager.cleanup�   sc   � ���*�+�+�+��)� 	'� 	'�F���� � � 
'����A��&�&�&����3�4�4�4�4�4rW   N)	�__name__�
__module__�__qualname__�__doc__rV   rR   r^   rk   r[   � rW   rU   rN   rN   �   se   � � � � � �$�$�!� !� !�
+� +� +�� � �
+� +� +�5� 5� 5� 5� 5rW   rN   c                   �   � e Zd ZdZedededej        fd��   �         Zedede	fd��   �         Z
edej        defd��   �         Zd	S )
�DataProvideru%   Abstract Base Class für Datenquellen�symbol�period�returnc                 �   � d S rg   rv   )rT   ry   rz   s      rU   �get_datazDataProvider.get_data�   �   � ��rW   c                 �   � d S rg   rv   )rT   ry   s     rU   �get_live_pricezDataProvider.get_live_price�   r~   rW   �datac                 �   � d S rg   rv   �rT   r�   s     rU   �
validate_datazDataProvider.validate_data�   r~   rW   N)
rr   rs   rt   ru   r   �str�pd�	DataFramer}   �floatr�   �boolr�   rv   rW   rU   rx   rx   �   s�   � � � � � �/�/��
�s� 
�C� 
�B�L� 
� 
� 
� �^�
� �
�S� 
�U� 
� 
� 
� �^�
� �
�"�,� 
�4� 
� 
� 
� �^�
� 
� 
rW   rx   c            	       �   � e Zd ZdZedej        dej        deee	f         fd��   �         Z
edej        dej        fd��   �         Zedeeef         fd��   �         Z
dS )	�MLModelu#   Abstract Base Class für ML-Modelle�X�yr{   c                 �   � d S rg   rv   )rT   r�   r�   s      rU   �trainz
MLModel.train�   r~   rW   c                 �   � d S rg   rv   )rT   r�   s     rU   �predictzMLModel.predict�   r~   rW   c                 �   � d S rg   rv   rS   s    rU   �get_feature_importancezMLModel.get_feature_importance�   r~   rW   N)rr   rs   rt   ru   r   �np�ndarrayr   r�   r   r�   r�   r�   r�   rv   rW   rU   r�   r�   �   s�   � � � � � �-�-��
�r�z� 
�b�j� 
�T�#�s�(�^� 
� 
� 
� �^�
� �
��� 
��
� 
� 
� 
� �^�
� �
��S�%�Z�(8� 
� 
� 
� �^�
� 
� 
rW   r�   c                   �z   � e Zd ZdZedej        dej        fd��   �         Zedej        dej        fd��   �         Z	dS )�	Indicatoru/   Abstract Base Class für technische Indikatorenr�   r{   c                 �   � d S rg   rv   r�   s     rU   �	calculatezIndicator.calculate�   r~   rW   c                 �   � d S rg   rv   r�   s     rU   �get_signalszIndicator.get_signals�   r~   rW   N)
rr   rs   rt   ru   r   r�   r�   �Seriesr�   r�   rv   rW   rU   r�   r�   �   s|   � � � � � �9�9��
�b�l� 
�r�y� 
� 
� 
� �^�
� �
��� 
��� 
� 
� 
� �^�
� 
� 
rW   r�   c                   �t   � e Zd ZdZedej        dej        ddfd��   �         Z	edej        ddfd��   �         Z
dS )�
Visualizeru)   Abstract Base Class für Visualisierungenr�   �	containerr{   Nc                 �   � d S rg   rv   )rT   r�   r�   s      rU   �create_chartzVisualizer.create_chart�   r~   rW   c                 �   � d S rg   rv   r�   s     rU   �update_chartzVisualizer.update_chart�   r~   rW   )rr   rs   rt   ru   r   r�   r�   �tk�Widgetr�   r�   rv   rW   rU   r�   r�   �   s�   � � � � � �3�3��
��� 
�"�)� 
�� 
� 
� 
� �^�
� �
��� 
�$� 
� 
� 
� �^�
� 
� 
rW   r�   c                   �0  � e Zd ZdZd� Zddededej        fd�Zdededej        fd	�Z	de
eef         fd
�Zdede
dee         fd
�Zdede
eef         fd�Zde
eef         fd�Zde
eef         fd�Zdedefd�Zdej        defd�Zde
eef         fd�ZdS )�ComprehensiveDataProvideru�   
    📡 COMPREHENSIVE DATA PROVIDER
    =============================
    Sammelt Daten von 15+ Quellen für umfassende Analyse
    c           	      ��   � ddddddddd	�| _         d
ddd
�| _        dddd�| _        dddd�| _        i | _        i | _        i | _        i | _        t          d� �  �        | _	        t          j        d�  �         d S )Nz:https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDTz7https://api.coinbase.com/v2/exchange-rates?currency=BTCzzhttps://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_change=true&include_24hr_vol=truez2https://api.kraken.com/0/public/Ticker?pair=XBTUSDz.https://www.bitstamp.net/api/v2/ticker/btcusd/z*https://api.gemini.com/v1/pubticker/btcusdz.https://api-pub.bitfinex.com/v2/ticker/tBTCUSDz9https://api.huobi.pro/market/detail/merged?symbol=btcusdt)�binance�coinbase�	coingecko�kraken�bitstamp�gemini�bitfinex�huobiz%https://api.binance.com/api/v3/klinesz:https://api.exchange.coinbase.com/products/BTC-USD/candlesz$https://api.kraken.com/0/public/OHLC)�binance_klines�coinbase_candles�kraken_ohlczhttps://api.alternative.me/fng/z>https://newsapi.org/v2/everything?q=bitcoin&sortBy=publishedAtz%https://www.reddit.com/r/Bitcoin.json)�
fear_greed�newsapi�reddit_sentimentz!https://api.blockchain.info/statsz;https://api.glassnode.com/v1/metrics/market/price_usd_closez6https://api.coinmetrics.io/v4/timeseries/asset-metrics)�blockchain_info�	glassnode�coinmetricsc                  �   � ddg d�S )Nr   )r@   �error�
response_timerv   rv   rW   rU   �<lambda>z4ComprehensiveDataProvider.__init__.<locals>.<lambda>&  s   � ��Q�Y[�-\�-\� rW   z>Comprehensive Data Provider initialisiert mit 15+ Datenquellen)�
price_apis�
ohlcv_apis�sentiment_apis�onchain_apis�price_cache�ohlcv_cache�sentiment_cache�
onchain_cacher   �	api_statsra   rA   rS   s    rU   rV   z"ComprehensiveDataProvider.__init__   s�   � � T�Q� V�J�H�B�H�P�	
� 	
��� F� \�A�
� 
��� <�W� G�
� 
���  C�V�S�
� 
��� ������!������ %�%\�%\�]�]�����U�V�V�V�V�VrW   �BTC�1yry   rz   r{   c                 ��  � 	 | �                     ||�  �        }| �                    �   �         }|rO|j        sHt          j        t          |�                    �   �         �  �        �  �        |j        |j        d         df<   | �	                    |�  �        }|r|j        s| �
                    ||�  �        }| �                    �   �         }|r]|j        sV|�                    dd�  �        |j        |j        d         df<   |�                    dd�  �        |j        |j        d         df<   | �
                    �   �         }|r;|j        s4|�                    �   �         D ]\  }}	|	|j        |j        d         d	|� �f<   � t          j        d
t#          |�  �        � dt#          |j        �  �        � d��  �         |S # t&          $ r4}
t          j        d
|
� ��  �         t+          j        �   �         cY d}
~
S d}
~
ww xY w)zSammle umfassende Marktdaten������
Live_Pricer�   �2   �	Sentiment�news_sentimentr   �News_Sentiment�OnChain_zUmfassende Daten gesammelt: � Datenpunkte, �	 FeatureszFehler bei Datensammlung: N)�_get_yfinance_data�_get_live_prices�emptyr�   �median�list�values�loc�index�_get_ohlcv_data�_merge_ohlcv_data�_get_sentiment_data�get�_get_onchain_data�itemsra   rA   �len�columns�	Exceptionr�   r�   r�   )rT   ry   rz   �df�live_prices�
ohlcv_data�sentiment_data�onchain_data�key�value�es              rU   r}   z"ComprehensiveDataProvider.get_data*  s   � �	"��(�(���8�8�B� �/�/�1�1�K�� 
[�2�8� 
[�57�Y�t�K�DV�DV�DX�DX�?Y�?Y�5Z�5Z���r�x��|�\�1�2� �-�-�f�5�5�J�� 
<�"�(� 
<��+�+�B�
�;�;�� "�5�5�7�7�N�� 
a�b�h� 
a�4B�4F�4F�|�UW�4X�4X���r�x��|�[�0�1�9G�9K�9K�L\�^_�9`�9`���r�x��|�%5�5�6�  �1�1�3�3�L�� 
C�B�H� 
C�".�"4�"4�"6�"6� C� C�J�C��=B�B�F�2�8�B�<�)9�C�)9�)9�9�:�:��L�i��B���i�i�s�SU�S]���i�i�i�j�j�j��I��� 	"� 	"� 	"��M�:�q�:�:�;�;�;��<�>�>�!�!�!�!�!�!�����	"���s   �F.F1 �1
G/�;)G*�$G/�*G/c                 �~  � 	 t          j        |� d��  �        }|�                    |d��  �        }|j        s4|�                    �   �         }g d�|_        | �                    |�  �        r|S t          j        �   �         S # t          $ r4}t          j        d|� ��  �         t          j        �   �         cY d}~S d}~ww xY w)zHole OHLCV-Daten von yfinancez-USD�1h)rz   �interval��Open�High�Low�Close�Volumezyfinance Fehler: N)�yf�Ticker�historyr�   �dropnar�   r�   r�   r�   r�   ra   r�   )rT   ry   rz   �tickerr�   r�   s         rU   r�   z,ComprehensiveDataProvider._get_yfinance_dataM  s�   � �	"��Y�&����/�/�F����v���=�=�B��8� 
��Y�Y�[�[��G�G�G��
� �%�%�b�)�)� ��I��<�>�>�!��� 	"� 	"� 	"��M�1�a�1�1�2�2�2��<�>�>�!�!�!�!�!�!�����	"���s$   �A(A> �+A> �>
B<�)B7�1B<�7B<c                 ��  � i }| j         �                    �   �         D �]U\  }}	 t          j        �   �         }t          j        |d��  �        }t          j        �   �         |z
  }|j        dk    r�| �                    ||�                    �   �         �  �        }|rWd|cxk    rdk    rJn nG|||<   | j        |         dxx         dz
  cc<   | j        |         d         �	                    |�  �         n7| j        |         d	xx         dz
  cc<   n| j        |         d	xx         dz
  cc<   ��# t          $ r?}| j        |         d	xx         dz
  cc<   t          j        |� d
|� ��  �         Y d}~��Od}~ww xY w|S )z!Sammle Live-Preise von allen APIs�   rn   ��   i'  i � r@   �   r�   r�   z
 API Fehler: N)
r�   r�   �time�requestsr�   �status_code�_parse_price_response�jsonr�   rh   r�   ra   r>   )	rT   r�   �api_name�url�
start_time�responser�   �pricer�   s	            rU   r�   z*ComprehensiveDataProvider._get_live_pricesb  s�  � ���!�_�2�2�4�4� 	?� 	?�M�H�c�
?�!�Y�[�[�
�#�<��Q�7�7�7�� $�	���j� 8�
��'�3�.�.� �6�6�x������Q�Q�E�� ?��%�!9�!9�!9�!9�6�!9�!9�!9�!9�!9�05��H�-���x�0��;�;�;�q�@�;�;�;���x�0��A�H�H��W�W�W�W���x�0��9�9�9�Q�>�9�9�9�9��N�8�,�W�5�5�5��:�5�5�5���� 
?� 
?� 
?���x�(��1�1�1�Q�6�1�1�1���8� =� =�!� =� =�>�>�>�>�>�>�>�>�����
?���� �s   �DD'�'
E0�14E+�+E0r  r�   c                 �  � 	 |dk    rt          |d         �  �        S |dk    r!t          |d         d         d         �  �        S |dk    rt          |d         d	         �  �        S |d
k    rFt          t          |d         �                    �   �         �  �        d         d
         d         �  �        S |dk    rt          |d         �  �        S |dk    rt          |d         �  �        S |dk    rt          |d         �  �        S |dk    rt          |d         d         �  �        S dS # t          $ r%}t	          j        d|� d|� ��  �         Y d}~dS d}~ww xY w)z Parse Preis-Response je nach APIr�   r  r�   r�   �rates�USDr�   �bitcoin�usdr�   �resultr   �cr�   �lastr�   r�   �   r�   �tick�closeu   Parse-Fehler für �: N)r�   r�   r�   r�   ra   r>   )rT   r  r�   r�   s       rU   r  z/ComprehensiveDataProvider._parse_price_response}  s{  � �	��9�$�$��T�'�]�+�+�+��Z�'�'��T�&�\�'�2�5�9�:�:�:��[�(�(��T�)�_�U�3�4�4�4��X�%�%��T�$�x�.�"7�"7�"9�"9�:�:�1�=�c�B�1�E�F�F�F��Z�'�'��T�&�\�*�*�*��X�%�%��T�&�\�*�*�*��Z�'�'��T�!�W�~�~�%��W�$�$��T�&�\�'�2�3�3�3� %�$�� � 	� 	� 	��O�@��@�@�Q�@�@�A�A�A��4�4�4�4�4�����	���sA   �D% �&D% � D% �%AD% �1D% �D% �'D% � D% �%
E�/E�Ec                 ��  � i }	 | j         d         � d�}t          j        |d��  �        }|j        dk    r�|�                    �   �         }t          j        |g d���  �        }t          j        |d         d	�
�  �        |d<   |�                    d�  �        }|g d�         �	                    t          �  �        }||d<   n.# t          $ r!}t          j
        d
|� ��  �         Y d}~nd}~ww xY w|S )zSammle OHLCV-Daten von APIsr�   z%?symbol=BTCUSDT&interval=1h&limit=100�
   rn   r�   )�	timestamp�open�high�lowr  �volume�
close_time�quote_volume�trades�taker_buy_base�taker_buy_quoter7   �r�   r  �ms)�unit)r  r  r  r  r  r�   zBinance OHLCV Fehler: N)r�   r�   r�   r   r  r�   r�   �to_datetime�	set_index�astyper�   r�   ra   r>   )rT   ry   r�   r  r  �klinesr�   r�   s           rU   r�   z)ComprehensiveDataProvider._get_ohlcv_data�  s   � ��
�	:��_�%5�6�]�]�]�C��|�C��4�4�4�H��#�s�*�*�!�������\�&� 3� 3� 3� � � �� #%�.��K��t�"L�"L�"L��;���\�\�+�.�.���B�B�B�C�J�J�5�Q�Q��(*�
�9�%���� 	:� 	:� 	:��O�8�Q�8�8�9�9�9�9�9�9�9�9�����	:���� �s   �B8B= �=
C(�C#�#C(c                 �x  � i }	 t          j        | j        d         d��  �        }|j        dk    r8|�                    �   �         }t          |d         d         d         �  �        |d<   n.# t          $ r!}t          j        d|� ��  �         Y d	}~nd	}~ww xY wt          j
        �                    d
d�  �        |d<   |S )
zSammle Sentiment-Datenr�   r�   rn   r�   r�   r   r�   zFear & Greed API Fehler: Nr�   r�   r�   )r�   r�   r�   r   r  �intr�   ra   r>   r�   �random�uniform)rT   r�   r  r�   r�   s        rU   r�   z-ComprehensiveDataProvider._get_sentiment_data�  s�   � ���	=��|�D�$7��$E�q�Q�Q�Q�H��#�s�*�*��}�}����/2�4��<��?�7�3K�/L�/L��|�,���� 	=� 	=� 	=��O�;��;�;�<�<�<�<�<�<�<�<�����	=���� ,.�9�+<�+<�R��+C�+C��'�(��s   �A$A) �)
B�3B�Bc                 �  � i }	 t          j        | j        d         d��  �        }|j        dk    rb|�                    �   �         }|�                    dd�  �        |d<   |�                    dd�  �        |d<   |�                    dd�  �        d	z  |d
<   n.# t
          $ r!}t
          j        d|� ��  �         Y d}~nd}~ww xY w|S )
zSammle On-Chain-Datenr�   r�   rn   r�   �	hash_rater   �
difficulty�totalbci ���	total_btczBlockchain.info API Fehler: N)r�   r�   r�   r   r  r�   ra   r>   )rT   r�   r  r�   r�   s        rU   r�   z+ComprehensiveDataProvider._get_onchain_data�  s�   � ���		@��|�D�$5�6G�$H�RS�T�T�T�H��#�s�*�*��}�}����,0�H�H�[�!�,D�,D��[�)�-1�X�X�l�A�-F�-F��\�*�,0�H�H�Y��,B�,B�Y�,N��[�)���� 	@� 	@� 	@��O�>�1�>�>�?�?�?�?�?�?�?�?�����	@���� �s   �BB �
B>�B9�9B>c                 �   � | �                     �   �         }|r3t          j        t          |�                    �   �         �  �        �  �        S dS )zHole aktuellen Live-Preisg        )r�   r�   r�   r�   r�   )rT   ry   r�   s      rU   r�   z(ComprehensiveDataProvider.get_live_price�  sD   � ��+�+�-�-��� 	9��9�T�+�"4�"4�"6�"6�7�7�8�8�8��srW   c                 �X  �� �j         rdS g d�}t          �fd�|D �   �         �  �        sdS �d         j        d         dk     s�d         j        d         dk    rdS ��                    �   �         �                    �   �         �                    �   �         t          ��  �        dz  k    rdS d	S )
u   Validiere DatenqualitätF)r�   r�   r�   r�   c              3   �*   �K  � | ]
}|�j         v V � �d S rg   r   )�.0�colr�   s     �rU   �	<genexpr>z:ComprehensiveDataProvider.validate_data.<locals>.<genexpr>�  s*   �� � � �C�C�3�3�$�,�&�C�C�C�C�C�CrW   r�   r�   ��  i@B 皙�����?T)r�   �all�iloc�isnull�sumr�   )rT   r�   �required_columnss    ` rU   r�   z'ComprehensiveDataProvider.validate_data�  s�   �� ��:� 	��5�;�;�;���C�C�C�C�2B�C�C�C�C�C� 	��5� ��=��b�!�D�(�(�D��M�,>�r�,B�W�,L�,L��5� �;�;�=�=�����"�"�$�$�s�4�y�y�3��6�6��5��trW   c                 ��   � i }| j         �                    �   �         D ]_\  }}|d         |d         z   }|d         t          |d�  �        z  dz  }|d         rt          j        |d         �  �        nd}|||d�||<   �`|S )zHole API-Statistikenr@   r�   r�   �d   r�   r   )�success_rate�total_calls�avg_response_time)r�   r�   �maxr�   �mean)rT   �statsr  r�   r@  r?  rA  s          rU   �get_api_statisticsz,ComprehensiveDataProvider.get_api_statistics�  s�   � ���#'�>�#7�#7�#9�#9� 		� 		��H�i�#�I�.��7�1C�C�K�$�Y�/�#�k�1�2E�2E�E��K�L�GP�Q`�Ga� h���	�/�(B� C� C� C�gh�� !-�*�%6�� �E�(�O�O� �rW   N�r�   r�   )rr   rs   rt   ru   rV   r�   r�   r�   r}   r�   r   r�   r�   �dictr   r  r   r�   r�   r�   r�   r�   r�   rE  rv   rW   rU   r�   r�   �   s�  � � � � � �� �(W� (W� (W�T!"� !"�s� !"�C� !"�2�<� !"� !"� !"� !"�F"�� "�c� "�b�l� "� "� "� "�*�$�s�E�z�"2� � � � �6�c� �� �(�5�/� � � � �0�c� �d�3��8�n� � � � �8�T�#�s�(�^� � � � �&�4��S��>� � � � �$�S� �U� � � � ��"�,� �4� � � � �&�D��c��N� � � � � � rW   r�   c                   �   � e Zd ZdZd� Zdej        dej        deee	f         fd�Z
dej        dej        fd�Zdeeef         fd�Z
d	S )
�RandomForestModelzRandom Forest Classifierc                 ��   � t          |�                    dd�  �        |�                    dd�  �        d��  �        | _        t          �   �         | _        i | _        i | _        d S )N�n_estimatorsr>  �	max_depthr  �*   )rK  rL  �random_state)r   r�   �modelr   �scaler�feature_importance_�performance_metrics�rT   �kwargss     rU   rV   zRandomForestModel.__init__  sb   � �+����N�C�8�8��j�j��b�1�1��
� 
� 
��
�
 %�&�&���#%�� �#%�� � � rW   r�   r�   r{   c                 ��  � 	 | j         �                    |�  �        }| j        �                    ||�  �         t	          | j        ||dd��  �        }t          t
          | j        j        �  �        �  �        | _        |�	                    �   �         |�
                    �   �         |j        d         t          |�  �        d�| _
        | j
        S # t          $ r#}t          j        d|� ��  �         i cY d }~S d }~ww xY w)N�   �accuracy��cv�scoringr�   ��cv_mean�cv_std�
feature_count�training_sampleszRandom Forest Training Fehler: )rP  �
fit_transformrO  �fitr   rG  �	enumerate�feature_importances_rQ  rC  �std�shaper�   rR  r�   ra   r�   �rT   r�   r�   �X_scaled�	cv_scoresr�   s         rU   r�   zRandomForestModel.train  s�   � �	��{�0�0��3�3�H� 
�J�N�N�8�Q�'�'�'� (��
�H�a�A�z�Z�Z�Z�I� (,�I�d�j�6U�,V�,V�'W�'W�D�$� %�>�>�+�+�#�-�-�/�/�!"����$'��F�F�	(� (�D�$� �+�+��� 	� 	� 	��M�?�A�?�?�@�@�@��I�I�I�I�I�I�����	���s   �CC
 �

C7�C2�,C7�2C7c                 ��   � 	 | j         �                    |�  �        }| j        �                    |�  �        S # t          $ r5}t          j        d|� ��  �         t          j        g �  �        cY d }~S d }~ww xY w)Nz!Random Forest Prediction Fehler: �	rP  �	transformrO  �
predict_probar�   ra   r�   r�   �array�rT   r�   rg  r�   s       rU   r�   zRandomForestModel.predict.  s�   � �	 ��{�,�,�Q�/�/�H��:�+�+�H�5�5�5��� 	 � 	 � 	 ��M�A�a�A�A�B�B�B��8�B�<�<�����������	 ����   �36 �
A5� *A0�*A5�0A5c                 �   � | j         S rg   �rQ  rS   s    rU   r�   z(RandomForestModel.get_feature_importance6  �   � ��'�'rW   N�rr   rs   rt   ru   rV   r�   r�   r   r�   r   r�   r�   r�   r�   rv   rW   rU   rI  rI    s�   � � � � � �"�"�&� &� &��r�z� �b�j� �T�#�s�(�^� � � � �8 ���  ��
�  �  �  �  �(��S�%�Z�(8� (� (� (� (� (� (rW   rI  c                   �   � e Zd ZdZd� Zdej        dej        deee	f         fd�Z
dej        dej        fd�Zdeeef         fd�Z
d	S )
�XGBoostModelu%   XGBoost Classifier (falls verfügbar)c                 �  � t           rZt          j        |�                    dd�  �        |�                    dd�  �        |�                    dd�  �        d��  �        | _        nTt          |�                    dd�  �        |�                    dd�  �        |�                    dd�  �        d��  �        | _        t
          �   �         | _        i | _        i | _	        d S )	NrK  r>  rL  r  �
learning_rater7  rM  )rK  rL  rw  rN  )
�XGBOOST_AVAILABLE�xgb�
XGBClassifierr�   rO  r   r   rP  rQ  rR  rS  s     rU   rV   zXGBoostModel.__init__<  s�   � �� 	��*�#�Z�Z���<�<� �*�*�[�!�4�4�$�j�j��#�>�>��	� � �D�J�J� 4�#�Z�Z���<�<� �*�*�[�!�4�4�$�j�j��#�>�>��	� � �D�J� #�n�n���#%�� �#%�� � � rW   r�   r�   r{   c                 �  � 	 | j         �                    |�  �        }| j        �                    ||�  �         t	          | j        ||dd��  �        }t          | j        d�  �        r+t
          t          | j        j        �  �        �  �        | _	        |�
                    �   �         |�                    �   �         |j        d         t          |�  �        d�| _        | j        S # t          $ r#}t!          j        d|� ��  �         i cY d }~S d }~ww xY w)NrV  rW  rX  rc  r�   r[  zXGBoost Training Fehler: )rP  r`  rO  ra  r   �hasattrrG  rb  rc  rQ  rC  rd  re  r�   rR  r�   ra   r�   rf  s         rU   r�   zXGBoostModel.trainQ  s  � �	��{�0�0��3�3�H��J�N�N�8�Q�'�'�'�'��
�H�a�A�z�Z�Z�Z�I��t�z�#9�:�:� 
\�+/�	�$�*�:Y�0Z�0Z�+[�+[��(� %�>�>�+�+�#�-�-�/�/�!"����$'��F�F�	(� (�D�$� �+�+��� 	� 	� 	��M�9�a�9�9�:�:�:��I�I�I�I�I�I�����	���s   �CC �
D�)D�D�Dc                 ��   � 	 | j         �                    |�  �        }| j        �                    |�  �        S # t          $ r5}t          j        d|� ��  �         t          j        g �  �        cY d }~S d }~ww xY w)NzXGBoost Prediction Fehler: rj  rn  s       rU   r�   zXGBoostModel.predicth  s�   � �	 ��{�,�,�Q�/�/�H��:�+�+�H�5�5�5��� 	 � 	 � 	 ��M�;��;�;�<�<�<��8�B�<�<�����������	 ���ro  c                 �   � | j         S rg   rq  rS   s    rU   r�   z#XGBoostModel.get_feature_importancep  rr  rW   Nrs  rv   rW   rU   ru  ru  9  s�   � � � � � �/�/�&� &� &�*�r�z� �b�j� �T�#�s�(�^� � � � �. ���  ��
�  �  �  �  �(��S�%�Z�(8� (� (� (� (� (� (rW   ru  c                   �   � e Zd ZdZd� Zd� Zdej        dej        dee	e
f         fd�Zdej        dej        fd	�Zdej        dej        fd
�Z
dee	ef         fd�ZdS )
�	LSTMModelu1   LSTM Neural Network (falls TensorFlow verfügbar)c                 ��   � |�                     dd�  �        | _        |�                     dd�  �        | _        d | _        t	          �   �         | _        i | _        i | _        t          r| �	                    �   �          d S d S )N�sequence_length�<   �featuresr  )
r�   r�  r�  rO  r   rP  rQ  rR  �TENSORFLOW_AVAILABLE�_build_modelrS  s     rU   rV   zLSTMModel.__init__v  sz   � �%�z�z�*;�R�@�@����
�
�:�r�2�2��
���
�"�n�n���#%�� �#%�� �� 	 ���������	 � 	 rW   c                 �  � 	 t          t          dd| j        | j        f��  �        t	          d�  �        t          dd��  �        t	          d�  �        t          d�  �        t          dd	�
�  �        g�  �        | _        | j        �                    t          d��  �        d
dg��  �         dS # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)zBaue LSTM-Modellr�   T)�return_sequences�input_shape皙�����?F)r�  �   rm   �softmax)�
activationg����MbP?)rw  �sparse_categorical_crossentropyrW  )�	optimizer�loss�metricszLSTM Model Build Fehler: N)r$   r%   r�  r�  r'   r&   rO  �compiler*   r�   ra   r�   )rT   r�   s     rU   r�  zLSTMModel._build_model�  s  � �	;�#��R�$�T�=Q�SW�S`�<a�b�b�b������R�%�0�0�0������b�	�	��a�I�.�.�.�
%� � �D�J� 
�J����U�3�3�3�6�#�� 
� 
� 
� 
� 
� 
�� � 	;� 	;� 	;��M�9�a�9�9�:�:�:�:�:�:�:�:�:�����	;���s   �B'B+ �+
C�5C�Cr�   r�   r{   c                 �H  � 	 t           r| j        �dddd�S | j        �                    |�  �        }| �                    |�  �        }t          |�  �        dk     rdddd�S | j        �                    ||d t          |�  �        �         ddd	d
��  �        }d|j        v rt          |j        d         �  �        nd}|d
|j	        d         t          |�  �        d�| _
        | j
        S # t          $ r4}t          j
        d|� ��  �         ddt          |�  �        d�cY d }~S d }~ww xY w)N�      �?r7  u   TensorFlow nicht verfügbar)r\  r]  �noter  u   Zu wenig Daten für LSTMr�   �    r   r�  )�epochs�
batch_size�verbose�validation_split�val_accuracyg�������?r�   r[  zLSTM Training Fehler: )r\  r]  r�   )r�  rO  rP  r`  �_create_sequencesr�   ra  r�   rB  re  rR  r�   ra   r�   r�   )rT   r�   r�   rg  �X_sequencesr�   r�  r�   s           rU   r�   zLSTMModel.train�  sx  � � 	D�'� 
^�4�:�+=�#&�#�?\�]�]�]� �{�0�0��3�3�H��0�0��:�:�K��;���"�$�$�#&�#�?Y�Z�Z�Z� �j�n�n��Q�0��K� 0� 0�0�1��b�!�!$� %� � �G� DR�U\�Ud�Cd�Cd�3�w��~�>�?�?�?�jm�L� (��!"����$'��$4�$4�	(� (�D�$� �+�+��� 	D� 	D� 	D��M�6�1�6�6�7�7�7�"�c�C��F�F�C�C�C�C�C�C�C�C�����	D���s*   �C# �AC# �BC# �#
D!�-)D�D!�D!r�   c                 ��   � g }t          | j        t          |�  �        �  �        D ]'}|�                    ||| j        z
  |�         �  �         �(t	          j        |�  �        S )u   Erstelle Sequenzen für LSTM)�ranger�  r�   rh   r�   rm  )rT   r�   �	sequences�is       rU   r�  zLSTMModel._create_sequences�  sb   � ��	��t�+�S��Y�Y�7�7� 	=� 	=�A����T�!�D�$8�"8��":�;�<�<�<�<��x�	�"�"�"rW   c                 ��  � 	 t           r| j        �t          j        ddgg�  �        S | j        �                    |�  �        }| �                    |�  �        }t          |�  �        dk    rt          j        ddgg�  �        S | j        �                    |dd �         d��  �        S # t          $ r8}t          j        d|� ��  �         t          j        ddgg�  �        cY d }~S d }~ww xY w)Nr�  r   r�   )r�  zLSTM Prediction Fehler: )r�  rO  r�   rm  rP  rk  r�  r�   r�   r�   ra   r�   )rT   r�   rg  r�  r�   s        rU   r�   zLSTMModel.predict�  s�   � �	*�'� 
.�4�:�+=��x�#�s���-�-�-��{�,�,�Q�/�/�H��0�0��:�:�K��;���1�$�$��x�#�s���-�-�-��:�%�%�k�"�#�#�&6��%�B�B�B��� 	*� 	*� 	*��M�8�Q�8�8�9�9�9��8�c�3�Z�L�)�)�)�)�)�)�)�)�����	*���s)   �$B$ �AB$ � #B$ �$
C&�.-C!�C&�!C&c                 �D   � � � fd�t          � j        �  �        D �   �         S )Nc                 �,   �� i | ]}d |� �d�j         z  ��S )�feature_g      �?)r�  )r3  r�  rT   s     �rU   �
<dictcomp>z4LSTMModel.get_feature_importance.<locals>.<dictcomp>�  s(   �� �P�P�P�a��1����D�M� 1�P�P�PrW   )r�  r�  rS   s   `rU   r�   z LSTMModel.get_feature_importance�  s'   �� �P�P�P�P�5���;O�;O�P�P�P�PrW   N)rr   rs   rt   ru   rV   r�  r�   r�   r   r�   r   r�   r�  r�   r�   r�   rv   rW   rU   r�  r�  s  s�   � � � � � �;�;�	 � 	 � 	 �;� ;� ;�*!D�r�z� !D�b�j� !D�T�#�s�(�^� !D� !D� !D� !D�F#�b�j� #�R�Z� #� #� #� #�*��� *��
� *� *� *� *�"Q��S�%�Z�(8� Q� Q� Q� Q� Q� QrW   r�  c                   �h   � e Zd ZdZd
defd�Zdej        dej        fd�Z	dej        dej        fd�Z
d	S )�RSIIndicatorzRelative Strength Index�   rz   c                 �   � || _         d S rg   )rz   )rT   rz   s     rU   rV   zRSIIndicator.__init__�  s
   � �����rW   r�   r{   c                 �P  � 	 |d         }|�                     �   �         }|�                    |dk    d�  �        �                    | j        ��  �        �                    �   �         }|�                    |dk     d�  �         �                    | j        ��  �        �                    �   �         }||z  }ddd|z   z  z
  }|�                    d�  �        S # t          $ rM}t          j        d|� ��  �         t          j
        dgt          |�  �        z  |j        ��  �        cY d	}~S d	}~ww xY w)
zBerechne RSIr�   r   ��windowr>  r�   r�   zRSI Berechnung Fehler: �r�   N)
�diff�where�rollingrz   rC  �fillnar�   ra   r�   r�   r�   r�   r�   )	rT   r�   �prices�delta�gainr�  �rs�rsir�   s	            rU   r�   zRSIIndicator.calculate�  s'  � �	A��'�]�F��K�K�M�M�E��K�K���	�1�-�-�6�6�d�k�6�J�J�O�O�Q�Q�D��[�[����A�.�.�.�7�7�t�{�7�K�K�P�P�R�R�D����B����B���(�C��:�:�b�>�>�!��� 	A� 	A� 	A��M�7�A�7�7�8�8�8��9�b�T�C��I�I�-�T�Z�@�@�@�@�@�@�@�@�@�����	A���s   �CC �
D%�AD �D%� D%c                 �   � | �                     |�  �        }t          j        d|j        ��  �        }d||dk     <   d||dk    <   |S )zGeneriere RSI-Signaler   r�  r�   �   r�   �F   �r�   r�   r�   r�   )rT   r�   r�  �signalss       rU   r�   zRSIIndicator.get_signals�  sJ   � ��n�n�T�"�"���)�A�T�Z�0�0�0�����b������b����rW   N)r�  )rr   rs   rt   ru   r(  rV   r�   r�   r�   r�   r�   rv   rW   rU   r�  r�  �  s�   � � � � � �!�!�� �s� � � � �A�b�l� A�r�y� A� A� A� A�$��� ��� � � � � � rW   r�  c                   �p   � e Zd ZdZddededefd�Zd	ej        d
ej        fd�Zd	ej        d
ej	        fd�Z
d
S )�
MACDIndicatorz%Moving Average Convergence Divergence�   �   �	   �fast�slowr\   c                 �0   � || _         || _        || _        d S rg   )r�  r�  r\   )rT   r�  r�  r\   s       rU   rV   zMACDIndicator.__init__�  s   � ���	���	�����rW   r�   r{   c           	      �f  � 	 |d         }|�                     | j        ��  �        �                    �   �         }|�                     | j        ��  �        �                    �   �         }||z
  }|�                     | j        ��  �        �                    �   �         }||z
  }t          j        |||d��  �        S # t          $ rs}t          j	        d|� ��  �         t          j        dgt          |�  �        z  dgt          |�  �        z  dgt          |�  �        z  d�|j        ��  �        cY d}~S d}~ww xY w)z
Berechne MACDr�   ��span)�MACD�Signal�	HistogramzMACD Berechnung Fehler: r   r�  N)�ewmr�  rC  r�  r\   r�   r�   r�   ra   r�   r�   r�   )	rT   r�   r�  �ema_fast�ema_slow�	macd_line�signal_line�	histogramr�   s	            rU   r�   zMACDIndicator.calculate  sX  � �	!��'�]�F��z�z�t�y�z�1�1�6�6�8�8�H��z�z�t�y�z�1�1�6�6�8�8�H� �8�+�I�#�-�-�T�[�-�9�9�>�>�@�@�K�!�K�/�I��<�!�%�&�!� !� � � 
�� � 	!� 	!� 	!��M�8�Q�8�8�9�9�9��<���c�$�i�i���#��D�	�	�/��S�3�t�9�9�_�!� !� �Z�	!� !� !� 
!� 
!� 
!� 
!� 
!� 
!�����	!���s   �B0B3 �3
D0�=A(D+�%D0�+D0c                 �  � | �                     |�  �        }t          j        d|j        ��  �        }|d         |d         k    |d         �                    d�  �        |d         �                    d�  �        k    z  }|d         |d         k     |d         �                    d�  �        |d         �                    d�  �        k    z  }d||<   d||<   |S )zGeneriere MACD-Signaler   r�  r�  r�  r�   r�   )r�   r�   r�   r�   �shift)rT   r�   �	macd_datar�  �
bullish_cross�
bearish_crosss         rU   r�   zMACDIndicator.get_signals  s�   � ��N�N�4�(�(�	��)�A�T�Z�0�0�0�� #�6�*�Y�x�-@�@�!�&�)�/�/��2�2�i��6I�6O�6O�PQ�6R�6R�R�T�
� #�6�*�Y�x�-@�@�!�&�)�/�/��2�2�i��6I�6O�6O�PQ�6R�6R�R�T�
� "#��
��!#��
���rW   N)r�  r�  r�  )rr   rs   rt   ru   r(  rV   r�   r�   r�   r�   r�   rv   rW   rU   r�  r�  �  s�   � � � � � �/�/�� �S� �S� �s� � � � �
!�b�l� !�r�|� !� !� !� !�4��� ��� � � � � � rW   r�  c                   �l   � e Zd ZdZddedefd�Zdej        dej        fd	�Z	dej        dej
        fd
�ZdS )
�BollingerBandsIndicatorzBollinger Bands�   rm   rz   �std_devc                 �"   � || _         || _        d S rg   )rz   r�  )rT   rz   r�  s      rU   rV   z BollingerBandsIndicator.__init__3  s   � ��������rW   r�   r{   c           
      �L  � 	 |d         }|�                     | j        ��  �        �                    �   �         }|�                     | j        ��  �        �                    �   �         }||| j        z  z   }||| j        z  z
  }||z
  ||z
  z  }t          j        ||||�                    d�  �        d��  �        S # t          $ rd}t          j
        d|� ��  �         t          j        |d         |d         |d         dgt          |�  �        z  d�|j        ��  �        cY d}~S d}~ww xY w)zBerechne Bollinger Bandsr�   r�  r�  )�Upper�Middle�Lower�Positionz#Bollinger Bands Berechnung Fehler: r�  N)
r�  rz   rC  rd  r�  r�   r�   r�  r�   ra   r�   r�   r�   )	rT   r�   r�  �smard  �
upper_band�
lower_band�bb_positionr�   s	            rU   r�   z!BollingerBandsIndicator.calculate7  sd  � �	!��'�]�F��.�.���.�4�4�9�9�;�;�C��.�.���.�4�4�8�8�:�:�C���d�l� 2�3�J���d�l� 2�3�J� "�J�.�:�
�3J�K�K��<�#��#�'�.�.�s�3�3�	!� !� � � 
�� � 	!� 	!� 	!��M�C��C�C�D�D�D��<��g���w�-��g�� �E�C��I�I�-�	!� !�
 �Z�!� !� !� 
!� 
!� 
!� 
!� 
!� 
!�����	!���s   �B2B5 �5
D#�?AD�D#�D#c                 �   � | �                     |�  �        }|d         }t          j        d|j        ��  �        }d|||d         k    <   d|||d         k    <   |S )z!Generiere Bollinger Bands-Signaler�   r   r�  r�   r�  r�   r�  r�  )rT   r�   �bb_datar�  r�  s        rU   r�   z#BollingerBandsIndicator.get_signalsU  sc   � ��.�.��&�&���g����)�A�T�Z�0�0�0�� /0���'�'�*�*�+� /1���'�'�*�*�+��rW   N)r�  rm   )rr   rs   rt   ru   r(  r�   rV   r�   r�   r�   r�   r�   rv   rW   rU   r�  r�  0  s�   � � � � � ���� �s� �%� � � � �!�b�l� !�r�|� !� !� !� !�<
��� 
��� 
� 
� 
� 
� 
� 
rW   r�  c                   �  � e Zd ZdZd� Zdej        dej        fd�Zdej        dej        fd�Zdej        dej        fd�Z	dej        d	e
dej        fd
�Zdej        dej        fd�Z
dej        dej        fd�Zdej        dej        fd
�Zdej        dej        fd�Zdej        dej        fd�Zdej        deej        ee         f         fd�ZdS )�
FeatureEngineu�   
    🔧 COMPREHENSIVE FEATURE ENGINEERING
    ===================================
    Erstellt 50+ Features für ML-Training
    c                 �   � t          d�  �        t          d�  �        t          �   �         t          d�  �        t          d�  �        d�| _        g | _        d S )Nr�  �   r�  r  )�rsi_14�rsi_21�macd�bb_20�bb_10)r�  r�  r�  �
indicators�
feature_namesrS   s    rU   rV   zFeatureEngine.__init__o  sS   � �"�2�&�&�"�2�&�&�!�O�O�,�R�0�0�,�R�0�0�
� 
���  ����rW   r�   r{   c                 �
  � 	 |j         st          |�  �        dk     r't          j        d�  �         t	          j        �   �         S |�                    �   �         }| �                    |�  �        }| �                    |�  �        }| �	                    |�  �        }| �
                    |�  �        }| �                    |�  �        }| �                    |�  �        }| �
                    |�  �        }|�                    d��  �        �                    d�  �        }d� |j        D �   �         | _        t          j        dt          | j        �  �        � d��  �         |S # t$          $ r#}t          j        d	|� ��  �         |cY d
}~S d
}~ww xY w)zErstelle umfassende Featuresr�   u'   Zu wenig Daten für Feature Engineering�ffill)�methodr   c                 �   � g | ]}|d v�|��	S )r�   rv   �r3  r4  s     rU   �
<listcomp>z1FeatureEngine.create_features.<locals>.<listcomp>�  s#   � �!~�!~�!~�#��S}�H}�H}�#�H}�H}�H}rW   zFeatures erstellt: r�   zFeature Engineering Fehler: N)r�   r�   ra   r>   r�   r�   �copy�_add_price_features�_add_technical_indicators�_add_volume_features�_add_volatility_features�_add_momentum_features�_add_time_features�_add_external_featuresr�  r�   r�  rA   r�   r�   )rT   r�   �features_dfr�   s       rU   �create_featureszFeatureEngine.create_featuresz  s�  � �'	��z� 
&�S��Y�Y��^�^��� I�J�J�J��|�~�~�%��)�)�+�+�K� �2�2�;�?�?�K� �8�8��E�E�K� �3�3�K�@�@�K� �7�7��D�D�K� �5�5�k�B�B�K� �1�1�+�>�>�K� �5�5�k�B�B�K� &�,�,�G�,�<�<�C�C�A�F�F�K� "�!~��1D�!~�!~�!~�D���L�Q�s�4�3E�/F�/F�Q�Q�Q�R�R�R����� 	� 	� 	��M�<��<�<�=�=�=��K�K�K�K�K�K�����	���s%   �A E �DE �
F�E=�7F�=Fr�   c                 �2  � 	 |d         �                     d�  �        |d<   |d         �                     d�  �        |d<   |d         �                     d�  �        |d<   |d         �                     d�  �        |d	<   d
D ]�}|d         �                    |�  �        �                    �   �         |d|� �<   |d         �                    |��  �        �                    �   �         |d
|� �<   |d         |d|� �         z  |d|� d�<   ��|d         |d         z  |d<   |d         |d         z  |d<   |d         |d         z  |d<   |d         |d         �                    d�  �        �                    �   �         z
  |d         �                    d�  �        �                    �   �         |d         �                    d�  �        �                    �   �         z
  z  |d<   |d         |d         �                    d�  �        �                    �   �         z
  |d         �                    d�  �        �                    �   �         |d         �                    d�  �        �                    �   �         z
  z  |d<   |S # t          $ r#}t          j        d|� ��  �         |cY d}~S d}~ww xY w)u#   Füge Preis-basierte Features hinzur�   r�   �Return_1r�   �Return_5r  �	Return_10r�  �	Return_20)r�   r  r�  r�   �SMA_r�  �EMA_�
Price_SMA_�_Ratior�   r�   �High_Low_Ratio�Close_High_Ratio�Close_Low_Ratio�Price_Position_5�Price_Position_20zPreis-Features Fehler: N)	�
pct_changer�  rC  r�  �minrB  r�   ra   r�   �rT   r�   rz   r�   s       rU   r�  z!FeatureEngine._add_price_features�  s�  � �	���[�3�3�A�6�6�B�z�N���[�3�3�A�6�6�B�z�N� ��k�4�4�R�8�8�B�{�O� ��k�4�4�R�8�8�B�{�O� *� 
T� 
T��&(��k�&9�&9�&�&A�&A�&F�&F�&H�&H��?�&�?�?�#�&(��k�o�o�6�o�&B�&B�&G�&G�&I�&I��?�&�?�?�#�24�W�+��?�&�?�?�@S�2S��.��.�.�.�/�/� $&�f�:��5�	�#9�B�� �%'��[�2�f�:�%=�B�!�"�$&�w�K�"�U�)�$;�B� �!� ')��k�B�u�I�4E�4E�a�4H�4H�4L�4L�4N�4N�&N�$&�v�J�$6�$6�q�$9�$9�$=�$=�$?�$?�"�U�)�BS�BS�TU�BV�BV�BZ�BZ�B\�B\�$\�&^�B�!�"�')�'�{�R��Y�5F�5F�r�5J�5J�5N�5N�5P�5P�'P�%'��Z�%7�%7��%;�%;�%?�%?�%A�%A�B�u�I�DU�DU�VX�DY�DY�D]�D]�D_�D_�%_�'a�B�"�#� �I��� 	� 	� 	��M�7�A�7�7�8�8�8��I�I�I�I�I�I�����	���s   �I&I) �)
J�3J�J�Jc                 �&  � 	 | j         �                    �   �         D ]a\  }}d|v rX|�                    |�  �        ||�                    �   �         � <   |�                    |�  �        ||�                    �   �         � d�<   �b| j         d         �                    |�  �        }|d         |d<   |d         |d<   |d         |d<   | j         d         �                    |�  �        |d	<   | j         �                    �   �         D ]�\  }}d
|v r�|�                    |�  �        }|d         ||�                    �   �         � d�<   |d
         |d         z
  |d         z  ||�                    �   �         � d�<   |�                    |�  �        ||�                    �   �         � d�<   ��| �                    |d�  �        |d<   |d         �                    d�  �        �                    �   �         |d<   |S # t          $ r#}t          j
        d|� ��  �         |cY d}~S d}~ww xY w)u"   Füge technische Indikatoren hinzur�  �_Signalr�  r�  r�  �MACD_Signalr�  �MACD_Histogram�MACD_Signal_Flag�bbr�  �	_Positionr�  r�  r�  �_Widthr�  �Stoch_KrV  �Stoch_DzTechnische Indikatoren Fehler: N)r�  r�   r�   �upperr�   �_calculate_stochasticr�  rC  r�   ra   r�   )rT   r�   �name�	indicatorr�  r�  r�   s          rU   r�  z'FeatureEngine._add_technical_indicators�  s8  � �	�#'�?�#8�#8�#:�#:� 
M� 
M���i��D�=�=�,5�,?�,?��,C�,C�B�$�*�*�,�,�(�)�3<�3H�3H��3L�3L�B�$�*�*�,�,�/�/�/�0�� ���/�9�9�"�=�=�I�"�6�*�B�v�J� )�(� 3�B�}��#,�[�#9�B�� �%)�_�V�%<�%H�%H��%L�%L�B�!�"� $(�?�#8�#8�#:�#:� 
M� 
M���i��4�<�<�'�1�1�"�5�5�G�5<�Z�5H�B�$�*�*�,�,�1�1�1�2�3:�7�3C�g�g�FV�3V�Za�bj�Zk�2k�B�$�*�*�,�,�.�.�.�/�3<�3H�3H��3L�3L�B�$�*�*�,�,�/�/�/�0�� !�6�6�r�2�>�>�B�y�M��y�M�1�1�!�4�4�9�9�;�;�B�y�M��I��� 	� 	� 	��M�?�A�?�?�@�@�@��I�I�I�I�I�I�����	���s   �G G# �#
H�-H�H�Hrz   c                 �  � 	 |d         �                     |�  �        �                    �   �         }|d         �                     |�  �        �                    �   �         }d|d         |z
  z  ||z
  z  }|�                    d�  �        S # t          $ rM}t          j        d|� ��  �         t          j        dgt          |�  �        z  |j
        ��  �        cY d}~S d}~ww xY w)	zBerechne Stochastic Oscillatorr�   r�   r>  r�   r�   zStochastic Berechnung Fehler: r�  N)r�  r  rB  r�  r�   ra   r�   r�   r�   r�   r�   )rT   r�   rz   �low_min�high_max�stoch_kr�   s          rU   r  z#FeatureEngine._calculate_stochastic�  s�   � �		=���i�'�'��/�/�3�3�5�5�G��&�z�)�)�&�1�1�5�5�7�7�H��R��[�7�2�3�x�'�7I�J�G��>�>�"�%�%�%��� 	=� 	=� 	=��M�>�1�>�>�?�?�?��9�b�T�C��G�G�^�2�8�<�<�<�<�<�<�<�<�<�����	=���s   �BB �
C�AC�C�Cc                 ��  � 	 d|j         vrd|d<   |d         �                    d�  �        �                    �   �         |d<   |d         �                    d�  �        �                    �   �         |d<   |d         |d         z  |d<   |d         t          j        |d         �                    �   �         �  �        z  �                    �   �         |d	<   |d	         �                    d�  �        �                    �   �         |d
<   |d         |d         �                    �   �         z  �                    �   �         |d<   |S # t          $ r#}t          j
        d|� ��  �         |cY d
}~S d
}~ww xY w)u   Füge Volumen-Features hinzur�   r6  r  �
Volume_SMA_10r�  �
Volume_SMA_20�Volume_Ratior�   �OBV�
OBV_SMA_10�VPTzVolumen-Features Fehler: N)r�   r�  rC  r�   �signr�  �cumsumr  r�   ra   r�   �rT   r�   r�   s      rU   r�  z"FeatureEngine._add_volume_features�  se  � �	��r�z�)�)�#��8�� #%�X�,�"6�"6�r�":�":�"?�"?�"A�"A�B���"$�X�,�"6�"6�r�":�":�"?�"?�"A�"A�B���!#�H���?�0C�!C�B�~�� �H�����7��0@�0@�0B�0B�(C�(C�C�K�K�M�M�B�u�I�!�%�y�0�0��4�4�9�9�;�;�B�|�� �H���7��(>�(>�(@�(@�@�H�H�J�J�B�u�I��I��� 	� 	� 	��M�9�a�9�9�:�:�:��I�I�I�I�I�I�����	���s   �D5D8 �8
E%�E �E%� E%c                 �&  � 	 t          j        |d         |d         z
  t          j        t          |d         |d         �                    d�  �        z
  �  �        t          |d         |d         �                    d�  �        z
  �  �        �  �        �  �        |d<   |d         �                    d�  �        �                    �   �         |d<   |d         �                    d�  �        �                    �   �         |d	<   d
D ]P}|d         �                    |�  �        �                    �   �         |d|� �<   |d|� �         |d         z  |d|� d�<   �Q|d
         |d         z  |d<   |S # t          $ r#}t          j	        d|� ��  �         |cY d}~S d}~ww xY w)u"   Füge Volatilitäts-Features hinzur�   r�   r�   r�   �TRr�  �ATR_14r�  �ATR_20�r�   r  r�  �Volatility_�_Norm�
Volatility_10�
Volatility_20�Volatility_Ratiou   Volatilitäts-Features Fehler: N)
r�   �maximum�absr�  r�  rC  rd  r�   ra   r�   r  s       rU   r�  z&FeatureEngine._add_volatility_features  s�  � �	��z��6�
�R��Y�&��
���6�
�R��[�%6�%6�q�%9�%9�9�:�:���5�	�B�w�K�$5�$5�a�$8�$8�8�9�9�� �� �B�t�H� �d�8�+�+�B�/�/�4�4�6�6�B�x�L��d�8�+�+�B�/�/�4�4�6�6�B�x�L� &� 
[� 
[��-/��[�-@�-@��-H�-H�-L�-L�-N�-N��)��)�)�*�24�5K�6�5K�5K�2L�r�RY�{�2Z��.��.�.�.�/�/� &(��%8�2�o�;N�%N�B�!�"��I��� 	� 	� 	��M�?�A�?�?�@�@�@��I�I�I�I�I�I�����	���s   �E E# �#
F�-F�F�Fc                 �
  � 	 dD ]&}|d         �                     |�  �        dz  |d|� �<   �'dD ],}|d         |d         �                    |�  �        z
  |d|� �<   �-dD ]v}|d         �                    |�  �        �                    �   �         }|d         �                    |�  �        �                    �   �         }d	||d         z
  z  ||z
  z  |d
|� �<   �w|S # t
          $ r#}t
          j        d|� ��  �         |cY d}~S d}~ww xY w)
u   Füge Momentum-Features hinzur2  r�   r>  �ROC_�	Momentum_)r�  r�  r�   r�   i�����Williams_R_zMomentum-Features Fehler: N)r  r�  r�  rB  r  r�   ra   r�   )rT   r�   rz   r"  r!  r�   s         rU   r�  z$FeatureEngine._add_momentum_features*  s[  � �	�%� 
K� 
K��&(��k�&<�&<�V�&D�&D�s�&J��?�&�?�?�#�#� &� 
S� 
S��+-�g�;��G��9J�9J�6�9R�9R�+R��'�v�'�'�(�(� #� 
d� 
d���f�:�-�-�f�5�5�9�9�;�;���U�)�+�+�F�3�3�7�7�9�9��-1�X��7��5K�-L�PX�[b�Pb�-c��)��)�)�*�*��I��� 	� 	� 	��M�:�q�:�:�;�;�;��I�I�I�I�I�I�����	���s   �CC �
D�C=�7D�=Dc                 �r  � 	 |j         j        |d<   t          j        dt          j        z  |d         z  dz  �  �        |d<   t          j        dt          j        z  |d         z  dz  �  �        |d<   |j         j        |d<   t          j        dt          j        z  |d         z  dz  �  �        |d<   t          j        dt          j        z  |d         z  dz  �  �        |d	<   |d         d
k    �                    t          �  �        |d<   |S # t          $ r#}t          j        d|� ��  �         |cY d
}~S d
}~ww xY w)u!   Füge zeitbasierte Features hinzu�Hourrm   �   �Hour_Sin�Hour_Cos�	DayOfWeek�   �
DayOfWeek_Sin�
DayOfWeek_Cosr�   �	IsWeekendzZeit-Features Fehler: N)r�   �hourr�   �sin�pi�cos�	dayofweekr%  r(  r�   ra   r�   r-  s      rU   r�  z FeatureEngine._add_time_featuresA  s0  � �	����B�v�J��V�A���I��6�
�$:�R�$?�@�@�B�z�N��V�A���I��6�
�$:�R�$?�@�@�B�z�N� !�h�0�B�{�O�"$�&��R�U��R��_�)D�q�)H�"I�"I�B���"$�&��R�U��R��_�)D�q�)H�"I�"I�B���  "�+��!�3�;�;�C�@�@�B�{�O��I��� 	� 	� 	��M�6�1�6�6�7�7�7��I�I�I�I�I�I�����	���s   �DD	 �	
D6�D1�+D6�1D6c                 �&  � 	 d|j         v rA|d         dz
  dz  |d<   |d         �                    d�  �        �                    �   �         |d<   d� |j         D �   �         }|D ]�}||         �                    �   �         �                    �   �         rS||         �                    �   �         ||� d�<   ||         �                    d�  �        �                    �   �         ||� d�<   ��|S # t          $ r#}t          j        d	|� ��  �         |cY d
}~S d
}~ww xY w)u2   Füge externe Features hinzu (Sentiment, On-Chain)r�   r�   �Sentiment_Normr�   �Sentiment_SMA_5c                 �<   � g | ]}|�                     d �  �        �|��S )r�   )�
startswithr�  s     rU   r�  z8FeatureEngine._add_external_features.<locals>.<listcomp>`  s)   � �T�T�T�C����
�9S�9S�T�C�T�T�TrW   �_Change�_SMA_5zExterne Features Fehler: N)	r�   r�  rC  �notna�anyr  r�   ra   r�   )rT   r�   �onchain_colsr4  r�   s        rU   r�  z$FeatureEngine._add_external_featuresW  s9  � �	��b�j�(�(�(*�;��"�(<��'B��#�$�(*�;��(?�(?��(B�(B�(G�(G�(I�(I��$�%� U�T�2�:�T�T�T�L�#� 
C� 
C���c�7�=�=�?�?�&�&�(�(� C�*,�S�'�*<�*<�*>�*>�B�#����'�)+�C�����);�);�)@�)@�)B�)B�B�#�~�~�~�&���I��� 	� 	� 	��M�9�a�9�9�:�:�:��I�I�I�I�I�I�����	���s   �C C# �#
D�-D�D�Dc                 �  � � 	 � fd�|j         D �   �         }|s*t          j        d�  �         t          j        g �  �        g fS ||         j        }t          j        |�  �        �                    d��  �         }||         }||fS # t          $ r7}t          j	        d|� ��  �         t          j        g �  �        g fcY d}~S d}~ww xY w)u$   Hole Feature-Matrix für ML-Trainingc                 �&   �� g | ]
}|�j         v �|��S rv   )r�  )r3  r4  rT   s     �rU   r�  z4FeatureEngine.get_feature_matrix.<locals>.<listcomp>o  s&   �� �V�V�V�s�C�4�CU�<U�<U�s�<U�<U�<UrW   zKeine Features gefundenr�   )�axiszFeature-Matrix Fehler: N)
r�   ra   r>   r�   rm  r�   �isnanrU  r�   r�   )rT   r�   �feature_columnsr�   �
valid_rowsr�   s   `     rU   �get_feature_matrixz FeatureEngine.get_feature_matrixl  s�   �� �	$�V�V�V�V�b�j�V�V�V�O�"� 
(��� 9�:�:�:��x��|�|�R�'�'��?�#�*�A� �(�1�+�+�/�/�q�/�1�1�1�J��*�
�A��o�%�%��� 	$� 	$� 	$��M�7�A�7�7�8�8�8��8�B�<�<��#�#�#�#�#�#�#�����	$���s$   �>B �AB �
C�,C �:C� CN)rr   rs   rt   ru   rV   r�   r�   r   r�  r�  r(  r�   r  r�  r�  r�  r�  r�  r
   r�   r�   r	   r�   r]  rv   rW   rU   r�  r�  h  s�  � � � � � �� �	 � 	 � 	 �)�B�L� )�R�\� )� )� )� )�V�b�l� �r�|� � � � �@ �B�L�  �R�\�  �  �  �  �D=��� =�c� =�b�i� =� =� =� =��r�|� ��� � � � �0�2�<� �B�L� � � � �<��� �"�,� � � � �.�R�\� �b�l� � � � �,��� �"�,� � � � �*$�R�\� $�e�B�J��S�	�<Q�6R� $� $� $� $� $� $rW   r�  c                   �   � e Zd ZdZdZd� Zd� Zdedefd�Z	dedefd�Z
ddededefd
�Zddeee
f         fd�Zdej        dej        fd�ZdS )�UltimateModularTradingSystemu�   
    🚀 ULTIMATE MODULAR TRADING SYSTEM
    ==================================
    Hauptklasse für das modulare Trading-System
    zULTIMATE_MODULAR 1.0c                 ��  � � t          d� j        � ��  �         t          d�  �         t          �   �         � _        t	          �   �         � _        t          t          t          � fd�� fd�� fd�d�� _	        i � _
        i � _        i � _        t          j        �   �         � _        t          j        �   �         � _        t#          d��  �        � _        d � _        d � _        i � _        i � _        d	� _        d	� _        d	� _        d � _        d � _        t9          j        �   �         � _        t?          j         �   �         d
d
d
d
d�� _!        tE          j#        d� j        � d
��  �         d S )Nu&   
🚀 ULTIMATE MODULAR TRADING SYSTEM zP================================================================================c                  �*   ��  �j         t          fi | ��S rg   )�_create_sklearn_modelr   �rT  rT   s    �rU   r�   z7UltimateModularTradingSystem.__init__.<locals>.<lambda>�  s   �� �)C��)C�DV�)a�)a�Z`�)a�)a� rW   c                  �*   ��  �j         t          fi | ��S rg   )rb  r   rc  s    �rU   r�   z7UltimateModularTradingSystem.__init__.<locals>.<lambda>�  s   �� �$>�D�$>�s�$M�$M�f�$M�$M� rW   c                  �*   ��  �j         t          fi | ��S rg   )rb  r   rc  s    �rU   r�   z7UltimateModularTradingSystem.__init__.<locals>.<lambda>�  s   �� �/I�t�/I�Jd�/o�/o�hn�/o�/o� rW   )�
random_forest�xgboost�lstm�logistic�svm�gradient_boostr6  )�maxlenFr   )r  �total_predictions�successful_predictions�models_trained�data_updatesz Ultimate Modular Trading System z initialisiert)$�print�VERSIONr�   �
data_providerr�  �feature_enginerI  ru  r�  �available_models�
active_models�model_performances�ensemble_weightsr�   r�   �market_data�
features_datar   �predictions_history�root�notebook�figures�canvases�
is_running�auto_update�
auto_training�
update_thread�training_thread�	threading�Event�
stop_eventr   �now�
session_statsra   rA   rS   s   `rU   rV   z%UltimateModularTradingSystem.__init__�  sp  �� �
�F���F�F�G�G�G�
�h���� 7�8�8���+�o�o��� /�#��a�a�a�a�M�M�M�M�o�o�o�o�
!
� !
���  ���"$��� "��� �<�>�>����\�^�^���#(��#5�#5�#5�� � ��	���
������
�  ��� ���"��� "���#���#�/�+�+��� #�,�.�.�!"�&'���
� 
��� 	��T���T�T�T�U�U�U�U�UrW   c                 �:   �  G d� dt           �  �        } ||fi |��S )z.Erstelle Sklearn-Modell mit Standard-Interfacec                   �   � e Zd Zd� Zdej        dej        deeef         fd�Z	dej        dej        fd�Z
deeef         fd�ZdS )	�OUltimateModularTradingSystem._create_sklearn_model.<locals>.SklearnModelWrapperc                 �b   �  |di |��| _         t          �   �         | _        i | _        i | _        d S )Nrv   )rO  r   rP  rQ  rR  )rT   �model_classrT  s      rU   rV   zXUltimateModularTradingSystem._create_sklearn_model.<locals>.SklearnModelWrapper.__init__�  s<   � �(�[�2�2�6�2�2��
�,�.�.���+-��(�+-��(�(�(rW   r�   r�   r{   c                 ��  � 	 | j         �                    |�  �        }| j        �                    ||�  �         t	          | j        ||dd��  �        }t          | j        d�  �        r,t
          t          | j        j        �  �        �  �        | _	        nSt          | j        d�  �        r>t
          t          t          | j        j        d         �  �        �  �        �  �        | _	        |�                    �   �         |�
                    �   �         |j        d         t          |�  �        d�| _        | j        S # t"          $ r#}t%          j        d	|� ��  �         i cY d }~S d }~ww xY w)
NrV  rW  rX  rc  �coef_r   r�   r[  zSklearn Model Training Fehler: )rP  r`  rO  ra  r   r|  rG  rb  rc  rQ  r9  r�  rC  rd  re  r�   rR  r�   ra   r�   rf  s         rU   r�   zUUltimateModularTradingSystem._create_sklearn_model.<locals>.SklearnModelWrapper.train�  sP  � ��#�{�8�8��;�;�H��J�N�N�8�Q�/�/�/� /��
�H�a�A�Wa� b� b� b�I��t�z�+A�B�B� ]�37�	�$�*�Ba�8b�8b�3c�3c��0�0� ���W�5�5� ]�37�	�#�d�j�FV�WX�FY�BZ�BZ�8[�8[�3\�3\��0� $-�>�>�#3�#3�"+�-�-�/�/�)*����,/��F�F�	0� 0�D�,�  �3�3�� � � � ��M�"G�A�"G�"G�H�H�H��I�I�I�I�I�I��������s   �D0D3 �3
E �=E�E �E c                 ��  � 	 | j         �                    |�  �        }t          | j        d�  �        r| j        �                    |�  �        S | j        �                    |�  �        }t
          j        t          |�  �        df�  �        }d||dk    df<   d||dk    df<   d||dk    df<   d||dk    df<   |S # t          $ r8}t          j        d|� ��  �         t
          j        ddgg�  �        cY d }~S d }~ww xY w)	Nrl  rm   g�������?r   r�  r�   z!Sklearn Model Prediction Fehler: r�  )
rP  rk  r|  rO  rl  r�   r�   �zerosr�   r�   ra   r�   rm  )rT   r�   rg  �pred�probar�   s         rU   r�   zWUltimateModularTradingSystem._create_sklearn_model.<locals>.SklearnModelWrapper.predict�  s  � �2�#�{�4�4�Q�7�7�H��t�z�?�;�;� 
%�#�z�7�7��A�A�A�#�z�1�1�(�;�;�� "��#�d�)�)�Q�� 8� 8��.1��d�a�i��l�+�.1��d�a�i��l�+�.1��d�a�i��l�+�.1��d�a�i��l�+�$��� � 2� 2� 2��M�"I�a�"I�"I�J�J�J��8�c�3�Z�L�1�1�1�1�1�1�1�1�����2���s%   �AB6 �A*B6 �6
C8� -C3�-C8�3C8c                 �   � | j         S rg   rq  rS   s    rU   r�   zfUltimateModularTradingSystem._create_sklearn_model.<locals>.SklearnModelWrapper.get_feature_importance�  s   � ��/�/rW   N)
rr   rs   rt   rV   r�   r�   r   r�   r   r�   r�   r�   r�   rv   rW   rU   �SklearnModelWrapperr�  �  s�   � � � � � �
.� 
.� 
.�
�r�z� 
�b�j� 
�T�#�s�(�^� 
� 
� 
� 
�2
2��� 
2��
� 
2� 
2� 
2� 
2�$
0��S�%�Z�0@� 
0� 
0� 
0� 
0� 
0� 
0rW   r�  )r�   )rT   r�  rT  r�  s       rU   rb  z2UltimateModularTradingSystem._create_sklearn_model�  sH   � �3	0� 3	0� 3	0� 3	0� 3	0�'� 3	0� 3	0� 3	0�j #�"�;�9�9�&�9�9�9rW   �
model_namer{   c                 �   � 	 || j         vrt          j        d|� d��  �         dS | j         |         } |d	i |��}|| j        |<   t          j        d|� d��  �         dS # t
          $ r%}t          j        d|� d|� ��  �         Y d}~dS d}~ww xY w)
zLade ein ML-Modell�Modell 'u   ' nicht verfügbarFz' erfolgreich geladenTzFehler beim Laden von Modell '�': Nrv   )ru  ra   r�   rv  rA   r�   )rT   r�  rT  r�  �model_instancer�   s         rU   �
load_modelz'UltimateModularTradingSystem.load_model�  s�   � �	���!6�6�6��
�G��G�G�G�H�H�H��u��/�
�;�K�(�[�2�2�6�2�2�N�-;�D��z�*��L�E�J�E�E�E�F�F�F��4��� 	� 	� 	��M�M�:�M�M�!�M�M�N�N�N��5�5�5�5�5�����	���s   �!A �7A �
B
�(B�B
c                 �6  � 	 || j         v rD| j         |= || j        v r| j        |= || j        v r| j        |= t          j        d|� d��  �         dS t          j        d|� d��  �         dS # t          $ r%}t          j        d|� d|� ��  �         Y d}~dS d}~ww xY w)	zEntlade ein ML-Modellr�  z
' entladenTz' ist nicht geladenFz!Fehler beim Entladen von Modell 'r�  N)rv  rw  rx  ra   rA   r>   r�   r�   )rT   r�  r�   s      rU   �unload_modelz)UltimateModularTradingSystem.unload_model  s�   � �	��T�/�/�/��&�z�2���!8�8�8��/�
�;���!6�6�6��-�j�9���>�
�>�>�>�?�?�?��t��� J�:� J� J� J�K�K�K��u��� 	� 	� 	��M�P�j�P�P�Q�P�P�Q�Q�Q��5�5�5�5�5�����	���s   �AA) �A) �)
B�3B�Br�   r�   ry   rz   c                 �P  � 	 t          j        d�  �         | j        �                    ||�  �        | _        | j        j        rt          j        d�  �         dS | j        �                    | j        �  �        | _	        | j	        j        rt          j        d�  �         dS | j
        dxx         dz
  cc<   t          j        dt          | j        �  �        � dt          | j	        j        �  �        � d	��  �         d
S # t          $ r"}t          j        d|� ��  �         Y d}~dS d}~ww xY w)
zSammle MarktdatenzSammle umfassende Marktdaten...zKeine Marktdaten erhaltenFz!Feature-Erstellung fehlgeschlagenrp  r�   zDaten gesammelt: r�   r�   TzDatensammlung fehlgeschlagen: N)ra   rA   rs  r}   ry  r�   r�   rt  r   rz  r�  r�   r�   r�   )rT   ry   rz   r�   s       rU   �collect_dataz)UltimateModularTradingSystem.collect_data$  sC  � �	��L�:�;�;�;�  $�1�:�:�6�6�J�J�D����%� 
��
�9�:�:�:��u� "&�!4�!D�!D�T�EU�!V�!V�D���!�'� 
��
�A�B�B�B��u���~�.�.�.�!�3�.�.�.��L�|�S��1A�-B�-B�|�|�RU�VZ�Vh�Vp�Rq�Rq�|�|�|�}�}�}��4��� 	� 	� 	��M�>�1�>�>�?�?�?��5�5�5�5�5�����	���s&   �AC9 �AC9 �AC9 �9
D%�D � D%Nc                 ��  �� 	 | j         j        rt          j        d�  �         i S | j        st          j        d�  �         i S | j        �                    | j         �  �        \  }}t          |�  �        dk     rt          j        d�  �         i S | �                    | j         d         �  �        }t          t          |�  �        t          |�  �        �  �        }|d|�         }|d|�         }i }t          | j        �  �        }t          | j        �                    �   �         �  �        D ]�\  }\  }	}
	 |r||z  dz  } |d|	� d	�|�  �         |
�                    ||�  �        }|rY|||	<   || j
        |	<   |�                    d
d�  �        }
t          d|
�  �        | j        |	<   t          j        d
|	� d|
d���  �         nt          j        d|	� d��  �         ��# t&          $ r$}t          j        d|	� d|� ��  �         Y d}~��d}~ww xY w| j        rPt)          | j        �                    �   �         �  �        ��fd�| j        �                    �   �         D �   �         | _        |r |dd�  �         | j        dxx         t          |�  �        z
  cc<   t          j        dt          |�  �        � d��  �         |S # t&          $ r#}t          j        d|� ��  �         i cY d}~S d}~ww xY w)zTrainiere alle aktiven Modelleu'   Keine Features für Training verfügbarz$Keine aktiven Modelle zum Trainierenr�   u   Zu wenig Daten für Trainingr�   Nr>  z	Training z...r\  r�  r7  u   ✅ z: Accuracy=z.3fu   ❌ z: Training fehlgeschlagenzTraining-Fehler bei r  c                 �"   �� i | ]\  }}||�z  ��S rv   rv   )r3  �k�v�total_weights      �rU   r�  z=UltimateModularTradingSystem.train_models.<locals>.<dictcomp>x  s#   �� �(e�(e�(e�t�q�!��A�l�N�(e�(e�(erW   zTraining abgeschlossen!ro  zTraining abgeschlossen: z Modelle erfolgreich trainiertz Modell-Training fehlgeschlagen: )rz  r�   ra   r�   rv  rt  r]  r�   �_create_targetsr  rb  r�   r�   rw  r�   rB  rx  rA   r>   r�   r;  r�   r�  )rT   �progress_callbackr�   r�  r�   �
min_length�results�total_modelsr�  r�  rO  �progress�performancerW  r�   r�  s                  @rU   �train_modelsz)UltimateModularTradingSystem.train_models?  s^  �� �B	��!�'� 
��
�G�H�H�H��	��%� 
��
�D�E�E�E��	�  $�2�E�E�d�FX�Y�Y��A�}��1�v�v��{�{��
�<�=�=�=��	� �$�$�T�%7��%@�A�A�A� �S��V�V�S��V�V�,�,�J��+�:�+��A��+�:�+��A��G��t�1�2�2�L�*3�D�4F�4L�4L�4N�4N�*O�*O� 
� 
�&��&�J���(� Q�$%��$4��#;��)�)�*E�j�*E�*E�*E�x�P�P�P� #(�+�+�a��"3�"3�K�"� 
V�.9��
�+�>I��/�
�;� $/�?�?�9�c�#B�#B��<?��X�<N�<N��-�j�9���%Q�J�%Q�%Q�8�%Q�%Q�%Q�R�R�R�R���(T�z�(T�(T�(T�U�U�U��� � � � ��M�"J��"J�"J�q�"J�"J�K�K�K��H�H�H�H���������
 �$� 
f�"�4�#8�#?�#?�#A�#A�B�B��(e�(e�(e�(e�t�G\�Gb�Gb�Gd�Gd�(e�(e�(e��%� � 
B�!�!�";�S�A�A�A���/�0�0�0�C��L�L�@�0�0�0��L�`�C��L�L�`�`�`�a�a�a��N��� 	� 	� 	��M�@�Q�@�@�A�A�A��I�I�I�I�I�I�����	���s\   �!J3 �J3 �A
J3 �
B"J3 �0B#G�J3 �
H�G=�8J3 �=H�B0J3 �3
K �=K�K �K r�  c                 �H  � 	 |�                     �   �         �                    d�  �        }|dk    �                    t          �  �        }|�                    �   �         }|j        S # t          $ r5}t          j        d|� ��  �         t          j
        g �  �        cY d}~S d}~ww xY w)u!   Erstelle Targets für ML-Trainingr�   r   z"Target-Erstellung fehlgeschlagen: N)r  r�  r%  r(  r�   r�   r�   ra   r�   r�   rm  )rT   r�  �
price_changes�targetsr�   s        rU   r�  z,UltimateModularTradingSystem._create_targets�  s�   � �	 �"�-�-�/�/�5�5�b�9�9�M�$�q�(�0�0��5�5�G� �n�n�&�&�G��>�!��� 	 � 	 � 	 ��M�B�q�B�B�C�C�C��8�B�<�<�����������	 ���s   �AA" �"
B!�,*B�B!�B!rF  rg   )rr   rs   rt   ru   rr  rV   rb  r�   r�   r�  r�  r�  r   r   r�  r�   r�   r�   r�   r�  rv   rW   rU   r_  r_  �  s  � � � � � �� � %�G�5V� 5V� 5V�n7:� 7:� 7:�r�S� �t� � � � �$�s� �t� � � � �(� �3� �� �t� � � � �6D� D�d�3��8�n� D� D� D� D�L �b�i�  �B�J�  �  �  �  �  �  rW   r_  �__main__u$   🚀 ULTIMATE MODULAR TRADING SYSTEMz*Hauptsystem geladen - Fortsetzung folgt...)�ru   rb   �osr�  r�   r\   rY   ra   r  �pickle�sqlite3�asyncio�aiohttp�pathlibr   r   r   �collectionsr   r   �typingr   r	   r
   r   r   r
   �warnings�abcr   r   �numpyr�   �pandasr�   r�   �yfinancer�   �sklearn.ensembler   r   r   �sklearn.linear_modelr   r   �sklearn.svmr   �sklearn.neural_networkr   �sklearn.preprocessingr   r   r   �sklearn.metricsr   r   r   r   �sklearn.model_selectionr   r   r    �sklearn.feature_selectionr!   r"   r#   �
tensorflow�tf�tensorflow.keras.modelsr$   �tensorflow.keras.layersr%   r&   r'   r(   r)   �tensorflow.keras.optimizersr*   r�  �ImportErrorrg  ry  rx  �talib�TALIB_AVAILABLE�tkinterr�   r+   r,   r-   �matplotlib.pyplot�pyplot�plt�!matplotlib.backends.backend_tkaggr.   r/   �matplotlib.dates�dates�mdates�matplotlib.patchesr0   r1   r2   �matplotlib.collectionsr3   �patches�mpatches�matplotlib.widgetsr4   �matplotlib.colorsr5   �mpl_toolkits.mplot3dr6   �seaborn�sns�filterwarnings�environ�style�use�rcParams�COLORS�basicConfig�INFO�FileHandlerr�  �strftime�
StreamHandlerrN   �process_managerrx   r�   r�   r�   r�   rI  ru  r�  r�  r�  r�  r�  r_  rr   rq  rv   rW   rU   �<module>r�     s�  ��&� &�P �
�
�
� 	�	�	�	� � � � � ���� 
�
�
�
� 
�
�
�
� ���� ���� 
�
�
�
� ���� ���� ���� � � � � � � (� (� (� (� (� (� (� (� *� *� *� *� *� *� *� *� :� :� :� :� :� :� :� :� :� :� :� :� :� :� :� :� ���� #� #� #� #� #� #� #� #� � � � � � � � � ���� � � � � f� e� e� e� e� e� e� e� e� e� B� B� B� B� B� B� B� B� � � � � � � 0� 0� 0� 0� 0� 0� L� L� L� L� L� L� L� L� L� L� b� b� b� b� b� b� b� b� b� b� b� b� S� S� S� S� S� S� S� S� S� S� A� A� A� A� A� A� A� A� A� A�!�����2�2�2�2�2�2�R�R�R�R�R�R�R�R�R�R�R�R�R�R�0�0�0�0�0�0������ !� !� !� ����!�������������� � � �����������L�L�L��O�O��� � � ��O�O�O����� � � � � /� /� /� /� /� /� /� /� /� /�  � � � � � � U� U� U� U� U� U� U� U� !� !� !� !� !� !� @� @� @� @� @� @� @� @� @� @� 1� 1� 1� 1� 1� 1� %� %� %� %� %� %� %� %� %� %� %� %� 5� 5� 5� 5� 5� 5� '� '� '� '� '� '� � � � � �� �� !� !� !�%(��
�!� "� �	�
�
��  �  �  �#,���
�  �!*���
� �$-���
 � !� ��������������
� 
��$ �� �
�,�6����_������8O�8O�PX�8Y�8Y�_�_�_�`�`�������� � � �5� 5� 5� 5� 5� 5� 5� 5�B !�.�"�"��

� 

� 

� 

� 

�3� 

� 

� 

�

� 

� 

� 

� 

�c� 

� 

� 

�	
� 	
� 	
� 	
� 	
�� 	
� 	
� 	
�	
� 	
� 	
� 	
� 	
�� 	
� 	
� 	
�F� F� F� F� F�� F� F� F�X2(� 2(� 2(� 2(� 2(�� 2(� 2(� 2(�h8(� 8(� 8(� 8(� 8(�7� 8(� 8(� 8(�t`Q� `Q� `Q� `Q� `Q�� `Q� `Q� `Q�L �  �  �  �  �9�  �  �  �D3� 3� 3� 3� 3�I� 3� 3� 3�j2� 2� 2� 2� 2�i� 2� 2� 2�pW$� W$� W$� W$� W$� W$� W$� W$�zN � N � N � N � N � N � N � N �` �z���	�E�
0�1�1�1�	�E�
6�7�7�7�7�7� �s6   �> C �C)�(C)�-C4 �4C>�=C>�D	 �	D�D