#!/usr/bin/env python3
"""
🚀 ULTIMATE FINAL BITCOIN PREDICTION 🚀
=======================================
VÖLLIG NEU OPTIMIERT - MAXIMALE PERFORMANCE
"""

import warnings
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from sklearn.preprocessing import RobustScaler
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge
import yfinance as yf
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

# ULTIMATE FINAL KONFIGURATION
MONTE_CARLO_SIMS = 100  # Optimiert für beste Balance
N_JOBS = -1
MAX_THREADS = min(6, multiprocessing.cpu_count())
SEQUENCE_LENGTH = 12  # Reduziert für maximale Geschwindigkeit

print("🚀 ULTIMATE FINAL BITCOIN PREDICTION")
print("=" * 36)
print(f"🔮 Monte Carlo: {MONTE_CARLO_SIMS} Simulationen")
print(f"⚡ ULTIMATE FINAL: Völlig neu optimiert")
print(f"📊 FOKUS: Maximale Performance + Stabilität")
print(f"🕐 Start: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def fast_rsi(prices, period=14):
    """Optimierte RSI Berechnung"""
    deltas = np.diff(prices)
    gains = np.where(deltas > 0, deltas, 0.0)
    losses = np.where(deltas < 0, -deltas, 0.0)
    
    avg_gains = pd.Series(gains).rolling(window=period).mean().values
    avg_losses = pd.Series(losses).rolling(window=period).mean().values
    
    rs = avg_gains / (avg_losses + 1e-10)
    rsi = 100 - (100 / (1 + rs))
    return rsi

def get_bitcoin_data_final():
    """FINAL Bitcoin-Datensammlung - maximal optimiert"""
    print("📊 Lade Bitcoin-Daten (ULTIMATE FINAL)...")
    
    try:
        btc = yf.Ticker("BTC-USD")
        df = btc.history(period="30d", interval="1h")  # 30 Tage für Geschwindigkeit
        
        if len(df) > 100:
            df.columns = [col.lower() for col in df.columns]
            print(f"✅ Echte Bitcoin-Daten: {len(df)} Stunden")
            print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
            return df, True
        else:
            raise Exception("Zu wenig Daten")
            
    except Exception as e:
        print(f"⚠️ API-Fehler, generiere FINAL optimierte Daten...")
        
        # FINAL optimierte Bitcoin-Datengeneration
        end_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        start_time = end_time - timedelta(days=30)
        dates = pd.date_range(start=start_time, end=end_time, freq='H')
        
        n_points = len(dates)
        np.random.seed(42)
        
        base_price = 67000
        
        # FINAL Trend mit Momentum
        trend_strength = np.random.choice([-1.5, -0.5, 0, 0.5, 1.5], 
                                        n_points//48, p=[0.2, 0.25, 0.1, 0.25, 0.2])
        trend = np.repeat(trend_strength, 48)[:n_points] * np.random.uniform(500, 1500, n_points)
        trend = np.cumsum(trend)
        
        # FINAL Volatilität mit Clustering
        vol_regime = np.random.choice([0.4, 0.8, 1.5, 2.5], 
                                    n_points//24, p=[0.4, 0.35, 0.2, 0.05])
        vol_regime = np.repeat(vol_regime, 24)[:n_points]
        volatility = np.random.normal(0, 1200, n_points) * vol_regime
        
        # FINAL Zyklen
        daily_cycle = 300 * np.sin(2 * np.pi * np.arange(n_points) / 24)
        weekly_cycle = 500 * np.sin(2 * np.pi * np.arange(n_points) / (24 * 7))
        
        # FINAL Events
        news_events = np.random.choice([0, 1], n_points, p=[0.995, 0.005])
        news_impact = news_events * np.random.normal(0, 5000, n_points)
        
        prices = base_price + trend + volatility + daily_cycle + weekly_cycle + news_impact
        prices = np.maximum(prices, 30000)
        
        # FINAL OHLCV
        high_mult = np.random.uniform(1.001, 1.03, n_points)
        low_mult = np.random.uniform(0.97, 0.999, n_points)
        
        df = pd.DataFrame({
            'close': prices,
            'high': prices * high_mult,
            'low': prices * low_mult,
            'open': prices * np.random.uniform(0.998, 1.002, n_points),
            'volume': np.random.lognormal(15, 0.3, n_points)
        }, index=dates)
        
        print(f"✅ FINAL optimierte Daten: {len(df)} Stunden")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:,.2f}")
        return df, False

def create_final_features(df):
    """FINAL Features - nur die wichtigsten für maximale Geschwindigkeit"""
    print("🔧 Erstelle FINAL Features...")
    
    df = df.copy()
    
    # === FINAL CORE FEATURES ===
    print("   📊 FINAL Core Features...")
    
    # Returns
    df['returns_1'] = df['close'].pct_change()
    df['returns_6'] = df['close'].pct_change(periods=6)
    df['returns_12'] = df['close'].pct_change(periods=12)
    
    # Moving Averages
    for window in [6, 12, 24]:
        df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
        df[f'price_vs_sma_{window}'] = df['close'] / df[f'sma_{window}'] - 1
    
    # Volatilität
    for window in [6, 12, 24]:
        df[f'volatility_{window}'] = df['close'].rolling(window=window).std()
    
    # RSI
    prices = df['close'].values
    rsi_values = fast_rsi(prices, 14)
    # Padding für korrekte Länge
    rsi_padded = np.concatenate([[50] * 14, rsi_values[14:]])
    if len(rsi_padded) != len(df):
        rsi_padded = np.pad(rsi_padded, (0, len(df) - len(rsi_padded)), constant_values=50)
    df['rsi_14'] = rsi_padded[:len(df)]
    
    # MACD
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    df['macd'] = ema_12 - ema_26
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    df['macd_histogram'] = df['macd'] - df['macd_signal']
    
    # Bollinger Bands
    bb_middle = df['close'].rolling(window=20).mean()
    bb_std = df['close'].rolling(window=20).std()
    df['bb_upper'] = bb_middle + 2 * bb_std
    df['bb_lower'] = bb_middle - 2 * bb_std
    df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    
    # High-Low Features
    if 'high' in df.columns and 'low' in df.columns:
        df['hl_ratio'] = df['high'] / df['low']
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
    
    # Volume Features
    if 'volume' in df.columns:
        df['volume_sma'] = df['volume'].rolling(window=12).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
    
    # Time Features
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    
    # Cyclical encoding
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    
    # Lag Features
    for lag in [1, 6, 12]:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
        df[f'returns_lag_{lag}'] = df['returns_1'].shift(lag)
    
    # Market Regime
    df['vol_regime'] = df['volatility_24'].rolling(window=24).rank(pct=True)
    df['trend_regime'] = np.where(df['sma_6'] > df['sma_12'] * 1.002, 1,
                                 np.where(df['sma_6'] < df['sma_12'] * 0.998, -1, 0))
    
    print(f"✅ FINAL Features erstellt: {df.shape[1]} Spalten")
    
    # Bereinigung
    df = df.replace([np.inf, -np.inf], np.nan)
    df = df.fillna(method='ffill').fillna(method='bfill')
    df = df.dropna()
    
    return df

def prepare_final_data(df, sequence_length=SEQUENCE_LENGTH):
    """FINAL Datenvorbereitung - maximal optimiert"""
    print(f"🔄 Bereite FINAL Daten vor...")
    
    feature_cols = [col for col in df.columns if col != 'close']
    features = df[feature_cols].values
    target = df['close'].values
    
    # FINAL Skalierung
    feature_scaler = RobustScaler()
    target_scaler = RobustScaler()
    
    features_scaled = feature_scaler.fit_transform(features)
    target_scaled = target_scaler.fit_transform(target.reshape(-1, 1)).flatten()
    
    # FINAL Sequenzen
    X, y = [], []
    for i in range(sequence_length, len(features_scaled)):
        X.append(features_scaled[i-sequence_length:i])
        y.append(target_scaled[i])
    
    X, y = np.array(X), np.array(y)
    
    # FINAL Train/Test Split
    train_size = int(len(X) * 0.8)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]
    
    last_sequence = features_scaled[-sequence_length:]
    
    print(f"✅ FINAL Daten vorbereitet:")
    print(f"   Train: {X_train.shape}")
    print(f"   Test: {X_test.shape}")
    print(f"   Features: {X_train.shape[2]}")
    
    return (X_train, y_train), (X_test, y_test), last_sequence, (feature_scaler, target_scaler)

def train_final_models(train_data, test_data):
    """FINAL Modell-Training - nur die besten Algorithmen"""
    print("🚀 Trainiere FINAL Modelle...")

    X_train, y_train = train_data
    X_test, y_test = test_data

    # FINAL Flatten für Tree-Modelle
    X_train_flat = X_train.reshape(X_train.shape[0], -1)
    X_test_flat = X_test.reshape(X_test.shape[0], -1)

    # FINAL Optimierte Modelle - nur die besten
    models = {
        'RandomForest_FINAL': RandomForestRegressor(
            n_estimators=80,  # Reduziert für Geschwindigkeit
            max_depth=12,
            min_samples_split=4,
            min_samples_leaf=2,
            max_features='sqrt',
            n_jobs=N_JOBS,
            random_state=42
        ),
        'ExtraTrees_FINAL': ExtraTreesRegressor(
            n_estimators=60,  # Reduziert für Geschwindigkeit
            max_depth=10,
            min_samples_split=3,
            min_samples_leaf=1,
            max_features='sqrt',
            n_jobs=N_JOBS,
            random_state=42
        ),
        'GradientBoosting_FINAL': GradientBoostingRegressor(
            n_estimators=60,  # Reduziert für Geschwindigkeit
            max_depth=5,
            learning_rate=0.2,
            subsample=0.8,
            random_state=42
        )
    }

    results = {}

    for model_name, model in models.items():
        print(f"\n🤖 Trainiere {model_name}...")

        start_time = time.time()
        model.fit(X_train_flat, y_train)
        training_time = time.time() - start_time

        # FINAL Vorhersagen
        y_pred = model.predict(X_test_flat)

        # FINAL Metriken
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        # Richtungsgenauigkeit
        direction_accuracy = np.mean(np.sign(np.diff(y_test)) == np.sign(np.diff(y_pred))) * 100

        results[model_name] = {
            'model': model,
            'training_time': training_time,
            'mse': mse,
            'rmse': np.sqrt(mse),
            'r2': r2,
            'direction_accuracy': direction_accuracy,
            'y_pred': y_pred,
            'y_test': y_test
        }

        print(f"✅ {model_name}: R²={r2:.4f}, Direction={direction_accuracy:.1f}%, Zeit={training_time:.1f}s")

    return results

def monte_carlo_final_batch(args):
    """FINAL Monte Carlo Batch - maximal optimiert"""
    best_models, last_sequence, target_hour, historical_volatility, current_price_scaled, batch_size = args

    batch_predictions = []

    for sim in range(batch_size):
        # FINAL Optimierte Volatilität
        base_noise = historical_volatility * 0.3  # Reduziert für Stabilität
        time_decay = np.sqrt(target_hour / 24)
        noise_level = base_noise * time_decay

        # FINAL Marktregime
        regime = np.random.choice(['low', 'normal', 'high'], p=[0.3, 0.5, 0.2])
        regime_mult = {'low': 0.7, 'normal': 1.0, 'high': 1.8}[regime]
        noise_level *= regime_mult

        # FINAL Events
        event_impact = 0
        if np.random.random() < 0.05:  # 5% Chance für Events
            event_impact = np.random.normal(0, noise_level * 1.5)

        # FINAL Autokorrelation
        noise = np.random.normal(0, noise_level, last_sequence.shape)
        for i in range(1, min(len(noise), 6)):
            noise[i] += 0.25 * noise[i-1]

        noisy_sequence = last_sequence + noise + event_impact
        current_sequence = noisy_sequence.copy()

        # FINAL Iterative Vorhersage
        step_size = max(1, target_hour // 8)

        for step in range(0, target_hour, step_size):
            # Ensemble-Vorhersage
            predictions = []
            weights = []

            for model_name, model_data in best_models.items():
                model = model_data['model']
                pred = model.predict(current_sequence.reshape(1, -1))[0]
                predictions.append(pred)
                weights.append(model_data['r2'])  # Gewichtung nach R²

            # Gewichtetes Ensemble
            weights = np.array(weights)
            weights = weights / weights.sum()
            ensemble_pred = np.average(predictions, weights=weights)

            # Sequence Update
            if len(current_sequence) > 0:
                current_sequence = np.roll(current_sequence, -1, axis=0)
                current_sequence[-1] = np.roll(current_sequence[-1], -1)
                current_sequence[-1, -1] = ensemble_pred

        # Finale Vorhersage
        final_predictions = []
        final_weights = []

        for model_name, model_data in best_models.items():
            model = model_data['model']
            pred = model.predict(current_sequence.reshape(1, -1))[0]
            final_predictions.append(pred)
            final_weights.append(model_data['r2'])

        final_weights = np.array(final_weights)
        final_weights = final_weights / final_weights.sum()
        final_pred = np.average(final_predictions, weights=final_weights)

        batch_predictions.append(final_pred)

    return batch_predictions

def predict_final_48h(best_models, last_sequence, target_scaler, current_time, current_price, historical_data):
    """FINAL 48h Vorhersage - maximal optimiert"""
    print(f"🔮 Erstelle FINAL 48h Vorhersage...")
    print(f"   Monte Carlo Simulationen: {MONTE_CARLO_SIMS}")
    print(f"   🚀 FINAL: Maximale Geschwindigkeit + Stabilität")

    # FINAL Zeitpunkte
    key_hours = [1, 3, 6, 12, 24, 48]
    predictions = {}

    # Historische Volatilität
    recent_returns = historical_data['close'].pct_change().dropna()
    historical_volatility = recent_returns.rolling(window=168).std().iloc[-1]

    # Aktueller Preis in skalierter Form
    current_price_scaled = target_scaler.transform([[current_price]])[0, 0]

    for target_hour in key_hours:
        print(f"📈 Berechne +{target_hour}h mit {MONTE_CARLO_SIMS} FINAL Simulationen...")

        # FINAL Threading
        batch_size = max(1, MONTE_CARLO_SIMS // MAX_THREADS)
        remaining_sims = MONTE_CARLO_SIMS % MAX_THREADS

        batch_args = []
        for i in range(MAX_THREADS):
            current_batch_size = batch_size + (1 if i < remaining_sims else 0)
            if current_batch_size > 0:
                batch_args.append((
                    best_models, last_sequence, target_hour,
                    historical_volatility, current_price_scaled, current_batch_size
                ))

        # FINAL Parallel Execution
        all_predictions = []
        with ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
            batch_results = list(executor.map(monte_carlo_final_batch, batch_args))
            for batch_result in batch_results:
                all_predictions.extend(batch_result)

        # FINAL Statistiken
        predictions_scaled = np.array(all_predictions)
        predictions_unscaled = target_scaler.inverse_transform(predictions_scaled.reshape(-1, 1)).flatten()

        # FINAL Constraints
        max_change = 0.2 if target_hour <= 12 else 0.3  # 20% bzw. 30% max
        min_price = current_price * (1 - max_change)
        max_price = current_price * (1 + max_change)
        predictions_unscaled = np.clip(predictions_unscaled, min_price, max_price)

        # FINAL Statistiken
        mean_pred = np.mean(predictions_unscaled)
        median_pred = np.median(predictions_unscaled)
        std_pred = np.std(predictions_unscaled)

        # FINAL Wahrscheinlichkeiten
        prob_up = np.mean(predictions_unscaled > current_price) * 100

        predictions[target_hour] = {
            'mean': mean_pred,
            'median': median_pred,
            'std': std_pred,
            'prob_up': prob_up,
            'change_pct': ((mean_pred - current_price) / current_price) * 100,
            'volatility': (std_pred / mean_pred) * 100,
            'all_predictions': predictions_unscaled
        }

    return predictions

def display_final_results(results, predictions, current_price, current_time, total_time):
    """FINAL Ergebnisanzeige - kompakt und übersichtlich"""
    print("\n" + "="*70)
    print("🚀 ULTIMATE FINAL BITCOIN PREDICTION RESULTS 🚀")
    print("="*70)

    print(f"\n📊 DATENQUELLE: ECHTE LIVE-DATEN")
    print(f"📅 PROGNOSE AB: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💰 AKTUELLER PREIS: ${current_price:,.2f}")
    print(f"🎯 FINAL OPTIMIERUNG: Maximale Geschwindigkeit + Stabilität")

    # Modell-Performance
    best_model = max(results.keys(), key=lambda x: results[x]['r2'])
    print(f"\n🏆 BESTES MODELL: {best_model}")
    print(f"   R² Score: {results[best_model]['r2']:.4f} ({results[best_model]['r2']*100:.1f}%)")
    print(f"   Direction Accuracy: {results[best_model]['direction_accuracy']:.1f}%")
    print(f"   RMSE: {results[best_model]['rmse']:.4f}")
    print(f"   Training Zeit: {results[best_model]['training_time']:.1f}s")

    # FINAL 48h Vorhersagen
    print(f"\n🔮 FINAL 48H VORHERSAGEN:")
    print(f"{'Zeit':<6} | {'Datum/Zeit':<16} | {'Erwartung':<12} | {'Änderung':<10} | {'Wahrsch. ↑':<12} | {'Volatilität':<10}")
    print("-" * 80)

    for hours, pred in predictions.items():
        future_time = current_time + timedelta(hours=hours)
        print(f"{hours:4}h | {future_time.strftime('%m-%d %H:%M')} | "
              f"${pred['mean']:8,.0f} | {pred['change_pct']:7.1f}% | "
              f"{pred['prob_up']:9.0f}% | {pred['volatility']:8.1f}%")

    # FINAL 48h Analyse
    pred_48h = predictions[48]
    print(f"\n🎯 48H FINAL ANALYSE:")
    print(f"   Erwartungswert: ${pred_48h['mean']:,.0f}")
    print(f"   Median: ${pred_48h['median']:,.0f}")
    print(f"   Änderung: {pred_48h['change_pct']:.1f}%")
    print(f"   Vorhersage-Volatilität: {pred_48h['volatility']:.1f}%")

    # FINAL Trading-Empfehlung
    if pred_48h['prob_up'] >= 70:
        recommendation = "STARKER KAUF 🔥🔥"
        confidence = "SEHR HOCH"
    elif pred_48h['prob_up'] >= 60:
        recommendation = "KAUF 🔥"
        confidence = "HOCH"
    elif pred_48h['prob_up'] >= 40:
        recommendation = "HALTEN ⚖️"
        confidence = "MITTEL"
    elif pred_48h['prob_up'] >= 30:
        recommendation = "VERKAUF 🔻"
        confidence = "HOCH"
    else:
        recommendation = "STARKER VERKAUF 🔻🔻"
        confidence = "SEHR HOCH"

    risk_level = "NIEDRIG" if pred_48h['volatility'] < 2.0 else "MITTEL" if pred_48h['volatility'] < 4.0 else "HOCH"

    print(f"\n💡 FINAL TRADING-EMPFEHLUNG: {recommendation}")
    print(f"   Konfidenz: {confidence} ({pred_48h['prob_up']:.1f}% Aufwärts-Wahrscheinlichkeit)")
    print(f"   Risiko-Level: {risk_level} (Volatilität: {pred_48h['volatility']:.1f}%)")

    # FINAL Wahrscheinlichkeiten
    all_preds = pred_48h['all_predictions']
    prob_gain_2 = np.mean(all_preds > current_price * 1.02) * 100
    prob_gain_5 = np.mean(all_preds > current_price * 1.05) * 100
    prob_loss_2 = np.mean(all_preds < current_price * 0.98) * 100
    prob_loss_5 = np.mean(all_preds < current_price * 0.95) * 100

    print(f"\n📈 WAHRSCHEINLICHKEITEN:")
    print(f"   Preis steigt: {pred_48h['prob_up']:.1f}%")
    print(f"   Gewinn >2%: {prob_gain_2:.1f}%")
    print(f"   Gewinn >5%: {prob_gain_5:.1f}%")
    print(f"   Verlust >2%: {prob_loss_2:.1f}%")
    print(f"   Verlust >5%: {prob_loss_5:.1f}%")

    # FINAL Optimierungen
    print(f"\n🚀 FINAL OPTIMIERUNGEN:")
    print(f"   ✅ Reduzierte Monte Carlo Simulationen (100)")
    print(f"   ✅ Optimierte Modell-Parameter")
    print(f"   ✅ Reduzierte Sequence Length (12)")
    print(f"   ✅ Nur beste Algorithmen (RF, ET, GB)")
    print(f"   ✅ Gewichtetes Ensemble")
    print(f"   ✅ Adaptive Constraints")
    print(f"   ✅ Threading für Monte Carlo")
    print(f"   ✅ Robuste Datenbereinigung")

    print(f"\n⚡ FINAL PERFORMANCE: {total_time:.1f}s | Monte Carlo: {MONTE_CARLO_SIMS} | Threading: {MAX_THREADS}")
    print("="*70)

    print(f"\n🎉 FINAL 48H ANALYSE ABGESCHLOSSEN in {total_time:.1f}s! 🎉")

def create_final_visualization(predictions, current_price, current_time):
    """FINAL Visualisierung - kompakt und informativ"""
    print("\n📊 Erstelle FINAL Visualisierung...")

    hours = list(predictions.keys())
    means = [predictions[h]['mean'] for h in hours]
    stds = [predictions[h]['std'] for h in hours]

    times = [current_time + timedelta(hours=h) for h in hours]

    plt.figure(figsize=(12, 8))

    # Hauptplot
    plt.subplot(2, 2, 1)
    plt.plot([current_time] + times, [current_price] + means, 'o-', color='#00ff88', linewidth=3, label='FINAL Prognose')

    # Konfidenzintervall
    upper = [m + s for m, s in zip(means, stds)]
    lower = [m - s for m, s in zip(means, stds)]
    plt.fill_between(times, upper, lower, alpha=0.3, color='#00ff88', label='±1σ Bereich')

    plt.axhline(y=current_price, color='white', linestyle='--', alpha=0.7, label='Aktueller Preis')
    plt.title('🚀 FINAL 48H Bitcoin Preisprognose', fontsize=16, color='white')
    plt.xlabel('Zeit', color='white')
    plt.ylabel('Preis (USD)', color='white')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Änderungen
    plt.subplot(2, 2, 2)
    changes = [predictions[h]['change_pct'] for h in hours]
    colors = ['#00ff88' if c >= 0 else '#ff4757' for c in changes]
    plt.bar(range(len(hours)), changes, color=colors, alpha=0.8)
    plt.axhline(y=0, color='white', linestyle='-', alpha=0.7)
    plt.title('FINAL Preisänderungen (%)', fontsize=14, color='white')
    plt.xlabel('Stunden', color='white')
    plt.ylabel('Änderung (%)', color='white')
    plt.xticks(range(len(hours)), [f'{h}h' for h in hours])
    plt.grid(True, alpha=0.3)

    # Wahrscheinlichkeiten
    plt.subplot(2, 2, 3)
    probs = [predictions[h]['prob_up'] for h in hours]
    plt.plot(hours, probs, 'o-', color='#3742fa', linewidth=3, markersize=8)
    plt.axhline(y=50, color='white', linestyle='--', alpha=0.7, label='50% Linie')
    plt.title('FINAL Aufwärts-Wahrscheinlichkeit', fontsize=14, color='white')
    plt.xlabel('Stunden', color='white')
    plt.ylabel('Wahrscheinlichkeit (%)', color='white')
    plt.ylim(0, 100)
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Volatilität
    plt.subplot(2, 2, 4)
    vols = [predictions[h]['volatility'] for h in hours]
    plt.plot(hours, vols, 'o-', color='#ff6b35', linewidth=3, markersize=8)
    plt.title('FINAL Vorhersage-Volatilität', fontsize=14, color='white')
    plt.xlabel('Stunden', color='white')
    plt.ylabel('Volatilität (%)', color='white')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('btc_final_prediction.png', dpi=300, bbox_inches='tight', facecolor='black')
    plt.show()

    print("✅ FINAL Visualisierung gespeichert: btc_final_prediction.png")

def main():
    """FINAL Hauptfunktion"""
    start_time = time.time()

    try:
        # 1. FINAL Datensammlung
        print("\n" + "="*50)
        print("PHASE 1: FINAL DATENSAMMLUNG")
        print("="*50)
        df, is_real = get_bitcoin_data_final()
        current_price = df['close'].iloc[-1]
        current_time = df.index[-1].to_pydatetime()

        # 2. FINAL Feature Engineering
        print("\n" + "="*50)
        print("PHASE 2: FINAL FEATURE ENGINEERING")
        print("="*50)
        df = create_final_features(df)

        # 3. FINAL Datenvorbereitung
        print("\n" + "="*50)
        print("PHASE 3: FINAL DATENAUFBEREITUNG")
        print("="*50)
        train_data, test_data, last_sequence, scalers = prepare_final_data(df)
        feature_scaler, target_scaler = scalers

        # 4. FINAL Modelle
        print("\n" + "="*50)
        print("PHASE 4: FINAL MODEL TRAINING")
        print("="*50)
        results = train_final_models(train_data, test_data)

        # 5. Beste Modelle
        sorted_results = sorted(results.items(), key=lambda x: x[1]['r2'], reverse=True)
        best_models = dict(sorted_results)

        # 6. FINAL 48h Vorhersage
        print("\n" + "="*50)
        print("PHASE 5: FINAL 48H VORHERSAGE")
        print("="*50)

        predictions = predict_final_48h(
            best_models, last_sequence, target_scaler, current_time, current_price, df
        )

        total_time = time.time() - start_time

        # 7. FINAL Ergebnisse
        display_final_results(results, predictions, current_price, current_time, total_time)

        # 8. FINAL Visualisierung
        create_final_visualization(predictions, current_price, current_time)

    except Exception as e:
        print(f"❌ FINAL Fehler: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
