#!/usr/bin/env python3
"""
🖥️ NEUE DESKTOP-VERKNÜPFUNG ERSTELLER 🖥️
========================================
🏆 AUTOMATISCHE DESKTOP-VERKNÜPFUNG FÜR BESTEN LAUNCHER 🏆
✅ Erstellt neue Desktop-Verknüpfung für Ultimate User Friendly Launcher
✅ Beide Typen: Windows-Verknüpfung (.lnk) + Batch-Datei (.bat)
✅ Automatische Erkennung des besten verfügbaren Launchers
✅ Sofortiger Test der erstellten Verknüpfung

💡 EINFACH AUSFÜHREN UND NEUE DESKTOP-VERKNÜPFUNG IST FERTIG!
"""

import os
import sys
import shutil

def find_best_launcher():
    """Finde den besten verfügbaren Launcher"""
    
    print("🔍 Suche nach dem besten verfügbaren Launcher...")
    
    # Prioritätsliste der Launcher (bester zuerst)
    launcher_priority = [
        {
            'file': 'ultimate_user_friendly_launcher.py',
            'name': 'Ultimate User Friendly Launcher',
            'description': 'Benutzerfreundlich, übersichtlich, alle 3 Modelle',
            'shortcut_name': 'Bitcoin Trading - BENUTZERFREUNDLICH'
        },
        {
            'file': 'ultimate_launcher_fixed_final.py',
            'name': 'Ultimate Launcher Fixed Final',
            'description': '100% funktionsfähig, alle 3 Modelle + Gesamtprognose',
            'shortcut_name': 'Bitcoin Trading - FIXED FINAL'
        },
        {
            'file': 'bitcoin_launcher_working.py',
            'name': 'Bitcoin Launcher Working',
            'description': 'Bewährt funktionsfähig, robuster Launcher',
            'shortcut_name': 'Bitcoin Trading - WORKING'
        },
        {
            'file': 'ultimate_complete_bitcoin_trading_FAVORITE.py',
            'name': 'Ultimate Complete Bitcoin Trading FAVORITE',
            'description': 'Das bewährte FAVORIT-System (Session #15+)',
            'shortcut_name': 'Bitcoin Trading - FAVORIT'
        }
    ]
    
    # Finde ersten verfügbaren Launcher
    for launcher in launcher_priority:
        if os.path.exists(launcher['file']):
            file_size = os.path.getsize(launcher['file'])
            print(f"✅ Gefunden: {launcher['name']}")
            print(f"   📄 Datei: {launcher['file']}")
            print(f"   📊 Größe: {file_size:,} Bytes")
            print(f"   💡 Beschreibung: {launcher['description']}")
            return launcher
    
    print("❌ Kein geeigneter Launcher gefunden!")
    return None

def create_windows_shortcut(launcher_info):
    """Erstelle Windows-Verknüpfung (.lnk)"""
    
    print(f"🖥️ Erstelle Windows-Verknüpfung für {launcher_info['name']}...")
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        # Pfade ermitteln
        current_dir = os.getcwd()
        launcher_path = os.path.join(current_dir, launcher_info['file'])
        python_path = sys.executable
        
        # Desktop-Pfad ermitteln
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, f"{launcher_info['shortcut_name']}.lnk")
        
        # Erstelle Windows-Verknüpfung
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        
        # Verknüpfungs-Eigenschaften
        shortcut.Targetpath = python_path
        shortcut.Arguments = f'"{launcher_path}"'
        shortcut.WorkingDirectory = current_dir
        shortcut.Description = f"{launcher_info['name']} - {launcher_info['description']}"
        shortcut.IconLocation = python_path + ",0"  # Python-Icon verwenden
        
        # Speichere Verknüpfung
        shortcut.save()
        
        print(f"✅ Windows-Verknüpfung erfolgreich erstellt!")
        print(f"📍 Speicherort: {shortcut_path}")
        
        return True
        
    except ImportError:
        print(f"💡 winshell/pywin32 nicht verfügbar - überspringe Windows-Verknüpfung")
        return False
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Windows-Verknüpfung: {e}")
        return False

def create_batch_shortcut(launcher_info):
    """Erstelle Batch-Datei (.bat)"""
    
    print(f"🔧 Erstelle Batch-Datei für {launcher_info['name']}...")
    
    try:
        current_dir = os.getcwd()
        launcher_path = os.path.join(current_dir, launcher_info['file'])
        
        # Desktop-Pfad ermitteln
        try:
            import winshell
            desktop = winshell.desktop()
        except:
            # Fallback für Desktop-Pfad
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        
        # Batch-Datei Pfad
        batch_path = os.path.join(desktop, f"{launcher_info['shortcut_name']}.bat")
        
        # Batch-Inhalt
        batch_content = f'''@echo off
title {launcher_info['name']}
color 0A
echo.
echo ========================================
echo    {launcher_info['name'].upper()}
echo ========================================
echo    {launcher_info['description']}
echo ========================================
echo.
cd /d "{current_dir}"
python "{launcher_path}"
if errorlevel 1 (
    echo.
    echo ❌ Fehler beim Starten des Launchers
    echo 💡 Stellen Sie sicher, dass Python installiert ist
    echo 📄 Launcher-Datei: {launcher_path}
    echo.
    pause
) else (
    echo.
    echo ✅ Launcher erfolgreich beendet
    echo 👋 Bis zum naechsten Mal!
    echo.
    timeout /t 3 >nul
)
'''
        
        # Schreibe Batch-Datei auf Desktop
        with open(batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"✅ Batch-Datei erfolgreich erstellt!")
        print(f"📍 Desktop-Speicherort: {batch_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Batch-Datei: {e}")
        return False

def test_desktop_shortcuts(launcher_info):
    """Teste erstellte Desktop-Verknüpfungen"""
    
    print("🧪 Teste erstellte Desktop-Verknüpfungen...")
    
    try:
        # Desktop-Pfad ermitteln
        try:
            import winshell
            desktop = winshell.desktop()
        except:
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        
        # Prüfe verfügbare Verknüpfungen
        shortcut_lnk = os.path.join(desktop, f"{launcher_info['shortcut_name']}.lnk")
        shortcut_bat = os.path.join(desktop, f"{launcher_info['shortcut_name']}.bat")
        
        found_shortcuts = []
        
        if os.path.exists(shortcut_lnk):
            found_shortcuts.append(("Windows-Verknüpfung (.lnk)", shortcut_lnk))
        
        if os.path.exists(shortcut_bat):
            found_shortcuts.append(("Batch-Datei (.bat)", shortcut_bat))
        
        if found_shortcuts:
            print(f"✅ {len(found_shortcuts)} Desktop-Verknüpfung(en) erfolgreich erstellt:")
            for shortcut_type, path in found_shortcuts:
                print(f"   📄 {shortcut_type}: {os.path.basename(path)}")
            return True
        else:
            print(f"❌ Keine Desktop-Verknüpfungen gefunden!")
            return False
            
    except Exception as e:
        print(f"❌ FEHLER beim Testen der Desktop-Verknüpfungen: {e}")
        return False

def show_usage_instructions(launcher_info):
    """Zeige Verwendungsanweisungen"""
    
    print(f"\n💡 VERWENDUNG DER NEUEN DESKTOP-VERKNÜPFUNG:")
    print("=" * 80)
    
    print(f"🚀 LAUNCHER: {launcher_info['name']}")
    print(f"📝 BESCHREIBUNG: {launcher_info['description']}")
    print(f"📄 DATEI: {launcher_info['file']}")
    
    print(f"\n🖱️ SO VERWENDEN SIE DIE VERKNÜPFUNG:")
    print(f"1. Schauen Sie auf Ihren Desktop")
    print(f"2. Doppelklick auf '{launcher_info['shortcut_name']}'")
    print(f"3. Launcher startet automatisch")
    
    # Spezifische Anweisungen je nach Launcher
    if 'user_friendly' in launcher_info['file']:
        print(f"\n🎯 BENUTZERFREUNDLICHE BEDIENUNG:")
        print(f"   • Wählen Sie '1' für FAVORIT (empfohlen)")
        print(f"   • Wählen Sie 'a' für alle 3 Modelle")
        print(f"   • Wählen Sie 'g' für Gesamtprognose")
        print(f"   • Wählen Sie 'h' für Hilfe")
        print(f"   • Wählen Sie 'q' zum Beenden")
    elif 'fixed_final' in launcher_info['file']:
        print(f"\n🎯 FIXED FINAL BEDIENUNG:")
        print(f"   • Wählen Sie 's1/s2/s3' für einzelne Systeme")
        print(f"   • Wählen Sie 'a' für alle Systeme")
        print(f"   • Wählen Sie 'e' für Gesamtprognose")
        print(f"   • Wählen Sie 'i' für Status")
        print(f"   • Wählen Sie 'q' zum Beenden")
    elif 'FAVORITE' in launcher_info['file']:
        print(f"\n🎯 FAVORIT-SYSTEM:")
        print(f"   • Das bewährte System startet automatisch")
        print(f"   • Session #15+ mit 100% Genauigkeit")
        print(f"   • Warten Sie auf die Ergebnisse")
    
    print(f"\n✅ IHRE NEUE DESKTOP-VERKNÜPFUNG IST BEREIT!")

def main():
    """Hauptfunktion"""
    
    print("🖥️ NEUE DESKTOP-VERKNÜPFUNG ERSTELLER")
    print("=" * 60)
    print("🚀 Erstelle neue Desktop-Verknüpfung für besten verfügbaren Launcher...")
    print("")
    
    # Finde besten Launcher
    launcher_info = find_best_launcher()
    
    if not launcher_info:
        print("❌ Kein geeigneter Launcher gefunden!")
        print("💡 Stellen Sie sicher, dass Sie im richtigen Verzeichnis sind.")
        return
    
    print("")
    
    # Erstelle beide Verknüpfungstypen
    windows_success = create_windows_shortcut(launcher_info)
    batch_success = create_batch_shortcut(launcher_info)
    
    print("\n" + "=" * 60)
    
    if windows_success or batch_success:
        print("🎉 ERFOLGREICH!")
        print("✅ Neue Desktop-Verknüpfung wurde erstellt")
        
        # Teste Verknüpfungen
        test_success = test_desktop_shortcuts(launcher_info)
        
        # Zeige Verwendungsanweisungen
        show_usage_instructions(launcher_info)
        
    else:
        print("❌ FEHLER!")
        print("💡 Starten Sie den Launcher manuell mit:")
        print(f"   python {launcher_info['file']}")
    
    print("\n👋 Desktop-Verknüpfung Setup abgeschlossen!")

if __name__ == "__main__":
    main()
