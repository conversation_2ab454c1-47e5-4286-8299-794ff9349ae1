#!/usr/bin/env python3
"""
🎯 FINAL SIMPLE BITCOIN PREDICTION - MAXIMUM SIMPLICITY
Extrem vereinfacht für maximale Stabilität
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import warnings
import time
warnings.filterwarnings('ignore')

print("🎯 FINAL SIMPLE BITCOIN PREDICTION")
print("=" * 50)
print("🚀 Maximum Simplicity für Maximum Stability")
print("💡 Linear Models für kleine Datensätze")
print("=" * 50)

# SIMPLE KONFIGURATION
CONFIG = {
    'data_file': 'crypto_data.csv',
    'train_split': 0.80,
    'test_split': 0.20,
    'look_back': 5,                # SEHR kurz
    'target_accuracy': 0.70        # Realistisch
}

class SimpleBitcoinPredictor:
    """Extrem einfacher aber stabiler Predictor"""
    
    def __init__(self):
        self.scalers = {}
        self.models = {}
    
    def load_and_create_simple_features(self):
        """NUR die allereinfachsten Features"""
        print("\n📊 SIMPLE FEATURE ENGINEERING")
        print("-" * 35)
        
        df = pd.read_csv(CONFIG['data_file'])
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        print(f"📈 Daten: {len(df)} Punkte")
        print(f"💰 Aktueller Preis: ${df['close'].iloc[-1]:.2f}")
        
        # NUR die wichtigsten Features
        features = pd.DataFrame(index=df.index)
        
        # 1. Basis OHLCV
        features['close'] = df['close']
        features['volume'] = df['volume']
        features['high'] = df['high']
        features['low'] = df['low']
        
        # 2. Einfache Returns
        features['returns'] = df['close'].pct_change()
        
        # 3. Einfache Moving Averages
        features['sma_5'] = df['close'].rolling(5).mean()
        features['sma_10'] = df['close'].rolling(10).mean()
        
        # 4. Preis Ratios
        features['price_sma5_ratio'] = df['close'] / features['sma_5']
        features['hl_ratio'] = df['high'] / df['low']
        
        # 5. Volumen Ratio
        features['volume_sma'] = df['volume'].rolling(5).mean()
        features['volume_ratio'] = df['volume'] / features['volume_sma']
        
        # Cleanup
        features = features.dropna()
        
        print(f"   ✅ {len(features.columns)} Simple Features")
        print(f"   📊 {len(features)} saubere Datenpunkte")
        
        return features
    
    def create_simple_sequences(self, data, target_col, look_back):
        """Einfache Sequenz-Erstellung"""
        X, y = [], []
        
        feature_cols = [col for col in data.columns if col != target_col]
        
        for i in range(look_back, len(data)):
            # Features der letzten look_back Perioden
            X.append(data[feature_cols].iloc[i-look_back:i].values.flatten())
            # Target: nächster Close-Preis
            y.append(data[target_col].iloc[i])
        
        return np.array(X), np.array(y)
    
    def prepare_simple_data(self, features):
        """Einfache Datenaufbereitung"""
        print("\n🔄 SIMPLE DATENAUFBEREITUNG")
        print("-" * 30)
        
        # Sequenzen erstellen
        print(f"   📦 Erstelle Sequenzen (Look-back: {CONFIG['look_back']})...")
        X, y = self.create_simple_sequences(features, 'close', CONFIG['look_back'])
        
        print(f"   ✅ {len(X)} Sequenzen erstellt")
        print(f"   📐 Feature-Shape: {X.shape}")
        
        # Einfache Skalierung
        print("   🔧 Skaliere Daten...")
        feature_scaler = StandardScaler()
        target_scaler = StandardScaler()
        
        X_scaled = feature_scaler.fit_transform(X)
        y_scaled = target_scaler.fit_transform(y.reshape(-1, 1)).flatten()
        
        # Speichere Scaler
        self.scalers['feature'] = feature_scaler
        self.scalers['target'] = target_scaler
        
        return X_scaled, y_scaled
    
    def split_simple_data(self, X, y):
        """Einfache Datenaufteilung"""
        print("\n✂️  SIMPLE DATENAUFTEILUNG")
        print("-" * 25)
        
        total_size = len(X)
        train_size = int(total_size * CONFIG['train_split'])
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        X_test = X[train_size:]
        y_test = y[train_size:]
        
        print(f"📊 Training: {len(X_train)} ({len(X_train)/total_size*100:.1f}%)")
        print(f"📊 Test: {len(X_test)} ({len(X_test)/total_size*100:.1f}%)")
        
        return (X_train, y_train), (X_test, y_test)
    
    def train_simple_models(self, X_train, y_train):
        """Trainiere einfache aber robuste Modelle"""
        print("\n🤖 SIMPLE MODEL TRAINING")
        print("-" * 30)
        
        models = {}
        
        # 1. Linear Regression
        print("   📈 Trainiere Linear Regression...")
        lr = LinearRegression()
        lr.fit(X_train, y_train)
        models['Linear'] = lr
        
        # 2. Ridge Regression (mit Regularisierung)
        print("   📈 Trainiere Ridge Regression...")
        ridge = Ridge(alpha=1.0)
        ridge.fit(X_train, y_train)
        models['Ridge'] = ridge
        
        # 3. Random Forest (einfach)
        print("   🌲 Trainiere Random Forest...")
        rf = RandomForestRegressor(n_estimators=50, max_depth=5, random_state=42)
        rf.fit(X_train, y_train)
        models['RandomForest'] = rf
        
        self.models = models
        return models
    
    def evaluate_simple_models(self, X_test, y_test):
        """Evaluiere alle Modelle"""
        print("\n📊 SIMPLE EVALUATION")
        print("-" * 25)
        
        results = {}
        
        for name, model in self.models.items():
            print(f"\n   🔍 Evaluiere {name}...")
            
            # Vorhersagen
            y_pred = model.predict(X_test)
            
            # Skalierung rückgängig
            y_test_orig = self.scalers['target'].inverse_transform(y_test.reshape(-1, 1)).flatten()
            y_pred_orig = self.scalers['target'].inverse_transform(y_pred.reshape(-1, 1)).flatten()
            
            # Metriken
            r2 = r2_score(y_test_orig, y_pred_orig)
            rmse = np.sqrt(mean_squared_error(y_test_orig, y_pred_orig))
            mae = mean_absolute_error(y_test_orig, y_pred_orig)
            mape = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig)) * 100
            
            # Richtungsgenauigkeit
            if len(y_test_orig) > 1:
                true_direction = np.diff(y_test_orig) > 0
                pred_direction = np.diff(y_pred_orig) > 0
                direction_acc = np.mean(true_direction == pred_direction) * 100
            else:
                direction_acc = 0
            
            # Accuracy Bands
            accuracy_5pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.05) * 100
            accuracy_10pct = np.mean(np.abs((y_test_orig - y_pred_orig) / y_test_orig) < 0.10) * 100
            
            results[name] = {
                'r2': r2,
                'rmse': rmse,
                'mae': mae,
                'mape': mape,
                'direction_accuracy': direction_acc,
                'accuracy_5pct': accuracy_5pct,
                'accuracy_10pct': accuracy_10pct,
                'y_test_orig': y_test_orig,
                'y_pred_orig': y_pred_orig
            }
            
            print(f"      📈 R²: {r2:.4f} ({r2*100:.1f}%)")
            print(f"      💰 RMSE: ${rmse:.2f}")
            print(f"      📊 MAPE: {mape:.2f}%")
            print(f"      🎯 Direction: {direction_acc:.1f}%")
            print(f"      ✅ 5% Acc: {accuracy_5pct:.1f}%")
        
        return results
    
    def create_simple_ensemble(self, results):
        """Einfaches Ensemble der besten Modelle"""
        print(f"\n🏆 SIMPLE ENSEMBLE")
        print("-" * 20)
        
        # Nur Modelle mit positivem R² verwenden
        good_models = {name: res for name, res in results.items() if res['r2'] > 0}
        
        if not good_models:
            print("   ❌ Keine Modelle mit positivem R² - verwende bestes Modell")
            best_model = max(results.items(), key=lambda x: x[1]['r2'])
            return best_model[1]
        
        print(f"   ✅ {len(good_models)} gute Modelle für Ensemble")
        
        # Einfache Gewichtung basierend auf R²
        weights = []
        predictions = []
        
        for name, result in good_models.items():
            weight = max(0.1, result['r2'])  # Mindestgewicht 10%
            weights.append(weight)
            predictions.append(result['y_pred_orig'])
            print(f"      {name}: R²={result['r2']:.3f}, Gewicht={weight:.3f}")
        
        # Normalisiere Gewichte
        weights = np.array(weights)
        weights = weights / np.sum(weights)
        
        # Gewichtete Vorhersage
        ensemble_pred = np.average(predictions, axis=0, weights=weights)
        
        # Evaluation
        y_test_orig = list(good_models.values())[0]['y_test_orig']
        
        ensemble_r2 = r2_score(y_test_orig, ensemble_pred)
        ensemble_rmse = np.sqrt(mean_squared_error(y_test_orig, ensemble_pred))
        ensemble_mape = np.mean(np.abs((y_test_orig - ensemble_pred) / y_test_orig)) * 100
        
        # Richtungsgenauigkeit
        if len(y_test_orig) > 1:
            true_direction = np.diff(y_test_orig) > 0
            pred_direction = np.diff(ensemble_pred) > 0
            ensemble_direction_acc = np.mean(true_direction == pred_direction) * 100
        else:
            ensemble_direction_acc = 0
        
        ensemble_accuracy_5pct = np.mean(np.abs((y_test_orig - ensemble_pred) / y_test_orig) < 0.05) * 100
        
        ensemble_result = {
            'model_name': 'Simple_Ensemble',
            'r2': ensemble_r2,
            'rmse': ensemble_rmse,
            'mape': ensemble_mape,
            'direction_accuracy': ensemble_direction_acc,
            'accuracy_5pct': ensemble_accuracy_5pct,
            'y_test_orig': y_test_orig,
            'y_pred_orig': ensemble_pred
        }
        
        print(f"\n   🎯 ENSEMBLE PERFORMANCE:")
        print(f"      📈 R²: {ensemble_r2:.4f} ({ensemble_r2*100:.1f}%)")
        print(f"      💰 RMSE: ${ensemble_rmse:.2f}")
        print(f"      📊 MAPE: {ensemble_mape:.2f}%")
        print(f"      🎯 Direction: {ensemble_direction_acc:.1f}%")
        
        return ensemble_result

    def plot_simple_results(self, results, ensemble_result):
        """Einfache Visualisierung"""
        print(f"\n📈 SIMPLE VISUALISIERUNG")
        print("-" * 25)

        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('🎯 FINAL SIMPLE BITCOIN PREDICTION - RESULTS', fontsize=14, fontweight='bold')

        # 1. Model Comparison
        model_names = list(results.keys()) + ['Ensemble']
        r2_scores = [results[name]['r2'] for name in results.keys()] + [ensemble_result['r2']]

        colors = ['blue', 'green', 'orange', 'gold']
        axes[0, 0].bar(model_names, r2_scores, color=colors[:len(model_names)])
        axes[0, 0].set_title('R² Score Vergleich')
        axes[0, 0].set_ylabel('R² Score')
        axes[0, 0].tick_params(axis='x', rotation=45)
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].axhline(y=CONFIG['target_accuracy'], color='red', linestyle='--', label=f'Ziel: {CONFIG["target_accuracy"]*100:.0f}%')
        axes[0, 0].legend()

        # 2. Ensemble Prediction
        y_test = ensemble_result['y_test_orig'][:50]  # Erste 50 Punkte
        y_pred = ensemble_result['y_pred_orig'][:50]

        axes[0, 1].plot(y_test, 'g-', label='Actual', linewidth=2)
        axes[0, 1].plot(y_pred, 'r--', label='Ensemble', linewidth=2)
        axes[0, 1].set_title('Ensemble Vorhersage')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 3. Scatter Plot
        axes[1, 0].scatter(ensemble_result['y_test_orig'], ensemble_result['y_pred_orig'], alpha=0.6)
        min_val = min(ensemble_result['y_test_orig'].min(), ensemble_result['y_pred_orig'].min())
        max_val = max(ensemble_result['y_test_orig'].max(), ensemble_result['y_pred_orig'].max())
        axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
        axes[1, 0].set_title('Scatter Plot')
        axes[1, 0].set_xlabel('Actual')
        axes[1, 0].set_ylabel('Predicted')
        axes[1, 0].grid(True, alpha=0.3)

        # 4. Summary
        axes[1, 1].axis('off')

        target_reached = "✅ JA" if ensemble_result['r2'] >= CONFIG['target_accuracy'] else "❌ NEIN"

        summary_text = f"""
🎯 FINAL SIMPLE RESULTS

📈 ENSEMBLE PERFORMANCE:
   R²: {ensemble_result['r2']:.4f} ({ensemble_result['r2']*100:.1f}%)
   RMSE: ${ensemble_result['rmse']:.2f}
   MAPE: {ensemble_result['mape']:.2f}%
   Direction: {ensemble_result['direction_accuracy']:.1f}%

✅ ACCURACY:
   5% Band: {ensemble_result['accuracy_5pct']:.1f}%

🎯 ZIEL ERREICHT: {target_reached}
   Target: {CONFIG['target_accuracy']*100:.0f}%
   Erreicht: {ensemble_result['r2']*100:.1f}%

📊 SYSTEM:
   Modelle: {len(results)}
   Test Samples: {len(ensemble_result['y_test_orig'])}
        """

        axes[1, 1].text(0.1, 0.5, summary_text, fontsize=10, verticalalignment='center',
                        bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()
        plt.show()

        print("   ✅ Visualisierung abgeschlossen")

def main():
    """FINAL SIMPLE HAUPTFUNKTION"""
    print("🎯 STARTE FINAL SIMPLE SYSTEM")
    print("=" * 40)

    start_time = time.time()

    # System initialisieren
    predictor = SimpleBitcoinPredictor()

    # === DATENAUFBEREITUNG ===
    print(f"\n📊 DATENAUFBEREITUNG")
    print("=" * 25)

    # Simple Features erstellen
    features = predictor.load_and_create_simple_features()

    # Daten vorbereiten
    X, y = predictor.prepare_simple_data(features)

    # Daten aufteilen
    (X_train, y_train), (X_test, y_test) = predictor.split_simple_data(X, y)

    print(f"\n✅ DATENAUFBEREITUNG ABGESCHLOSSEN!")
    print(f"   📊 Features: {X.shape[1]}")
    print(f"   📦 Sequenzen: {len(X)}")

    # === MODELL-TRAINING ===
    print(f"\n🤖 SIMPLE MODEL TRAINING")
    print("=" * 30)

    # Trainiere einfache Modelle
    models = predictor.train_simple_models(X_train, y_train)

    # === EVALUATION ===
    results = predictor.evaluate_simple_models(X_test, y_test)

    # === ENSEMBLE ===
    ensemble_result = predictor.create_simple_ensemble(results)

    # === FINALE ANALYSE ===
    print(f"\n🏆 FINALE SIMPLE ANALYSE")
    print("=" * 35)

    total_time = time.time() - start_time

    # Ziel-Check
    if ensemble_result['r2'] >= CONFIG['target_accuracy']:
        print(f"\n🎉🎉🎉 ZIEL ERREICHT! 🎉🎉🎉")
        print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
        improvement = (ensemble_result['r2'] - CONFIG['target_accuracy']) * 100
        print(f"🚀 Übererfüllung: +{improvement:.1f} Prozentpunkte!")
    elif ensemble_result['r2'] >= 0.50:
        print(f"\n🔥🔥 SEHR GUT! 🔥🔥")
        print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
        gap = (CONFIG['target_accuracy'] - ensemble_result['r2']) * 100
        print(f"Nur noch {gap:.1f} Prozentpunkte bis zum Ziel!")
    elif ensemble_result['r2'] >= 0.30:
        print(f"\n💪💪 GUT! 💪💪")
        print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
        gap = (CONFIG['target_accuracy'] - ensemble_result['r2']) * 100
        print(f"Noch {gap:.1f} Prozentpunkte bis zum Ziel")
    elif ensemble_result['r2'] >= 0:
        print(f"\n✅ POSITIVE ERGEBNISSE!")
        print(f"Target: {CONFIG['target_accuracy']*100:.1f}% - Erreicht: {ensemble_result['r2']*100:.1f}%")
        print(f"Modell lernt - gute Basis!")
    else:
        print(f"\n🔧 WEITERE ANPASSUNG NÖTIG")
        print(f"R²: {ensemble_result['r2']*100:.1f}% - Modell braucht mehr Daten")

    # Performance Details
    print(f"\n📈 DETAILLIERTE PERFORMANCE:")
    print(f"   R²: {ensemble_result['r2']:.4f} ({ensemble_result['r2']*100:.1f}%)")
    print(f"   RMSE: ${ensemble_result['rmse']:.2f}")
    print(f"   MAPE: {ensemble_result['mape']:.2f}%")
    print(f"   Direction: {ensemble_result['direction_accuracy']:.1f}%")
    print(f"   5% Accuracy: {ensemble_result['accuracy_5pct']:.1f}%")

    # System Stats
    print(f"\n⚡ SYSTEM-STATISTIKEN:")
    print(f"   Zeit: {total_time:.1f}s")
    print(f"   Modelle: {len(results)}")
    print(f"   Test Samples: {len(ensemble_result['y_test_orig'])}")
    print(f"   Look-back: {CONFIG['look_back']}")

    # Beste Einzelmodelle
    best_model = max(results.items(), key=lambda x: x[1]['r2'])
    print(f"\n🏆 BESTES EINZELMODELL:")
    print(f"   {best_model[0]}: {best_model[1]['r2']*100:.1f}% R²")

    # Visualisierung
    predictor.plot_simple_results(results, ensemble_result)

    print(f"\n✅ FINAL SIMPLE SYSTEM ABGESCHLOSSEN!")

    return ensemble_result

if __name__ == "__main__":
    result = main()

    if result['r2'] >= CONFIG['target_accuracy']:
        print(f"\n🎯 MISSION ACCOMPLISHED! 🎯")
        print(f"Simple System erreicht das Ziel von {CONFIG['target_accuracy']*100:.0f}%!")
    elif result['r2'] >= 0:
        print(f"\n💪 GUTE BASIS GESCHAFFEN!")
        print(f"Positive R² erreicht - System funktioniert!")
    else:
        print(f"\n🔧 MEHR DATEN BENÖTIGT")
        print(f"System braucht größeren Datensatz für bessere Performance.")
