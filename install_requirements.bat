@echo off
title Bitcoin Trading System - Paket Installation
color 0C

echo ================================================================================
echo BITCOIN TRADING SYSTEM - PAKET INSTALLATION
echo ================================================================================
echo.
echo Installiere alle erforderlichen Python-Pakete...
echo.

cd /d "e:\Dev"

echo Pruefe Python Installation...
python --version
if errorlevel 1 (
    echo FEHLER: Python ist nicht installiert oder nicht im PATH!
    echo Bitte installieren Sie Python 3.8+ von https://python.org
    pause
    exit /b 1
)

echo.
echo ================================================================================
echo INSTALLIERE BASIS-PAKETE
echo ================================================================================
echo.

echo Installiere yfinance...
pip install yfinance

echo Installiere pandas...
pip install pandas

echo Installiere numpy...
pip install numpy

echo Installiere scikit-learn...
pip install scikit-learn

echo Installiere requests...
pip install requests

echo.
echo ================================================================================
echo INSTALLIERE GUI-PAKETE
echo ================================================================================
echo.

echo Installiere matplotlib...
pip install matplotlib

echo Installiere seaborn...
pip install seaborn

echo.
echo ================================================================================
echo INSTALLIERE ERWEITERTE ML-PAKETE
echo ================================================================================
echo.

echo Installiere xgboost...
pip install xgboost

echo Installiere scipy...
pip install scipy

echo.
echo ================================================================================
echo INSTALLATION ABGESCHLOSSEN
echo ================================================================================
echo.
echo Alle Pakete wurden erfolgreich installiert!
echo Sie koennen jetzt die Bitcoin Trading Scripts starten.
echo.
pause
