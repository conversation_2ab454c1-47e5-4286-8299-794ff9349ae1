#!/usr/bin/env python3
"""
🖥️ DESKTOP-VERKNÜPFUNG FÜR BENUTZERFREUNDLICHEN LAUNCHER 🖥️
============================================================
🏆 AUTOMATISCHE DESKTOP-VERKNÜPFUNG ERSTELLER 🏆
✅ Erstellt Desktop-Verknüpfung für Ultimate User Friendly Launcher
✅ Übersichtlich, bedienungsfreundlich, alle 3 bewährten Modelle
✅ Windows-kompatibel mit professionellem Icon
✅ Ein-Klick-Start für einfaches Bitcoin Trading

💡 EINFACH AUSFÜHREN UND BENUTZERFREUNDLICHER LAUNCHER IST VOM DESKTOP STARTBAR!
"""

import os
import sys
import shutil

def create_user_friendly_shortcut():
    """Erstelle Desktop-Verknüpfung für User Friendly Launcher"""
    
    print("🖥️ Erstelle Desktop-Verknüpfung für Ultimate User Friendly Launcher...")
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        # Pfade ermitteln
        current_dir = os.getcwd()
        launcher_path = os.path.join(current_dir, "ultimate_user_friendly_launcher.py")
        python_path = sys.executable
        
        # Desktop-Pfad ermitteln
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "Ultimate Bitcoin Trading Launcher - BENUTZERFREUNDLICH.lnk")
        
        # Prüfe ob User Friendly Launcher existiert
        if not os.path.exists(launcher_path):
            print(f"❌ FEHLER: ultimate_user_friendly_launcher.py nicht gefunden!")
            print(f"💡 Stellen Sie sicher, dass der User Friendly Launcher im aktuellen Ordner ist.")
            return False
        
        # Erstelle Windows-Verknüpfung
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        
        # Verknüpfungs-Eigenschaften
        shortcut.Targetpath = python_path
        shortcut.Arguments = f'"{launcher_path}"'
        shortcut.WorkingDirectory = current_dir
        shortcut.Description = "Ultimate Bitcoin Trading Launcher - BENUTZERFREUNDLICH: Übersichtlich, einfach zu bedienen, alle 3 bewährten Modelle"
        shortcut.IconLocation = python_path + ",0"  # Python-Icon verwenden
        
        # Speichere Verknüpfung
        shortcut.save()
        
        print(f"✅ Windows-Verknüpfung erfolgreich erstellt!")
        print(f"📍 Speicherort: {shortcut_path}")
        print(f"🎯 Ziel: {launcher_path}")
        
        return True
        
    except ImportError as e:
        print(f"💡 winshell/pywin32 nicht verfügbar - verwende Batch-Alternative")
        return False
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Windows-Verknüpfung: {e}")
        return False

def copy_batch_to_desktop():
    """Erstelle und kopiere Batch-Datei auf Desktop"""
    
    print("🔧 Erstelle und kopiere Batch-Datei auf Desktop...")
    
    try:
        current_dir = os.getcwd()
        launcher_path = os.path.join(current_dir, "ultimate_user_friendly_launcher.py")
        
        # Desktop-Pfad ermitteln
        try:
            import winshell
            desktop = winshell.desktop()
        except:
            # Fallback für Desktop-Pfad
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        
        # Batch-Datei Pfad
        desktop_batch = os.path.join(desktop, "Ultimate Bitcoin Trading Launcher - BENUTZERFREUNDLICH.bat")
        
        # Batch-Inhalt
        batch_content = f'''@echo off
title Ultimate Bitcoin Trading Launcher - BENUTZERFREUNDLICH
color 0A
echo.
echo ========================================
echo    ULTIMATE BITCOIN TRADING LAUNCHER
echo    BENUTZERFREUNDLICH
echo ========================================
echo    Uebersichtlich • Einfach zu bedienen
echo    Alle 3 bewaehrten Modelle
echo    Intelligente Menuefuehrung
echo ========================================
echo.
cd /d "{current_dir}"
python "{launcher_path}"
if errorlevel 1 (
    echo.
    echo ❌ Fehler beim Starten des Benutzerfreundlichen Launchers
    echo 💡 Stellen Sie sicher, dass Python installiert ist
    echo 📄 Launcher-Datei: {launcher_path}
    echo.
    pause
) else (
    echo.
    echo ✅ Benutzerfreundlicher Launcher erfolgreich beendet
    echo 👋 Bis zum naechsten Mal!
    echo.
    timeout /t 3 >nul
)
'''
        
        # Schreibe Batch-Datei auf Desktop
        with open(desktop_batch, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"✅ Batch-Datei erfolgreich auf Desktop erstellt!")
        print(f"📍 Desktop-Speicherort: {desktop_batch}")
        
        # Erstelle auch eine Kopie im aktuellen Verzeichnis
        local_batch_path = os.path.join(current_dir, "Ultimate Bitcoin Trading Launcher - BENUTZERFREUNDLICH.bat")
        with open(local_batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"✅ Lokale Batch-Datei erstellt: {local_batch_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ FEHLER beim Erstellen der Batch-Datei: {e}")
        return False

def check_launcher_files():
    """Prüfe verfügbare Launcher-Dateien"""
    
    print("📄 Prüfe verfügbare Launcher-Dateien...")
    
    current_dir = os.getcwd()
    launcher_files = [
        "ultimate_user_friendly_launcher.py",
        "ultimate_launcher_fixed_final.py",
        "bitcoin_launcher_working.py",
        "ultimate_complete_bitcoin_trading_FAVORITE.py"
    ]
    
    available_files = []
    
    for file in launcher_files:
        file_path = os.path.join(current_dir, file)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            available_files.append((file, file_size))
            print(f"✅ {file} - {file_size:,} Bytes")
        else:
            print(f"❌ {file} - Nicht gefunden")
    
    if available_files:
        print(f"\n💡 {len(available_files)} Launcher-Dateien verfügbar")
        return True
    else:
        print(f"\n❌ Keine Launcher-Dateien gefunden!")
        return False

def test_desktop_shortcut():
    """Teste ob Desktop-Verknüpfung funktioniert"""
    
    print("🧪 Teste Desktop-Verknüpfung...")
    
    try:
        # Desktop-Pfad ermitteln
        try:
            import winshell
            desktop = winshell.desktop()
        except:
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        
        # Prüfe verfügbare Verknüpfungen
        shortcut_lnk = os.path.join(desktop, "Ultimate Bitcoin Trading Launcher - BENUTZERFREUNDLICH.lnk")
        shortcut_bat = os.path.join(desktop, "Ultimate Bitcoin Trading Launcher - BENUTZERFREUNDLICH.bat")
        
        found_shortcuts = []
        
        if os.path.exists(shortcut_lnk):
            found_shortcuts.append(("Windows-Verknüpfung (.lnk)", shortcut_lnk))
        
        if os.path.exists(shortcut_bat):
            found_shortcuts.append(("Batch-Datei (.bat)", shortcut_bat))
        
        if found_shortcuts:
            print(f"✅ {len(found_shortcuts)} Desktop-Verknüpfung(en) gefunden:")
            for shortcut_type, path in found_shortcuts:
                print(f"   📄 {shortcut_type}: {os.path.basename(path)}")
            return True
        else:
            print(f"❌ Keine Desktop-Verknüpfungen gefunden!")
            return False
            
    except Exception as e:
        print(f"❌ FEHLER beim Testen der Desktop-Verknüpfung: {e}")
        return False

def main():
    """Hauptfunktion"""
    
    print("🖥️ DESKTOP-VERKNÜPFUNG FÜR ULTIMATE BENUTZERFREUNDLICHEN LAUNCHER")
    print("=" * 90)
    print("🚀 Erstelle Desktop-Verknüpfung für ultimate_user_friendly_launcher.py...")
    print("")
    
    # Prüfe verfügbare Dateien
    files_available = check_launcher_files()
    
    if not files_available:
        print("❌ Keine Launcher-Dateien gefunden!")
        print("💡 Stellen Sie sicher, dass Sie im richtigen Verzeichnis sind.")
        return
    
    print("")
    
    # Versuche Windows-Verknüpfung zu erstellen
    shortcut_success = create_user_friendly_shortcut()
    
    # Erstelle Batch-Datei auf Desktop
    batch_success = copy_batch_to_desktop()
    
    print("\n" + "=" * 90)
    
    if shortcut_success or batch_success:
        print("🎉 ERFOLGREICH!")
        print("✅ Desktop-Verknüpfung wurde erstellt")
        print("🖱️ Schauen Sie auf Ihren Desktop nach:")
        if shortcut_success:
            print("   📄 'Ultimate Bitcoin Trading Launcher - BENUTZERFREUNDLICH.lnk'")
        if batch_success:
            print("   📄 'Ultimate Bitcoin Trading Launcher - BENUTZERFREUNDLICH.bat'")
    else:
        print("❌ FEHLER!")
        print("💡 Starten Sie den Launcher manuell mit:")
        print("   python ultimate_user_friendly_launcher.py")
    
    # Teste Desktop-Verknüpfung
    print("")
    test_success = test_desktop_shortcut()
    
    print("\n💡 ULTIMATE BENUTZERFREUNDLICHER LAUNCHER FEATURES:")
    print("🏅 Alle 3 bewährten Bitcoin Trading Modelle:")
    print("   1. FAVORIT - Das bewährte System (EMPFOHLEN)")
    print("   2. SCHNELL - Das effiziente System")
    print("   3. KI-SYSTEM - Das intelligente System")
    print("🎯 Übersichtliche Menüführung ohne komplizierte Befehle")
    print("🔮 Gesamtprognose-Funktionalität aus allen 3 Modellen")
    print("🔄 Kontinuierliche Berechnung für bessere Ergebnisse")
    print("📊 Detaillierter Status und Live-Monitoring")
    print("💡 Integrierte Hilfe und Erklärungen")
    print("🛑 Automatischer Script-Stop beim Beenden")
    
    print("\n🚀 EINFACHE VERWENDUNG:")
    print("1. Doppelklick auf Desktop-Verknüpfung")
    print("2. Wählen Sie '1' für FAVORIT (empfohlen)")
    print("3. Wählen Sie 'a' für alle 3 Modelle")
    print("4. Wählen Sie 'g' für Gesamtprognose")
    print("5. Wählen Sie 'k' für kontinuierliche Berechnung")
    print("6. Wählen Sie 'h' für Hilfe")
    print("7. Wählen Sie 'q' zum Beenden")
    
    print("\n🎯 BENUTZERFREUNDLICHE BEDIENUNG:")
    print("✅ Klare, übersichtliche Menüs")
    print("✅ Einfache Buchstaben/Zahlen-Eingabe")
    print("✅ Detaillierte Beschreibungen aller Optionen")
    print("✅ Integrierte Hilfe-Funktion")
    print("✅ Empfehlungen für optimale Nutzung")
    
    print("\n👋 Desktop-Verknüpfung Setup abgeschlossen!")

if __name__ == "__main__":
    main()
