import requests
import pandas as pd
import json

# Alpha Vantage API-URL und Parameter
AV_API_KEY = "4BE4K72DV3XQLNQ8"
AV_BASE_URL = "https://www.alphavantage.co/query"
AV_PARAMS = {
    "function": "DIGITAL_CURRENCY_DAILY",
    "symbol": "BTC",
    "market": "USD",
    "apikey": AV_API_KEY
}

# CoinCompare API-URL und Parameter
CC_API_KEY = "ada072649005d071f98d9a975775fd3813feab86a24f5c3eca749cac19211d2e"
CC_BASE_URL = "https://min-api.cryptocompare.com/data/v2/histoday"
CC_PARAMS = {
    "fsym": "BTC",
    "tsym": "USD",
    "limit": 2000,
    "api_key": CC_API_KEY
}

# Funktion zum Abrufen der Daten mit Fehlerbehandlung
def fetch_data(url, params, api_name):
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()  # Check for HTTP errors
        data = response.json()
        if "Note" in data:
            print(f"Rate limit exceeded for {api_name}. Please try again later.")
            return None
        return data
    except requests.exceptions.HTTPError as errh:
        print(f"Http Error: {errh}")
    except requests.exceptions.ConnectionError as errc:
        print(f"Error Connecting: {errc}")
    except requests.exceptions.Timeout as errt:
        print(f"Timeout Error: {errt}")
    except requests.exceptions.RequestException as err:
        print(f"Something went wrong with {api_name}: {err}")
    return None

# Datenabruf von Alpha Vantage
data_av = fetch_data(AV_BASE_URL, AV_PARAMS, "Alpha Vantage")
if data_av and "Time Series (Digital Currency Daily)" in data_av:
    data_av = data_av["Time Series (Digital Currency Daily)"]
    df_av = pd.DataFrame(data_av).T
    df_av.index = pd.to_datetime(df_av.index)
else:
    print("No valid data received from Alpha Vantage.")
    df_av = pd.DataFrame()

# Datenabruf von CoinCompare
data_cc = fetch_data(CC_BASE_URL, CC_PARAMS, "CoinCompare")
if data_cc and "Data" in data_cc and "Data" in data_cc["Data"]:
    data_cc = data_cc["Data"]["Data"]
    df_cc = pd.DataFrame(data_cc)
    df_cc['time'] = pd.to_datetime(df_cc['time'], unit='s')
    df_cc.set_index('time', inplace=True)
else:
    print("No valid data received from CoinCompare.")
    df_cc = pd.DataFrame()

# Datenkombination und Bereinigung nur ausführen, wenn beide DataFrames vorhanden sind
if not df_av.empty and not df_cc.empty:
    df_av.columns = [col.split(" (USD)")[-1] for col in df_av.columns]
    df = pd.concat([df_av, df_cc], axis=1)
    df = df.interpolate().dropna()
    print(df.head())
else:
    print("DataFrames are incomplete, cannot proceed with the analysis.")

